import { LogoutButton } from "@/components/auth/logout-button";
import RenwuLogo from "@/components/svg/renwu-logo";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { goToMainDomain } from "@/lib/utils";
import { Computer, HelpCircle, LogOut, User } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Blocks } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { getUserProfileAction } from "../src/db/actions/user-profile.action";

export function NoAccess() {
  return (
    <div className="flex flex-col items-center justify-center w-screen min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <Suspense fallback={<Skeleton className="w-full h-12 py-2 px-6" />}>
        <Topbar />
      </Suspense>
      <div className="w-full max-w-3xl px-4 py-8 text-center">
        <div className="relative">
          <h1 className="text-[150px] font-bold bg-clip-text text-transparent bg-gradient-to-r from-orange-500 to-red-500 animate-fade-in md:text-[200px]">
            403
          </h1>
          <div className="absolute inset-0 blur-3xl bg-gradient-to-r from-orange-500/20 to-red-500/20 animate-pulse" />
        </div>

        <h2 className="mt-8 mb-4 text-2xl font-semibold text-slate-700 dark:text-slate-200 animate-fade-in">
          Organization Access Denied
        </h2>

        <p className="max-w-md mx-auto mb-8 text-slate-600 dark:text-slate-400 animate-fade-in">
          You don&apos;t have permission to access this organization. Please contact the
          organization administrator if you believe this is a mistake.
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 animate-fade-in">
          <Link href={goToMainDomain("auth/organizations")} className="inline-block">
            <Button variant="default" className="w-full sm:w-auto">
              <Blocks className="w-4 h-4 mr-2" />
              Switch Organizations
            </Button>
          </Link>

          <LogoutButton variant="default" className="w-full sm:w-auto" withIcon={true} />
        </div>

        <div className="mt-12 animate-fade-in">
          <svg
            className="w-48 h-48 mx-auto text-slate-300 dark:text-slate-700"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="0.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {/* Lock icon */}
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
            <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            <circle cx="12" cy="16" r="1" />
            <path d="M12 16v3" />
            {/* Warning elements */}
            <path d="M4 8l16 8M4 16l16-8" />
          </svg>
        </div>
      </div>
    </div>
  );
}

export default NoAccess;

async function Topbar() {
  const userProfile = await getUserProfileAction();

  if (!userProfile) {
    return null;
  }

  return (
    <div className="absolute top-0 left-0 right-0 z-50">
      <div className="flex items-center justify-between h-16 px-4 md:px-6">
        {/* Logo and brand */}
        <div className="flex items-center gap-14">
          <Link href="/" className="flex items-center">
            <RenwuLogo className="w-16 h-16" />
          </Link>
        </div>

        {/* Topbar Actions */}
        <div className="flex items-center gap-4">
          <ThemeToggle />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative w-8 h-8 rounded-full">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={userProfile.avatar} alt="User" />
                  <AvatarFallback>{userProfile.avatarFallback}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <Link href="/home/<USER>">Account</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <Computer className="w-4 h-4" />
                  <Link href={goToMainDomain("auth/organizations")}>My Organizations</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" />
                  <Link href="/home/<USER>">Help</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <LogOut className="w-4 h-4" />
                  <LogoutButton variant="ghost" isMenuButton />
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
