import { source } from "@/lib/fumadocs/source";
import { NextResponse } from "next/server";

export const revalidate = false;

// Simple search implementation without Orama
export async function GET(request: Request) {
  const url = new URL(request.url);
  const query = url.searchParams.get("query")?.toLowerCase() || "";

  if (!query) {
    return NextResponse.json({ results: [] });
  }

  // Simple search logic that doesn't use Orama
  const results = source
    .getPages()
    .filter((page) => {
      const title = page.data.title?.toLowerCase() || "";
      const description = page.data.description?.toLowerCase() || "";
      return title.includes(query) || description.includes(query);
    })
    .map((page) => ({
      title: page.data.title,
      description: page.data.description,
      url: page.url,
      id: page.url,
    }));

  return NextResponse.json({ results });
}
