import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";
import { getTeamsWithMembersByTenantByUserAction } from "@/db/actions/team.actions";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get user teams and user role in parallel
    const [teamsResult, userRoleResult] = await Promise.all([
      getTeamsWithMembersByTenantByUserAction(),
      getCurrentUserOrganizationRoleAction(),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        teams: teamsResult.teams,
        currentUserName: teamsResult.currentUserName,
        userRole: userRoleResult.data,
      },
    });
  } catch (error) {
    console.error("[API] Error fetching user teams:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch user teams",
      },
      { status: 500 }
    );
  }
}
