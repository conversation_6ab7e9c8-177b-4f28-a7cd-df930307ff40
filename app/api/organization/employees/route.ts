import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";
import {
  getAllOrganizationMembersAction,
  getCurrentOrganizationAction,
} from "@/db/actions/organization.action";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get all organization members
    const result = await getAllOrganizationMembersAction();
    const organization = await getCurrentOrganizationAction();

    // Check if user is admin
    const { data: userRole } = await getCurrentUserOrganizationRoleAction();
    const isAdmin = userRole?.isAdmin;

    // Handle error case
    if (!result.data || !organization) {
      return NextResponse.json(
        {
          success: false,
          error: result.message || "Error loading employees",
          data: null,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        employees: result.data,
        organization: organization,
        isAdmin: isAdmin,
      },
    });
  } catch (error) {
    console.info("Error fetching organization employees:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Error loading employees. Please try again later.",
        data: null,
      },
      { status: 500 }
    );
  }
}
