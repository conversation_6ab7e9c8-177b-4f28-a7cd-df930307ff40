import { getClusterProjects } from "@/db/actions/cluster.action";
import { getTeamsByProjectAction } from "@/db/actions/team-projects.action";
import { getTeamsWithMembersByTenantAction } from "@/db/actions/team.actions";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get all projects belonging to the tenant
    const clusterProjects = await getClusterProjects();
    
    if (!clusterProjects) {
      return NextResponse.json({
        success: true,
        data: { projects: [], availableTeams: [] },
      });
    }

    // Get all teams to determine which ones are available for assignment
    const { teams: allTeams } = await getTeamsWithMembersByTenantAction();

    // Flatten projects from all clusters and pre-format them
    const projectsPromises =
      clusterProjects?.flatMap((cluster) =>
        cluster.projects.map(async (project) => {
          // Fetch teams for each project
          const teamsResult = await getTeamsByProjectAction(project.id);
          const teams = teamsResult.success ? teamsResult.data : [];

          return {
            id: project.id,
            name: project.name,
            description: project.description,
            slug: project.slug,
            status: project.status,
            clusterId: cluster.id,
            clusterName: cluster.name,
            clusterSlug: cluster.slug,
            teams: teams,
          };
        })
      ) || [];

    // Wait for all projects to be processed
    const projects = await Promise.all(projectsPromises);

    // Format teams for assignment dropdown
    const availableTeams = allTeams.map((team) => ({
      id: team.id,
      name: team.name,
      description: team.description || null,
    }));

    return NextResponse.json({
      success: true,
      data: { projects, availableTeams },
    });
  } catch (error) {
    console.error("[API] Error fetching organization projects:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch organization projects",
      },
      { status: 500 }
    );
  }
}
