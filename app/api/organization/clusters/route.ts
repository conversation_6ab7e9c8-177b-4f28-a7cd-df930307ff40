import { getClusterProjects } from "@/db/actions/cluster.action";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const clusters = await getClusterProjects();

    return NextResponse.json({
      success: true,
      data: clusters,
    });
  } catch (error) {
    console.error("[API] Error fetching organization clusters:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch organization clusters",
      },
      { status: 500 }
    );
  }
}
