import { getPendingOrganizationInvitationsAction } from "@/db/actions/organization-invitations.action";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          error: "Organization ID is required",
          invitations: [],
        },
        { status: 400 }
      );
    }

    const result = await getPendingOrganizationInvitationsAction(organizationId);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.message || "Failed to fetch invitations",
          invitations: [],
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      invitations: result.invitations || [],
    });
  } catch (error) {
    console.info("Error fetching organization invitations:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Error loading invitations. Please try again later.",
        invitations: [],
      },
      { status: 500 }
    );
  }
}
