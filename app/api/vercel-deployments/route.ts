import { NextResponse } from "next/server";

export async function GET() {
  const { VERCEL_TOKEN, VERCEL_PROJECT_ID } = process.env;

  if (!VERCEL_TOKEN || !VERCEL_PROJECT_ID) {
    return NextResponse.json({ error: "Missing env variables" }, { status: 500 });
  }

  try {
    const response = await fetch(
      `https://api.vercel.com/v6/deployments?projectId=${VERCEL_PROJECT_ID}&limit=5`,
      {
        headers: {
          Authorization: `Bearer ${VERCEL_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      const error = await response.json();
      return NextResponse.json({ error }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data.deployments);
  } catch (error) {
    return NextResponse.json({ error: `Failed to fetch deployments ${error}` }, { status: 500 });
  }
}
