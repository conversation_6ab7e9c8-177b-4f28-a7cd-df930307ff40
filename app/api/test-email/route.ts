import { testResendEmail } from "@/lib/test-email";
import { NextRequest, NextResponse } from "next/server";

/**
 * API route for testing email sending functionality
 * Call with: GET /api/test-email?email=<EMAIL>
 */
export async function GET(request: NextRequest) {
  // Extract email from query parameter or use a default
  const searchParams = request.nextUrl.searchParams;
  const email = searchParams.get("email");

  if (!email) {
    return NextResponse.json(
      {
        success: false,
        message: "Email parameter is required",
      },
      { status: 400 }
    );
  }

  try {
    const result = await testResendEmail(email);

    return NextResponse.json({
      success: result.success,
      message: result.success
        ? `Test email sent successfully to ${email}`
        : result.error || "Failed to send test email",
    });
  } catch (error) {
    console.error("Error in test-email route:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "An unknown error occurred",
      },
      { status: 500 }
    );
  }
}
