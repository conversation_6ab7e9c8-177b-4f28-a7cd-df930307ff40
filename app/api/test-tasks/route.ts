import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

// Use service role to bypass authentication
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function GET() {
  try {
    // Create supabase client with service role key to bypass auth
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get tasks with manual SQL query to avoid the team_visibility issue
    const { data, error } = await supabase.from("tasks").select("*").limit(10);

    if (error) {
      console.error("Error fetching tasks:", error);
      return NextResponse.json(
        { error: `Error fetching tasks: ${error.message}` },
        { status: 500 }
      );
    }

    // Parse JSON fields properly
    const formattedTasks = data.map((task) => {
      let subtasks = [];
      let tags = [];
      let assignees = [];

      try {
        if (task.subtasks) {
          subtasks = typeof task.subtasks === "string" ? JSON.parse(task.subtasks) : task.subtasks;
        }
      } catch (e) {
        console.warn("Error parsing subtasks:", e);
      }

      try {
        if (task.tags_data) {
          tags = typeof task.tags_data === "string" ? JSON.parse(task.tags_data) : task.tags_data;
        }
      } catch (e) {
        console.warn("Error parsing tags:", e);
      }

      try {
        if (task.assignees) {
          assignees =
            typeof task.assignees === "string" ? JSON.parse(task.assignees) : task.assignees;
        }
      } catch (e) {
        console.warn("Error parsing assignees:", e);
      }

      return {
        ...task,
        subtasks,
        tags,
        assignees,
        visibleToTeams: task.visible_to_team_ids || [],
      };
    });

    return NextResponse.json({ data: formattedTasks });
  } catch (error: unknown) {
    console.error("Unexpected error in test-tasks:", error);
    return NextResponse.json(
      { error: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}
