import { addOrganizationMemberInDb } from "@/db/organization.db";
import { NextRequest, NextResponse } from "next/server";

type JoinOrganizationRequest = {
  userId: string;
  organizationId: string;
  subdomain?: string;
};

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as JoinOrganizationRequest;
    const { userId, organizationId, subdomain } = body;

    if (!userId) {
      return NextResponse.json({ success: false, error: "User ID is required" }, { status: 400 });
    }

    if (!organizationId && !subdomain) {
      return NextResponse.json(
        { success: false, error: "Either organization ID or subdomain is required" },
        { status: 400 }
      );
    }

    // Use the server-side function to add the user to the organization
    const { success, error } = await addOrganizationMemberInDb(organizationId, userId, "member");

    if (!success) {
      return NextResponse.json(
        { success: false, error: error?.message || "Failed to add user to organization" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
