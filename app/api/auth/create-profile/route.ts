import { createProfileInDb } from "@/db/profile.db";
import { UserProfileDB } from "@/types/user.types";
import { NextRequest, NextResponse } from "next/server";

type CreateProfileRequest = {
  userId: string;
  profileData: Partial<UserProfileDB>;
};

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateProfileRequest;
    const { userId, profileData } = body;

    if (!userId) {
      return NextResponse.json({ success: false, error: "User ID is required" }, { status: 400 });
    }

    // Ensure we have all required profile fields
    if (!profileData || !profileData.first_name || !profileData.last_name || !profileData.email) {
      return NextResponse.json(
        { success: false, error: "Missing required profile fields" },
        { status: 400 }
      );
    }

    // Use the server-side function to create the profile
    const { success, error } = await createProfileInDb(userId, profileData);

    if (!success) {
      return NextResponse.json(
        { success: false, error: error?.message || "Failed to create profile" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("CREATE PROFILE API - Caught exception:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
