import { getTeamMemberByIdFromDb } from "@/db/team-members.db";
import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const memberId = searchParams.get("memberId");

  if (!memberId) {
    return NextResponse.json(
      {
        success: false,
        message: "Missing memberId parameter",
      },
      { status: 400 }
    );
  }

  try {
    // Get the team member details to get user_id and tenant_id
    const { data: memberData, error: memberError } = await getTeamMemberByIdFromDb(memberId);

    if (memberError || !memberData || memberData.length === 0) {
      console.info("[verify-member] Error fetching team member:", memberError);

      return NextResponse.json(
        {
          success: false,
          message: memberError?.message || "Team member not found",
          error: memberError,
        },
        { status: 404 }
      );
    }

    const member = memberData[0];

    // Get the user_id and tenant_id
    const userId = member.user_id;
    const tenantId = member.tenant_id;

    if (!userId || !tenantId) {
      return NextResponse.json(
        {
          success: false,
          message: "Team member has incomplete data (missing user_id or tenant_id)",
        },
        { status: 400 }
      );
    }

    // Verify if the user is in the organization
    const supabase = await createClient();

    // For regular users, check team_members with tenant_id
    const { data: teamMembers, error: teamMembersError } = await supabase
      .from("team_members")
      .select("id, team_id")
      .eq("user_id", userId)
      .eq("tenant_id", tenantId);

    if (teamMembersError) {
      console.info("[verify-member] Error checking team membership:", teamMembersError);

      return NextResponse.json(
        {
          success: false,
          message: "Error verifying team membership",
          error: teamMembersError,
        },
        { status: 500 }
      );
    }

    const isInOrg = teamMembers && teamMembers.length > 0;

    // Return the result of the verification
    return NextResponse.json({
      success: true,
      message: isInOrg
        ? "User is a member of the organization"
        : "User is not a member of the organization",
      data: {
        isInOrganization: isInOrg,
        teams: isInOrg ? teamMembers.map((m) => m.team_id) : [],
      },
    });
  } catch (error) {
    console.info("[verify-member] Unexpected error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "An unexpected error occurred",
        error,
      },
      { status: 500 }
    );
  }
}
