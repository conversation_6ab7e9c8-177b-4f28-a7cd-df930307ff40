@import "tailwindcss";
@import "tw-animate-css";
@import "fumadocs-ui/css/vitepress.css";
@import "fumadocs-ui/css/preset.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --text-tiny: 0.625rem;
  --text-tiny--line-height: 1rem;
  --text-tiny--letter-spacing: 0em;
  --text-tiny--font-weight: 300;
  --text-tiny--font-family:
    system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol";
}
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.26 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.26 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.26 0 0);
  --primary: oklch(0.38 0.05 271.62);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.96 0 56.37);
  --secondary-foreground: oklch(0.33 0 0);
  --muted: oklch(0.98 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.98 0 0);
  --accent-foreground: oklch(0.33 0 0);
  --destructive: oklch(0.62 0.21 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.94 0 0);
  --input: oklch(0.94 0 0);
  --ring: oklch(0.77 0 0);
  --chart-1: oklch(0.82 0.13 84.49);
  --chart-2: oklch(0.8 0.11 203.6);
  --chart-3: oklch(0.42 0.17 266.78);
  --chart-4: oklch(0.92 0.08 125.58);
  --chart-5: oklch(0.92 0.1 116.19);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0.26 0 0);
  --sidebar-primary: oklch(0.33 0 0);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.98 0 0);
  --sidebar-accent-foreground: oklch(0.33 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --font-sans:
    ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New",
    monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.14 0 285.82);
  --foreground: oklch(0.98 0 0);
  --card: oklch(0.14 0 285.82);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.14 0 285.82);
  --popover-foreground: oklch(0.98 0 0);
  --primary: oklch(0.31 0.06 272.79);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.27 0.01 286.03);
  --secondary-foreground: oklch(0.98 0 0);
  --muted: oklch(0.27 0.01 286.03);
  --muted-foreground: oklch(0.71 0.01 286.07);
  --accent: oklch(0.27 0.01 286.03);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.4 0.13 25.72);
  --destructive-foreground: oklch(0.97 0.01 17.38);
  --border: oklch(0.27 0.01 286.03);
  --input: oklch(0.27 0.01 286.03);
  --ring: oklch(0.87 0.01 286.29);
  --chart-1: oklch(0.53 0.19 262.13);
  --chart-2: oklch(0.7 0.13 165.46);
  --chart-3: oklch(0.72 0.15 60.63);
  --chart-4: oklch(0.62 0.2 312.73);
  --chart-5: oklch(0.61 0.21 6.39);
  --sidebar: oklch(0.21 0.01 285.88);
  --sidebar-foreground: oklch(0.97 0 286.38);
  --sidebar-primary: oklch(0.49 0.22 264.39);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.27 0.01 286.03);
  --sidebar-accent-foreground: oklch(0.97 0 286.38);
  --sidebar-border: oklch(0.27 0.01 286.03);
  --sidebar-ring: oklch(0.87 0.01 286.29);
  --font-sans:
    ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New",
    monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Potential bg colors */
/* bg-[#f8f9fa] dark:bg-[#121212] */

.body {
  font-family:
    system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol";
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Remove focus styling globally */
  button,
  [role="button"],
  a,
  input,
  select,
  search,
  textarea,
  command,
  [tabindex]:not([tabindex="-1"]) {
    @apply focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus-within:ring-[0px];
  }
}

@utility clean-focus {
  @apply focus:ring-0! focus:ring-offset-0! focus-visible:ring-0! focus-visible:ring-offset-0! focus-visible:outline-none!;
}

@utility active-menu {
  @apply bg-gray-200/50 dark:bg-accent/50;
}

@utility text-tiny {
  font-size: var(--text-tiny);
  line-height: var(--text-tiny--line-height);
  font-family: var(--text-tiny--font-family);
}

button {
  cursor: pointer;
}

/* Collapsible */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state="open"] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state="closed"] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}

/* Gradient Animation Keyframes */
@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes icon-pulse {
  0% {
    color: #f39829;
  }
  50% {
    color: #9c40ff;
  }
  100% {
    color: #f39829;
  }
}

.gradient-animate {
  animation: gradient-flow 3s ease infinite;
  background-size: 200% 200%;
}

.icon-pulse {
  animation: icon-pulse 2s ease-in-out infinite;
}

/* Add these new animation classes after your existing animations */
@keyframes blur-in {
  0% {
    opacity: 0;
    transform: scale(0.98);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}

@keyframes blur-out {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.98);
    filter: blur(8px);
  }
}

.blur-animate-in {
  animation: blur-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.blur-animate-out {
  animation: blur-out 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.hero-blur {
  @keyframes heroBlur {
    from {
      filter: blur(12px);
    }
    to {
      filter: blur(0px);
    }
  }

  animation: heroBlur 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: filter;
}
