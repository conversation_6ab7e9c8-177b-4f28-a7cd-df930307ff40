"use client";

import { source } from "@/lib/fumadocs/source";
import { DocsLayout } from "fumadocs-ui/layouts/docs";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import { baseOptions } from "./layout.config";

export default function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();

  if (pathname === "/docs/verify-access") {
    return children;
  }

  return (
    <DocsLayout tree={source.pageTree} {...baseOptions}>
      {children}
    </DocsLayout>
  );
}
