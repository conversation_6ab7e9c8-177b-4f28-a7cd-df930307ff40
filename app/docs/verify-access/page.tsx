import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getCurrentDomain } from "@/lib/utils";
import { Metadata } from "next";
import { cookies } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Developer Access - Documentation",
  robots: "noindex, nofollow",
};

// Environment variables
const isEnvDev = process.env.NODE_ENV === "development";
const currentDomain = getCurrentDomain();

// Server action to handle form submission
async function verifyDevToken(formData: FormData) {
  "use server";

  const token = formData.get("dev_token") as string;
  const validToken = process.env.DEV_DOCS_TOKEN || "renwudocs2025";

  if (token === validToken) {
    // Set the token as a cookie - need to await cookies() in Next.js 15
    const cookieStore = await cookies();
    cookieStore.set("dev_token", token, {
      // For localhost, don't use domain setting at all
      ...(isEnvDev ? {} : { domain: `.${currentDomain}` }),
      httpOnly: true,
      maxAge: 60 * 60 * 24, // 24 hours
      path: "/",
      sameSite: "lax",
      secure: !isEnvDev,
    });

    // Redirect to docs
    redirect("/docs");
  }

  // If token is invalid, redirect back with error param
  redirect("/docs/verify-access?error=invalid");
}

export default async function DocsAccessPage({
  searchParams,
}: {
  searchParams: Promise<{ error?: string }>;
}) {
  const params = await searchParams;
  const hasError = params.error === "invalid";

  return (
    <div className="bg-gray-950 text-gray-50 min-h-screen flex items-center justify-center">
      <Card className="max-w-md w-full mx-4 bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-2xl text-white/80">Developer Access</CardTitle>
          <CardDescription className="text-gray-400">
            Enter your developer token to access the documentation.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={verifyDevToken}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="dev_token" className="text-gray-300">
                  Developer Token
                </Label>
                <Input
                  id="dev_token"
                  name="dev_token"
                  type="password"
                  placeholder="Enter developer token"
                  required
                  className="bg-gray-800 border-gray-700 text-white/80"
                />
                {hasError && (
                  <p className="text-sm text-red-500 mt-1">Invalid token. Please try again.</p>
                )}
              </div>
            </div>
            <div className="mt-4">
              <Button type="submit" className="w-full bg-indigo-600 hover:bg-indigo-700">
                Access Documentation
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/" className="text-sm text-gray-400 hover:text-gray-300">
            Return to main application
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
