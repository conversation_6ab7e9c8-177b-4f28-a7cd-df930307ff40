import { SignUpForm } from "@/components/auth/sign-up-form";
import { validateOrganizationInvitationTokenAction } from "@/db/actions/organization-invitations.action";
import { verifySubdomainOrRedirect } from "@/db/organization.db";
import { getCurrentSubdomain, goToMainDomain } from "@/lib/utils";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ invitation_token?: string }>;
}) {
  const headersList = await headers();
  const hostname = headersList.get("host") || "";
  const subdomain = getCurrentSubdomain(hostname);

  // Await searchParams
  const params = await searchParams;

  // Check if we have an invitation token
  const invitationToken = params.invitation_token;

  // If there's an invitation token, validate it first
  if (invitationToken) {
    const { isValid, invitation } =
      await validateOrganizationInvitationTokenAction(invitationToken);

    // If the token is invalid, redirect to main domain
    if (!isValid || !invitation) {
      redirect(goToMainDomain("/"));
    }

    // If we get here, the token is valid, proceed with sign-up
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-lg">
          <SignUpForm invitationToken={invitationToken} invitationEmail={invitation.email} />
        </div>
      </div>
    );
  }

  // For subdomain requests without a token, verify the organization exists
  if (subdomain) {
    const { shouldRedirect } = await verifySubdomainOrRedirect(subdomain);

    if (shouldRedirect) {
      // Redirect to the main domain sign-up page
      redirect(goToMainDomain("auth/sign-up"));
    }
  }

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-lg">
        <SignUpForm />
      </div>
    </div>
  );
}
