import { LoginForm } from "@/components/auth/login-form";
import { verifySubdomainOrRedirect } from "@/db/organization.db";
import { getCurrentSubdomain, goToMainDomain } from "@/lib/utils";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ invitation?: string; email?: string }>;
}) {
  const headersList = await headers();
  const hostname = headersList.get("host") || "";
  const subdomain = getCurrentSubdomain(hostname);

  // Await searchParams
  const params = await searchParams;

  // If this is a subdomain request, verify the organization exists
  if (subdomain) {
    const { shouldRedirect } = await verifySubdomainOrRedirect(subdomain);

    if (shouldRedirect) {
      // Redirect to the main domain login page
      redirect(goToMainDomain("auth/login"));
    }
  }

  return (
    <div className="flex h-full w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-lg">
        <LoginForm
          subdomain={subdomain}
          invitationToken={params.invitation}
          invitationEmail={params.email}
        />
      </div>
    </div>
  );
}
