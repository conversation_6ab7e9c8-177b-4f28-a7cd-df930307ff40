"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  createOrganizationAction,
  getAllSubdomainsAction,
} from "@/db/actions/organization-create.action";
import { OrganizationSchema } from "@/db/schemas/organization.schema";
import { getCurrentDomain, getProtocol } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Check, Loader2, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Define the form data type from the schema
type OrganizationFormData = z.infer<typeof OrganizationSchema>;
type SubdomainAvailability = "available" | "unavailable" | "checking" | "invalid" | null;

export function CreateOrganizationForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subdomainAvailability, setSubdomainAvailability] = useState<SubdomainAvailability>(null);
  const [existingSubdomains, setExistingSubdomains] = useState<string[]>([]);
  const router = useRouter();
  // Fetch all subdomains once when component mounts
  useEffect(() => {
    async function fetchSubdomains() {
      const subdomains = await getAllSubdomainsAction();
      if (subdomains) {
        setExistingSubdomains(subdomains);
      }
    }
    fetchSubdomains();
  }, []);

  // Initialize form with the local schema
  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(OrganizationSchema),
    defaultValues: {
      name: "",
      description: "",
      subdomain_id: "",
      max_users: 10,
    },
  });

  const subdomain = form.watch("subdomain_id");
  // Add debounced subdomain check
  const checkSubdomainStatus = useCallback(
    (subdomain: string) => {
      if (!subdomain || subdomain.length < 3) {
        setSubdomainAvailability(null);
        return;
      }

      setSubdomainAvailability("checking");

      setTimeout(() => {
        const isAvailable = !existingSubdomains.includes(subdomain);
        setSubdomainAvailability(isAvailable ? "available" : "unavailable");
      }, 300);
    },
    [existingSubdomains]
  );

  useEffect(() => {
    const timer = setTimeout(() => checkSubdomainStatus(subdomain), 200);
    return () => clearTimeout(timer);
  }, [subdomain, checkSubdomainStatus]);

  async function onSubmit(data: OrganizationFormData) {
    setIsSubmitting(true);
    try {
      const result = await createOrganizationAction(data);
      if (result.success) {
        toast.success(result.message);
        // Use window.location.href for cross-domain navigation
        const subdomain = data.subdomain_id;
        const isDev = process.env.NODE_ENV === "development";
        const domain = getCurrentDomain();
        const protocol = getProtocol();
        window.location.href = isDev
          ? `${protocol}://${domain}/home` // In dev, stay on main domain
          : `${protocol}://${subdomain}.${domain}/home`; // In prod, go to subdomain
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Failed to create organization", {
        description: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Render helpers
  const renderSubdomainStatus = () => {
    if (!subdomainAvailability) return null;

    return (
      <div className="absolute right-3 top-1/2 -translate-y-1/2">
        {subdomainAvailability === "checking" && (
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        )}
        {subdomainAvailability === "available" && <Check className="h-4 w-4 text-green-500" />}
        {subdomainAvailability === "unavailable" && <X className="h-4 w-4 text-red-500" />}
      </div>
    );
  };

  const handleGoBack = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    router.push("/auth/organizations");
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 text-left">
        <div className="space-y-8">
          {/* Organization Name Field */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="name">
                  Organization Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input id="name" placeholder="Acme Inc." autoComplete="off" {...field} />
                </FormControl>
                <FormDescription className="text-tiny!">
                  The name of your organization as it will appear to your members.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Subdomain Field */}
          <FormField
            control={form.control}
            name="subdomain_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="subdomain_id">
                  <div className="flex items-center justify-between w-4/5">
                    <span>
                      Subdomain <span className="text-red-500">*</span>
                    </span>
                    <span className="text-tiny text-muted-foreground pr-2">
                      {field.value.length}/16
                    </span>
                  </div>
                </FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <div className="relative w-4/5">
                      <Input
                        id="subdomain_id"
                        placeholder="acme"
                        autoComplete="off"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value
                            .toLowerCase()
                            .replace(/[^a-z0-9+]/g, "")
                            .slice(0, 16);
                          field.onChange(value);
                        }}
                      />
                      {renderSubdomainStatus()}
                    </div>
                    <span className="text-sm text-muted-foreground whitespace-nowrap">
                      .renwu.app
                    </span>
                  </div>
                </FormControl>
                <FormDescription className="text-tiny!">
                  Choose a unique subdomain for your organization. This will be your URL: https://
                  {field.value || "your-company"}.renwu.app
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description Field */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="description">
                  Description <span className="text-gray-500">(Optional)</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    id="description"
                    placeholder="Tell us about your organization..."
                    className="min-h-[120px]"
                    autoComplete="off"
                    {...field}
                  />
                </FormControl>
                <FormDescription className="text-tiny!">
                  A brief description of your organization&apos;s purpose.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Max Users Field */}
          <FormField
            control={form.control}
            name="max_users"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="max_users">
                  Maximum Users <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    id="max_users"
                    type="number"
                    min={1}
                    max={1000}
                    autoComplete="off"
                    value={field.value}
                    onChange={(e) => {
                      const value = e.target.value === "" ? 10 : parseInt(e.target.value, 10);
                      field.onChange(isNaN(value) ? 10 : value);
                    }}
                    onBlur={field.onBlur}
                  />
                </FormControl>
                <FormDescription className="text-tiny!">
                  The maximum number of users allowed in your organization.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex flex-col sm:flex-row items-center justify-between w-full gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleGoBack}
            aria-label="Go back"
            className="w-full sm:w-2/5"
          >
            <ArrowLeft className="h-4 w-4" /> Back to Organizations
          </Button>
          <Button type="submit" className="w-full sm:w-1/2" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Organization"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
