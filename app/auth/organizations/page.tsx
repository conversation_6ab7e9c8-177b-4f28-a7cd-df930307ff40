import { DeleteAccountButton } from "@/components/auth/delete-account-button";
import { UserName } from "@/components/auth/user-name";
import { OrganizationsSkeleton } from "@/components/skeletons";
import { Button } from "@/components/ui/button";
import { goToMainDomain } from "@/lib/utils";
import { Building2, PlusCircle } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import OrganizationsList from "./_components/organizations-list";

export default function OrganizationsPage() {
  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col p-4 sm:p-8">
      <div className="flex flex-col gap-4 max-w-3xl w-full mx-auto">
        <header className="text-center space-y-6">
          <div className="inline-flex p-3.5 rounded-2xl bg-primary/10 shadow-sm dark:bg-white/20">
            <Building2 className="h-4 w-4 text-primary dark:text-white/90" />
          </div>
          <div className="space-y-3">
            <h1 className="text-primary dark:text-white/90 text-md sm:text-3xl font-bold tracking-tight">
              Welcome <UserName />
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base max-w-md mx-auto leading-relaxed">
              Please select which organization you belong to, you want to access
            </p>
          </div>
          <div className="flex justify-end">
            <Button size="sm" variant="ghost" asChild>
              <Link href={goToMainDomain("auth/organizations/new")}>
                <PlusCircle className="h-4 w-4" /> Create Organization
              </Link>
            </Button>
          </div>
        </header>
        <main className="flex-1">
          <Suspense fallback={<OrganizationsSkeleton />}>
            <OrganizationsList />
          </Suspense>
        </main>
        <footer className="fixed bottom-0 right-0 p-4 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <DeleteAccountButton />
        </footer>
      </div>
    </div>
  );
}
