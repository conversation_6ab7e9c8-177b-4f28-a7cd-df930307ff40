import UserAvatar from "@/components/origin-ui/user-avatar";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { getUserOrganizationsAction } from "@/db/actions/organization.action";
import { goToSubdomain } from "@/lib/utils";
import { Briefcase, CircleDashed, Clock1, Computer, Copy } from "lucide-react";
import Link from "next/link";

const OrganizationsList = async () => {
  const organizations = await getUserOrganizationsAction();

  return (
    <div className="grid sm:grid-cols-2 gap-4">
      {organizations && organizations.length > 0 ? (
        <>
          {organizations?.map((org) => (
            <Card key={org.id} className="group">
              <CardContent className="p-4">
                <div className="flex items-start gap-3 mb-4">
                  <Avatar className="h-10 w-10 bg-primary/10 border border-primary/20">
                    <AvatarFallback className="font-semibold text-sm text-primary dark:text-white/90">
                      {org.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-base mb-0.5 truncate">{org.name}</h3>
                    <div className="flex flex-col gap-1">
                      <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                        <Copy className="h-2.5 w-2.5" />
                        <span className="truncate">{org.id}</span>
                      </p>
                      <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                        <Computer className="h-2.5 w-2.5" />
                        <span className="truncate">{org.subdomain_id}</span>
                      </p>
                      <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                        <Clock1 className="h-2.5 w-2.5" />
                        <span>
                          {new Date(org.updated_at).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between gap-2">
                  <Badge
                    variant="secondary"
                    className="h-6 text-xs font-medium flex items-center gap-1"
                  >
                    <Briefcase className="h-3 w-3" />
                    {org.member_role}
                  </Badge>
                  <Button size="sm" className="group-hover:bg-primary/90" asChild>
                    <Link href={goToSubdomain(org.subdomain_id, "home")}>Access</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          <PersonalWorkspace />
        </>
      ) : (
        <Card className="col-span-full">
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground text-sm">No organizations found</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default OrganizationsList;

const PersonalWorkspace = () => {
  return (
    <Card key="org-123" className="group">
      <CardContent className="p-4">
        <div className="flex items-start gap-3 mb-4">
          <UserAvatar />
          <div className="min-w-0 flex-1">
            <h3 className="font-semibold text-base mb-0.5 truncate">{"Personal Workspace"}</h3>
            <div className="flex flex-col gap-1">
              <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                <Copy className="h-2.5 w-2.5" />
                <span className="truncate">{"-"}</span>
              </p>
              <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                <Computer className="h-2.5 w-2.5" />
                <span className="truncate">{"-"}</span>
              </p>
              <p className="flex items-center gap-1.5 text-muted-foreground text-xs">
                <Clock1 className="h-2.5 w-2.5" />
                <span>{"-"}</span>
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between gap-2">
          <Badge variant="secondary" className="h-6 text-xs font-medium flex items-center gap-1">
            <CircleDashed className="h-3 w-3" />
            {"Not created"}
          </Badge>
          <Button size="sm" disabled variant="outline">
            Unavailable
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
