"use client";

import { LogoutButton } from "@/components/auth/logout-button";
import RenwuLogo from "@/components/svg/renwu-logo";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { goToMainDomain } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  const path = usePathname();

  return (
    <div className="h-screen bg-gradient-to-b from-background to-muted/30 flex items-center justify-center p-4 sm:p-8 relative">
      <nav className="absolute top-0 right-0 p-4 flex items-center justify-between w-full">
        <Link href={goToMainDomain("/")}>
          <RenwuLogo className="w-16 h-16" />
        </Link>
        <div className="flex items-center gap-4">
          <ThemeToggle />

          {path === "/auth/organizations" && <LogoutButton />}
        </div>
      </nav>
      {children}
    </div>
  );
}
