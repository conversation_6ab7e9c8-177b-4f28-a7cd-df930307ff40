import { ThemeProvider } from "@/components/theme-provider";
import { QueryClientProvider } from "@/lib/react-query-provider";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { RootProvider } from "fumadocs-ui/provider";
import type { Metada<PERSON> } from "next";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { Toaster } from "sonner";
import "./globals.css";

export const metadata: Metadata = {
  title: "Renwu - AI-powered project management at scale",
  description: "Renwu is an AI-powered platform for project management at scale.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased flex flex-col min-h-screen">
        <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
          <RootProvider>
            <QueryClientProvider>
              <NuqsAdapter>{children}</NuqsAdapter>
            </QueryClientProvider>
          </RootProvider>
        </ThemeProvider>
        <Toaster />
        <SpeedInsights />
      </body>
    </html>
  );
}
