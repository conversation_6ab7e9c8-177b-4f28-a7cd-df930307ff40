import { OrganizationTeam, Project, Team } from "../_types/teams.types";

// Mock data for projects
export const projectsMockData: Project[] = [
  { id: "proj_1", name: "Project Alpha", color: "default" },
  { id: "proj_2", name: "Project Beta", color: "default" },
  { id: "proj_3", name: "Project Gamma", color: "default" },
  { id: "proj_4", name: "Project Delta", color: "default" },
];

// Mock data for assigned teams
export const assignedTeamsMockData: Team[] = [
  {
    id: "team_1",
    name: "Engineering",
    members: [
      {
        id: "mem_1",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "member",
        position: "Software Engineer",
        avatarUrl: "https://github.com/shadcn.png",
      },
      {
        id: "mem_2",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "member",
        position: "Software Engineer",
        avatarUrl: "https://github.com/shadcn.png",
      },
      {
        id: "mem_3",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "admin",
        position: "Software Engineer",
        avatarUrl: "https://github.com/TonyKYC.png",
      },
      {
        id: "mem_4",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "member",
        position: "Software Engineer",
        avatarUrl: "https://github.com/shadcn.png",
      },
      {
        id: "mem_5",
        name: "Emma Davis",
        email: "<EMAIL>",
        role: "guest",
        position: "Guest",
        avatarUrl: "https://github.com/shadcn.png",
      },
    ],
    projects: [projectsMockData[0], projectsMockData[1]],
  },
  {
    id: "team_2",
    name: "Design",
    members: [
      {
        id: "mem_6",
        name: "Robert Miller",
        email: "<EMAIL>",
        role: "member",
        position: "Designer",
        avatarUrl: "https://github.com/shadcn.png",
      },
      {
        id: "mem_7",
        name: "Lisa Anderson",
        email: "<EMAIL>",
        role: "member",
        position: "Designer",
        avatarUrl: "https://github.com/shadcn.png",
      },
      {
        id: "mem_8",
        name: "David Taylor",
        email: "<EMAIL>",
        role: "admin",
        position: "Product Manager",
        avatarUrl: "https://github.com/shadcn.png",
      },
    ],
    projects: [projectsMockData[2], projectsMockData[3]],
  },
];

// Mock data for available teams in the organization
export const organizationTeamsMockData: OrganizationTeam[] = [
  {
    id: "team_3",
    name: "Product",
    memberCount: 4,
    projects: [projectsMockData[0], projectsMockData[1]],
  },
  {
    id: "team_4",
    name: "Marketing",
    memberCount: 6,
    projects: [projectsMockData[2]],
  },
  {
    id: "team_5",
    name: "Sales",
    memberCount: 3,
    projects: [projectsMockData[0], projectsMockData[2], projectsMockData[3]],
  },
  {
    id: "team_6",
    name: "Customer Success",
    memberCount: 7,
    projects: [],
  },
  {
    id: "team_7",
    name: "Operations",
    memberCount: 4,
    projects: [projectsMockData[1]],
  },
  {
    id: "team_8",
    name: "Finance",
    memberCount: 6,
    projects: [projectsMockData[0]],
  },
  {
    id: "team_9",
    name: "HR",
    memberCount: 3,
    projects: [],
  },
  {
    id: "team_10",
    name: "Legal",
    memberCount: 7,
    projects: [projectsMockData[3]],
  },
];
