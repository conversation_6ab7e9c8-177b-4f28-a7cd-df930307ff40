"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Reorder } from "framer-motion";
import {
  Activity,
  CalendarClock,
  Clock,
  GripHorizontal,
  ListChecks,
  PenIcon,
  SaveIcon,
  TrashIcon,
  User,
  UserRound,
  Users,
} from "lucide-react";
import { useState } from "react";

interface KanbanColumn {
  id: string;
  title: string;
  items: KanbanItem[];
}

interface KanbanItem {
  id: string;
  content: string;
}

interface ProjectKanbanProps {
  columns?: KanbanColumn[];
}

const defaultColumns: KanbanColumn[] = [
  {
    id: "todo",
    title: "To Do",
    items: [
      { id: "1", content: "Task 1" },
      { id: "2", content: "Task 2" },
    ],
  },
  {
    id: "in-progress",
    title: "In Progress",
    items: [
      { id: "3", content: "Task 3" },
      { id: "4", content: "Task 4" },
    ],
  },
  {
    id: "in-review",
    title: "In Review",
    items: [
      { id: "5", content: "Task 5" },
      { id: "6", content: "Task 6" },
    ],
  },
  {
    id: "done",
    title: "Done",
    items: [],
  },
];

export const ProjectSettingsKanban = ({
  columns: initialColumns = defaultColumns,
}: ProjectKanbanProps) => {
  const [columns, setColumns] = useState(initialColumns);
  const [hasChanges] = useState(false);
  return (
    <div className="flex flex-col gap-4">
      {/* Columns Order */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div className="flex flex-col">
            <CardTitle>Columns Order</CardTitle>
            <CardDescription>
              Reorder the columns by dragging and dropping.
            </CardDescription>
          </div>
          {!hasChanges && (
            <Button variant="default" size="sm">
              <SaveIcon className="h-3 w-3" />
              Save
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <Reorder.Group
            axis="y"
            values={columns}
            onReorder={setColumns}
            className="flex flex-col gap-2"
          >
            {columns.map((column) => (
              <Reorder.Item
                key={column.id}
                value={column}
                className="cursor-move w-full"
              >
                <Card className="flex flex-row items-center justify-between px-4 py-2 rounded-md shadow-none hover:shadow-sm transition-colors">
                  <div className="flex items-center gap-4 relative">
                    <GripHorizontal className="h-4 w-4 text-muted-foreground hover:cursor-grab" />
                    <span className="w-32 font-medium">{column.title}</span>
                    <span className="text-sm text-muted-foreground">
                      {column.items.length} tasks
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <PenIcon className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="hover:text-red-500"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </Card>
              </Reorder.Item>
            ))}
          </Reorder.Group>
        </CardContent>
      </Card>

      {/* Tasks Options */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div className="flex flex-col">
            <CardTitle>Tasks Options</CardTitle>
            <CardDescription>
              Toggle the elements you want to appear on the tasks.
            </CardDescription>
          </div>
          {!hasChanges && (
            <Button variant="default" size="sm">
              <SaveIcon className="h-3 w-3" />
              Save
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Display Elements */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Display Elements</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-assignee"
                    className="flex items-center w-42 space-x-2"
                  >
                    <UserRound className="h-4 w-4 text-muted-foreground" />
                    <span>Assignee</span>
                  </Label>
                  <Switch id="show-assignee" defaultChecked />
                </div>
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-due-date"
                    className="flex items-center w-42 space-x-2"
                  >
                    <CalendarClock className="h-4 w-4 text-muted-foreground" />
                    <span>Due Date</span>
                  </Label>
                  <Switch id="show-due-date" defaultChecked />
                </div>
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-priority"
                    className="flex items-center w-42 space-x-2"
                  >
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <span>Priority</span>
                  </Label>
                  <Switch id="show-priority" defaultChecked />
                </div>
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-work-duration"
                    className="flex items-center w-42 space-x-2"
                  >
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>Work Duration</span>
                  </Label>
                  <Switch id="show-work-duration" defaultChecked />
                </div>
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-subtasks"
                    className="flex items-center w-42 space-x-2"
                  >
                    <ListChecks className="h-4 w-4 text-muted-foreground" />
                    <span>Subtasks</span>
                  </Label>
                  <Switch id="show-subtasks" defaultChecked />
                </div>
                <div className="flex items-center space-x-2">
                  <Label
                    htmlFor="show-subtasks"
                    className="flex items-center w-42 space-x-2"
                  >
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>Project Leader</span>
                  </Label>
                  <Switch id="show-project-leader" defaultChecked />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-8">
              {/* Card Density */}
              <div className="flex flex-col gap-4">
                <Label htmlFor="card-density">Card Density</Label>
                <RadioGroup
                  defaultValue="comfortable"
                  id="card-density"
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-4">
                    <RadioGroupItem value="compact" id="density-compact" />
                    <Label htmlFor="density-compact">Compact</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="comfortable"
                      id="density-comfortable"
                    />
                    <Label htmlFor="density-comfortable">Comfortable</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="spacious" id="density-spacious" />
                    <Label htmlFor="density-spacious">Spacious</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Color Tasks By */}
              <div className="flex flex-col gap-4">
                <Label htmlFor="task-color-by">Color Tasks By</Label>
                <Select defaultValue="priority">
                  <SelectTrigger id="task-color-by" className="w-full">
                    <SelectValue placeholder="Select option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="priority">Priority</SelectItem>
                    <SelectItem value="label">Label</SelectItem>
                    <SelectItem value="assignee">Assignee</SelectItem>
                    <SelectItem value="none">No Color</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* Task Behavior */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Task Behavior</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between space-x-2">
                  <Label
                    htmlFor="allow-multiple-assignees"
                    className="flex items-center space-x-2"
                  >
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>Allow Multiple Assignees</span>
                  </Label>
                  <Switch id="allow-multiple-assignees" />
                </div>
                <div className="flex items-center justify-between space-x-2">
                  <Label
                    htmlFor="allow-subtasks"
                    className="flex items-center space-x-2"
                  >
                    <ListChecks className="h-4 w-4 text-muted-foreground" />
                    <span>Allow Subtasks</span>
                  </Label>
                  <Switch id="allow-subtasks" defaultChecked />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectSettingsKanban;
