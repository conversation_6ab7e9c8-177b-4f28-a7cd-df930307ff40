"use client";

import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowLeftRightIcon, ArrowUpDownIcon, PlusIcon, StarIcon } from "lucide-react";
import React, { useState } from "react";
import { assignedTeamsMockData, organizationTeamsMockData } from "../_data/teams-mock-data";
import { Member, OrganizationTeam, Team } from "../_types/teams.types";

export function ProjectSettingsTeams() {
  const [assignedTeams, setAssignedTeams] = useState(assignedTeamsMockData);
  const [availableTeams, setAvailableTeams] = useState(organizationTeamsMockData);

  return (
    <div className="flex flex-col gap-4 pb-4">
      <ProjectTeams
        assignedTeams={assignedTeams}
        availableTeams={availableTeams}
        onAssign={(team) => {
          setAvailableTeams((prev) => prev.filter((t) => t.id !== team.id));
          setAssignedTeams((prev) => [
            ...prev,
            {
              id: team.id,
              name: team.name,
              members: [], // Fetch team members
              projects: team.projects, // Preserve projects
            },
          ]);
        }}
        onUnassign={(team) => {
          setAssignedTeams((prev) => prev.filter((t) => t.id !== team.id));
          setAvailableTeams((prev) => [
            ...prev,
            {
              id: team.id,
              name: team.name,
              memberCount: team.members.length,
              projects: team.projects, // Preserve projects
            },
          ]);
        }}
      />
      <ProjectTeamMembers teams={assignedTeams} />
    </div>
  );
}

type ProjectTeamsProps = {
  assignedTeams: Team[];
  availableTeams: OrganizationTeam[];
  onAssign: (team: OrganizationTeam) => void;
  onUnassign: (team: Team) => void;
};

function ProjectTeams({ assignedTeams, availableTeams, onAssign, onUnassign }: ProjectTeamsProps) {
  const [searchTeamQuery, setSearchTeamQuery] = useState("");

  const filteredAvailableTeams = availableTeams.filter((team) =>
    team.name.toLowerCase().includes(searchTeamQuery.toLowerCase())
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <CardTitle>Teams</CardTitle>
            <CardDescription>
              Select the teams that will have access to this project.
            </CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <PlusIcon className="w-4 h-4" />
            Create Team
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-6">
          {/* Assigned Teams */}
          <div className="flex-1">
            <div className="h-12 flex items-center gap-4">
              <h3 className="font-base text-md">Assigned to the project</h3>
              <Badge variant="secondary" className="h-6 w-6 rounded-full text-neutral-500">
                {assignedTeams.length}
              </Badge>
            </div>
            <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
              {assignedTeams.map((team) => (
                <div
                  key={team.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <p className="font-medium">
                    {team.name}
                    <span className="text-muted-foreground text-sm ml-2">Team</span>
                  </p>
                  <div className="flex items-center gap-4">
                    {team.projects.length > 0 && (
                      <div className="flex items-center gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <div className="flex items-center gap-1">
                                {team.projects.slice(0, 2).map((project) => (
                                  <Badge key={project.id} variant="default" className="text-xs">
                                    {project.name}
                                  </Badge>
                                ))}
                                {team.projects.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{team.projects.length - 2}
                                  </Badge>
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-sm">Assigned to projects:</p>
                              <ul className="list-disc ml-4 mt-1">
                                {team.projects.map((project) => (
                                  <li key={project.id} className="text-sm">
                                    {project.name}
                                  </li>
                                ))}
                              </ul>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}
                    <Button variant="ghost" size="sm" onClick={() => onUnassign(team)}>
                      Unassign
                    </Button>
                  </div>
                </div>
              ))}
              {assignedTeams.length === 0 && (
                <div className="flex items-center justify-center h-20 text-sm text-muted-foreground border rounded-lg">
                  No teams assigned yet
                </div>
              )}
            </div>
          </div>

          {/* Arrow Icon */}
          <div className="flex items-center px-4">
            <ArrowLeftRightIcon className="w-4 h-4" />
          </div>

          {/* Available Teams */}
          <div className="flex-1">
            <div className="h-12 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h3 className="font-base text-md">Available in the organization</h3>
                <Badge variant="secondary" className="h-6 w-6 rounded-full text-neutral-500">
                  {filteredAvailableTeams.length}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <ArrowUpDownIcon className="w-4 h-4" />
                  Sort
                </Button>
                <SearchInputWithVoice
                  id="available-teams-search"
                  placeholder="Search teams..."
                  className="w-full"
                  onChange={setSearchTeamQuery}
                />
              </div>
            </div>

            <div className="space-y-2 max-h-[340px] overflow-y-auto">
              {filteredAvailableTeams
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((team) => (
                  <div
                    key={team.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <p className="font-medium">
                      {team.name}
                      <span className="text-muted-foreground text-sm ml-2">Team</span>
                    </p>
                    <div className="flex items-center gap-4">
                      {team.projects.length > 0 && (
                        <div className="flex items-center gap-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="flex items-center gap-1">
                                  {team.projects.slice(0, 2).map((project) => (
                                    <Badge key={project.id} variant="default" className="text-xs">
                                      {project.name}
                                    </Badge>
                                  ))}
                                  {team.projects.length > 2 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{team.projects.length - 2}
                                    </Badge>
                                  )}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-sm">Assigned to projects:</p>
                                <ul className="list-disc ml-4 mt-1">
                                  {team.projects.map((project) => (
                                    <li key={project.id} className="text-sm">
                                      {project.name}
                                    </li>
                                  ))}
                                </ul>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      )}
                      <Button variant="ghost" size="sm" onClick={() => onAssign(team)}>
                        Assign
                      </Button>
                    </div>
                  </div>
                ))}
              {filteredAvailableTeams.length === 0 && (
                <div className="flex items-center justify-center h-20 text-sm text-muted-foreground">
                  No teams found
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

type ProjectTeamMembersProps = {
  teams: Team[];
};

export function ProjectTeamMembers({ teams }: ProjectTeamMembersProps) {
  const userNameMock = "Anthony ABRAMO";
  const [searchQuery, setSearchQuery] = useState("");

  // Filter function
  const filterMembers = (members: Member[]) => {
    if (!searchQuery) return members;

    const query = searchQuery.toLowerCase();
    return members.filter(
      (member) =>
        member.name.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query) ||
        member.role.toLowerCase().includes(query) ||
        member.position.toLowerCase().includes(query)
    );
  };

  // Filter teams and their members
  const filteredTeams = teams
    .map((team) => ({
      ...team,
      members: filterMembers(team.members),
    }))
    .filter((team) => team.members.length > 0);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Team Members</CardTitle>
            <CardDescription>Manage project team members and their roles.</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <SearchInputWithVoice
              id="team-members-search"
              placeholder="Search by name, email, role or position"
              className="w-[350px]"
              onChange={setSearchQuery}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Member</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Position</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTeams.map((team, index) => (
              <React.Fragment key={team.id}>
                {index > 0 && <TableRow className="h-8" />}

                {/* Team Header */}
                <TableRow className="bg-muted/50">
                  <TableCell colSpan={4} className="py-2">
                    <div className="flex items-center gap-6">
                      <span>{team.name}</span>
                      <Badge variant="outline" className="text-xs text-muted-foreground">
                        {team.members.length}
                      </Badge>
                    </div>
                  </TableCell>
                </TableRow>
                {/* Team Members */}
                {team.members.map((member) => (
                  <TableRow key={member.id}>
                    {/* Member */}
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatarUrl} alt={member.name} />
                          <AvatarFallback>
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="font-medium">{member.name}</span>
                          <span className="text-sm text-muted-foreground">{member.email}</span>
                        </div>
                        {member.name === userNameMock && <Badge variant="default">You</Badge>}
                      </div>
                    </TableCell>
                    {/* Role */}
                    <TableCell>
                      <Select defaultValue={member.role}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Admin">
                            <div className="flex items-center gap-2">
                              Admin
                              <StarIcon className="w-4 h-4" />
                            </div>
                          </SelectItem>
                          <SelectItem value="Member">Member</SelectItem>
                          <SelectItem value="Guest">Guest</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    {/* Position */}
                    <TableCell>
                      <Select defaultValue={member.position}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Software Engineer">Software Engineer</SelectItem>
                          <SelectItem value="Product Manager">Product Manager</SelectItem>
                          <SelectItem value="Designer">Designer</SelectItem>
                          <SelectItem value="Project Manager">Project Manager</SelectItem>
                          <SelectItem value="Guest">Guest</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    {/* Actions */}
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        Move
                      </Button>
                      <Button variant="ghost" size="sm">
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
