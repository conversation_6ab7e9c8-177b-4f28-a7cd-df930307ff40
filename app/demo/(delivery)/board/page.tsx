"use client";

import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { closestCorners, DndContext, DragOverlay } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { ListFilterIcon } from "lucide-react";
import { lazy, memo, Suspense, useId, useMemo } from "react";
import { BoardStatsCards } from "./_components/board-stats";
import BoardViewSwitch from "./_components/board-view-switch";
import FilterTask from "./_components/filter-task";
import { TaskCard } from "./_components/task-card";
import { initialColumns } from "./data/mock-kanban-data";
import { useKanbanBoard } from "./hooks/useKanbanBoard";
import type { Column, Task } from "./types/board.types";
import { getColumnStyle } from "./utils/column-styles";
const SortableTask = lazy(() => import("./_components/sortable-task"));

// Separate component for each column to prevent full board re-renders
const KanbanColumn = memo(function KanbanColumn({
  column,
}: {
  column: Column;
}) {
  const style = getColumnStyle(column.title);
  const IconComponent = style.icon;
  const columnHeaderIconSize = "h-3.5 w-3.5";

  // Memoize the task IDs to prevent unnecessary re-renders
  const taskIds = useMemo(
    () => column.tasks.map((task) => task.id),
    [column.tasks]
  );

  return (
    <div
      key={column.id}
      id="column"
      className={cn(
        "rounded-lg border shadow-2xs hover:shadow-sm transition-all duration-200 w-full flex flex-col overflow-hidden"
      )}
    >
      <header
        id="column-header"
        className={cn(
          "py-3 px-4 flex items-center justify-between border-b",
          style.header
        )}
      >
        <div className="flex items-center gap-2">
          <IconComponent
            className={cn(columnHeaderIconSize, style.iconColor)}
          />
          <h2 className={cn("text-sm font-medium", style.title)}>
            {column.title}
          </h2>
        </div>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {column.tasks.filter((task: Task) => !task.isDummy).length}
        </span>
      </header>
      <section
        id="column-section"
        className="bg-gray-50/20 dark:bg-[#0d1117] flex-1 p-3 md:p-4 overflow-y-auto"
      >
        <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
          <Suspense
            fallback={
              <div className="space-y-3 min-h-[100px]">
                {column.tasks.map((_task: Task, index) => (
                  <Skeleton key={`loading-${index}`} className="w-full h-20" />
                ))}
              </div>
            }
          >
            <div className="space-y-3 min-h-[100px]">
              {column.tasks.map((task: Task) => {
                const isDummy = task.isDummy;

                if (isDummy) {
                  return (
                    <div key={task.id} className="relative h-20">
                      <div className="absolute inset-0 z-10 opacity-0 pointer-events-none">
                        <SortableTask key={task.id} task={task} disabled />
                      </div>
                      <div className="flex items-center justify-center h-20 text-sm italic text-gray-500 border border-gray-300 border-dashed rounded-md dark:text-gray-400 dark:border-gray-700">
                        Drop a task here
                      </div>
                    </div>
                  );
                }

                return <SortableTask key={task.id} task={task} />;
              })}
            </div>
          </Suspense>
        </SortableContext>
      </section>
    </div>
  );
});

const BoardHeader = () => {
  const id = useId();

  return (
    <>
      <BoardStatsCards />
      <div className="flex flex-row items-start justify-between h-9">
        <div className="flex flex-row items-center gap-2 group-data-[collapsible=icon]:hidden">
          <SearchInputWithVoice id={id} />
          <FilterTask>
            <Button variant="outline" size="icon" aria-label="Filters">
              <ListFilterIcon size={16} aria-hidden="true" />
            </Button>
          </FilterTask>
        </div>
        <BoardViewSwitch currentView="board" />
      </div>
    </>
  );
};

const KanbanBoard = () => {
  const { columns, activeTask, sensors, handlers } =
    useKanbanBoard(initialColumns);
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handlers.handleDragStart}
      onDragOver={handlers.handleDragOver}
      onDragEnd={handlers.handleDragEnd}
    >
      <div className="flex flex-col md:flex-row gap-4 md:gap-6 justify-evenly max-h-[calc(100vh-10rem)] overflow-x-auto">
        {columns.map((column) => (
          <KanbanColumn key={column.id} column={column} />
        ))}
      </div>
      <DragOverlay>
        {activeTask ? <TaskCard task={activeTask} /> : null}
      </DragOverlay>
    </DndContext>
  );
};

export default function BoardPage() {
  return (
    <div className="p-2 md:p-4 md:pt-0">
      <div className="space-y-4">
        <BoardHeader />
        <KanbanBoard />
      </div>
    </div>
  );
}
