import {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { arrayMove, sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { useCallback, useMemo, useState } from "react";
import type { Column, Task } from "../types/board.types";

export function useKanbanBoard(initialColumns: Column[]) {
  // Memoize initial columns with dummies to prevent recreation on every render
  const initialColumnsWithDummies = useMemo(
    () =>
      initialColumns.map((col) => ({
        ...col,
        tasks:
          col.tasks.length > 0
            ? col.tasks
            : [
                {
                  id: `dummy-${col.id}`,
                  title: "Dummy task",
                  tags: [],
                  isDummy: true,
                },
              ],
      })),
    [initialColumns]
  );

  const [columns, setColumns] = useState<Column[]>(initialColumnsWithDummies);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  // Memoize sensors to prevent recreation on every render
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Add activation constraints with minimum distance required to start dragging
      activationConstraint: {
        distance: 2, // 8px minimum distance before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Memoize this helper function
  const findContainer = useCallback(
    (id: string) => {
      return columns.find((col) => col.tasks.some((task) => task.id === id))
        ?.id;
    },
    [columns]
  );

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;

      // Find the task in the columns
      const task = columns
        .find((col) => col.tasks.some((t) => t.id === active.id))
        ?.tasks.find((t) => t.id === active.id);

      // Skip if it's a dummy task or if task not found
      if (!task || task.isDummy) {
        return;
      }

      // Set the active task
      setActiveTask(task);
    },
    [columns]
  );

  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { active, over } = event;
      if (!over) return;

      const activeId = active.id;
      const overId = over.id;

      const activeContainer = findContainer(activeId.toString());
      const overContainer = findContainer(overId.toString());

      if (
        !activeContainer ||
        !overContainer ||
        activeContainer === overContainer
      ) {
        return;
      }

      // Move the task to the new column - do this in a single state update
      setColumns((prev) => {
        const activeIndex = prev.findIndex((col) => col.id === activeContainer);
        const overIndex = prev.findIndex((col) => col.id === overContainer);

        const task = prev[activeIndex].tasks.find((t) => t.id === activeId);
        if (!task) return prev;

        // Create new arrays to avoid mutation
        const newColumns = [...prev];

        // Update active column - remove the task
        const newActiveColumn = {
          ...newColumns[activeIndex],
          tasks: newColumns[activeIndex].tasks.filter((t) => t.id !== activeId),
        };
        newColumns[activeIndex] = newActiveColumn;

        // Update over column - add the task
        const newOverColumn = {
          ...newColumns[overIndex],
          tasks: newColumns[overIndex].tasks
            .filter((t) => !t.isDummy)
            .concat(task),
        };
        newColumns[overIndex] = newOverColumn;

        // Add dummy tasks to empty columns
        return newColumns.map((col) => {
          if (col.tasks.length === 0) {
            return {
              ...col,
              tasks: [
                {
                  id: `dummy-${col.id}`,
                  title: "Dummy task",
                  tags: [],
                  isDummy: true,
                },
              ],
            };
          }
          return col;
        });
      });
    },
    [findContainer]
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      if (!over) return;

      const activeId = active.id;
      const overId = over.id;

      const activeContainer = findContainer(activeId.toString());
      const overContainer = findContainer(overId.toString());

      if (!activeContainer || !overContainer) return;

      // Single state update for both operations
      setColumns((prev) => {
        const newColumns = [...prev];

        if (activeContainer === overContainer) {
          const columnIndex = newColumns.findIndex(
            (col) => col.id === activeContainer
          );
          const column = newColumns[columnIndex];

          // If the column is empty or has a dummy task, just cleanup
          if (column.tasks.some((t) => t.isDummy)) {
            setActiveTask(null);
            return newColumns;
          }

          const oldIndex = column.tasks.findIndex(
            (task) => task.id === activeId
          );
          const newIndex = column.tasks.findIndex((task) => task.id === overId);

          // Move the task within the same column
          const tasks = arrayMove(column.tasks, oldIndex, newIndex);
          newColumns[columnIndex] = { ...column, tasks };
        }

        // Ensure we clean up any empty columns by adding a dummy task in the same update
        return newColumns.map((col) => {
          if (col.tasks.length === 0) {
            return {
              ...col,
              tasks: [
                {
                  id: `dummy-${col.id}`,
                  title: "Dummy task",
                  tags: [],
                  isDummy: true,
                },
              ],
            };
          }
          return col;
        });
      });

      setActiveTask(null);
    },
    [findContainer]
  );

  // Memoize handlers object to prevent recreation on every render
  const handlers = useMemo(
    () => ({
      handleDragStart,
      handleDragOver,
      handleDragEnd,
    }),
    [handleDragStart, handleDragOver, handleDragEnd]
  );

  return {
    columns,
    activeTask,
    sensors,
    handlers,
  };
}
