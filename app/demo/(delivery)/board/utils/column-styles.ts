import { AlertCircle, CheckCircle2, Circle, ListChecks } from "lucide-react";
import { ColumnStyle } from "../types/board.types";

// Get column style based on column title
export const getColumnStyle = (title: string): ColumnStyle => {
  switch (title) {
    case "To Do":
      return {
        header:
          "text-blue-800 dark:text-blue-400 bg-blue-50/80 dark:bg-blue-500/20 border-blue-100 dark:border-blue-950/30",
        title: "text-blue-800 dark:text-blue-400",
        icon: Circle,
        iconColor: "text-blue-500 dark:text-blue-500",
      };
    case "In Progress":
      return {
        header:
          "text-amber-700 dark:text-amber-400 bg-amber-50/80 dark:bg-amber-500/20 border-amber-100 dark:border-amber-950/30",
        title: "text-amber-700 dark:text-amber-400",
        icon: AlertCircle,
        iconColor: "text-amber-700 dark:text-amber-500",
      };
    case "In Review":
      return {
        header:
          "text-purple-800 dark:text-purple-400 bg-purple-50/80 dark:bg-purple-500/20 border-purple-100 dark:border-purple-950/30",
        title: "text-purple-800 dark:text-purple-400",
        icon: ListChecks,
        iconColor: "text-purple-500 dark:text-purple-500",
      };
    case "Done":
      return {
        header:
          "text-green-800 dark:text-green-400 bg-green-50/80 dark:bg-green-500/20 border-green-100 dark:border-green-950/30",
        title: "text-green-800 dark:text-green-400",
        icon: CheckCircle2,
        iconColor: "text-green-500 dark:text-green-500",
      };
    default:
      return {
        header:
          "text-gray-800 dark:text-gray-300 bg-gray-50/80 dark:bg-gray-800/90 border-gray-100 dark:border-gray-800/30",
        title: "text-gray-800 dark:text-gray-300",
        icon: ListChecks,
        iconColor: "text-gray-500 dark:text-gray-400",
      };
  }
};
