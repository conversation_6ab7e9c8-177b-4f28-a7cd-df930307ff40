export type TaskTag =
  | "feature"
  | "architecture"
  | "bug"
  | "refactor"
  | "performance"
  | "scalability"
  | "security"
  | "testing"
  | "monitoring";

type TagColor = {
  text: string;
  border: string;
  bg: string;
  darkBg: string;
  darkBorder: string;
  darkText: string;
};

type TagConfig = {
  label: string;
  color: TagColor;
};

const TAG_CONFIG: Record<TaskTag, TagConfig> = {
  feature: {
    label: "Feature",
    color: {
      text: "text-blue-600",
      border: "border-blue-200",
      bg: "bg-blue-50",
      darkBg: "dark:bg-blue-500/10",
      darkBorder: "dark:border-blue-300",
      darkText: "dark:text-blue-300",
    },
  },
  architecture: {
    label: "Architecture",
    color: {
      text: "text-sky-600",
      border: "border-sky-200",
      bg: "bg-sky-50",
      darkBg: "dark:bg-sky-500/10",
      darkBorder: "dark:border-sky-300",
      darkText: "dark:text-sky-300",
    },
  },
  bug: {
    label: "Bug",
    color: {
      text: "text-rose-600",
      border: "border-rose-200",
      bg: "bg-rose-50",
      darkBg: "dark:bg-rose-500/10",
      darkBorder: "dark:border-rose-300",
      darkText: "dark:text-rose-300",
    },
  },
  refactor: {
    label: "Refactor",
    color: {
      text: "text-teal-600",
      border: "border-teal-200",
      bg: "bg-teal-50",
      darkBg: "dark:bg-teal-500/10",
      darkBorder: "dark:border-teal-300",
      darkText: "dark:text-teal-300",
    },
  },
  performance: {
    label: "Performance",
    color: {
      text: "text-fuchsia-600",
      border: "border-fuchsia-200",
      bg: "bg-fuchsia-50",
      darkBg: "dark:bg-fuchsia-500/10",
      darkBorder: "dark:border-fuchsia-300",
      darkText: "dark:text-fuchsia-300",
    },
  },
  scalability: {
    label: "Scalability",
    color: {
      text: "text-amber-600",
      border: "border-amber-200",
      bg: "bg-amber-50",
      darkBg: "dark:bg-amber-500/10",
      darkBorder: "dark:border-amber-300",
      darkText: "dark:text-amber-300",
    },
  },
  security: {
    label: "Security",
    color: {
      text: "text-red-600",
      border: "border-red-200",
      bg: "bg-red-50",
      darkBg: "dark:bg-red-500/10",
      darkBorder: "dark:border-red-300",
      darkText: "dark:text-red-300",
    },
  },
  testing: {
    label: "Testing",
    color: {
      text: "text-violet-600",
      border: "border-violet-200",
      bg: "bg-violet-50",
      darkBg: "dark:bg-violet-500/10",
      darkBorder: "dark:border-violet-300",
      darkText: "dark:text-violet-300",
    },
  },
  monitoring: {
    label: "Monitoring",
    color: {
      text: "text-lime-600",
      border: "border-lime-200",
      bg: "bg-lime-50",
      darkBg: "dark:bg-lime-500/10",
      darkBorder: "dark:border-lime-300",
      darkText: "dark:text-lime-300",
    },
  },
};

/**
 * Gets the display label for a given task tag
 */
export const getTagLabel = (tag: TaskTag): string => {
  return TAG_CONFIG[tag].label;
};

/**
 * Gets the color configuration for a given task tag
 */
export const getTagColors = (tag: TaskTag): TagColor => {
  return TAG_CONFIG[tag].color;
};

/**
 * Gets all available task tags
 */
export const getTaskTags = (): TaskTag[] => {
  return Object.keys(TAG_CONFIG) as TaskTag[];
};

/**
 * Gets the combined className string for a tag
 */
export const getTagClassName = (tag: TaskTag): string => {
  // Return a default style if the tag doesn't exist in our config
  if (!tag || !TAG_CONFIG[tag]) {
    return "text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-300 dark:bg-gray-500/10 dark:border-gray-300";
  }

  const colors = TAG_CONFIG[tag].color;
  return `${colors.text} ${colors.bg} ${colors.border} ${colors.darkText} ${colors.darkBg} ${colors.darkBorder}`;
};
