import { type LucideIcon } from "lucide-react";

export interface Task {
  id: string;
  title: string;
  tags: string[];
  isDummy?: boolean;
  status?: string;
  duration?: string;
  dueDate?: string;
  assignees?: {
    id: string;
    name: string;
    avatar?: string;
    initials?: string;
  }[];
  description?: string;
}

export interface Column {
  id: string;
  title: string;
  tasks: Task[];
}

export interface ColumnStyle {
  header: string;
  title: string;
  icon: LucideIcon;
  iconColor: string;
}
