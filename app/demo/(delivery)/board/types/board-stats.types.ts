export interface PriorityDistribution {
  high: number;
  medium: number;
  low: number;
}

export interface CategoryStats {
  count: number;
  trend: string;
  priority: PriorityDistribution;
}

export interface BoardStats {
  totalItems: number;
  categories: {
    bugs: CategoryStats;
    features: CategoryStats;
    improvements: CategoryStats;
  };
  recentActivity: {
    lastUpdated: string;
    addedLast7Days: number;
    completedLast7Days: number;
  };
  teamMetrics: {
    averageTimeToResolution: string;
    backlogGrowthRate: string;
    healthScore: number;
  };
}
