import { Column } from "../types/board.types";

export const initialColumns: Column[] = [
  {
    id: "todo",
    title: "To Do",
    tasks: [
      {
        id: "001",
        title: "Optimize database queries for dashboard",
        description:
          "Analyze and optimize slow-performing database queries in the dashboard. Implement query caching and indexing where appropriate.",
        duration: "3d",
        status: "todo",
        tags: ["performance"],
      },
      {
        id: "002",
        title: "Fix user authentication edge cases",
        description:
          "Address edge cases in user authentication flow, including session handling and token refresh mechanisms.",
        duration: "2d",
        status: "todo",
        tags: ["bug", "testing"],
      },
      {
        id: "009",
        title: "Design microservices architecture",
        description:
          "Create comprehensive architecture documentation for breaking down monolith into microservices, including service boundaries and communication patterns.",
        duration: "7d",
        status: "todo",
        tags: ["architecture"],
      },
      {
        id: "010",
        title: "Implement rate limiting middleware",
        description:
          "Develop and implement rate limiting middleware to prevent API abuse and ensure fair usage.",
        duration: "2d",
        status: "todo",
        tags: ["security"],
      },
    ],
  },
  {
    id: "in-progress",
    title: "In Progress",
    tasks: [
      {
        id: "003",
        title: "Resolve infinite scroll memory leak",
        description:
          "Investigate and fix memory leak issues in infinite scroll implementation, focusing on proper cleanup and optimization.",
        duration: "4d",
        status: "in-progress",
        tags: ["bug"],
      },
      {
        id: "004",
        title: "Implement Redis caching layer",
        description:
          "Set up Redis caching infrastructure to improve application performance and reduce database load.",
        duration: "5d",
        status: "in-progress",
        tags: ["scalability"],
      },
    ],
  },
  {
    id: "in-review",
    title: "In Review",
    tasks: [
      {
        id: "005",
        title: "Restructure API response handling",
        description:
          "Refactor API response handling to implement consistent error handling and response formatting across all endpoints.",
        duration: "3d",
        status: "in-review",
        tags: ["refactor"],
      },
      {
        id: "006",
        title: "Add real-time notifications system",
        description:
          "Implement WebSocket-based real-time notification system for instant updates to users.",
        duration: "6d",
        status: "in-review",
        tags: ["feature"],
      },
      {
        id: "013",
        title: "Implement OAuth2 security flow",
        description:
          "Set up secure OAuth2 authentication flow with multiple providers and proper token management.",
        duration: "4d",
        status: "in-review",
        tags: ["security"],
      },
    ],
  },
  {
    id: "done",
    title: "Done",
    tasks: [
      {
        id: "007",
        title: "Set up Jest and E2E testing pipeline",
        description:
          "Configure automated testing pipeline with Jest for unit tests and Cypress for end-to-end testing.",
        duration: "3d",
        status: "done",
        tags: ["testing"],
      },
      {
        id: "008",
        title: "Design and implement user onboarding flow",
        description:
          "Create intuitive onboarding experience with step-by-step guide and progress tracking.",
        duration: "5d",
        status: "done",
        tags: ["feature"],
      },
      {
        id: "015",
        title: "Implement service mesh architecture",
        description:
          "Deploy and configure service mesh solution for better service discovery, monitoring, and security.",
        duration: "8d",
        status: "done",
        tags: ["architecture"],
      },
      {
        id: "016",
        title: "Add security headers middleware",
        description:
          "Implement security headers middleware to enhance application security posture with HSTS, CSP, and other headers.",
        duration: "1d",
        status: "done",
        tags: ["security"],
      },
    ],
  },
];
