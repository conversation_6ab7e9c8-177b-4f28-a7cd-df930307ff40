import { BoardStats } from "../types/board-stats.types";

export const mockBoardStats: BoardStats = {
  totalItems: 13, // Total non-dummy tasks across all columns
  categories: {
    bugs: {
      count: 2,
      trend: "-15%", // Decreasing bugs is good
      priority: {
        high: 1,
        medium: 1,
        low: 0,
      },
    },
    features: {
      count: 3,
      trend: "+10%",
      priority: {
        high: 2,
        medium: 1,
        low: 0,
      },
    },
    improvements: {
      count: 8,
      trend: "+5%",
      priority: {
        high: 3,
        medium: 3,
        low: 2,
      },
    },
  },
  recentActivity: {
    lastUpdated: "2024-03-26T10:30:00Z",
    addedLast7Days: 5,
    completedLast7Days: 4, // Tasks in "Done" column
  },
  teamMetrics: {
    averageTimeToResolution: "3.5 days", // Average duration of completed tasks
    backlogGrowthRate: "-2.5%", // Decreasing backlog is good
    healthScore: 85, // High due to good task distribution and completion rate
  },
};

// Helper function to get random mock data for dynamic updates
export const getRandomBoardStats = (): BoardStats => {
  const randomInt = (min: number, max: number) =>
    Math.floor(Math.random() * (max - min + 1)) + min;

  const totalItems = randomInt(10, 20);
  const bugsCount = randomInt(1, 4);
  const featuresCount = randomInt(2, 5);
  const improvementsCount = totalItems - bugsCount - featuresCount;

  return {
    totalItems,
    categories: {
      bugs: {
        count: bugsCount,
        trend: `${Math.random() > 0.7 ? "+" : "-"}${randomInt(5, 20)}%`,
        priority: {
          high: randomInt(0, 2),
          medium: randomInt(1, 2),
          low: randomInt(0, 1),
        },
      },
      features: {
        count: featuresCount,
        trend: `${Math.random() > 0.4 ? "+" : "-"}${randomInt(5, 15)}%`,
        priority: {
          high: randomInt(1, 2),
          medium: randomInt(1, 2),
          low: randomInt(0, 1),
        },
      },
      improvements: {
        count: improvementsCount,
        trend: `${Math.random() > 0.3 ? "+" : "-"}${randomInt(5, 15)}%`,
        priority: {
          high: randomInt(1, 3),
          medium: randomInt(2, 4),
          low: randomInt(1, 2),
        },
      },
    },
    recentActivity: {
      lastUpdated: new Date().toISOString(),
      addedLast7Days: randomInt(3, 8),
      completedLast7Days: randomInt(2, 6),
    },
    teamMetrics: {
      averageTimeToResolution: `${randomInt(2, 5)}.${randomInt(0, 9)} days`,
      backlogGrowthRate: `${Math.random() > 0.6 ? "+" : "-"}${randomInt(
        1,
        5
      )}.${randomInt(0, 9)}%`,
      healthScore: randomInt(75, 95),
    },
  };
};
