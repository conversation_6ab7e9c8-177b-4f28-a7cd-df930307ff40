"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SeperatorVertical } from "@/components/ui/separator-vertical";
import { cn } from "@/lib/utils";
import { Ellipsis } from "lucide-react";
import { memo, useState } from "react";
import type { Task } from "../types/board.types";
import {
  type TaskTag,
  getTagClassName,
  getTagLabel,
  getTaskTags,
} from "../utils/tag-styles";
import TaskDetails from "./task-details";
import TaskMenu from "./task-menu";

interface TaskCardProps {
  task: Task;
  // onCardAction?: (taskId: string, action: "view" | "menu") => void;
}

// Helper to check if a string is a valid TaskTag (case-insensitive)
const isValidTaskTag = (tag: string): tag is TaskTag => {
  const tagLower = tag.toLowerCase();
  return getTaskTags().some((validTag) => validTag.toLowerCase() === tagLower);
};

// Helper to get the normalized tag (converted to proper TaskTag format)
const getNormalizedTag = (tag: string): TaskTag | undefined => {
  const tagLower = tag.toLowerCase();
  const validTag = getTaskTags().find(
    (validTag) => validTag.toLowerCase() === tagLower
  );
  return validTag;
};

export const TaskCard = memo(function TaskCard({ task }: TaskCardProps) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsSheetOpen(true);
  };

  return (
    <>
      <div
        id="task-card"
        className="flex flex-col justify-between  bg-white dark:bg-[#21262d] border border-[#e5e7eb] dark:border-[#30363d] rounded-lg p-2 shadow-xs hover:shadow-md transition-all duration-200 cursor-pointer group"
        onClick={handleCardClick}
      >
        <header className="flex flex-row items-center justify-between">
          <div className="flex flex-row flex-wrap gap-1">
            {task?.tags?.map((tag) => {
              const isValid = isValidTaskTag(tag);
              const normalizedTag = getNormalizedTag(tag);

              return (
                <Badge
                  key={tag}
                  variant="outline"
                  className={cn(
                    "w-fit px-1.5 py-0.2 text-tiny! font-medium border-[.08rem]",
                    isValid && normalizedTag
                      ? getTagClassName(normalizedTag)
                      : "text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-300 dark:bg-gray-500/10 dark:border-gray-300"
                  )}
                >
                  {isValid && normalizedTag ? getTagLabel(normalizedTag) : tag}
                </Badge>
              );
            })}
          </div>
          <div className="flex flex-row items-center gap-1">
            <span className="text-xs text-gray-400">{task.duration}</span>
            <TaskMenuBoard>
              <Ellipsis className="size-3 text-gray-400" />
            </TaskMenuBoard>
          </div>
        </header>

        <div className="flex-1 cursor-pointer pt-2 pb-3">
          <span className="text-[#374151] dark:text-[#c9d1d9] text-sm font-medium line-clamp-2">
            {task.title}
          </span>
        </div>

        <footer className="flex flex-row items-center justify-between gap-2 text-xs">
          <div className="flex flex-row items-center gap-2">
            <span>#-{task.id}</span>

            <SeperatorVertical height="4" />
            <Badge variant="outline" className="text-tiny!">
              {task.status}
            </Badge>
          </div>
          <Avatar className="size-5">
            <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
        </footer>
      </div>

      <TaskDetails
        key={task.id}
        task={task}
        isSheetOpen={isSheetOpen}
        setIsSheetOpen={setIsSheetOpen}
      />
    </>
  );
});

interface TaskMenuProps {
  children: React.ReactNode;
}

const TaskMenuBoard = ({ children }: TaskMenuProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(true);
  };

  return (
    <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen} modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="xs"
          className="cursor-pointer"
          onClick={handleMenuClick}
        >
          {children}
        </Button>
      </DropdownMenuTrigger>
      <TaskMenu />
    </DropdownMenu>
  );
};
