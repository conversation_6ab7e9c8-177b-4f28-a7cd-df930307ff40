import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Bar } from "@/components/ui/scroll-area";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ListIcon as RemixListIcon,
  SquareKanbanIcon,
  TruckIcon,
} from "lucide-react";
import Link from "next/link";

export default function BoardViewSwitch({ currentView = "board" }) {
  const defaultTab =
    {
      board: "tab-1",
      backlog: "tab-2",
      release: "tab-3",
    }[currentView] || "tab-1";

  return (
    <Tabs value={defaultTab}>
      <ScrollArea>
        <TabsList className="mb-3">
          <Link href="/demo/backlog">
            <TabsTrigger value="tab-2" className="group cursor-pointer">
              <RemixListIcon
                className="-ms-0.5 me-1.5 opacity-60"
                size={16}
                aria-hidden="true"
              />
              List
              <Badge
                className="bg-primary/15 ms-1.5 min-w-5 px-1 transition-opacity group-data-[state=inactive]:opacity-50"
                variant="secondary"
              >
                3
              </Badge>
            </TabsTrigger>
          </Link>
          <Link href="/demo/board">
            <TabsTrigger value="tab-1" className="group cursor-pointer">
              <SquareKanbanIcon
                className="-ms-0.5 me-1.5 opacity-60"
                size={16}
                aria-hidden="true"
              />
              Kanban
            </TabsTrigger>
          </Link>
          <Link href="/demo/release">
            <TabsTrigger value="tab-3" className="group cursor-pointer">
              <TruckIcon
                className="-ms-0.5 me-1.5 opacity-60"
                size={16}
                aria-hidden="true"
              />
              Release
              <Badge className="ms-1.5 transition-opacity group-data-[state=inactive]:opacity-50">
                1
              </Badge>
            </TabsTrigger>
          </Link>
        </TabsList>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
      {/* <TabsContent value="tab-1">
        <p className="text-muted-foreground p-4 pt-1 text-center text-xs">Content for Tab 1</p>
      </TabsContent>
      <TabsContent value="tab-2">
        <p className="text-muted-foreground p-4 pt-1 text-center text-xs">Content for Tab 2</p>
      </TabsContent>
      <TabsContent value="tab-3">
        <p className="text-muted-foreground p-4 pt-1 text-center text-xs">Content for Tab 3</p>
      </TabsContent> */}
    </Tabs>
  );
}
