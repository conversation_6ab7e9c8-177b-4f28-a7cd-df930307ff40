import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useId } from "react";
import { getTagLabel, getTaskTags, TaskTag } from "../utils/tag-styles";

interface FilterTaskProps {
  children: React.ReactNode;
}

export default function FilterTask({ children }: FilterTaskProps) {
  const id = useId();
  const tags = getTaskTags();

  return (
    <div className="flex flex-col gap-4">
      <Popover>
        <PopoverTrigger asChild>{children}</PopoverTrigger>
        <PopoverContent className="w-36 p-3">
          <div className="space-y-3">
            <div className="text-muted-foreground text-xs font-medium">
              Filters
            </div>
            <form>
              <div className="space-y-3">
                {tags.map((tag: TaskTag) => (
                  <div key={tag} className="flex items-center gap-2">
                    <Checkbox id={`${id}-${tag}`} />
                    <Label htmlFor={`${id}-${tag}`} className="font-normal">
                      {getTagLabel(tag)}
                    </Label>
                  </div>
                ))}
              </div>
              <div
                role="separator"
                aria-orientation="horizontal"
                className="bg-border -mx-3 my-3 h-px"
              ></div>
              <div className="flex justify-between gap-2">
                <Button size="sm" variant="outline" className="h-7 px-2">
                  Clear
                </Button>
                <Button size="sm" className="h-7 px-2">
                  Apply
                </Button>
              </div>
            </form>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
