import { Badge } from "@/components/ui/badge";
import { Card, CardDescription, CardTitle } from "@/components/ui/card";
import {
  BugIcon,
  ClockIcon,
  LightbulbIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  WrenchIcon,
} from "lucide-react";
import { mockBoardStats } from "../data/mock-board-stats";
import { BoardStats } from "../types/board-stats.types";

type StatCardProps = {
  variant: "bugs" | "features" | "improvements" | "overall";
  data: BoardStats;
};

const getTrendIcon = (trend: string) => {
  return trend.startsWith("+") ? (
    <TrendingUpIcon className="size-3" />
  ) : (
    <TrendingDownIcon className="size-3" />
  );
};

const getCategoryIcon = (variant: StatCardProps["variant"]) => {
  switch (variant) {
    case "bugs":
      return <BugIcon className="size-4" />;
    case "features":
      return <LightbulbIcon className="size-4" />;
    case "improvements":
      return <WrenchIcon className="size-4" />;
    default:
      return <ClockIcon className="size-4" />;
  }
};

const BoardStatCard = ({ variant, data }: StatCardProps) => {
  const getCardContent = () => {
    switch (variant) {
      case "bugs":
        return {
          title: "Active Bugs",
          value: data.categories.bugs.count,
          trend: data.categories.bugs.trend,
          description: `${data.categories.bugs.priority.high} high priority`,
          footer: "Bug resolution metrics",
        };
      case "features":
        return {
          title: "Feature Requests",
          value: data.categories.features.count,
          trend: data.categories.features.trend,
          description: `${data.categories.features.priority.high} high priority`,
          footer: "Feature development tracking",
        };
      case "improvements":
        return {
          title: "Improvements",
          value: data.categories.improvements.count,
          trend: data.categories.improvements.trend,
          description: `${data.categories.improvements.priority.high} high priority`,
          footer: "Improvement initiatives",
        };
      default:
        return {
          title: "Total Board",
          value: data.totalItems,
          trend: data.teamMetrics.backlogGrowthRate,
          description: `Health Score: ${data.teamMetrics.healthScore}%`,
          footer: `Avg. Resolution: ${data.teamMetrics.averageTimeToResolution}`,
        };
    }
  };

  const content = getCardContent();

  return (
    <Card className="p-4">
      <div className="relative flex flex-col gap-1">
        <CardDescription className="flex items-center gap-2 text-sm">
          {getCategoryIcon(variant)} {content.title}
        </CardDescription>
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl font-semibold tabular-nums">
            {content.value}
          </CardTitle>
          <Badge
            variant="outline"
            className={`flex gap-1 rounded-lg text-xs ${
              content.trend.startsWith("+") ? "text-green-600" : "text-red-600"
            }`}
          >
            {getTrendIcon(content.trend)}
            {content.trend}
          </Badge>
        </div>
        <div className="mt-1 flex flex-col gap-0.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {content.description}
          </div>
          <div className="text-muted-foreground">{content.footer}</div>
        </div>
      </div>
    </Card>
  );
};

// Export the variants to be used in the page
export const BoardStatsCards = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <BoardStatCard variant="overall" data={mockBoardStats} />
      <BoardStatCard variant="bugs" data={mockBoardStats} />
      <BoardStatCard variant="features" data={mockBoardStats} />
      <BoardStatCard variant="improvements" data={mockBoardStats} />
    </div>
  );
};

export default BoardStatsCards;
