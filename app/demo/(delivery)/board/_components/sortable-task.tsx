import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { memo, useMemo } from "react";
import type { Task } from "../types/board.types";
import { TaskCard } from "./task-card";

interface SortableTaskProps {
  task: Task;
  disabled?: boolean;
}

function SortableTask({ task, disabled = false }: SortableTaskProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    disabled,
  });

  // Memoize style calculations
  const style = useMemo(
    () => ({
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
      position: "relative" as const,
      zIndex: isDragging ? 10 : 0,
    }),
    [transform, transition, isDragging]
  );

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative touch-manipulation focus:outline-none"
      {...attributes}
      {...listeners}
      suppressHydrationWarning
    >
      <TaskCard task={task} />
    </div>
  );
}

// Export memoized component
export default memo(SortableTask);
