import MenuTabs1 from "@/components/origin-ui/menu-tabs-1";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { SeperatorVertical } from "@/components/ui/separator-vertical";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import {
  CalendarClockIcon,
  CircleDotIcon,
  Clock12Icon,
  CopyIcon,
  FileTextIcon,
  PaperclipIcon,
  TagIcon,
  UsersIcon,
} from "lucide-react";
import Image from "next/image";
import { Task } from "../types/board.types";
import { TaskTag, getTagClassName, getTagLabel } from "../utils/tag-styles";

interface TaskDetailsProps {
  task: Task;
  isSheetOpen: boolean;
  setIsSheetOpen: (isOpen: boolean) => void;
}

const TaskDetails = ({
  task,
  isSheetOpen,
  setIsSheetOpen,
}: TaskDetailsProps) => {
  return (
    <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
      <SheetContent className="overflow-y-auto sm:max-w-2xl p-0 rounded-l-lg">
        {/* Header */}
        <div className="flex flex-row items-center px-8 pt-4 gap-2">
          <Button variant="ghost" size="icon" className="size-auto p    -1">
            <div className="text-sm font-semibold text-neutral-600">
              #-{task.id}
            </div>
            <CopyIcon className="h-3 w-3 text-neutral-600" />
          </Button>
          <SeperatorVertical height="5 mx-2 mr-3" />
          <SheetBreadcrumb />
          {/* <ExpandIcon className="h-4 w-4 text-neutral-800" /> */}
        </div>
        <div className="flex flex-col h-full px-4 pb-6">
          <SheetHeader>
            <SheetTitle className="text-4xl font-medium text-neutral-800">
              {task.title}
            </SheetTitle>
          </SheetHeader>

          <div className="w-full space-y-4 p-4">
            {/* Status */}
            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <div className="flex items-center gap-3">
                <CircleDotIcon className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Status</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock12Icon className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">In Progress</span>
              </div>
            </div>

            {/* Due date */}
            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <div className="flex items-center gap-3">
                <CalendarClockIcon className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Due date</span>
              </div>
              <span className="text-sm font-medium">
                {task.dueDate || "26 March 2025"}
              </span>
            </div>

            {/* Assignees */}
            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <div className="flex items-center gap-3">
                <UsersIcon className="h-4 w-4  text-gray-600" />
                <span className="text-sm text-gray-600">Assignee</span>
              </div>
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-tiny! bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200">
                    CT
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">Calum Tyler</span>
              </div>
            </div>

            {/* Tags */}
            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <div className="flex items-center gap-3">
                <TagIcon className="h-4 w-4  text-gray-600" />
                <span className="text-sm  text-gray-600">Tags</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {task.tags?.map((tag: string, index: number) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className={`px-2 py-0.5 text-xs rounded-full ${getTagClassName(
                      tag as TaskTag
                    )}`}
                  >
                    {getTagLabel(tag as TaskTag)}
                  </Badge>
                )) || (
                  <span className="text-sm text-muted-foreground">No tags</span>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-4 pt-2">
              <div className="flex items-center gap-3">
                <FileTextIcon className="h-4 w-4 text-gray-600" />
                <h3 className="text-sm  text-gray-600">Description</h3>
              </div>
              <Textarea
                placeholder={
                  task.description ||
                  "This page aims to provide real-time insights into employee performance metrics and key business indicators."
                }
                className="w-full"
                rows={4}
              />
            </div>

            {/* Attachments */}
            <div className="flex flex-col justify-between gap-2">
              <div className="flex flex-row justify-between items-center gap-2">
                <div className="flex items-center gap-3">
                  <PaperclipIcon className="h-4 w-4 text-gray-600" />
                  <span className="text-sm text-gray-600">Attachments</span>
                  <span className="text-sm text-gray-600">(2)</span>
                </div>
                <Button variant="outline" size="sm">
                  Download All
                </Button>
              </div>
              <Image
                src="/mock-files.png"
                alt="Attachments"
                width={500}
                height={100}
              />
            </div>

            {/* Tabs - Subtasks, Comments, Activity */}
            <div className="">
              <MenuTabs1 />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default TaskDetails;

function SheetBreadcrumb() {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="#">Organization</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="#">Project</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Board</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
}
