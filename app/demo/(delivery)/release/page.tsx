import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import React from "react";
import BoardViewSwitch from "../board/_components/board-view-switch";
import EnvHealth from "./_components/env-health";
import PipelineStatus from "./_components/pipeline-status";
import ReleaseAnalytics from "./_components/release-analytics";
import ReleaseBanner from "./_components/release-banner";
import ReleaseTaskList from "./_components/release-task-list";
import {
  CurrentVersion,
  ReleaseVersions,
} from "./_components/release-versions";
import ScheduledDeployments from "./_components/schedule-deployments";

const ReleaseManagementPage = () => {
  return (
    <div className="h-full pt-0 p-6 dark:bg-black">
      <div className="flex flex-row items-center justify-between">
        <h1 className="text-lg md:text-2xl font-medium pb-2 dark:text-gray-100">
          Release Management
        </h1>
        <BoardViewSwitch currentView="release" />
      </div>
      <div className="grid grid-cols-6 grid-rows-7 gap-2">
        <GridItem className="col-span-1 row-span-3">
          <EnvHealth />
        </GridItem>
        <GridItem className="col-span-3 row-span-1">
          <ReleaseBanner />
        </GridItem>
        <GridItem className="col-span-2 row-span-2">
          <ScheduledDeployments />
        </GridItem>
        <GridItem className="col-span-3 row-span-6">
          <ReleaseTaskList />
        </GridItem>
        <GridItem className="col-span-2 row-span-3">
          <PipelineStatus />
        </GridItem>
        <GridItem className="col-span-1 row-span-3">
          <ReleaseVersions />
        </GridItem>
        <GridItem className="col-span-2 row-span-2">
          <ReleaseAnalytics />
        </GridItem>
        <GridItem className="col-span-1 row-span-1">
          <CurrentVersion />
        </GridItem>
      </div>
    </div>
  );
};

export default ReleaseManagementPage;

function GridItem({
  children,
  className,
}: {
  children: React.ReactNode;
  className: string;
}) {
  return (
    <Card
      className={cn(
        "flex flex-col justify-center items-center p-0 rounded-lg shadow-xs",
        className
      )}
    >
      {children}
    </Card>
  );
}
