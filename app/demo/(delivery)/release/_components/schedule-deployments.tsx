import React from "react";

type DeploymentSchedule = {
  serviceName: string;
  version: string;
  environment: string;
  scheduledTime: string;
  displayTime: string;
};

const deploymentSchedules: DeploymentSchedule[] = [
  {
    serviceName: "Frontend",
    version: "v2.4.1",
    environment: "Production",
    scheduledTime: "18:00",
    displayTime: "Today 18:00",
  },
  {
    serviceName: "Backend API",
    version: "v1.8.3",
    environment: "Staging",
    scheduledTime: "09:00",
    displayTime: "Tomorrow 09:00",
  },
  {
    serviceName: "Auth Service",
    version: "v2.0.2",
    environment: "Pre-Production",
    scheduledTime: "14:00",
    displayTime: "Tomorrow 14:00",
  },
];

const ScheduledDeploymentItem = ({
  deployment,
}: {
  deployment: DeploymentSchedule;
}) => (
  <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-accent/50 rounded-md">
    <div className="flex flex-col">
      <span className="text-xs font-medium dark:text-gray-100">
        {deployment.serviceName} {deployment.version}
      </span>
      <span className="text-[10px] text-muted-foreground dark:text-gray-400">
        To {deployment.environment}
      </span>
    </div>
    <span className="text-[10px] text-blue-600 dark:text-blue-400">
      {deployment.displayTime}
    </span>
  </div>
);

const ScheduledDeployments = () => {
  return (
    <div className="flex flex-col gap-3 w-full h-full p-3">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium mb-1 dark:text-gray-100">
            Scheduled Deployments
          </h3>
          <p className="text-xs text-muted-foreground dark:text-gray-400">
            Next 24 hours
          </p>
        </div>
        <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
          <span className="text-xs text-blue-600 dark:text-blue-400">
            + Create schedule
          </span>
        </button>
      </div>

      <div className="flex flex-col gap-2">
        {deploymentSchedules.map((deployment) => (
          <ScheduledDeploymentItem
            key={deployment.serviceName}
            deployment={deployment}
          />
        ))}
      </div>
    </div>
  );
};

export default ScheduledDeployments;
