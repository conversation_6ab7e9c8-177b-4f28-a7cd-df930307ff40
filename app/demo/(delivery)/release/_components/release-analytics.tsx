import React from "react";

type AnalyticMetric = {
  label: string;
  value: string;
  change: {
    value: string;
    trend: "up" | "down";
    color: "green" | "red" | "blue";
  };
};

type EnvironmentDistribution = {
  name: string;
  percentage: number;
  color: string;
};

const metrics: AnalyticMetric[] = [
  {
    label: "Success Rate",
    value: "98.5%",
    change: { value: "2.1%", trend: "up", color: "green" },
  },
  {
    label: "Avg Duration",
    value: "7m 12s",
    change: { value: "45s", trend: "up", color: "red" },
  },
  {
    label: "Rollbacks",
    value: "2",
    change: { value: "1", trend: "down", color: "green" },
  },
  {
    label: "Deployments",
    value: "24",
    change: { value: "8", trend: "up", color: "blue" },
  },
];

const environments: EnvironmentDistribution[] = [
  { name: "Production", percentage: 45, color: "bg-green-500" },
  { name: "Staging", percentage: 30, color: "bg-yellow-500" },
  { name: "Dev", percentage: 25, color: "bg-blue-500" },
];

const MetricCard = ({ metric }: { metric: AnalyticMetric }) => (
  <div className="bg-gray-50 dark:bg-accent/50 rounded-md p-2">
    <div className="text-[10px] text-muted-foreground dark:text-gray-400">
      {metric.label}
    </div>
    <div className="flex items-center gap-1">
      <div className="text-xs font-medium dark:text-gray-100">
        {metric.value}
      </div>
      <span
        className={`text-[10px] text-${metric.change.color}-600 dark:text-${metric.change.color}-400`}
      >
        {metric.change.trend === "up" ? "↑" : "↓"}
        {metric.change.value}
      </span>
    </div>
  </div>
);

const EnvironmentDistributionBar = () => (
  <div className="mt-2">
    <div className="text-[10px] text-muted-foreground dark:text-gray-400 mb-1">
      Environment Distribution
    </div>
    <div className="flex gap-1">
      {environments.map((env) => (
        <div
          key={env.name}
          className={`flex-1 h-1.5 rounded-full ${env.color} dark:opacity-80`}
        />
      ))}
    </div>
    <div className="flex justify-between mt-1 text-[10px] text-muted-foreground dark:text-gray-400">
      {environments.map((env) => (
        <span key={env.name}>{`${env.name} (${env.percentage}%)`}</span>
      ))}
    </div>
  </div>
);

const ReleaseAnalytics = () => {
  return (
    <div className="flex flex-col gap-3 w-full h-full p-3">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-sm font-medium mb-1 dark:text-gray-100">
          Deployment Analytics
        </h3>
        <p className="text-xs text-muted-foreground dark:text-gray-400">
          Last 30 days
        </p>
      </div>

      <div className="grid grid-cols-2 gap-2">
        {metrics.map((metric) => (
          <MetricCard key={metric.label} metric={metric} />
        ))}
      </div>

      <EnvironmentDistributionBar />
    </div>
  );
};

export default ReleaseAnalytics;
