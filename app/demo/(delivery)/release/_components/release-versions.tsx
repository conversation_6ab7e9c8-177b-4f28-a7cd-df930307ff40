import React from "react";

type VersionStatus = "stable" | "in-qa" | "in-dev";

type Version = {
  serviceName: string;
  version: string;
  status: VersionStatus;
  timestamp: string;
};

const getStatusStyles = (status: VersionStatus) => {
  switch (status) {
    case "stable":
      return {
        dot: "bg-green-500 dark:bg-green-400",
        ring: "ring-green-50 dark:ring-green-400/20",
        text: "text-muted-foreground dark:text-gray-400",
        label: status,
      };
    case "in-qa":
      return {
        dot: "bg-yellow-500 dark:bg-yellow-400",
        ring: "ring-yellow-50 dark:ring-yellow-400/20",
        text: "text-yellow-600 dark:text-yellow-400",
        label: "In QA",
      };
    case "in-dev":
      return {
        dot: "bg-blue-500 dark:bg-blue-400",
        ring: "ring-blue-50 dark:ring-blue-400/20",
        text: "text-blue-600 dark:text-blue-400",
        label: "In Dev",
      };
  }
};

const VersionItem = ({ version }: { version: Version }) => {
  const styles = getStatusStyles(version.status);

  return (
    <div className="flex items-center gap-2">
      <div
        className={`z-10 h-3 w-3 rounded-full ${styles.dot} ring-4 ${styles.ring} shadow-sm`}
      />
      <div className="flex-1 flex items-center justify-between p-2 bg-gray-50 dark:bg-accent/50 rounded-md">
        <div className="flex flex-col">
          <span className="text-xs font-medium dark:text-gray-100">
            {version.serviceName}
          </span>
          <span className="text-[10px] text-muted-foreground dark:text-gray-400">
            {version.version}
          </span>
        </div>
        <span className={`text-[10px] ${styles.text}`}>
          {version.status === "stable" ? version.timestamp : styles.label}
        </span>
      </div>
    </div>
  );
};

export const CurrentVersion = () => {
  const currentVersion = {
    version: "v2.4.0",
    status: "stable" as VersionStatus,
    tasks: 12,
    completion: 80,
  };

  const styles = getStatusStyles(currentVersion.status);

  return (
    <div className="flex flex-col gap-2 w-full h-full p-3">
      <div className="flex flex-row items-center justify-between">
        <div>
          <div className="flex flex-row items-center gap-2">
            <h3 className="text-sm font-medium dark:text-gray-100">
              Current Version
            </h3>
            <p className="text-xs text-muted-foreground dark:text-gray-400">
              {currentVersion.version}
            </p>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground dark:text-gray-400">
            <div className={`h-2 w-2 rounded-full ${styles.dot}`} />
            <span className="text-xs text-muted-foreground dark:text-gray-400">
              Stable
            </span>
          </div>
        </div>
      </div>
      <span className="text-xs text-muted-foreground dark:text-gray-400 bg-gray-50 dark:bg-accent/50 rounded-md p-2">
        Number of tasks: {currentVersion.tasks} <br />
        Completion:{" "}
        <span className="font-semibold dark:text-gray-300">
          {currentVersion.completion}%
        </span>
      </span>
    </div>
  );
};

export const ReleaseVersions = () => {
  const versions: Version[] = [
    {
      serviceName: "Frontend",
      version: "v2.4.0",
      status: "stable",
      timestamp: "2h ago",
    },
    {
      serviceName: "Backend API",
      version: "v1.8.2",
      status: "stable",
      timestamp: "1d ago",
    },
    {
      serviceName: "Mobile App",
      version: "v3.1.0-rc",
      status: "in-qa",
      timestamp: "",
    },
    {
      serviceName: "Analytics Service",
      version: "v1.2.0-dev",
      status: "in-dev",
      timestamp: "",
    },
    {
      serviceName: "Auth Service",
      version: "v2.0.1",
      status: "stable",
      timestamp: "3d ago",
    },
  ];

  return (
    <div className="flex flex-col gap-3 w-full h-full p-3">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium mb-1 dark:text-gray-100">
            Latest Versions
          </h3>
          <p className="text-xs text-muted-foreground dark:text-gray-400">
            Last updated: 1m ago
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-2 relative">
        <div className="absolute left-[5px] top-4 bottom-4 w-[2px] bg-gray-200 dark:bg-gray-700" />
        {versions.map((version) => (
          <VersionItem key={version.serviceName} version={version} />
        ))}
      </div>
    </div>
  );
};
