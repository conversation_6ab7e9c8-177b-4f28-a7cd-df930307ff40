import React from "react";

type TaskMetric = {
  label: string;
  value: string;
};

type SuccessMetric = {
  rate: number;
  label: string;
};

const taskMetrics: TaskMetric[] = [
  { label: "Active Tasks", value: "24" },
  { label: "Completed Today", value: "12" },
  { label: "Tasks Blocked", value: "3" },
];

const successMetric: SuccessMetric = {
  rate: 87,
  label: "Success Rate",
};

const Divider = () => (
  <div className="h-10 w-[1px] bg-gray-100 dark:bg-gray-700/50" />
);

const MetricItem = ({ label, value }: TaskMetric) => (
  <div className="flex flex-col">
    <div className="flex flex-col items-center gap-2">
      <span className="text-2xl font-semibold dark:text-gray-100">{value}</span>
      <span className="text-xs text-muted-foreground dark:text-gray-400">
        {label}
      </span>
    </div>
  </div>
);

const ReleaseBanner = () => {
  return (
    <div className="flex flex-row items-center w-full h-full px-4">
      <div className="w-3/4 flex justify-center">
        <div className="flex items-center gap-4">
          {taskMetrics.map((metric, index) => (
            <React.Fragment key={metric.label}>
              <MetricItem {...metric} />
              {index < taskMetrics.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </div>
      </div>
      <div className="w-1/4 flex justify-center">
        <div className="flex flex-col items-center">
          <span className="text-2xl font-semibold dark:text-gray-100">
            {successMetric.rate}%
          </span>
          <span className="text-xs text-muted-foreground dark:text-gray-400">
            {successMetric.label}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ReleaseBanner;
