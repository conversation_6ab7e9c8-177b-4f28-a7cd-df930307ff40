import React from "react";

type Task = {
  id: string;
  title: string;
  version: string;
};

type ReleaseTaskItemProps = {
  task: Task;
  environment: "production" | "staging" | "development";
};

const ReleaseTaskItem = ({ task, environment }: ReleaseTaskItemProps) => {
  const getEnvironmentStyles = () => {
    switch (environment) {
      case "production":
        return {
          hover: "hover:bg-green-100/50 dark:hover:bg-green-400/20",
          badge:
            "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-400",
        };
      case "staging":
        return {
          hover: "hover:bg-amber-100/50 dark:hover:bg-yellow-400/20",
          badge:
            "bg-amber-100 text-amber-700 dark:bg-amber-900/50 dark:text-amber-400",
        };
      case "development":
        return {
          hover: "hover:bg-blue-100/50 dark:hover:bg-blue-400/20",
          badge:
            "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400",
        };
    }
  };

  const renderActionButtons = () => {
    switch (environment) {
      case "production":
        return (
          <>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Deploy"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21.2 15c.7-1.2 1-2.5.7-3.9-.6-2-2.4-3.5-4.4-3.5h-1.2c-.7-3-3.2-5.2-6.2-5.6-3-.3-5.9 1.3-7.3 4-1.2 2.5-1 5.5.5 7.7" />
                <path d="M12 12v9" />
                <path d="m16 16-4-4-4 4" />
              </svg>
            </button>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Release"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M4 11v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                <path d="m20 11-8-8-8 8" />
              </svg>
            </button>
          </>
        );
      case "staging":
        return (
          <>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Test"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
              </svg>
            </button>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Promote"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m5 15 7-7 7 7" />
              </svg>
            </button>
          </>
        );
      case "development":
        return (
          <>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Commit"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="4" />
                <path d="M16 12v1.5a2.5 2.5 0 0 0 5 0V12a9 9 0 1 0-9 9" />
              </svg>
            </button>
            <button
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400"
              title="Branch"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="6" y1="3" x2="6" y2="15" />
                <circle cx="18" cy="6" r="3" />
                <circle cx="6" cy="18" r="3" />
                <path d="M18 9a9 9 0 0 1-9 9" />
              </svg>
            </button>
          </>
        );
    }
  };

  const styles = getEnvironmentStyles();

  return (
    <div
      key={task.id}
      className={`flex items-center justify-between p-2 bg-gray-50 dark:bg-accent/50 cursor-pointer rounded ${styles.hover}`}
    >
      <div className="flex items-center gap-3">
        <span className="text-xs text-muted-foreground dark:text-gray-500">
          #-{task.id}
        </span>
        <span className="text-sm dark:text-gray-100">{task.title}</span>
      </div>
      <div className="flex items-center gap-2">
        <span
          className={`px-2 py-0.5 text-[10px] ${styles.badge} rounded-full`}
        >
          v{task.version}
        </span>
        {renderActionButtons()}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-gray-600 dark:text-gray-400"
        >
          <circle cx="18" cy="18" r="3" />
          <circle cx="6" cy="6" r="3" />
          <path d="M13 6h3a2 2 0 0 1 2 2v7" />
          <line x1="6" x2="6" y1="9" y2="21" />
        </svg>
      </div>
    </div>
  );
};

const ReleaseTaskList = () => {
  const productionTasks = [
    {
      id: "123",
      title: "Update authentication flow",
      version: "2.1.0",
    },
    { id: "124", title: "Deploy API endpoints", version: "2.0.1" },
    {
      id: "128",
      title: "Performance optimization",
      version: "2.0.0",
    },
    {
      id: "129",
      title: "Security patches deployment",
      version: "1.9.5",
    },
  ];

  const stagingTasks = [
    {
      id: "125",
      title: "Test new feature rollout",
      version: "2.2.0-beta",
    },
    { id: "130", title: "Integration testing", version: "2.1.1-rc" },
    { id: "131", title: "Load testing setup", version: "2.1.0-test" },
    {
      id: "132",
      title: "User acceptance testing",
      version: "2.0.2-qa",
    },
  ];

  const developmentTasks = [
    {
      id: "126",
      title: "Implement user dashboard",
      version: "2.3.0-dev",
    },
    {
      id: "127",
      title: "Fix responsive layout issues",
      version: "2.2.1-dev",
    },
    {
      id: "133",
      title: "Add new API endpoints",
      version: "2.2.0-dev",
    },
    {
      id: "134",
      title: "Refactor authentication module",
      version: "2.1.2-dev",
    },
    { id: "135", title: "Update dependencies", version: "2.1.1-dev" },
  ];

  return (
    <div className="w-full h-full p-4 flex flex-col gap-4 overflow-auto">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium dark:text-gray-100">
          Tasks by Environment
        </h3>
        <div className="flex gap-2">
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="17 8 12 3 7 8" />
              <line x1="12" y1="3" x2="12" y2="15" />
            </svg>
          </button>
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex flex-col gap-6">
        {/* Production Environment */}
        <div className="space-y-2">
          <h4 className="text-xs text-green-600 dark:text-green-400 font-bold">
            Production
          </h4>
          <div className="space-y-1">
            {productionTasks.map((task) => (
              <ReleaseTaskItem
                key={task.id}
                task={task}
                environment="production"
              />
            ))}
          </div>
        </div>

        {/* Staging Environment */}
        <div className="space-y-2">
          <h4 className="text-xs text-amber-600/70 dark:text-amber-400/90 font-bold">
            Staging
          </h4>
          <div className="space-y-1">
            {stagingTasks.map((task) => (
              <ReleaseTaskItem
                key={task.id}
                task={task}
                environment="staging"
              />
            ))}
          </div>
        </div>

        {/* Development Environment */}
        <div className="space-y-2">
          <h4 className="text-xs text-blue-600/70 dark:text-blue-400/90 font-bold">
            Development
          </h4>
          <div className="space-y-1">
            {developmentTasks.map((task) => (
              <ReleaseTaskItem
                key={task.id}
                task={task}
                environment="development"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReleaseTaskList;
