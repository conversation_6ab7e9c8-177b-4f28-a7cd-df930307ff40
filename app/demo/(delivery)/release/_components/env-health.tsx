import React from "react";

type Environment = {
  name: string;
  services: number;
  status: "healthy" | "degraded" | "critical";
};

type SystemMetric = {
  name: string;
  value: string;
};

const environments: Environment[] = [
  { name: "Production", services: 4, status: "healthy" },
  { name: "Pre-Production", services: 3, status: "healthy" },
  { name: "Staging", services: 2, status: "degraded" },
  { name: "Development", services: 3, status: "degraded" },
];

const systemMetrics: SystemMetric[] = [
  { name: "CPU", value: "24%" },
  { name: "Memory", value: "67%" },
];

const getStatusStyles = (status: Environment["status"]) => {
  switch (status) {
    case "healthy":
      return { bg: "bg-green-500", text: "text-green-600", label: "Healthy" };
    case "degraded":
      return {
        bg: "bg-yellow-500",
        text: "text-yellow-600",
        label: "Degraded",
      };
    case "critical":
      return { bg: "bg-red-500", text: "text-red-600", label: "Critical" };
  }
};

const EnvHealth = () => {
  return (
    <div className="flex flex-col gap-3 w-full h-full p-3">
      <div>
        <h3 className="text-sm font-medium mb-1 dark:text-gray-100">
          Environment Health
        </h3>
        <p className="text-xs text-muted-foreground dark:text-gray-400">
          Last updated: 2m ago
        </p>
      </div>

      <div className="flex flex-col gap-2">
        {environments.map((env) => {
          const status = getStatusStyles(env.status);
          return (
            <div
              key={env.name}
              className="flex items-center justify-between p-2 bg-gray-50 dark:bg-accent/50 rounded-md"
            >
              <div className="flex flex-col">
                <span className="text-xs font-medium dark:text-gray-100">
                  {env.name}
                </span>
                <span className="text-[10px] text-muted-foreground dark:text-gray-400">
                  {env.services} services
                </span>
              </div>
              <div className="flex items-center gap-1.5">
                <div className={`h-2 w-2 rounded-full ${status.bg}`} />
                <span
                  className={`text-[10px] ${status.text} dark:text-opacity-90`}
                >
                  {status.label}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-auto">
        <div className="text-xs font-medium mb-1 dark:text-gray-100">
          System Metrics
        </div>
        <div className="flex gap-2">
          {systemMetrics.map((metric) => (
            <div
              key={metric.name}
              className="flex-1 bg-gray-50 dark:bg-accent/50 rounded-md p-2"
            >
              <div className="text-[10px] text-muted-foreground dark:text-gray-400">
                {metric.name}
              </div>
              <div className="text-xs font-medium dark:text-gray-100">
                {metric.value}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnvHealth;
