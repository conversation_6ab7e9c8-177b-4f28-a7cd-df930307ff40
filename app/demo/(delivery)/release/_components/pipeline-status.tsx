import React from "react";

type PipelineStep = {
  name: string;
  status: "success" | "in-progress" | "pending";
  details: string;
  statusText: string;
};

const pipelineSteps: PipelineStep[] = [
  {
    name: "Build",
    status: "success",
    details: "Completed in 2m 30s",
    statusText: "Success",
  },
  {
    name: "Unit Tests",
    status: "success",
    details: "98% coverage",
    statusText: "Passed",
  },
  {
    name: "Integration Tests",
    status: "in-progress",
    details: "Running...",
    statusText: "In Progress",
  },
  {
    name: "Deployment",
    status: "pending",
    details: "Waiting for tests",
    statusText: "Pending",
  },
];

const StatusIcon = ({ status }: { status: PipelineStep["status"] }) => {
  switch (status) {
    case "success":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-green-600"
        >
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
      );
    case "in-progress":
      return <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" />;
    case "pending":
      return <div className="h-2 w-2 rounded-full bg-gray-300" />;
  }
};

const getStatusTextColor = (status: PipelineStep["status"]) => {
  switch (status) {
    case "success":
      return "text-green-600";
    case "in-progress":
      return "text-blue-600";
    case "pending":
      return "text-gray-600";
  }
};

const PipelineStatus = () => {
  const progress = 65;

  return (
    <div className="flex flex-col gap-3 w-full h-full p-3">
      <div>
        <h3 className="text-sm font-medium mb-1 dark:text-gray-100">
          Deployment Pipeline
        </h3>
        <p className="text-xs text-muted-foreground dark:text-gray-400">
          Current status
        </p>
      </div>

      <div className="flex flex-col gap-2">
        {pipelineSteps.map((step) => (
          <div key={step.name} className="flex items-center gap-2">
            <StatusIcon status={step.status} />
            <div className="flex-1 flex items-center justify-between p-2 bg-gray-50 dark:bg-accent/50 rounded-md">
              <div className="flex flex-col">
                <span className="text-xs font-medium dark:text-gray-100">
                  {step.name}
                </span>
                <span className="text-[10px] text-muted-foreground dark:text-gray-400">
                  {step.details}
                </span>
              </div>
              <span
                className={`text-[10px] ${getStatusTextColor(
                  step.status
                )} dark:text-opacity-90`}
              >
                {step.statusText}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-auto flex flex-col gap-2">
        <div className="bg-gray-50 dark:bg-accent/50 rounded-md p-2">
          <div className="text-[10px] text-muted-foreground dark:text-gray-400 mb-1">
            Progress
          </div>
          <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-full h-1.5">
            <div
              className="bg-blue-500 dark:bg-blue-400 h-1.5 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="text-[10px] text-muted-foreground dark:text-gray-400 mt-1">
            {progress}% Complete
          </div>
        </div>
      </div>
    </div>
  );
};

export default PipelineStatus;
