import {
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  BoltIcon,
  CopyPlusIcon,
  FilesIcon,
  PenIcon,
  TrashIcon,
} from "lucide-react";

export default function TaskMenu() {
  return (
    <DropdownMenuContent>
      <DropdownMenuGroup>
        <DropdownMenuItem>
          <CopyPlusIcon size={16} className="opacity-60" aria-hidden="true" />
          Copy
        </DropdownMenuItem>
        <DropdownMenuItem>
          <PenIcon size={16} className="opacity-60" aria-hidden="true" />
          Rename
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem>
          <BoltIcon size={16} className="opacity-60" aria-hidden="true" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem>
          <FilesIcon size={16} className="opacity-60" aria-hidden="true" />
          Clone
        </DropdownMenuItem>
        <DropdownMenuItem variant="destructive">
          <TrashIcon size={16} aria-hidden="true" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  );
}
