import { BacklogStats } from "../types/backlog.types";

export const mockBacklogStats: BacklogStats = {
  totalItems: 147,
  categories: {
    bugs: {
      count: 42,
      trend: "+12%",
      priority: {
        high: 15,
        medium: 18,
        low: 9,
      },
    },
    features: {
      count: 73,
      trend: "-5%",
      priority: {
        high: 21,
        medium: 35,
        low: 17,
      },
    },
    improvements: {
      count: 32,
      trend: "+8%",
      priority: {
        high: 8,
        medium: 14,
        low: 10,
      },
    },
  },
  recentActivity: {
    lastUpdated: "2024-03-26T10:30:00Z",
    addedLast7Days: 23,
    completedLast7Days: 18,
  },
  teamMetrics: {
    averageTimeToResolution: "4.2 days",
    backlogGrowthRate: "+3.5%",
    healthScore: 78, // percentage
  },
};

// Helper function to get random mock data for dynamic updates
export const getRandomBacklogStats = (): BacklogStats => {
  const randomInt = (min: number, max: number) =>
    Math.floor(Math.random() * (max - min + 1)) + min;

  return {
    totalItems: randomInt(100, 200),
    categories: {
      bugs: {
        count: randomInt(30, 50),
        trend: `${Math.random() > 0.5 ? "+" : "-"}${randomInt(1, 15)}%`,
        priority: {
          high: randomInt(10, 20),
          medium: randomInt(15, 25),
          low: randomInt(5, 15),
        },
      },
      features: {
        count: randomInt(60, 90),
        trend: `${Math.random() > 0.5 ? "+" : "-"}${randomInt(1, 15)}%`,
        priority: {
          high: randomInt(15, 25),
          medium: randomInt(30, 40),
          low: randomInt(15, 25),
        },
      },
      improvements: {
        count: randomInt(25, 40),
        trend: `${Math.random() > 0.5 ? "+" : "-"}${randomInt(1, 15)}%`,
        priority: {
          high: randomInt(5, 10),
          medium: randomInt(10, 20),
          low: randomInt(8, 15),
        },
      },
    },
    recentActivity: {
      lastUpdated: new Date().toISOString(),
      addedLast7Days: randomInt(15, 30),
      completedLast7Days: randomInt(10, 25),
    },
    teamMetrics: {
      averageTimeToResolution: `${randomInt(2, 7)}.${randomInt(0, 9)} days`,
      backlogGrowthRate: `${Math.random() > 0.5 ? "+" : "-"}${randomInt(
        1,
        5
      )}.${randomInt(0, 9)}%`,
      healthScore: randomInt(65, 95),
    },
  };
};
