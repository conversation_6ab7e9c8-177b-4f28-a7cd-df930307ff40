"use client";

import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { AnimatePresence } from "framer-motion";
import { Ellipsis, ListFilterIcon, PlusIcon } from "lucide-react";
import { useId, useState } from "react";
import BoardViewSwitch from "../board/_components/board-view-switch";
import FilterTask from "../board/_components/filter-task";
import { initialColumns } from "../board/data/mock-kanban-data";
import { Task } from "../board/types/board.types";
import { getColumnStyle } from "../board/utils/column-styles";
import {
  getTagClassName,
  getTagLabel,
  type TaskTag,
} from "../board/utils/tag-styles";
import { BacklogStatsCards } from "./_components/backlog-stats";
import TaskMenu from "./_components/task-menu";

const BacklogHeader = () => {
  const id = useId();

  return (
    <>
      <BacklogStatsCards />
      <div className="flex flex-row items-start justify-between h-9">
        <div className="flex flex-row items-center gap-2 group-data-[collapsible=icon]:hidden">
          <SearchInputWithVoice id={id} />
          <FilterTask>
            <Button variant="outline" size="icon" aria-label="Filters">
              <ListFilterIcon size={16} aria-hidden="true" />
            </Button>
          </FilterTask>
        </div>
        <BoardViewSwitch currentView="backlog" />
      </div>
    </>
  );
};

interface TaskItemProps {
  task: Task;
  columnStyle: ReturnType<typeof getColumnStyle>;
}

const TaskItem = ({ task, columnStyle }: TaskItemProps) => {
  const IconComponent = columnStyle.icon;
  const columnHeaderIconSize = "h-3.5 w-3.5";

  return (
    <div
      className={cn(
        "flex items-center justify-between gap-4 rounded-lg p-2 shadow-none transition-all duration-200 hover:cursor-pointer",
        task.status === "todo" &&
          "hover:bg-blue-50/50 dark:hover:bg-blue-900/40",
        task.status === "in-progress" &&
          "hover:bg-amber-50/50 dark:hover:bg-amber-900/40",
        task.status === "in-review" &&
          "hover:bg-purple-50/50 dark:hover:bg-purple-900/40",
        task.status === "done" &&
          "hover:bg-green-50/50 dark:hover:bg-green-900/40"
      )}
    >
      <div className="flex items-center gap-4 min-w-0">
        <span className="text-xs text-muted-foreground whitespace-nowrap w-8">
          #-{task.id}
        </span>
        <span className="text-xs text-muted-foreground whitespace-nowrap">
          <IconComponent
            className={cn(columnHeaderIconSize, columnStyle.iconColor)}
          />
        </span>
        <h3 className="text-sm font-medium text-[#374151] dark:text-[#c9d1d9] truncate">
          {task.title}
        </h3>
      </div>
      <div className="flex flex-wrap gap-2 items-center">
        {task.tags?.map((tag) => (
          <Badge
            key={tag}
            variant="outline"
            className={cn(
              "w-fit px-1.5 py-0.2 text-tiny! font-medium border-[.08rem]",
              getTagClassName(tag as TaskTag)
            )}
          >
            {getTagLabel(tag as TaskTag)}
          </Badge>
        ))}
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {task.duration}
        </span>
        <Avatar className="size-5">
          <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
        <TaskMenuBacklog>
          <Ellipsis className="size-3 text-gray-400" />
        </TaskMenuBacklog>
      </div>
    </div>
  );
};

interface ColumnHeaderProps {
  title: string;
  count: number;
  style: ReturnType<typeof getColumnStyle>;
}

const ColumnHeader = ({ title, count, style }: ColumnHeaderProps) => {
  const IconComponent = style.icon;
  const columnHeaderIconSize = "h-3.5 w-3.5";

  return (
    <header
      className={cn(
        "py-3 px-4 flex items-center justify-between rounded-md border-b",
        style.header
      )}
    >
      <div className="flex items-center gap-2">
        <IconComponent className={cn(columnHeaderIconSize, style.iconColor)} />
        <h2 className={cn("text-sm font-medium", style.title)}>{title}</h2>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {count}
        </span>
      </div>
      <PlusIcon className="h-4 w-4 text-muted-foreground" />
    </header>
  );
};

const BacklogContent = () => {
  return (
    <div className="min-h-[100vh] flex-1 md:min-h-min space-y-4">
      {initialColumns.map((column) => {
        const style = getColumnStyle(column.title);
        const taskCount = column.tasks.filter(
          (task: Task) => !task.isDummy
        ).length;

        return (
          <div key={column.id}>
            <ColumnHeader
              title={column.title}
              count={taskCount}
              style={style}
            />
            <div className="space-y-0 p-2">
              {column.tasks.map((task: Task) => (
                <TaskItem key={task.id} task={task} columnStyle={style} />
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

interface TaskMenuProps {
  children: React.ReactNode;
}

const TaskMenuBacklog = ({ children }: TaskMenuProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(true);
  };

  return (
    <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen} modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="xs"
          className="cursor-pointer"
          onClick={handleMenuClick}
        >
          {children}
        </Button>
      </DropdownMenuTrigger>
      <TaskMenu />
    </DropdownMenu>
  );
};

export default function BacklogPage() {
  return (
    <AnimatePresence mode="popLayout">
      <div
        key="loaded-content"
        className="flex flex-1 flex-col h-screen overflow-y-auto gap-4 p-4 pt-0"
      >
        <BacklogHeader />
        <BacklogContent />
      </div>
    </AnimatePresence>
  );
}
