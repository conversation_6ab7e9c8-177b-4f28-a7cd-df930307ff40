"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>Fallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangle,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  ChevronRight,
  ClipboardList,
  Clock,
  Download,
  FileText,
  Filter,
  Search,
  Users,
} from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
// Project performance data
const projectPerformance = [
  { month: "Jan", actual: 65, forecast: 70 },
  { month: "Feb", actual: 75, forecast: 72 },
  { month: "Mar", actual: 82, forecast: 80 },
  { month: "Apr", actual: 78, forecast: 85 },
  { month: "May", actual: 90, forecast: 88 },
  { month: "Jun", actual: 95, forecast: 92 },
];

// Team performance data
const teamPerformance = [
  { name: "Design", value: 92, color: "#3b82f6" },
  { name: "Development", value: 85, color: "#10b981" },
  { name: "QA", value: 78, color: "#f59e0b" },
  { name: "DevOps", value: 95, color: "#8b5cf6" },
];

// Documents data
const documents = [
  {
    id: "doc-1",
    name: "Q2 Project Status Report.pdf",
    category: "Report",
    date: "2024-04-01",
    size: "3.2 MB",
    status: "final",
    author: "Sarah Johnson",
  },
  {
    id: "doc-2",
    name: "Technical Architecture Overview.docx",
    category: "Documentation",
    date: "2024-03-28",
    size: "2.1 MB",
    status: "final",
    author: "Alex Kim",
  },
  {
    id: "doc-3",
    name: "Sprint 24 Planning.pptx",
    category: "Planning",
    date: "2024-03-25",
    size: "4.5 MB",
    status: "final",
    author: "Taylor Moore",
  },
  {
    id: "doc-4",
    name: "Feature Requirements Specification.docx",
    category: "Requirements",
    date: "2024-03-20",
    size: "1.8 MB",
    status: "draft",
    author: "Jamie Chen",
  },
  {
    id: "doc-5",
    name: "Project Budget Forecast.xlsx",
    category: "Financial",
    date: "2024-03-15",
    size: "1.2 MB",
    status: "draft",
    author: "Morgan Lee",
  },
];

// Milestones and key dates
const milestones = [
  {
    id: "m1",
    title: "Design System Implementation",
    date: "2024-04-15",
    status: "completed",
    owner: "Design Team",
    description: "Complete design system for consistent UI/UX patterns.",
  },
  {
    id: "m2",
    title: "MVP Release",
    date: "2024-05-01",
    status: "on-track",
    owner: "Development Team",
    description: "First public release of core feature set.",
  },
  {
    id: "m3",
    title: "User Acceptance Testing",
    date: "2024-05-15",
    status: "at-risk",
    owner: "QA Team",
    description: "Complete testing with stakeholder participation.",
  },
  {
    id: "m4",
    title: "Production Deployment",
    date: "2024-06-01",
    status: "planned",
    owner: "DevOps Team",
    description: "Deploy to production environment.",
  },
];

// Action items
const actionItems = [
  {
    id: "a1",
    task: "Review performance bottlenecks in API",
    owner: "Alex Kim",
    dueDate: "2024-04-10",
    status: "in-progress",
    priority: "high",
  },
  {
    id: "a2",
    task: "Prepare stakeholder presentation",
    owner: "Sarah Johnson",
    dueDate: "2024-04-12",
    status: "not-started",
    priority: "medium",
  },
  {
    id: "a3",
    task: "Update documentation for onboarding flow",
    owner: "Jamie Chen",
    dueDate: "2024-04-15",
    status: "in-progress",
    priority: "low",
  },
  {
    id: "a4",
    task: "Resolve authentication edge cases",
    owner: "Taylor Moore",
    dueDate: "2024-04-08",
    status: "completed",
    priority: "high",
  },
];

// Resource allocation
const resources = [
  { role: "Frontend Developer", allocated: 3, required: 3 },
  { role: "Backend Developer", allocated: 2, required: 4 },
  { role: "Designer", allocated: 2, required: 2 },
  { role: "QA Engineer", allocated: 1, required: 2 },
  { role: "DevOps Engineer", allocated: 1, required: 1 },
];

export default function ReportsPage() {
  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Project Reports</h1>
          <p className="text-muted-foreground">
            Comprehensive project status and documentation
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              className="pl-9 w-full sm:w-[200px]"
            />
          </div>
          <Select defaultValue="current">
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">Current Sprint</SelectItem>
              <SelectItem value="previous">Previous Sprint</SelectItem>
              <SelectItem value="q1">Q1 2024</SelectItem>
              <SelectItem value="q2">Q2 2024</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="summary">Executive Summary</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="milestones">Milestones</TabsTrigger>
          <TabsTrigger value="actions">Action Items</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-6">
          {/* Executive Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle>Executive Summary</CardTitle>
              <CardDescription>
                Status as of{" "}
                {new Date().toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="prose prose-sm max-w-none">
                <p>
                  The project is currently <strong>on track</strong> with 85% of
                  planned deliverables completed for this quarter. Key
                  accomplishments include the successful implementation of the
                  design system and completion of core API endpoints.
                </p>
                <p>
                  Several challenges have emerged related to resource allocation
                  for backend development, which has been escalated to
                  leadership. Despite these challenges, the team is maintaining
                  momentum and quality metrics remain strong.
                </p>
              </div>

              {/* Project KPIs */}
              <div>
                <h3 className="text-sm font-medium mb-3">Project KPIs</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-500">
                          85%
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Completion Rate
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-amber-500">
                          92%
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Quality Score
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-500">
                          +4%
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Ahead of Schedule
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Performance Charts */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Timeline Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <LineChart data={projectPerformance}>
                        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                        <XAxis dataKey="month" />
                        <YAxis domain={[0, 100]} />
                        <Tooltip />
                        <Line
                          type="monotone"
                          dataKey="actual"
                          name="Actual Progress (%)"
                          stroke="#3b82f6"
                          strokeWidth={2}
                        />
                        <Line
                          type="monotone"
                          dataKey="forecast"
                          name="Forecast (%)"
                          stroke="#d1d5db"
                          strokeWidth={2}
                          strokeDasharray="5 5"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Team Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <BarChart
                        data={teamPerformance}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                        <XAxis type="number" domain={[0, 100]} />
                        <YAxis dataKey="name" type="category" width={80} />
                        <Tooltip
                          formatter={(value) => [
                            `${value}%`,
                            "Performance Score",
                          ]}
                        />
                        <Bar dataKey="value" radius={[0, 4, 4, 0]}>
                          {teamPerformance.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Risk Assessment */}
              <div>
                <h3 className="text-sm font-medium mb-3">Risk Assessment</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-amber-50 dark:bg-amber-950/30 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-amber-700 dark:text-amber-400">
                          Resource Allocation
                        </h4>
                        <p className="text-sm text-amber-700/80 dark:text-amber-400/80 mt-1">
                          Backend development team is currently understaffed by
                          2 developers.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-red-50 dark:bg-red-950/30 p-4 rounded-lg border border-red-200 dark:border-red-800">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-700 dark:text-red-400">
                          Testing Timeline
                        </h4>
                        <p className="text-sm text-red-700/80 dark:text-red-400/80 mt-1">
                          UAT may be delayed due to stakeholder availability in
                          mid-May.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-6 flex justify-between">
              <div className="text-sm text-muted-foreground">
                Report generated:{" "}
                {new Date().toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </div>
              <Button variant="outline" size="sm">
                Export Report <Download className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Project Documents</CardTitle>
                <CardDescription>
                  Access and manage project documentation
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" /> Filter
                </Button>
                <Button>
                  <FileText className="mr-2 h-4 w-4" /> Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Author</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{doc.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{doc.category}</TableCell>
                      <TableCell>{doc.date}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            doc.status === "final" ? "default" : "secondary"
                          }
                        >
                          {doc.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{doc.author}</TableCell>
                      <TableCell>{doc.size}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="icon">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="border-t pt-6 flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {documents.length} documents
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="milestones" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Milestones</CardTitle>
              <CardDescription>Key milestones and target dates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {milestones.map((milestone) => (
                  <div
                    key={milestone.id}
                    className="relative pl-8 pb-8 border-l border-muted last:pb-0"
                  >
                    <div className="absolute left-0 top-0 transform -translate-x-1/2 flex h-6 w-6 items-center justify-center rounded-full">
                      {milestone.status === "completed" && (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      )}
                      {milestone.status === "on-track" && (
                        <Clock className="h-6 w-6 text-blue-500" />
                      )}
                      {milestone.status === "at-risk" && (
                        <AlertTriangle className="h-6 w-6 text-amber-500" />
                      )}
                      {milestone.status === "planned" && (
                        <Calendar className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    <div className="flex flex-col md:flex-row md:justify-between gap-2">
                      <div>
                        <h4 className="font-medium">{milestone.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {milestone.description}
                        </p>
                      </div>
                      <div className="flex flex-col md:items-end gap-1 min-w-[150px]">
                        <div className="text-sm">{milestone.date}</div>
                        <Badge
                          variant={
                            milestone.status === "completed"
                              ? "default"
                              : milestone.status === "on-track"
                              ? "outline"
                              : milestone.status === "at-risk"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {milestone.status}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {milestone.owner}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actions" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Action Items</CardTitle>
                <CardDescription>
                  Tasks requiring immediate attention
                </CardDescription>
              </div>
              <Button>
                <ClipboardList className="mr-2 h-4 w-4" /> New Action Item
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {actionItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="font-medium">{item.task}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback>
                              {item.owner
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span>{item.owner}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.dueDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            item.priority === "high"
                              ? "destructive"
                              : item.priority === "medium"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {item.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {item.status === "completed" && (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                          {item.status === "in-progress" && (
                            <Clock className="h-4 w-4 text-blue-500" />
                          )}
                          {item.status === "not-started" && (
                            <Clock className="h-4 w-4 text-gray-400" />
                          )}
                          {item.status}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          Update
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Resource Allocation</CardTitle>
              <CardDescription>
                Team resource allocation and needs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6">
                {resources.map((resource, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between">
                      <div className="font-medium">{resource.role}</div>
                      <div className="text-sm text-muted-foreground">
                        {resource.allocated}/{resource.required} allocated
                      </div>
                    </div>
                    <div className="relative pt-1">
                      <div className="flex mb-2 items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <Progress
                            value={
                              (resource.allocated / resource.required) * 100
                            }
                            className="w-[200px]"
                          />
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-semibold inline-block">
                            {Math.round(
                              (resource.allocated / resource.required) * 100
                            )}
                            %
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Card className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
                <CardContent className="p-4 flex gap-3 items-start">
                  <ArrowUpRight className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-700 dark:text-blue-400">
                      Resource Recommendation
                    </h4>
                    <p className="text-sm text-blue-700/80 dark:text-blue-400/80 mt-1">
                      Based on current project velocity and upcoming milestones,
                      we recommend adding 2 additional Backend Developers to the
                      team.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
