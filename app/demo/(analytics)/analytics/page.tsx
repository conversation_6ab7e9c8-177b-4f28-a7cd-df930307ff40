"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>D<PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Activity,
  AlertTriangle,
  BarChart2,
  Calendar,
  CheckCircle,
  Code,
  Flag,
  Milestone,
  TrendingUp,
  Users,
} from "lucide-react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
// Project performance data
const performanceData = [
  { name: "Week 1", planned: 15, completed: 12, backlog: 8 },
  { name: "Week 2", planned: 18, completed: 16, backlog: 10 },
  { name: "Week 3", planned: 20, completed: 17, backlog: 13 },
  { name: "Week 4", planned: 25, completed: 20, backlog: 18 },
  { name: "Week 5", planned: 22, completed: 21, backlog: 14 },
  { name: "Week 6", planned: 30, completed: 28, backlog: 12 },
];

// Project status distribution
const projectStatusData = [
  { name: "On Track", value: 65, color: "#10b981" },
  { name: "At Risk", value: 25, color: "#f59e0b" },
  { name: "Behind", value: 10, color: "#ef4444" },
];

// Team workload data
const teamWorkloadData = [
  { name: "Design", complete: 85, remaining: 15 },
  { name: "Development", complete: 65, remaining: 35 },
  { name: "Testing", complete: 40, remaining: 60 },
  { name: "Documentation", complete: 30, remaining: 70 },
];

// Recent activities data
const recentActivities = [
  {
    activity: "Task completed",
    details: "User authentication flow",
    user: "Alex Kim",
    time: "10 min ago",
    type: "task",
  },
  {
    activity: "Sprint started",
    details: "Sprint 24 - Feature expansion",
    user: "System",
    time: "1 hour ago",
    type: "sprint",
  },
  {
    activity: "Bug reported",
    details: "Dashboard loading issue on Safari",
    user: "Jamie Chen",
    time: "2 hours ago",
    type: "bug",
  },
  {
    activity: "Pull request merged",
    details: "Feature/user-onboarding",
    user: "Taylor Moore",
    time: "3 hours ago",
    type: "code",
  },
];

// Add these new data constants for the performance tab
const velocityTrend = [
  { sprint: "Sprint 20", planned: 30, actual: 28 },
  { sprint: "Sprint 21", planned: 32, actual: 29 },
  { sprint: "Sprint 22", planned: 34, actual: 32 },
  { sprint: "Sprint 23", planned: 33, actual: 31 },
  { sprint: "Sprint 24", planned: 35, actual: 32 },
  { sprint: "Sprint 25", planned: 38, actual: 36 },
];

const codeQualityMetrics = [
  { date: "Jan", coverage: 78, complexity: 24, duplication: 5.2 },
  { date: "Feb", coverage: 80, complexity: 22, duplication: 4.8 },
  { date: "Mar", coverage: 83, complexity: 21, duplication: 4.5 },
  { date: "Apr", coverage: 85, complexity: 18, duplication: 3.9 },
  { date: "May", coverage: 88, complexity: 16, duplication: 3.2 },
  { date: "Jun", coverage: 90, complexity: 15, duplication: 2.8 },
];

const bugTrend = [
  { sprint: "Sprint 20", created: 14, resolved: 12, carryOver: 2 },
  { sprint: "Sprint 21", created: 11, resolved: 10, carryOver: 3 },
  { sprint: "Sprint 22", created: 9, resolved: 11, carryOver: 1 },
  { sprint: "Sprint 23", created: 12, resolved: 10, carryOver: 3 },
  { sprint: "Sprint 24", created: 8, resolved: 9, carryOver: 2 },
  { sprint: "Sprint 25", created: 7, resolved: 8, carryOver: 1 },
];

const sprintGoals = [
  {
    sprint: "Current Sprint",
    goals: [
      { name: "Authentication Flow", status: "completed", progress: 100 },
      { name: "User Dashboard", status: "in-progress", progress: 75 },
      { name: "API Integration", status: "in-progress", progress: 60 },
      { name: "Performance Optimization", status: "not-started", progress: 0 },
    ],
  },
];

// Add these new data constants for the Team Activity tab
const teamMembers = [
  {
    id: 1,
    name: "Alex Kim",
    role: "Frontend Developer",
    avatar: "/avatars/alex.png",
    commits: 78,
    prs: 15,
    reviews: 23,
    tasks: { completed: 12, inProgress: 3, blocked: 0 },
  },
  {
    id: 2,
    name: "Jamie Chen",
    role: "Backend Developer",
    avatar: "/avatars/jamie.png",
    commits: 56,
    prs: 8,
    reviews: 31,
    tasks: { completed: 10, inProgress: 2, blocked: 1 },
  },
  {
    id: 3,
    name: "Taylor Moore",
    role: "Full Stack Developer",
    avatar: "/avatars/taylor.png",
    commits: 92,
    prs: 12,
    reviews: 18,
    tasks: { completed: 14, inProgress: 4, blocked: 0 },
  },
  {
    id: 4,
    name: "Morgan Lee",
    role: "DevOps Engineer",
    avatar: "/avatars/morgan.png",
    commits: 32,
    prs: 5,
    reviews: 27,
    tasks: { completed: 8, inProgress: 1, blocked: 0 },
  },
  {
    id: 5,
    name: "Robin Wang",
    role: "QA Engineer",
    avatar: "/avatars/robin.png",
    commits: 28,
    prs: 4,
    reviews: 42,
    tasks: { completed: 11, inProgress: 2, blocked: 0 },
  },
];

const codeContributions = [
  { name: "Alex Kim", additions: 4200, deletions: 1800, files: 45 },
  { name: "Jamie Chen", additions: 3600, deletions: 2100, files: 32 },
  { name: "Taylor Moore", additions: 5100, deletions: 2700, files: 53 },
  { name: "Morgan Lee", additions: 1800, deletions: 1200, files: 21 },
  { name: "Robin Wang", additions: 1200, deletions: 900, files: 18 },
];

const collaborationMatrix = [
  {
    person: "Alex",
    reviews: [
      { reviewer: "Jamie", count: 8 },
      { reviewer: "Taylor", count: 5 },
      { reviewer: "Morgan", count: 2 },
      { reviewer: "Robin", count: 0 },
    ],
  },
  {
    person: "Jamie",
    reviews: [
      { reviewer: "Alex", count: 6 },
      { reviewer: "Taylor", count: 3 },
      { reviewer: "Morgan", count: 4 },
      { reviewer: "Robin", count: 5 },
    ],
  },
  {
    person: "Taylor",
    reviews: [
      { reviewer: "Alex", count: 7 },
      { reviewer: "Jamie", count: 6 },
      { reviewer: "Morgan", count: 2 },
      { reviewer: "Robin", count: 3 },
    ],
  },
  {
    person: "Morgan",
    reviews: [
      { reviewer: "Alex", count: 1 },
      { reviewer: "Jamie", count: 4 },
      { reviewer: "Taylor", count: 0 },
      { reviewer: "Robin", count: 5 },
    ],
  },
  {
    person: "Robin",
    reviews: [
      { reviewer: "Alex", count: 2 },
      { reviewer: "Jamie", count: 3 },
      { reviewer: "Taylor", count: 3 },
      { reviewer: "Morgan", count: 4 },
    ],
  },
];

const commitActivity = [
  {
    time: "9 AM",
    "Alex Kim": 3,
    "Jamie Chen": 1,
    "Taylor Moore": 2,
    "Morgan Lee": 0,
    "Robin Wang": 0,
  },
  {
    time: "10 AM",
    "Alex Kim": 5,
    "Jamie Chen": 3,
    "Taylor Moore": 4,
    "Morgan Lee": 1,
    "Robin Wang": 2,
  },
  {
    time: "11 AM",
    "Alex Kim": 7,
    "Jamie Chen": 5,
    "Taylor Moore": 3,
    "Morgan Lee": 2,
    "Robin Wang": 3,
  },
  {
    time: "12 PM",
    "Alex Kim": 2,
    "Jamie Chen": 2,
    "Taylor Moore": 1,
    "Morgan Lee": 0,
    "Robin Wang": 1,
  },
  {
    time: "1 PM",
    "Alex Kim": 1,
    "Jamie Chen": 1,
    "Taylor Moore": 0,
    "Morgan Lee": 0,
    "Robin Wang": 0,
  },
  {
    time: "2 PM",
    "Alex Kim": 6,
    "Jamie Chen": 4,
    "Taylor Moore": 8,
    "Morgan Lee": 3,
    "Robin Wang": 2,
  },
  {
    time: "3 PM",
    "Alex Kim": 9,
    "Jamie Chen": 7,
    "Taylor Moore": 10,
    "Morgan Lee": 4,
    "Robin Wang": 5,
  },
  {
    time: "4 PM",
    "Alex Kim": 8,
    "Jamie Chen": 6,
    "Taylor Moore": 11,
    "Morgan Lee": 3,
    "Robin Wang": 4,
  },
  {
    time: "5 PM",
    "Alex Kim": 6,
    "Jamie Chen": 7,
    "Taylor Moore": 8,
    "Morgan Lee": 4,
    "Robin Wang": 6,
  },
];

// Add these new data constants for the Timeline tab
const milestones = [
  {
    id: "m1",
    title: "Design System Implementation",
    startDate: "2024-03-01",
    endDate: "2024-03-15",
    status: "completed",
    owner: "Alex Kim",
    description:
      "Implement the design system with component library for consistent UI",
  },
  {
    id: "m2",
    title: "API Implementation",
    startDate: "2024-03-10",
    endDate: "2024-03-31",
    status: "completed",
    owner: "Jamie Chen",
    description: "Build out the core API endpoints for the application",
  },
  {
    id: "m3",
    title: "Auth Flow Development",
    startDate: "2024-03-20",
    endDate: "2024-04-05",
    status: "completed",
    owner: "Taylor Moore",
    description: "Implement user authentication and authorization flows",
  },
  {
    id: "m4",
    title: "User Dashboard",
    startDate: "2024-04-01",
    endDate: "2024-04-30",
    status: "in-progress",
    owner: "Alex Kim",
    description: "Build out the user dashboard with core analytics features",
  },
  {
    id: "m5",
    title: "MVP Release",
    startDate: "2024-05-01",
    endDate: "2024-05-10",
    status: "planned",
    owner: "Team",
    description: "Release the MVP version to beta users",
  },
  {
    id: "m6",
    title: "Performance Optimization",
    startDate: "2024-05-10",
    endDate: "2024-05-31",
    status: "planned",
    owner: "Morgan Lee",
    description: "Optimize application performance and responsiveness",
  },
];

const releases = [
  {
    version: "v0.1.0",
    name: "Alpha Release",
    date: "2024-03-15",
    status: "released",
    features: [
      "Initial design system implementation",
      "Core component library",
      "Basic authentication screens",
    ],
  },
  {
    version: "v0.2.0",
    name: "Beta Release",
    date: "2024-04-10",
    status: "released",
    features: [
      "Complete authentication flow",
      "User profiles",
      "Basic dashboard UI",
    ],
  },
  {
    version: "v0.3.0",
    name: "MVP Candidate",
    date: "2024-05-01",
    status: "upcoming",
    features: [
      "Complete user dashboard",
      "Data visualization",
      "API integration",
    ],
  },
  {
    version: "v1.0.0",
    name: "Production Release",
    date: "2024-06-15",
    status: "planned",
    features: [
      "Complete feature set",
      "Performance optimizations",
      "Final UI polish",
    ],
  },
];

// Gantt chart data - simplified for demonstration
const ganttData = milestones.map((milestone) => ({
  task: milestone.title,
  startDate: new Date(milestone.startDate),
  endDate: new Date(milestone.endDate),
  status: milestone.status,
  owner: milestone.owner,
  id: milestone.id,
}));

// Helper function to calculate gantt chart positions
const calculateGanttPosition = (startDate: string, endDate: string) => {
  // For simplicity, using a fixed scale where project spans 6 months (Jan-Jun 2024)
  const projectStart = new Date("2024-01-01").getTime();
  const projectEnd = new Date("2024-06-30").getTime();
  const totalDuration = projectEnd - projectStart;

  const taskStart = new Date(startDate).getTime();
  const taskEnd = new Date(endDate).getTime();

  const startPercent = ((taskStart - projectStart) / totalDuration) * 100;
  const widthPercent = ((taskEnd - taskStart) / totalDuration) * 100;

  return { startPercent, widthPercent };
};

export default function AnalyticsPage() {
  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Project Analytics</h1>
          <p className="text-muted-foreground">
            Real-time performance metrics and team progress
          </p>
        </div>
        <Alert className="md:w-[300px] w-full">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Sprint Deadline</AlertTitle>
          <AlertDescription>
            2 days remaining in current sprint
          </AlertDescription>
        </Alert>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="team">Team Activity</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Sprint Progress
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">68%</div>
                <p className="text-xs text-muted-foreground">
                  24/35 story points completed
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Team Velocity
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">32 pts</div>
                <p className="text-xs text-muted-foreground">
                  +4 pts from last sprint
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Bugs Reported
                </CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">7</div>
                <p className="text-xs text-muted-foreground">
                  -3 from last sprint
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Team Members
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">
                  2 currently active
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Project Overview Charts */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="overflow-hidden  shadow-sm">
              <CardHeader className="pb-0">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Activity className="h-5 w-5 text-blue-500" />
                  Sprint Burndown
                </CardTitle>
                <CardDescription>
                  Tracking planned vs. completed work over time
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0 pt-4">
                <div className="p-4 pb-0">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                    <div>
                      <div className="text-2xl font-bold">
                        {performanceData[performanceData.length - 1].completed}{" "}
                        pts
                      </div>
                      <div className="text-sm text-muted-foreground">
                        of {performanceData[performanceData.length - 1].planned}{" "}
                        planned
                        <span className="ml-1 text-green-500">
                          (
                          {Math.round(
                            (performanceData[performanceData.length - 1]
                              .completed /
                              performanceData[performanceData.length - 1]
                                .planned) *
                              100
                          )}
                          %)
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                        <span className="text-sm">Planned</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-green-500"></div>
                        <span className="text-sm">Completed</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-amber-400"></div>
                        <span className="text-sm">Backlog</span>
                      </div>
                    </div>
                  </div>
                </div>
                <ResponsiveContainer width="100%" height={280}>
                  <AreaChart data={performanceData}>
                    <defs>
                      <linearGradient
                        id="plannedGradient"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#818cf8"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#818cf8"
                          stopOpacity={0}
                        />
                      </linearGradient>
                      <linearGradient
                        id="completedGradient"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#4ade80"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#4ade80"
                          stopOpacity={0}
                        />
                      </linearGradient>
                      <linearGradient
                        id="backlogGradient"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#fbbf24"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#fbbf24"
                          stopOpacity={0}
                        />
                      </linearGradient>
                    </defs>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      opacity={0.15}
                      vertical={false}
                    />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                      dy={10}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                      dx={-10}
                      domain={[0, "dataMax + 5"]}
                    />
                    <Tooltip
                      contentStyle={{
                        borderRadius: "8px",
                        border: "1px solid #e2e8f0",
                        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                      }}
                      itemStyle={{ padding: "2px 0" }}
                    />
                    <Area
                      type="monotone"
                      dataKey="planned"
                      stroke="#818cf8"
                      strokeWidth={2}
                      fill="url(#plannedGradient)"
                      activeDot={{ r: 6, strokeWidth: 0 }}
                      isAnimationActive={true}
                    />
                    <Area
                      type="monotone"
                      dataKey="completed"
                      stroke="#4ade80"
                      strokeWidth={2}
                      fill="url(#completedGradient)"
                      activeDot={{ r: 6, strokeWidth: 0 }}
                      isAnimationActive={true}
                    />
                    <Area
                      type="monotone"
                      dataKey="backlog"
                      stroke="#fbbf24"
                      strokeWidth={2}
                      fill="url(#backlogGradient)"
                      activeDot={{ r: 6, strokeWidth: 0 }}
                      isAnimationActive={true}
                    />
                  </AreaChart>
                </ResponsiveContainer>
                <CardFooter className="mt-4 pt-4 border-t flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    <span className="font-medium text-foreground">Trend:</span>{" "}
                    +7% completion rate vs previous sprint
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-normal">
                      ETA: 3 days
                    </Badge>
                    <Badge variant="secondary" className="font-normal">
                      Current Velocity: 4.6 pts/day
                    </Badge>
                  </div>
                </CardFooter>
              </CardContent>
            </Card>
            <Card className="overflow-hidden shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <BarChart2 className="h-5 w-5 text-emerald-500" />
                  Project Status
                </CardTitle>
                <CardDescription>
                  Distribution of project components
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="flex flex-col items-center justify-center">
                  <div className="relative w-full max-w-[330px] h-[250px] flex items-center justify-center">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={projectStatusData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                          labelLine={false}
                          label={({ percent }) =>
                            `${(percent * 100).toFixed(0)}%`
                          }
                          isAnimationActive={true}
                        >
                          {projectStatusData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                              stroke="none"
                            />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value) => [`${value}%`, "Percentage"]}
                          contentStyle={{
                            borderRadius: "8px",
                            border: "1px solid #e2e8f0",
                            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="grid grid-cols-3 gap-8 w-full max-w-md mt-2">
                    {projectStatusData.map((item, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div
                          className="w-5 h-5 rounded-full mb-2"
                          style={{ backgroundColor: item.color }}
                        ></div>
                        <div className="text-center">
                          <div className="font-semibold text-sm">
                            {item.name}
                          </div>
                          <div
                            className="text-2xl font-bold"
                            style={{ color: item.color }}
                          >
                            {item.value}%
                          </div>
                          <div className="text-xs text-muted-foreground">
                            of total
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <CardFooter className="px-0 mt-6 pt-4 border-t">
                  <div className="w-full">
                    <div className="text-sm mb-3">
                      <span className="font-medium">Risk Analysis:</span>
                      <span className="text-amber-600 ml-1">
                        Moderate concern
                      </span>{" "}
                      for components currently at risk
                    </div>
                    <div className="flex flex-wrap gap-2 justify-center">
                      <Badge variant="outline" className="bg-red-50">
                        8 blockers to resolve
                      </Badge>
                      <Badge variant="outline" className="bg-amber-50">
                        14 components need attention
                      </Badge>
                      <Badge variant="outline" className="bg-green-50">
                        65% on track to complete on schedule
                      </Badge>
                    </div>
                  </div>
                </CardFooter>
              </CardContent>
            </Card>
          </div>

          {/* Team Workload */}
          <Card className="overflow-hidden border-0 shadow-md">
            <CardHeader className="pb-0">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="h-5 w-5 text-indigo-500" />
                Team Workload
              </CardTitle>
              <CardDescription>
                Workload distribution across teams with completion status
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="mb-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                {teamWorkloadData.map((team, index) => (
                  <div
                    key={index}
                    className="bg-card border rounded-lg p-4 shadow-sm"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">{team.name}</h3>
                      <Badge
                        variant={
                          team.complete > 70
                            ? "default"
                            : team.complete > 30
                            ? "secondary"
                            : "outline"
                        }
                      >
                        {team.complete}% Complete
                      </Badge>
                    </div>
                    <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                      <div
                        className={`h-full ${
                          team.complete > 80
                            ? "bg-green-500"
                            : team.complete > 50
                            ? "bg-blue-500"
                            : team.complete > 30
                            ? "bg-amber-500"
                            : "bg-red-400"
                        }`}
                        style={{ width: `${team.complete}%` }}
                      ></div>
                    </div>
                    <div className="mt-3 text-sm text-muted-foreground">
                      {team.complete > 0
                        ? `${Math.round(team.complete / 10)} of ${Math.round(
                            (team.complete + team.remaining) / 10
                          )} tasks complete`
                        : "Not started yet"}
                    </div>
                  </div>
                ))}
              </div>

              <ResponsiveContainer width="100%" height={320}>
                <BarChart
                  data={teamWorkloadData}
                  layout="vertical"
                  margin={{ top: 20, right: 30, left: 80, bottom: 5 }}
                >
                  <defs>
                    <linearGradient
                      id="completeGradient"
                      x1="0"
                      y1="0"
                      x2="1"
                      y2="0"
                    >
                      <stop offset="0%" stopColor="#4f46e5" stopOpacity={0.8} />
                      <stop
                        offset="100%"
                        stopColor="#8b5cf6"
                        stopOpacity={0.8}
                      />
                    </linearGradient>
                    <linearGradient
                      id="remainingGradient"
                      x1="0"
                      y1="0"
                      x2="1"
                      y2="0"
                    >
                      <stop offset="0%" stopColor="#f3f4f6" stopOpacity={0.8} />
                      <stop
                        offset="100%"
                        stopColor="#e5e7eb"
                        stopOpacity={0.8}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    opacity={0.15}
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis
                    type="number"
                    domain={[0, 100]}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    width={70}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 13, fontWeight: 500 }}
                  />
                  <Tooltip
                    formatter={(value) => [`${value}%`, "Completion"]}
                    contentStyle={{
                      borderRadius: "8px",
                      border: "1px solid #e2e8f0",
                      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    }}
                  />
                  <Legend
                    verticalAlign="top"
                    height={36}
                    iconType="circle"
                    iconSize={8}
                  />
                  <Bar
                    dataKey="complete"
                    name="Completed"
                    stackId="a"
                    fill="url(#completeGradient)"
                    radius={[4, 0, 0, 4]}
                  >
                    {teamWorkloadData.map((entry, index) => (
                      <Cell
                        key={`cell-complete-${index}`}
                        fill={
                          entry.complete > 80
                            ? "#10b981"
                            : entry.complete > 50
                            ? "#3b82f6"
                            : entry.complete > 30
                            ? "#f59e0b"
                            : "#f87171"
                        }
                      />
                    ))}
                  </Bar>
                  <Bar
                    dataKey="remaining"
                    name="Remaining"
                    stackId="a"
                    fill="url(#remainingGradient)"
                    radius={[0, 4, 4, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Activity</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentActivities.map((activity, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {activity.type === "task" && (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                          {activity.type === "sprint" && (
                            <Calendar className="h-4 w-4 text-blue-500" />
                          )}
                          {activity.type === "bug" && (
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          )}
                          {activity.type === "code" && (
                            <Code className="h-4 w-4 text-purple-500" />
                          )}
                          {activity.activity}
                        </div>
                      </TableCell>
                      <TableCell>{activity.details}</TableCell>
                      <TableCell>{activity.user}</TableCell>
                      <TableCell>{activity.time}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Sprint Velocity
                </CardTitle>
                <CardDescription>Last 6 sprints performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold flex items-baseline gap-2">
                  36{" "}
                  <span className="text-sm text-muted-foreground">
                    story points
                  </span>
                </div>
                <p className="text-xs text-green-500 mt-1">
                  ****% from previous sprint average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Sprint Delivery Rate
                </CardTitle>
                <CardDescription>Completed vs. planned stories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold flex items-baseline gap-2">
                  94.7%{" "}
                  <span className="text-sm text-muted-foreground">
                    completion
                  </span>
                </div>
                <p className="text-xs text-green-500 mt-1">
                  +2.3% from previous sprint average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Mean Time to Resolution
                </CardTitle>
                <CardDescription>Average bug fix time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold flex items-baseline gap-2">
                  2.4{" "}
                  <span className="text-sm text-muted-foreground">days</span>
                </div>
                <p className="text-xs text-green-500 mt-1">
                  -0.8 days from previous sprint
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Sprint Velocity Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Sprint Velocity Trend</CardTitle>
              <CardDescription>
                Planned vs. actual velocity over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={velocityTrend}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="sprint" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar
                    dataKey="planned"
                    name="Planned Story Points"
                    fill="#8884d8"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="actual"
                    name="Completed Story Points"
                    fill="#82ca9d"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Code Quality Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Code Quality Metrics</CardTitle>
              <CardDescription>
                Test coverage, complexity, and duplication
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart
                  data={codeQualityMetrics}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" orientation="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="coverage"
                    name="Test Coverage (%)"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="complexity"
                    name="Cyclomatic Complexity"
                    stroke="#ff8042"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="duplication"
                    name="Code Duplication (%)"
                    stroke="#ff4d4f"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Bug Trends & Sprint Goals Section */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Bug Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Bug Metrics</CardTitle>
                <CardDescription>Creation and resolution rates</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart
                    data={bugTrend}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <defs>
                      <linearGradient
                        id="colorCreated"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#ff4d4f"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#ff4d4f"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                      <linearGradient
                        id="colorResolved"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#52c41a"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#52c41a"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="sprint" />
                    <YAxis />
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="created"
                      name="Bugs Created"
                      stroke="#ff4d4f"
                      fillOpacity={1}
                      fill="url(#colorCreated)"
                    />
                    <Area
                      type="monotone"
                      dataKey="resolved"
                      name="Bugs Resolved"
                      stroke="#52c41a"
                      fillOpacity={1}
                      fill="url(#colorResolved)"
                    />
                    <Line
                      type="monotone"
                      dataKey="carryOver"
                      name="Carried Over"
                      stroke="#faad14"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Sprint Goals */}
            <Card>
              <CardHeader>
                <CardTitle>Current Sprint Goals</CardTitle>
                <CardDescription>Progress towards completion</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {sprintGoals[0].goals.map((goal, idx) => (
                    <div key={idx} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="font-medium">{goal.name}</div>
                        <Badge
                          variant={
                            goal.status === "completed"
                              ? "default"
                              : goal.status === "in-progress"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {goal.status}
                        </Badge>
                      </div>
                      <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                        <div
                          className={`h-full ${
                            goal.progress === 100
                              ? "bg-green-500"
                              : goal.progress > 50
                              ? "bg-blue-500"
                              : goal.progress > 0
                              ? "bg-amber-500"
                              : "bg-gray-300"
                          }`}
                          style={{ width: `${goal.progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-end">
                        <span className="text-xs text-muted-foreground">
                          {goal.progress}% complete
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          {/* Team Members Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {teamMembers.map((member) => (
              <Card key={member.id}>
                <CardHeader className="p-4 pb-2 text-center">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 bg-muted rounded-full mb-2 overflow-hidden flex items-center justify-center">
                      <span className="text-xl font-bold">
                        {member.name.charAt(0)}
                      </span>
                    </div>
                    <CardTitle className="text-base font-medium">
                      {member.name}
                    </CardTitle>
                    <CardDescription>{member.role}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="flex justify-around text-center text-sm">
                    <div>
                      <div className="font-medium text-lg">
                        {member.commits}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Commits
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-lg">{member.prs}</div>
                      <div className="text-xs text-muted-foreground">PRs</div>
                    </div>
                    <div>
                      <div className="font-medium text-lg">
                        {member.reviews}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Reviews
                      </div>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="text-xs text-muted-foreground mb-1">
                      Tasks
                    </div>
                    <div className="flex h-2 rounded-full overflow-hidden">
                      <div
                        className="bg-green-500"
                        style={{
                          width: `${
                            (member.tasks.completed /
                              (member.tasks.completed +
                                member.tasks.inProgress +
                                member.tasks.blocked)) *
                            100
                          }%`,
                        }}
                      />
                      <div
                        className="bg-amber-500"
                        style={{
                          width: `${
                            (member.tasks.inProgress /
                              (member.tasks.completed +
                                member.tasks.inProgress +
                                member.tasks.blocked)) *
                            100
                          }%`,
                        }}
                      />
                      <div
                        className="bg-red-500"
                        style={{
                          width: `${
                            (member.tasks.blocked /
                              (member.tasks.completed +
                                member.tasks.inProgress +
                                member.tasks.blocked)) *
                            100
                          }%`,
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs mt-1">
                      <span>{member.tasks.completed} completed</span>
                      <span>{member.tasks.inProgress} in progress</span>
                      <span>{member.tasks.blocked} blocked</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Code Contribution Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Code Contributions</CardTitle>
              <CardDescription>
                Lines added and removed per developer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={codeContributions}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  barGap={0}
                  barCategoryGap={20}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} lines`, ""]} />
                  <Legend />
                  <Bar
                    dataKey="additions"
                    name="Lines Added"
                    stackId="a"
                    fill="#4ade80"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="deletions"
                    name="Lines Deleted"
                    stackId="a"
                    fill="#f87171"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Daily Commit Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Daily Commit Activity</CardTitle>
              <CardDescription>
                Commit frequency throughout the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart
                  data={commitActivity}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="Alex Kim"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Jamie Chen"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Taylor Moore"
                    stroke="#ffc658"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Morgan Lee"
                    stroke="#ff8042"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Robin Wang"
                    stroke="#0088fe"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Collaboration Matrix */}
          <Card>
            <CardHeader>
              <CardTitle>Team Collaboration</CardTitle>
              <CardDescription>
                Code review distribution across team members
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">
                      Reviewee ↓ Reviewer →
                    </TableHead>
                    {teamMembers.map((member) => (
                      <TableHead key={member.id} className="text-center">
                        {member.name.split(" ")[0]}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {collaborationMatrix.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {row.person}
                      </TableCell>
                      {row.reviews.map((cell, cellIndex) => (
                        <TableCell key={cellIndex} className="text-center">
                          {cell.count > 0 ? (
                            <Badge
                              variant={cell.count > 5 ? "default" : "secondary"}
                            >
                              {cell.count}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          {/* Timeline Overview Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Milestone className="h-4 w-4" />
                  Current Phase
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-semibold">User Dashboard</div>
                <div className="text-sm text-muted-foreground mt-1">
                  Apr 1 - Apr 30, 2024
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>45%</span>
                  </div>
                  <div className="w-full bg-secondary h-2 rounded-full overflow-hidden mt-1">
                    <div
                      className="bg-blue-500 h-full"
                      style={{ width: "45%" }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Flag className="h-4 w-4" />
                  Next Milestone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-semibold">MVP Release</div>
                <div className="text-sm text-muted-foreground mt-1">
                  May 1 - May 10, 2024
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-sm">
                    <span>Time Remaining</span>
                    <span>15 days</span>
                  </div>
                  <div className="w-full bg-secondary h-2 rounded-full overflow-hidden mt-1">
                    <div
                      className="bg-amber-500 h-full"
                      style={{ width: "70%" }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Project Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-semibold">6 Months</div>
                <div className="text-sm text-muted-foreground mt-1">
                  Jan 2024 - Jun 2024
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-sm">
                    <span>Completed</span>
                    <span>3 milestones</span>
                  </div>
                  <div className="w-full bg-secondary h-2 rounded-full overflow-hidden mt-1">
                    <div
                      className="bg-green-500 h-full"
                      style={{ width: "50%" }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Gantt Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Project Gantt Chart</CardTitle>
              <CardDescription>
                Timeline view of project milestones and phases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <div className="min-w-[800px]">
                  {/* Gantt Header - Quarters */}
                  <div className="flex mb-2 border-b pb-2">
                    <div className="w-1/4 font-medium">Task</div>
                    <div className="w-3/4 flex">
                      <div className="flex-1 text-center font-medium">
                        Q1 2024
                      </div>
                      <div className="flex-1 text-center font-medium">
                        Q2 2024
                      </div>
                    </div>
                  </div>

                  {/* Gantt Header - Months */}
                  <div className="flex mb-4 border-b pb-2">
                    <div className="w-1/4"></div>
                    <div className="w-3/4 flex">
                      <div className="flex-1 flex">
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          Jan
                        </div>
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          Feb
                        </div>
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          Mar
                        </div>
                      </div>
                      <div className="flex-1 flex">
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          Apr
                        </div>
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          May
                        </div>
                        <div className="flex-1 text-center text-xs text-muted-foreground">
                          Jun
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Gantt Rows */}
                  <div className="space-y-2">
                    {ganttData.map((item) => {
                      const { startPercent, widthPercent } =
                        calculateGanttPosition(
                          item.startDate.toISOString(),
                          item.endDate.toISOString()
                        );
                      return (
                        <div key={item.id} className="flex items-center h-10">
                          <div className="w-1/4 font-medium truncate pr-4">
                            {item.task}
                          </div>
                          <div className="w-3/4 relative h-6">
                            <div
                              className={`absolute h-full rounded-full flex items-center justify-center text-xs font-medium ${
                                item.status === "completed"
                                  ? "bg-green-500/80"
                                  : item.status === "in-progress"
                                  ? "bg-blue-500/80"
                                  : "bg-gray-400/80"
                              }`}
                              style={{
                                left: `${startPercent}%`,
                                width: `${widthPercent}%`,
                                color: "white",
                              }}
                            >
                              {widthPercent > 10 ? item.owner : ""}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Gantt Legend */}
                  <div className="flex gap-4 justify-end mt-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-xs">Completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-xs">In Progress</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                      <span className="text-xs">Planned</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Release Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Release Plan</CardTitle>
              <CardDescription>
                Version releases and feature sets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute left-16 w-0.5 h-full bg-muted" />

                {/* Timeline Items */}
                <div className="space-y-8">
                  {releases.map((release, index) => (
                    <div key={index} className="relative pl-24">
                      {/* Timeline Dot */}
                      <div
                        className={`absolute left-14 w-4 h-4 rounded-full transform -translate-x-1/2 ${
                          release.status === "released"
                            ? "bg-green-500"
                            : release.status === "upcoming"
                            ? "bg-amber-500"
                            : "bg-gray-400"
                        }`}
                      />

                      {/* Release Date */}
                      <div className="absolute left-0 top-0 w-12 text-sm font-medium">
                        {new Date(release.date).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </div>

                      {/* Release Content */}
                      <div className="bg-card border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-bold">{release.name}</h4>
                            <div className="text-sm text-muted-foreground">
                              {release.version}
                            </div>
                          </div>
                          <Badge
                            variant={
                              release.status === "released"
                                ? "default"
                                : release.status === "upcoming"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {release.status}
                          </Badge>
                        </div>
                        <div className="mt-2">
                          <div className="text-sm font-medium mb-1">
                            Features:
                          </div>
                          <ul className="space-y-1">
                            {release.features.map((feature, featureIdx) => (
                              <li
                                key={featureIdx}
                                className="text-sm flex items-center gap-2"
                              >
                                <div className="w-1 h-1 rounded-full bg-muted-foreground"></div>
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Milestones Details Table */}
          <Card>
            <CardHeader>
              <CardTitle>Milestone Details</CardTitle>
              <CardDescription>
                Detailed view of project milestones
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Milestone</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {milestones.map((milestone) => (
                    <TableRow key={milestone.id}>
                      <TableCell>
                        <div className="font-medium">{milestone.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {milestone.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(milestone.startDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(milestone.endDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{milestone.owner}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            milestone.status === "completed"
                              ? "default"
                              : milestone.status === "in-progress"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {milestone.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
