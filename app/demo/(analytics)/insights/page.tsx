"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangle,
  ArrowRight,
  BarChart2,
  Calendar,
  CheckCircle,
  Code,
  Download,
  Filter,
  Gauge,
  HelpCircle,
  Info,
  Lightbulb,
  LineChart as LineChartIcon,
  ListChecks,
  TrendingUp,
  User,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  YAxis,
} from "recharts";
// Project Metrics Data
const projectMetrics = [
  { category: "Code Quality", score: 92, target: 90, status: "above" },
  { category: "Team Velocity", score: 85, target: 80, status: "above" },
  { category: "Test Coverage", score: 78, target: 85, status: "below" },
  { category: "PR Turnaround", score: 95, target: 90, status: "above" },
];

// Trend data with forecasts
const trendData = [
  { month: "Jan", productivity: 65, quality: 80, engagement: 75, forecast: 70 },
  { month: "Feb", productivity: 70, quality: 75, engagement: 78, forecast: 74 },
  { month: "Mar", productivity: 75, quality: 85, engagement: 82, forecast: 78 },
  { month: "Apr", productivity: 72, quality: 88, engagement: 85, forecast: 84 },
  { month: "May", productivity: 78, quality: 90, engagement: 88, forecast: 87 },
  { month: "Jun", productivity: 82, quality: 92, engagement: 90, forecast: 92 },
  {
    month: "Jul",
    productivity: null,
    quality: null,
    engagement: null,
    forecast: 95,
  },
  {
    month: "Aug",
    productivity: null,
    quality: null,
    engagement: null,
    forecast: 97,
  },
];

// Detailed risk assessment data
const riskData = [
  { subject: "Technical Debt", current: 85, previous: 90, change: -5 },
  { subject: "Resource Allocation", current: 65, previous: 50, change: 15 },
  { subject: "Timeline Risk", current: 45, previous: 60, change: -15 },
  { subject: "Budget Risk", current: 30, previous: 35, change: -5 },
  { subject: "Scope Creep", current: 70, previous: 55, change: 15 },
];

// AI insights with categories
const aiInsights = [
  {
    id: "ins1",
    title: "Productivity Trend",
    description: "15% increase in team productivity over the last quarter",
    recommendation:
      "Consider implementing flexible work hours to maintain momentum",
    impact: "high",
    confidence: 0.85,
    category: "performance",
    icon: <TrendingUp className="h-5 w-5 text-blue-500" />,
    actionable: true,
  },
  {
    id: "ins2",
    title: "Quality Metrics",
    description:
      "Code quality metrics show consistent improvement with decreasing technical debt",
    recommendation:
      "Maintain current code review practices and testing protocols",
    impact: "medium",
    confidence: 0.92,
    category: "quality",
    icon: <Code className="h-5 w-5 text-green-500" />,
    actionable: true,
  },
  {
    id: "ins3",
    title: "Resource Risk",
    description:
      "Potential resource constraints in upcoming sprint cycle may impact delivery",
    recommendation:
      "Review team capacity and consider temporary staffing for backend development",
    impact: "high",
    confidence: 0.78,
    category: "risk",
    icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
    actionable: true,
  },
  {
    id: "ins4",
    title: "Team Engagement",
    description:
      "Team morale metrics indicate positive response to recent recognition program",
    recommendation:
      "Continue recognition initiatives and collect feedback on additional improvements",
    impact: "medium",
    confidence: 0.89,
    category: "team",
    icon: <User className="h-5 w-5 text-purple-500" />,
    actionable: false,
  },
  {
    id: "ins5",
    title: "Sprint Forecast",
    description:
      "Current sprint velocity indicates likelihood of completing all planned stories",
    recommendation:
      "Consider pulling forward stories from the next sprint in the backlog",
    impact: "low",
    confidence: 0.95,
    category: "performance",
    icon: <Calendar className="h-5 w-5 text-indigo-500" />,
    actionable: true,
  },
  {
    id: "ins6",
    title: "Testing Gap",
    description: "Test coverage has decreased by 5% over the past two sprints",
    recommendation:
      "Allocate time in the next sprint specifically for test coverage improvements",
    impact: "medium",
    confidence: 0.87,
    category: "quality",
    icon: <XCircle className="h-5 w-5 text-red-500" />,
    actionable: true,
  },
];

// Anomalies detected
const anomalies = [
  {
    metric: "PR Size",
    value: "45% increase",
    impact: "Medium",
    description:
      "Pull request size has increased significantly in the last sprint",
    recommendation:
      "Review team practices for breaking down work into smaller chunks",
  },
  {
    metric: "Bug Resolution Time",
    value: "30% decrease",
    impact: "Positive",
    description: "Time to resolve bugs has decreased significantly",
    recommendation: "Continue current testing and remediation practices",
  },
  {
    metric: "Documentation Updates",
    value: "70% decrease",
    impact: "High",
    description: "Documentation commits have decreased significantly",
    recommendation: "Schedule dedicated time for documentation updates",
  },
];

// Team performance patterns
const teamPerformancePatterns = [
  { day: "Mon", commits: 42, prs: 8, reviews: 15 },
  { day: "Tue", commits: 38, prs: 12, reviews: 20 },
  { day: "Wed", commits: 35, prs: 10, reviews: 18 },
  { day: "Thu", commits: 40, prs: 15, reviews: 25 },
  { day: "Fri", commits: 45, prs: 13, reviews: 22 },
];

export default function InsightsPage() {
  const [insightFilter, setInsightFilter] = useState<string>("all");

  // Filter insights based on selected category
  const filteredInsights =
    insightFilter === "all"
      ? aiInsights
      : aiInsights.filter((insight) => insight.category === insightFilter);

  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Project Insights</h1>
          <p className="text-muted-foreground">
            AI-powered analysis and actionable recommendations
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <HoverCard>
            <HoverCardTrigger asChild>
              <Button variant="outline" size="sm">
                <Info className="h-4 w-4 mr-2" /> About Insights
              </Button>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">
                  About Project Insights
                </h4>
                <p className="text-sm text-muted-foreground">
                  Insights are AI-generated recommendations based on project
                  data, team performance metrics, and industry benchmarks.
                </p>
                <p className="text-sm text-muted-foreground">
                  Each insight includes a confidence score and actionable
                  recommendations.
                </p>
              </div>
            </HoverCardContent>
          </HoverCard>
          <Button>
            <Download className="h-4 w-4 mr-2" /> Export Insights
          </Button>
        </div>
      </div>

      <Tabs defaultValue="insights" className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="metrics">Metrics & KPIs</TabsTrigger>
          <TabsTrigger value="trends">Trends & Forecasts</TabsTrigger>
          <TabsTrigger value="risks">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-6">
          {/* Insight Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={insightFilter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setInsightFilter("all")}
            >
              All Insights
            </Button>
            <Button
              variant={insightFilter === "performance" ? "default" : "outline"}
              size="sm"
              onClick={() => setInsightFilter("performance")}
            >
              Performance
            </Button>
            <Button
              variant={insightFilter === "quality" ? "default" : "outline"}
              size="sm"
              onClick={() => setInsightFilter("quality")}
            >
              Quality
            </Button>
            <Button
              variant={insightFilter === "risk" ? "default" : "outline"}
              size="sm"
              onClick={() => setInsightFilter("risk")}
            >
              Risks
            </Button>
            <Button
              variant={insightFilter === "team" ? "default" : "outline"}
              size="sm"
              onClick={() => setInsightFilter("team")}
            >
              Team
            </Button>
          </div>

          {/* AI Recommendations */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredInsights.map((insight) => (
              <Card
                key={insight.id}
                className="relative overflow-hidden border-l-4"
                style={{
                  borderLeftColor:
                    insight.category === "performance"
                      ? "#3b82f6"
                      : insight.category === "quality"
                      ? "#10b981"
                      : insight.category === "risk"
                      ? "#f59e0b"
                      : "#8b5cf6",
                }}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {insight.icon}
                      <CardTitle className="text-base font-medium">
                        {insight.title}
                      </CardTitle>
                    </div>
                    <HoverCard>
                      <HoverCardTrigger>
                        <Badge
                          variant={
                            insight.impact === "high"
                              ? "destructive"
                              : insight.impact === "medium"
                              ? "default"
                              : "secondary"
                          }
                          className="cursor-help"
                        >
                          {insight.impact}
                        </Badge>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">
                              Confidence Score
                            </p>
                            <p className="text-sm font-medium">
                              {Math.round(insight.confidence * 100)}%
                            </p>
                          </div>
                          <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary"
                              style={{ width: `${insight.confidence * 100}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Based on historical data and industry benchmarks
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3 pb-2">
                  <p className="text-sm text-muted-foreground">
                    {insight.description}
                  </p>
                  <div className="flex items-start gap-2 text-sm">
                    <Lightbulb className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                    <p>{insight.recommendation}</p>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  {insight.actionable && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs h-8 w-full justify-between"
                    >
                      Take Action <ArrowRight className="h-3 w-3" />
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Anomaly Detection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-amber-500" />
                Anomaly Detection
              </CardTitle>
              <CardDescription>
                Unusual patterns that may require attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {anomalies.map((anomaly, index) => (
                  <div
                    key={index}
                    className="flex flex-col md:flex-row md:items-center gap-4 p-3 rounded-lg border"
                  >
                    <div className="font-medium min-w-[150px]">
                      {anomaly.metric}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          anomaly.impact === "High"
                            ? "destructive"
                            : anomaly.impact === "Medium"
                            ? "default"
                            : "outline"
                        }
                      >
                        {anomaly.value}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {anomaly.description}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 ml-auto mt-2 md:mt-0">
                      <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{anomaly.recommendation}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          {/* KPI Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {projectMetrics.map((metric) => (
              <Card key={metric.category}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {metric.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-end justify-between">
                      <div className="text-2xl font-bold">{metric.score}%</div>
                      <div className="text-sm text-muted-foreground">
                        Target: {metric.target}%
                      </div>
                    </div>
                    <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                      <div
                        className={`h-full ${
                          metric.status === "above"
                            ? "bg-green-500"
                            : "bg-amber-500"
                        }`}
                        style={{ width: `${metric.score}%` }}
                      ></div>
                    </div>
                    <div className="flex items-center justify-end gap-1 text-xs">
                      {metric.status === "above" ? (
                        <span className="text-green-500">Above Target</span>
                      ) : (
                        <span className="text-amber-500">Below Target</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Team Performance Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart2 className="h-5 w-5 text-muted-foreground" />
                Team Performance Patterns
              </CardTitle>
              <CardDescription>Weekly activity breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={teamPerformancePatterns}>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="commits" name="Code Commits" fill="#3b82f6" />
                  <Bar dataKey="prs" name="Pull Requests" fill="#10b981" />
                  <Bar dataKey="reviews" name="Code Reviews" fill="#8b5cf6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          {/* Trend Analysis with Forecast */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <LineChartIcon className="h-5 w-5 text-muted-foreground" />
                    Performance Trends
                  </CardTitle>
                  <CardDescription>
                    Historical data with AI-generated forecast
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-950/50 dark:text-blue-400">
                    Productivity
                  </Badge>
                  <Badge className="bg-green-100 text-green-700 dark:bg-green-950/50 dark:text-green-400">
                    Quality
                  </Badge>
                  <Badge className="bg-amber-100 text-amber-700 dark:bg-amber-950/50 dark:text-amber-400">
                    Engagement
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <AreaChart data={trendData}>
                  <defs>
                    <linearGradient
                      id="colorProductivity"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
                    </linearGradient>
                    <linearGradient
                      id="colorQuality"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0} />
                    </linearGradient>
                    <linearGradient
                      id="colorEngagement"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#eab308" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#eab308" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="productivity"
                    name="Productivity"
                    stroke="#3b82f6"
                    fillOpacity={1}
                    fill="url(#colorProductivity)"
                  />
                  <Area
                    type="monotone"
                    dataKey="quality"
                    name="Quality"
                    stroke="#10b981"
                    fillOpacity={1}
                    fill="url(#colorQuality)"
                  />
                  <Area
                    type="monotone"
                    dataKey="engagement"
                    name="Team Engagement"
                    stroke="#eab308"
                    fillOpacity={1}
                    fill="url(#colorEngagement)"
                  />
                  <Line
                    type="monotone"
                    dataKey="forecast"
                    name="AI Forecast"
                    stroke="#9333ea"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <div className="text-sm text-muted-foreground">
                Forecast updated: {new Date().toLocaleDateString()}
              </div>
              <Button variant="outline" size="sm">
                Adjust Forecast Parameters
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="risks" className="space-y-6">
          {/* Enhanced Risk Assessment */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                    Risk Assessment
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" /> Filter Risks
                  </Button>
                </div>
                <CardDescription>
                  Current risk levels with change indicators
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {riskData.map((risk, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="font-medium">{risk.subject}</div>
                        <Badge
                          variant={
                            risk.current > 70
                              ? "destructive"
                              : risk.current > 40
                              ? "default"
                              : "secondary"
                          }
                        >
                          {risk.current}%
                        </Badge>
                      </div>
                      <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                        <div
                          className={`h-full ${
                            risk.current > 70
                              ? "bg-red-500"
                              : risk.current > 40
                              ? "bg-amber-500"
                              : "bg-green-500"
                          }`}
                          style={{ width: `${risk.current}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Previous: {risk.previous}%</span>
                        <span
                          className={
                            risk.change > 0 ? "text-red-500" : "text-green-500"
                          }
                        >
                          {risk.change > 0
                            ? `↑ ${risk.change}%`
                            : `↓ ${Math.abs(risk.change)}%`}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5 text-muted-foreground" />
                  Risk Radar
                </CardTitle>
                <CardDescription>
                  Visualized risk assessment matrix
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                    data={riskData}
                  >
                    <PolarGrid />
                    <PolarAngleAxis dataKey="subject" />
                    <PolarRadiusAxis domain={[0, 100]} />
                    <Radar
                      name="Current Risk Level"
                      dataKey="current"
                      stroke="#ef4444"
                      fill="#ef4444"
                      fillOpacity={0.5}
                    />
                    <Radar
                      name="Previous Period"
                      dataKey="previous"
                      stroke="#9ca3af"
                      fill="#9ca3af"
                      fillOpacity={0.3}
                    />
                    <Legend />
                    <Tooltip />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Risk Mitigation Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ListChecks className="h-5 w-5 text-muted-foreground" />
                Risk Mitigation Actions
              </CardTitle>
              <CardDescription>
                Recommended steps to address identified risks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {/* Technical Debt Action */}
                <div className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="mt-0.5">
                    <Badge variant="destructive">High Priority</Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium">Address Technical Debt</p>
                    <p className="text-sm text-muted-foreground">
                      Schedule dedicated sprints for refactoring and
                      documentation. Focus on the payment processing module
                      which has the highest technical debt score.
                    </p>
                    <div className="flex items-center gap-4 pt-2">
                      <Button variant="outline" size="sm">
                        <CheckCircle className="h-4 w-4 mr-2" /> Mark as
                        Addressed
                      </Button>
                      <Button variant="link" className="p-0 h-auto text-sm">
                        View Details <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Resource Planning Action */}
                <div className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="mt-0.5">
                    <Badge>Medium Priority</Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium">Resource Planning</p>
                    <p className="text-sm text-muted-foreground">
                      Review team allocation and identify potential bottlenecks.
                      Backend team is currently understaffed which may impact
                      upcoming feature delivery.
                    </p>
                    <div className="flex items-center gap-4 pt-2">
                      <Button variant="outline" size="sm">
                        <CheckCircle className="h-4 w-4 mr-2" /> Mark as
                        Addressed
                      </Button>
                      <Button variant="link" className="p-0 h-auto text-sm">
                        View Details <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Scope Management Action */}
                <div className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="mt-0.5">
                    <Badge variant="secondary">Low Priority</Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium">Scope Management</p>
                    <p className="text-sm text-muted-foreground">
                      Implement a more structured feature prioritization process
                      to reduce scope creep. Consider using the MoSCoW method
                      for backlog refinement.
                    </p>
                    <div className="flex items-center gap-4 pt-2">
                      <Button variant="outline" size="sm">
                        <CheckCircle className="h-4 w-4 mr-2" /> Mark as
                        Addressed
                      </Button>
                      <Button variant="link" className="p-0 h-auto text-sm">
                        View Details <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
