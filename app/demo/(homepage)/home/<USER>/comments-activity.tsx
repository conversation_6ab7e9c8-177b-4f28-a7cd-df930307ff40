import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Bell, ReplyIcon } from "lucide-react";

const CommentsActivity = () => {
  return (
    <Card className="col-span-1 rounded-xl p-4 shadow-xs">
      <div className="flex flex-row items-center justify-between">
        <h3 className="text-lg font-semibold">Recent Comments</h3>
        <Button
          variant="outline"
          size="icon"
          className="text-muted-foreground hover:text-foreground"
        >
          <Bell className="h-5 w-5" />
          <span className="sr-only">Notifications</span>
        </Button>
      </div>
      <div className="relative space-y-8 before:absolute before:left-5 before:top-0 before:h-full before:w-[2px] before:bg-muted-foreground/20">
        <div className="flex items-start gap-3">
          <Avatar className="h-10 w-10 ring-4 ring-background relative z-10">
            <AvatarImage src="https://github.com/shadcn.png" alt="User" />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">John Doe</p>
                <p className="text-xs text-gray-500">2 hours ago</p>
              </div>
              <Button variant="outline" size="icon" className="w-6 h-6">
                <ReplyIcon className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Great progress on the new feature implementation! The code looks
              clean and well-organized.
            </p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Avatar className="h-10 w-10 ring-4 ring-background relative z-10">
            <AvatarImage src="https://github.com/shadcn.png" alt="User" />
            <AvatarFallback>SA</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Sarah Anderson</p>
                <p className="text-xs text-gray-500">5 hours ago</p>
              </div>
              <Button variant="outline" size="icon" className="w-6 h-6">
                <ReplyIcon className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Can we schedule a quick review of the UI components? I have some
              suggestions for improvements.
            </p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Avatar className="h-10 w-10 ring-4 ring-background relative z-10">
            <AvatarImage src="https://github.com/shadcn.png" alt="User" />
            <AvatarFallback>MK</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Mike Kim</p>
                <p className="text-xs text-gray-500">Yesterday</p>
              </div>
              <Button variant="outline" size="icon" className="w-6 h-6">
                <ReplyIcon className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Updated the documentation with the latest API changes. Please
              review when you have a chance.
            </p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Avatar className="h-10 w-10 ring-4 ring-background relative z-10">
            <AvatarImage src="https://github.com/shadcn.png" alt="User" />
            <AvatarFallback>EJ</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Emma Johnson</p>
                <p className="text-xs text-gray-500">2 days ago</p>
              </div>
              <Button variant="outline" size="icon" className="w-6 h-6">
                <ReplyIcon className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              The performance optimizations look promising. I&apos;ve noticed a
              significant improvement in load times.
            </p>
          </div>
        </div>
      </div>
      <Button variant="outline" className="w-full mt-4">
        View All Comments
      </Button>
    </Card>
  );
};

export default CommentsActivity;
