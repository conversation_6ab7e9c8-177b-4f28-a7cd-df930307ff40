import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Calendar, Mail, MapPin } from "lucide-react";
import { UserProfile as UserProfileProps } from "../_types/user.types";

const UserProfile = ({ user }: { user: UserProfileProps }) => {
  return (
    <Card className="col-span-1 p-6 flex flex-col h-full shadow-xs">
      <div className="flex flex-col gap-6 flex-1 justify-between">
        <div className="flex flex-col gap-6">
          <div className="flex items-start gap-6">
            <Avatar className="h-24 w-24 flex-shrink-0">
              <AvatarImage src={user?.avatar} alt={`${user?.name}'s Profile`} />
              <AvatarFallback>{user?.avatarFallback}</AvatarFallback>
            </Avatar>

            <div className="flex flex-col flex-1 min-w-0">
              <div className="flex flex-row justify-between items-center w-full mb-2">
                <h2 className="text-xl font-semibold truncate">{user?.name}</h2>
                <Badge variant="secondary" className="ml-0 flex-shrink-0">
                  {user?.department}
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant="default" className="ml-0 flex-shrink-0">
                  {user?.role}
                </Badge>
                <span className="text-muted-foreground">•</span>
                <Badge variant="outline" className="text-xs">
                  {user?.position}
                </Badge>
              </div>

              <div className="flex flex-col gap-2 mt-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4 flex-shrink-0" />
                  <span className="truncate">{user?.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 flex-shrink-0" />
                  <span>{user?.location}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 flex-shrink-0" />
                  <span>Joined {user?.joinedDate}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-8 border rounded-lg p-4">
          <div className="flex flex-col items-center justify-center">
            <span className="text-xl font-semibold">{user?.stats?.projects}</span>
            <span className="text-xs text-muted-foreground mt-1">Projects</span>
          </div>
          <div className="flex flex-col items-center justify-center border-x">
            <span className="text-xl font-semibold">{user?.stats?.tasksDone}</span>
            <span className="text-xs text-muted-foreground mt-1">Tasks Done</span>
          </div>
          <div className="flex flex-col items-center justify-center">
            <span className="text-xl font-semibold">{user?.stats?.pullRequests}</span>
            <span className="text-xs text-muted-foreground mt-1">Pull Requests</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default UserProfile;
