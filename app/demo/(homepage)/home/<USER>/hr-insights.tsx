import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  CalendarClock,
  CalendarDays,
  Clock,
  ExternalLink,
  Laptop,
  Receipt,
  Stethoscope,
} from "lucide-react";

const HRInsights = () => {
  return (
    <Card className="col-span-1 p-6 shadow-xs">
      <div className="flex flex-col justify-between h-full">
        <div>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">My HR Profile</h3>
            <Button variant="ghost" size="sm">
              <span className="text-xs">Open HR Platform</span>
              <ExternalLink className="w-2 h-2" />
            </Button>
          </div>
        </div>
        <ViewOptions />

        <div className="grid grid-cols-3 gap-8 p-4 border rounded-lg">
          <div className="flex flex-col items-center justify-center">
            <span className="text-xl font-semibold">12</span>
            <span className="mt-1 text-xs text-muted-foreground">
              Annual Leave Left
            </span>
          </div>
          <div className="flex flex-col items-center justify-center border-x">
            <span className="text-xl font-semibold">3</span>
            <span className="mt-1 text-xs text-muted-foreground">
              Sick Days Used
            </span>
          </div>
          <div className="flex flex-col items-center justify-center">
            <span className="text-xl font-semibold">7</span>
            <span className="mt-1 text-xs text-muted-foreground">
              Overtime (hours)
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export function ViewOptions() {
  return (
    <div className="grid w-full grid-cols-3 gap-1">
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <CalendarDays className="w-4 h-4 mb-1" />
        <span className="text-xs">Leave Request</span>
      </Button>
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <Stethoscope className="w-4 h-4 mb-1" />
        <span className="text-xs">Sick Day</span>
      </Button>
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <Receipt className="w-4 h-4 mb-1" />
        <span className="text-xs">Reimbursement</span>
      </Button>
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <Clock className="w-4 h-4 mb-1" />
        <span className="text-xs">Overtime</span>
      </Button>
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <Laptop className="w-4 h-4 mb-1" />
        <span className="text-xs">Equipment Request</span>
      </Button>
      <Button
        variant="outline"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/5"
      >
        <CalendarClock className="w-4 h-4 mb-1" />
        <span className="text-xs">Schedule Meeting</span>
      </Button>
    </div>
  );
}

export default HRInsights;
