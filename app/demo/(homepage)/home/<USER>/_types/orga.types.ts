export interface Team {
  name: string;
  memberCount: number;
  projectCount: number;
}

export interface Role {
  name: string;
  description: string;
}

export interface Member {
  name: string;
  email: string;
  role: string;
}

export interface Project {
  name: string;
  team: string;
  status: "In Progress" | "Planning" | "Completed";
}

export interface OrganizationStats {
  totalMembers: number;
  activeProjects: number;
  teamCount: number;
}
