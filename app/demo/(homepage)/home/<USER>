import BannerCountdown from "@/components/origin-ui/banner-countdown";
import { Suspense } from "react";
import CommentsActivity from "./_components/comments-activity";
import HRInsights from "./_components/hr-insights";
import { CurrentProjects } from "./_components/project-list";
import { UserAgenda } from "./_components/user-agenda";
import UserProfile from "./_components/user-profile";
import { mockdata as projectsMockData } from "./_data/myproject-mock";
import { userProfileMock } from "./_data/user-profile-mock";
export default async function HomePage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-screen">
          <div className="w-10 h-10 border-t-2 border-b-2 border-gray-900 rounded-full animate-spin"></div>
        </div>
      }
    >
      <div className="flex flex-col h-screen p-4">
        <div className=" z-20 top-4 space-y-4">
          <BannerCountdown />
        </div>
        <div className="grid h-full gap-4 auto-rows-min md:grid-cols-3">
          <UserInfoSection user={userProfileMock} />
          <ActivitySection />
        </div>
      </div>
    </Suspense>
  );
}

const UserInfoSection = (props: React.ComponentProps<typeof UserProfile>) => (
  <div className="grid grid-cols-1 col-span-3 gap-4 md:grid-cols-2 lg:grid-cols-3">
    <UserProfile {...props} />
    <HRInsights />
    <UserAgenda />
  </div>
);

const ActivitySection = () => (
  <>
    <CommentsActivity />
    <CurrentProjects projects={projectsMockData} />
  </>
);
