import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Clock, Plus, Users } from "lucide-react";

type Meeting = {
  id: number;
  title: string;
  time: string;
  attendees: number;
  room: string;
};

const TODAYS_MEETINGS: Meeting[] = [
  {
    id: 1,
    title: "Team Standup",
    time: "09:00 AM",
    attendees: 5,
    room: "Room Alpha",
  },
  {
    id: 2,
    title: "Project Review",
    time: "11:30 AM",
    attendees: 3,
    room: "Room Beta",
  },
  {
    id: 3,
    title: "Client Meeting",
    time: "02:00 PM",
    attendees: 4,
    room: "Room Gamma",
  },
];

function MeetingCard({ meeting }: { meeting: Meeting }) {
  return (
    <div className="p-2 sm:p-1.5 rounded-md border bg-muted/20 text-xs sm:text-[10px]">
      <div className="flex flex-row items-center justify-between gap-1 sm:gap-0.5">
        <div className="font-medium truncate">{meeting.title}</div>
        <div className="flex items-center gap-1 sm:gap-0.5">
          <Users className="h-3 w-3 sm:h-2.5 sm:w-2.5" />
          {meeting.attendees}
        </div>
      </div>
      <div className="flex items-center justify-between gap-2 sm:gap-1.5 mt-1 sm:mt-0.5 text-muted-foreground">
        <div className="flex items-center gap-1 sm:gap-0.5">
          <Clock className="h-3 w-3 sm:h-2.5 sm:w-2.5" />
          {meeting.time}
        </div>
        <div>{meeting.room}</div>
      </div>
    </div>
  );
}

function AgendaSection() {
  return (
    <div className="flex flex-col w-full px-2 py-4 md:py-2">
      <div className="flex flex-row items-center justify-between w-full">
        <h3 className="text-xl md:text-lg font-semibold">Agenda</h3>
        <Button variant="outline" size="sm">
          <Plus className="h-3 w-3 md:h-2 md:w-2 mr-1 md:mr-0.5" />
          <span className="text-sm md:text-xs">New Meeting</span>
        </Button>
      </div>
      <div className="mt-4 md:mt-auto space-y-2 md:space-y-1">
        <h4 className="text-sm md:text-xs font-medium mb-2 md:mb-1">Today</h4>
        {TODAYS_MEETINGS.map((meeting) => (
          <MeetingCard key={meeting.id} meeting={meeting} />
        ))}
      </div>
    </div>
  );
}

function CalendarSection() {
  return (
    <div className="w-full md:w-1/2 flex items-center justify-center py-4 md:py-0">
      <Calendar
        mode="single"
        selected={new Date()}
        className="rounded-md border shadow"
        classNames={{
          months: "w-full",
          month: "w-full",
          table: "w-full",
          head_cell: "text-[0.6rem] font-medium",
          cell: "text-[0.7rem] p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
          day: "h-6 w-6 p-0 aria-selected:opacity-100",
          nav_button: "h-6 w-6",
          nav_button_previous: "absolute left-1",
          nav_button_next: "absolute right-1",
          caption: "relative h-6 items-center",
        }}
      />
    </div>
  );
}

export function UserAgenda() {
  return (
    <Card className="px-4 w-full md:min-w-[550px] shadow-xs">
      <div className="flex flex-col md:flex-row justify-between gap-4 h-full">
        <AgendaSection />
        <CalendarSection />
      </div>
    </Card>
  );
}
