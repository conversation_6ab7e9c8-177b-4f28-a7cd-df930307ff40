"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import {
  Building2,
  FolderKanban,
  Receipt,
  Settings,
  Shield,
  UserPlus,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";

const sidebarItems = [
  {
    label: "Overview",
    icon: Building2,
    section: "overview",
  },
  {
    label: "Teams",
    icon: Users,
    section: "teams",
  },
  {
    label: "Roles & Permissions",
    icon: Shield,
    section: "roles",
  },
  {
    label: "Members",
    icon: UserPlus,
    section: "members",
  },
  {
    label: "Projects",
    icon: FolderKanban,
    section: "projects",
  },
  {
    label: "Billing",
    icon: Receipt,
    section: "billing",
  },
  {
    label: "Settings",
    icon: Settings,
    section: "settings",
  },
] as const;

export function OrganizationSidebar() {
  const [activeSection, setActiveSection] = useState("overview");

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      {
        rootMargin: "0% 0px -50% 0px",
      }
    );

    const sections = sidebarItems.map((item) =>
      document.getElementById(item.section)
    );

    sections.forEach((section) => {
      if (section) observer.observe(section);
    });

    return () =>
      sections.forEach((section) => {
        if (section) observer.unobserve(section);
      });
  }, []);

  return (
    <ScrollArea className="h-full">
      <div className="pr-6">
        {sidebarItems.map((item) => (
          <Button
            key={item.label}
            variant="ghost"
            className={cn(
              "w-full justify-start gap-2 py-2 mb-0.5 font-medium text-sm",
              activeSection === item.section
                ? "bg-accent text-accent-foreground"
                : "text-muted-foreground hover:text-foreground"
            )}
            onClick={() => {
              const element = document.getElementById(item.section);
              if (element) {
                element.scrollIntoView({ behavior: "smooth" });
              }
            }}
          >
            <item.icon size={16} />
            {item.label}
          </Button>
        ))}
      </div>
    </ScrollArea>
  );
}
