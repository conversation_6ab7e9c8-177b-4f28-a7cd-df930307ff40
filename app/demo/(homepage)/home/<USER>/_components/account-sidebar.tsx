"use client";

import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { accountSidebarItems } from "../data/account-sidebar-data";

export function AccountSettingsSidebar() {
  const [activeSection, setActiveSection] = useState("profile");

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      {
        rootMargin: "0% 0px -50% 0px",
      }
    );

    const sections = accountSidebarItems.map((item) =>
      document.getElementById(item.section)
    );
    sections.push(document.getElementById("delete-account"));

    sections.forEach((section) => {
      if (section) observer.observe(section);
    });

    return () =>
      sections.forEach((section) => {
        if (section) observer.unobserve(section);
      });
  }, []);

  return (
    <ScrollArea className="h-full">
      <div className="pr-6">
        {accountSidebarItems.map((item) => (
          <Button
            key={item.label}
            variant="ghost"
            className={cn(
              "w-full justify-start gap-2 py-2 mb-0.5 font-medium text-sm",
              activeSection === item.section
                ? "bg-accent text-accent-foreground"
                : "text-muted-foreground hover:text-foreground"
            )}
            onClick={() => {
              const element = document.getElementById(item.section);
              if (element) {
                element.scrollIntoView({ behavior: "smooth" });
              }
            }}
          >
            <item.icon size={16} />
            {item.label}
          </Button>
        ))}

        <div className="pt-3 mt-3 border-t">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start gap-2 py-2 text-sm text-red-500 hover:text-red-600 hover:bg-red-50",
              activeSection === "delete-account" && "bg-red-50"
            )}
            onClick={() => {
              const element = document.getElementById("delete-account");
              if (element) {
                element.scrollIntoView({ behavior: "smooth" });
              }
            }}
          >
            <Trash2 size={16} />
            Delete Account
          </Button>
        </div>
      </div>
    </ScrollArea>
  );
}
