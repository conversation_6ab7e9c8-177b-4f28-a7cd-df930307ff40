import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Briefcase, MapPin, PencilIcon } from "lucide-react";

type ProfileCardProps = {
  firstName: string;
  lastName: string;
  jobTitle: string;
  location: {
    city: string;
    country: string;
  };
};

export function ProfileCard({
  firstName,
  lastName,
  jobTitle,
  location,
}: ProfileCardProps) {
  return (
    <Card id="profile" className="p-4 shadow-none">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">My Profile</h3>
        <Button variant="outline" size="sm" className="gap-1.5">
          <PencilIcon size={16} />
          Edit
        </Button>
      </div>

      <div className="flex flex-row items-center justify-between gap-6 pr-8">
        <div className="col-span-2">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-xl">
                {`${firstName[0]}${lastName[0]}`}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-lg font-semibold">{`${firstName} ${lastName}`}</h3>
              <p className="text-gray-500 text-sm flex items-center gap-1">
                <Briefcase size={14} /> {jobTitle}
              </p>
              <p className="text-gray-500 text-sm flex items-center gap-1">
                <MapPin size={14} /> {`${location.city}, ${location.country}`}
              </p>
            </div>
          </div>
        </div>

        <div>
          <p className="text-sm text-gray-500 mb-1">Department</p>
          <p className="font-medium">Product Development</p>
        </div>
        <div>
          <p className="text-sm text-gray-500 mb-1">Work Hours</p>
          <p className="font-medium">9:00 AM - 6:00 PM</p>
        </div>
        <div>
          <p className="text-sm text-gray-500 mb-1">Employee ID</p>
          <p className="font-medium">1234567890</p>
        </div>
      </div>
    </Card>
  );
}
