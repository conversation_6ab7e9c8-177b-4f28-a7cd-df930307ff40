"use client";

import { Card } from "@/components/ui/card";
import { OrganizationStats } from "../../_types/orga.types";

export function OverviewSection({ stats }: { stats: OrganizationStats }) {
  return (
    <Card id="overview" className="p-6">
      <h2 className="text-xl font-semibold mb-4">Organization Overview</h2>
      <div className="grid grid-cols-3 gap-4">
        <StatCard label="Total Members" value={stats.totalMembers} />
        <StatCard label="Active Projects" value={stats.activeProjects} />
        <StatCard label="Teams" value={stats.teamCount} />
      </div>
    </Card>
  );
}

function StatCard({ label, value }: { label: string; value: number }) {
  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-medium mb-1">{label}</h3>
      <p className="text-2xl font-bold">{value}</p>
    </div>
  );
}
