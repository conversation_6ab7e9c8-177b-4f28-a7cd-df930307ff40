"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Team } from "../../_types/orga.types";
import { SectionHeader } from "../ui/section-header";

export function TeamsSection({ teams }: { teams: Team[] }) {
  return (
    <Card id="teams" className="p-6">
      <SectionHeader
        title="Teams"
        actionLabel="Add Team"
        onAction={() => console.log("Add team clicked")}
      />
      <div className="space-y-4">
        {teams.map((team) => (
          <div
            key={team.name}
            className="p-4 border rounded-lg flex justify-between items-center"
          >
            <div>
              <h3 className="font-medium">{team.name}</h3>
              <p className="text-sm text-muted-foreground">
                {team.memberCount} members · {team.projectCount} projects
              </p>
            </div>
            <Button variant="outline" size="sm">
              Manage
            </Button>
          </div>
        ))}
      </div>
    </Card>
  );
}
