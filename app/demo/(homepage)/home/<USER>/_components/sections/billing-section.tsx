"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export function BillingSection() {
  return (
    <Card id="billing" className="p-6">
      <h2 className="text-xl font-semibold mb-4">Billing</h2>
      <div className="space-y-4">
        <div className="p-4 border rounded-lg">
          <h3 className="font-medium mb-2">Current Plan</h3>
          <p className="text-2xl font-bold mb-1">Professional</p>
          <p className="text-sm text-muted-foreground">
            $29/month · Up to 50 members
          </p>
          <Button className="mt-4" variant="outline">
            Upgrade Plan
          </Button>
        </div>
        <div className="p-4 border rounded-lg">
          <h3 className="font-medium mb-2">Payment Method</h3>
          <p className="text-sm">Visa ending in 4242</p>
          <Button className="mt-4" variant="outline" size="sm">
            Update
          </Button>
        </div>
      </div>
    </Card>
  );
}
