"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export function SettingsSection() {
  return (
    <Card id="settings" className="p-6">
      <h2 className="text-xl font-semibold mb-4">Organization Settings</h2>
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-medium">Organization Name</h3>
            <p className="text-sm text-muted-foreground">Acme Corp</p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Time Zone</h3>
            <p className="text-sm text-muted-foreground">UTC+8</p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Domain</h3>
            <p className="text-sm text-muted-foreground">acme.com</p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Security</h3>
            <p className="text-sm text-muted-foreground">
              2FA Required for all members
            </p>
          </div>
        </div>
        <Button variant="outline">Edit Settings</Button>
      </div>
    </Card>
  );
}
