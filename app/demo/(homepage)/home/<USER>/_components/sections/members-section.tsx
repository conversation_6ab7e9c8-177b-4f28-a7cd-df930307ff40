"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Member } from "../../_types/orga.types";
import { SectionHeader } from "../ui/section-header";

export function MembersSection({ members }: { members: Member[] }) {
  return (
    <Card id="members" className="p-6">
      <SectionHeader
        title="Members"
        actionLabel="Invite Member"
        onAction={() => console.log("Invite member clicked")}
      />
      <div className="space-y-4">
        {members.map((member) => (
          <div
            key={member.email}
            className="p-4 border rounded-lg flex justify-between items-center"
          >
            <div>
              <h3 className="font-medium">{member.name}</h3>
              <p className="text-sm text-muted-foreground">
                {member.email} · {member.role}
              </p>
            </div>
            <Button variant="outline" size="sm">
              Manage
            </Button>
          </div>
        ))}
      </div>
    </Card>
  );
}
