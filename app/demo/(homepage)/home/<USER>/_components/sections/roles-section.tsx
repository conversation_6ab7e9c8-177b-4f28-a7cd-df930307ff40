"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Role } from "../../_types/orga.types";
import { SectionHeader } from "../ui/section-header";

export function RolesSection({ roles }: { roles: Role[] }) {
  return (
    <Card id="roles" className="p-6">
      <SectionHeader
        title="Roles & Permissions"
        actionLabel="Create Role"
        onAction={() => console.log("Create role clicked")}
      />
      <div className="space-y-4">
        {roles.map((role) => (
          <div
            key={role.name}
            className="p-4 border rounded-lg flex justify-between items-center"
          >
            <div>
              <h3 className="font-medium">{role.name}</h3>
              <p className="text-sm text-muted-foreground">
                {role.description}
              </p>
            </div>
            <Button variant="outline" size="sm">
              Edit
            </Button>
          </div>
        ))}
      </div>
    </Card>
  );
}
