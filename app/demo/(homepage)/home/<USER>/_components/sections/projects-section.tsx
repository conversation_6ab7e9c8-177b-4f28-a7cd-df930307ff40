"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Project } from "../../_types/orga.types";
import { SectionHeader } from "../ui/section-header";

export function ProjectsSection({ projects }: { projects: Project[] }) {
  return (
    <Card id="projects" className="p-6">
      <SectionHeader
        title="Projects"
        actionLabel="New Project"
        onAction={() => console.log("New project clicked")}
      />
      <div className="space-y-4">
        {projects.map((project) => (
          <div
            key={project.name}
            className="p-4 border rounded-lg flex justify-between items-center"
          >
            <div>
              <h3 className="font-medium">{project.name}</h3>
              <p className="text-sm text-muted-foreground">
                {project.team} · {project.status}
              </p>
            </div>
            <Button variant="outline" size="sm">
              View
            </Button>
          </div>
        ))}
      </div>
    </Card>
  );
}
