"use client";

import { ArrowDownRight, ArrowUpRight } from "lucide-react";

interface ProjectAnalyticsProps {
  title: string;
  value: string;
  change: number;
  isPositive: boolean;
  icon: React.ReactNode;
}

export function ProjectAnalytics({
  title,
  value,
  change,
  isPositive,
  icon,
}: ProjectAnalyticsProps) {
  return (
    <div className="border rounded-lg p-3 flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-muted-foreground">{title}</span>
        <div className="bg-muted rounded-full p-1">{icon}</div>
      </div>
      <div className="flex items-end justify-between">
        <span className="text-lg font-semibold">{value}</span>
        <div
          className={`flex items-center text-xs ${
            isPositive ? "text-green-500" : "text-red-500"
          }`}
        >
          {isPositive ? (
            <ArrowUpRight className="h-3 w-3 mr-1" />
          ) : (
            <ArrowDownRight className="h-3 w-3 mr-1" />
          )}
          {change}%
        </div>
      </div>
    </div>
  );
}
