import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

type SettingField = {
  label: string;
  value: string | React.ReactNode;
};

type SettingsCardProps = {
  id?: string;
  title: string;
  fields: SettingField[][];
  actionIcon?: LucideIcon;
  actionLabel?: string;
  actionVariant?: "outline" | "destructive";
  onAction?: () => void;
};

export function SettingsCard({
  id,
  title,
  fields,
  actionIcon: ActionIcon,
  actionLabel = "Edit",
  actionVariant = "outline",
  onAction,
}: SettingsCardProps) {
  return (
    <Card id={id} className="p-4 shadow-none">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">{title}</h3>
        <Button
          variant={actionVariant}
          size="sm"
          className="gap-1.5"
          onClick={onAction}
        >
          {ActionIcon && <ActionIcon size={16} />}
          {actionLabel}
        </Button>
      </div>
      <div className="grid grid-cols-2 gap-y-6 gap-x-10">
        {fields.map((row, rowIndex) =>
          row.map((field, colIndex) => (
            <div
              key={`${rowIndex}-${colIndex}`}
              className={field.label === "Important Notice" ? "col-span-2" : ""}
            >
              <p className="text-sm text-gray-500 mb-1">{field.label}</p>
              <p
                className={`font-medium ${
                  field.label === "Important Notice"
                    ? "text-red-600"
                    : field.label === "Password Strength" &&
                      field.value === "Medium"
                    ? "text-yellow-600"
                    : ""
                }`}
              >
                {field.value}
              </p>
            </div>
          ))
        )}
      </div>
    </Card>
  );
}
