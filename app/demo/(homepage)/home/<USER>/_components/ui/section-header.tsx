import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface SectionHeaderProps {
  title: string;
  actionLabel: string;
  onAction: () => void;
}

export function SectionHeader({
  title,
  actionLabel,
  onAction,
}: SectionHeaderProps) {
  return (
    <div className="flex justify-between items-center mb-4">
      <h2 className="text-xl font-semibold">{title}</h2>
      <Button onClick={onAction}>
        <PlusCircle className="h-4 w-4 mr-2" />
        {actionLabel}
      </Button>
    </div>
  );
}
