"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CircleDot,
  ClockFading,
  LayoutDashboard,
  Settings,
  BarChart3,
  Users,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import Link from "next/link";
import { ProjectAnalytics } from "./project-analytics";
import { AvatarGroup } from "./avatar-group";

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    deadline: Date;
    role: string;
    progress: {
      todo: number;
      inProgress: number;
      done: number;
      blocked: number;
    };
    analytics: {
      commitRate: { value: number; change: number; isPositive: boolean };
      issueResolution: { value: number; change: number; isPositive: boolean };
      deploymentFrequency: {
        value: number;
        change: number;
        isPositive: boolean;
      };
      timelineAdherence: { value: number; change: number; isPositive: boolean };
    };
    teams: Array<{
      id: string;
      name: string;
      members: Array<{
        id: string;
        name: string;
        avatar: string;
      }>;
    }>;
  };
}

export function ProjectCard({ project }: ProjectCardProps) {
  const totalTasks =
    project.progress.todo +
    project.progress.inProgress +
    project.progress.done +
    project.progress.blocked;

  const progressPercentage = Math.round(
    (project.progress.done / totalTasks) * 100
  );

  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row justify-between items-center pb-2">
        <div>
          <h2 className="text-2xl font-semibold">{project.name}</h2>
          <div className="flex items-center gap-2 mt-1">
            <ClockFading className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Due by{" "}
              {project.deadline.toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
            <Badge variant="secondary" className="ml-2">
              {project.role}
            </Badge>
          </div>
        </div>
        <div className="flex gap-2">
          {/* <Link href={`/dashboard/${project.id}`}> */}
          <Link href={`/dashboard`}>
            <Button variant="outline" size="sm">
              <LayoutDashboard className="mr-2 h-4 w-4" />
              Dashboard
            </Button>
          </Link>
          {project.role === "Admin" && (
            // <Link href={`/admin/projects/${project.id}`}>
            <Link href={`/home`}>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Manage
              </Button>
            </Link>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Progress Overview</h3>

            <div className="flex flex-wrap gap-3">
              <Badge variant="outline" className="py-2">
                <CircleDot className="mr-1 h-4 w-4 text-gray-500" />
                {project.progress.todo} Not Started
              </Badge>
              <Badge variant="outline" className="py-2">
                <CircleDot className="mr-1 h-4 w-4 text-blue-500" />
                {project.progress.inProgress} In Progress
              </Badge>
              <Badge variant="outline" className="py-2">
                <CircleDot className="mr-1 h-4 w-4 text-green-500" />
                {project.progress.done} Completed
              </Badge>
              <Badge variant="outline" className="py-2">
                <CircleDot className="mr-1 h-4 w-4 text-red-500" />
                {project.progress.blocked} Blocked
              </Badge>
            </div>

            <div className="space-y-1.5">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Project Progress</span>
                <span className="font-medium">{progressPercentage}%</span>
              </div>
              <div className="h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Project Analytics</h3>
            <div className="grid grid-cols-2 gap-4">
              <ProjectAnalytics
                title="Commit Rate"
                value={`${project.analytics.commitRate.value}/week`}
                change={project.analytics.commitRate.change}
                isPositive={project.analytics.commitRate.isPositive}
                icon={<BarChart3 className="h-4 w-4" />}
              />
              <ProjectAnalytics
                title="Issue Resolution"
                value={`${project.analytics.issueResolution.value}%`}
                change={project.analytics.issueResolution.change}
                isPositive={project.analytics.issueResolution.isPositive}
                icon={<TrendingUp className="h-4 w-4" />}
              />
              <ProjectAnalytics
                title="Deployment Frequency"
                value={`${project.analytics.deploymentFrequency.value}/week`}
                change={project.analytics.deploymentFrequency.change}
                isPositive={project.analytics.deploymentFrequency.isPositive}
                icon={<ArrowUpRight className="h-4 w-4" />}
              />
              <ProjectAnalytics
                title="Timeline Adherence"
                value={`${project.analytics.timelineAdherence.value}%`}
                change={project.analytics.timelineAdherence.change}
                isPositive={project.analytics.timelineAdherence.isPositive}
                icon={<ArrowDownRight className="h-4 w-4" />}
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Teams</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {project.teams.map((team) => (
              <Card key={team.id} className="p-4 border border-muted">
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <h4 className="font-medium">{team.name}</h4>
                  </div>
                  <Badge variant="outline">{team.members.length} members</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <AvatarGroup>
                    {team.members.map((member) => (
                      <Avatar key={member.id} title={member.name}>
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>
                          {member.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <Button variant="ghost" size="sm">
                    View Team
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
