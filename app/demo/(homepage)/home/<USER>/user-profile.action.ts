import { getUserMetadataFromDb } from "@/db/user.db";
import { UserProfile } from "../_types/user.types";

export async function getUserProfileAction() {
  const userMetadata = await getUserMetadataFromDb();

  const user: UserProfile = {
    name: userMetadata?.display_name,
    email: userMetadata?.email,
    joinedDate: new Date(userMetadata?.created_at).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
    location: "Taiwan",
    avatar: "https://github.com/shadcn.png",
    avatarFallback: "A",
    department: "Engineering",
    role: "member",
    position: "Developer",
    stats: {
      projects: 3,
      tasksDone: 10,
      pullRequests: 2,
    },
  };

  return user;
}
