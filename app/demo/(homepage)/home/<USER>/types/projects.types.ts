export interface Project {
  id: string;
  name: string;
  deadline: Date;
  role: string;
  progress: {
    todo: number;
    inProgress: number;
    done: number;
    blocked: number;
  };
  analytics: {
    commitRate: AnalyticMetric;
    issueResolution: AnalyticMetric;
    deploymentFrequency: AnalyticMetric;
    timelineAdherence: AnalyticMetric;
  };
  teams: Team[];
}

export interface AnalyticMetric {
  value: number;
  change: number;
  isPositive: boolean;
}

export interface Team {
  id: string;
  name: string;
  members: TeamMember[];
}

export interface TeamMember {
  id: string;
  name: string;
  avatar: string;
}
