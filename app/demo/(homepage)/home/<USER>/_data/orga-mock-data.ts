import { Member, OrganizationStats, Project, Role, Team } from "../_types/orga.types";

export const MOCK_TEAMS: Team[] = [
  { name: "Product", memberCount: 8, projectCount: 3 },
  { name: "Engineering", memberCount: 12, projectCount: 5 },
  { name: "Design", memberCount: 6, projectCount: 4 },
  { name: "Marketing", memberCount: 4, projectCount: 2 },
  { name: "Sales", memberCount: 8, projectCount: 1 },
];

export const MOCK_ROLES: Role[] = [
  { name: "Admin", description: "Full access to all resources" },
  { name: "Team Lead", description: "Limited access to specific resources" },
  { name: "Developer", description: "Limited access to specific resources" },
  { name: "Designer", description: "Limited access to specific resources" },
  { name: "Guest", description: "Limited access to specific resources" },
];

export const MOCK_MEMBERS: Member[] = [
  { name: "<PERSON>", email: "<EMAIL>", role: "admin" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Team Lead" },
];

export const MOCK_PROJECTS: Project[] = [
  { name: "Website Redesign", team: "Design", status: "In Progress" },
  { name: "Mobile App", team: "Engineering", status: "Planning" },
];

export const MOCK_STATS: OrganizationStats = {
  totalMembers: 24,
  activeProjects: 8,
  teamCount: 5,
};
