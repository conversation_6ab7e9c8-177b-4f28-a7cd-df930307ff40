import { Project } from "../_types/myprojects.types";

export const mockdata: Project[] = [
  {
    name: "Dashboard Redesign",
    slug: "DBR",
    role: "admin",
    progress: { todo: 2, inProgress: 1, done: 8, blocked: 0 },
    deadline: new Date("2024-03-15"),
  },
  {
    name: "API Integration",
    slug: "API",
    role: "Developer",
    progress: { todo: 5, inProgress: 6, done: 3, blocked: 3 },
    deadline: new Date("2024-04-01"),
  },
  {
    name: "Mobile UI Overhaul",
    slug: "<PERSON><PERSON>",
    role: "Designer",
    progress: { todo: 2, inProgress: 3, done: 1, blocked: 1 },
    deadline: new Date("2024-04-30"),
  },
  {
    name: "Performance Testing",
    slug: "PFT",
    role: "QA",
    progress: { todo: 6, inProgress: 1, done: 4, blocked: 4 },
    deadline: new Date("2024-05-15"),
  },
  {
    name: "User Management System",
    slug: "UMS",
    role: "Product Owner",
    progress: { todo: 4, inProgress: 2, done: 5, blocked: 1 },
    deadline: new Date("2024-06-10"),
  },
  {
    name: "Authentication Module",
    slug: "AUT",
    role: "Security Engineer",
    progress: { todo: 3, inProgress: 4, done: 7, blocked: 2 },
    deadline: new Date("2024-03-25"),
  },
  {
    name: "Analytics Dashboard",
    slug: "ATD",
    role: "Data Scientist",
    progress: { todo: 7, inProgress: 5, done: 2, blocked: 0 },
    deadline: new Date("2024-05-05"),
  },
  {
    name: "Payment Gateway",
    slug: "PGY",
    role: "Backend Developer",
    progress: { todo: 1, inProgress: 8, done: 3, blocked: 2 },
    deadline: new Date("2024-04-12"),
  },
];
