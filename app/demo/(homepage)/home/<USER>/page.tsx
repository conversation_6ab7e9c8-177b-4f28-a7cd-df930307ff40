import { But<PERSON> } from "@/components/ui/button";
import { Suspense } from "react";
import { ProjectCard } from "./_components/project-card";
import { MOCK_PROJECTS } from "./data/project-mock-data";
import type { Project } from "./types/projects.types";

export default async function ProjectsAdminPage() {
  return (
    <div className="container h-full py-6 mx-auto space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Current Projects</h1>
        <div className="flex gap-2">
          <Button variant="outline">See archived projects</Button>
          <Button>Create New Project</Button>
        </div>
      </div>

      <Suspense
        fallback={
          <div className="space-y-4">
            <div className="h-[200px] bg-muted animate-pulse rounded-lg" />
            <div className="h-[200px] bg-muted animate-pulse rounded-lg" />
          </div>
        }
      >
        <ProjectList projects={MOCK_PROJECTS} />
      </Suspense>
    </div>
  );
}

function ProjectList({ projects }: { projects: Project[] }) {
  return (
    <div className="grid grid-cols-1 gap-8">
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}
