import { DownloadIcon, PencilIcon, Trash2 } from "lucide-react";
import { AccountSettingsSidebar } from "./_components/account-sidebar";
import { ProfileCard } from "./_components/profile-card";
import { SettingsCard } from "./_components/settings-card";
import { mockProfileData } from "./data/mock-profile";

export default function AccountSettingsPage() {
  return (
    <div className="container h-full py-6 mx-auto">
      <div className="relative flex gap-8">
        {/* Sidebar */}
        <aside className="sticky self-start w-1/4 top-6">
          <h1 className="mb-8 text-3xl font-bold">Account Settings</h1>
          <AccountSettingsSidebar />
        </aside>

        {/* Main Content */}
        <div className="w-3/4 space-y-4">
          <ProfileCard
            firstName={mockProfileData.firstName}
            lastName={mockProfileData.lastName}
            jobTitle={mockProfileData.jobTitle}
            location={mockProfileData.location}
          />

          <SettingsCard
            title="Personal Information"
            fields={[
              [
                { label: "First Name", value: mockProfileData.firstName },
                { label: "Last Name", value: mockProfileData.lastName },
              ],
              [
                { label: "Email address", value: mockProfileData.email },
                { label: "Phone", value: mockProfileData.phone },
              ],
              [{ label: "Bio", value: mockProfileData.bio }],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            title="Address"
            fields={[
              [
                { label: "Country", value: mockProfileData.address.country },
                {
                  label: "City/State",
                  value: mockProfileData.address.cityState,
                },
              ],
              [
                {
                  label: "Postal Code",
                  value: mockProfileData.address.postalCode,
                },
                { label: "TAX ID", value: mockProfileData.address.taxId },
              ],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            id="security"
            title="Security"
            fields={[
              [
                { label: "Password", value: "********" },
                { label: "2FA Status", value: "Not Enabled" },
              ],
              [
                { label: "Last Password Change", value: "3 months ago" },
                { label: "Password Strength", value: "Medium" },
              ],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            id="teams"
            title="Teams"
            fields={[
              [
                { label: "Current Team", value: "Product Team" },
                { label: "Role", value: "Team Lead" },
              ],
              [
                { label: "Team Members", value: "12 members" },
                { label: "Team Projects", value: "4 active projects" },
              ],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            id="notifications"
            title="Notifications"
            fields={[
              [
                { label: "Email Notifications", value: "Enabled" },
                { label: "Push Notifications", value: "Disabled" },
              ],
              [
                { label: "Marketing Emails", value: "Opted Out" },
                { label: "Weekly Digest", value: "Enabled" },
              ],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            id="billing"
            title="Billing"
            fields={[
              [
                { label: "Current Plan", value: "Professional" },
                { label: "Billing Period", value: "Monthly" },
              ],
              [
                { label: "Next Payment", value: "July 1, 2024" },
                { label: "Payment Method", value: "Visa ending in 4242" },
              ],
            ]}
            actionIcon={PencilIcon}
          />

          <SettingsCard
            id="data-export"
            title="Data Export"
            fields={[
              [
                { label: "Last Export", value: "Never" },
                { label: "Export Format", value: "JSON" },
              ],
              [
                { label: "Data Size", value: "2.3 MB" },
                { label: "Available Data", value: "All user data" },
              ],
            ]}
            actionIcon={DownloadIcon}
            actionLabel="Export"
          />

          <SettingsCard
            id="delete-account"
            title="Delete Account"
            fields={[
              [
                { label: "Account Status", value: "Active" },
                { label: "Created On", value: "June 1, 2023" },
              ],
              [
                {
                  label: "Important Notice",
                  value:
                    "Once you delete your account, all of your data will be permanently removed. This action cannot be undone.",
                },
              ],
            ]}
            actionIcon={Trash2}
            actionLabel="Delete"
            actionVariant="destructive"
          />
        </div>
      </div>
    </div>
  );
}
