import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Link from "next/link";

const helpItems = [
  {
    id: "getting-started",
    title: "Getting Started",
    items: [
      {
        id: "organization",
        title: "Creating Your Organization",
        content:
          "Learn how to set up your organization, invite team members, and configure basic settings.",
      },
      {
        id: "projects",
        title: "Managing Projects",
        content:
          "Discover how to create and organize projects, assign team members, and track progress.",
      },
    ],
  },
  {
    id: "common-questions",
    title: "Common Questions",
    items: [
      {
        id: "cluster",
        title: "What is a Cluster?",
        content:
          "A cluster is a group of projects that are managed by the platform. It is used to work at scale.",
      },
      {
        id: "billing",
        title: "Billing & Subscriptions",
        content: "Find answers about payment methods, plan upgrades, and billing cycles.",
      },
      {
        id: "account",
        title: "Account Settings",
        content:
          "Learn how to manage your profile, security settings, and notification preferences.",
      },
      {
        id: "team",
        title: "Team Management",
        content: "Understand how to manage team roles, permissions, and access controls.",
      },
    ],
  },
];

export default function HelpPage() {
  return (
    <div className="h-full container mx-auto py-8 px-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Help Center</h1>

      <div className="grid gap-8">
        {helpItems.map((section) => (
          <section key={section.id}>
            <h2 className="text-2xl font-semibold mb-4">{section.title}</h2>
            <Accordion type="single" collapsible className="w-full space-y-2">
              {section.items.map((item) => (
                <AccordionItem
                  value={item.id}
                  key={item.id}
                  className="bg-background has-focus-visible:border-ring has-focus-visible:ring-ring/50 rounded-md border px-4 py-1 outline-none last:border-b has-focus-visible:ring-[3px]"
                >
                  <AccordionTrigger className="py-2 text-[15px] leading-6 hover:no-underline focus-visible:ring-0">
                    {item.title}
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground pb-2">
                    {item.content}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </section>
        ))}

        <section>
          <h2 className="text-2xl font-semibold mb-4">Need More Help?</h2>
          <div className="flex gap-4">
            <Link
              href="mailto:<EMAIL>"
              className="flex-1 p-6 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <h3 className="font-medium mb-2">Contact Support</h3>
              <p className="text-muted-foreground">
                Get in touch with our support team for personalized assistance.
              </p>
            </Link>
            <Link
              href="/docs"
              className="flex-1 p-6 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <h3 className="font-medium mb-2">Documentation</h3>
              <p className="text-muted-foreground">
                Browse our detailed documentation for in-depth guides and tutorials.
              </p>
            </Link>
          </div>
        </section>
      </div>
    </div>
  );
}
