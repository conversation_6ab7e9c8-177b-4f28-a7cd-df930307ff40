import { But<PERSON> } from "@/components/ui/button";
import { Copy } from "lucide-react";
import { OrganizationSidebar } from "./_components/org-sidebar";
import { BillingSection } from "./_components/sections/billing-section";
import { MembersSection } from "./_components/sections/members-section";
import { OverviewSection } from "./_components/sections/overview-section";
import { ProjectsSection } from "./_components/sections/projects-section";
import { RolesSection } from "./_components/sections/roles-section";
import { SettingsSection } from "./_components/sections/settings-section";
import { TeamsSection } from "./_components/sections/teams-section";
import {
  MOCK_MEMBERS,
  MOCK_PROJECTS,
  MOCK_ROLES,
  MOCK_STATS,
  MOCK_TEAMS,
} from "./_data/orga-mock-data";

export default function OrganizationPage() {
  const organizationIdMock = "org_123456789"; // TODO: get from url

  return (
    <div className="container h-full py-6 mx-auto">
      <div className="relative flex gap-8">
        <div className="w-1/4">
          <div className="sticky top-6">
            <h1 className="mb-4 text-3xl font-bold">Organization Settings</h1>
            <Button variant="outline" size="sm" className="self-start">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {organizationIdMock}
                <Copy className="w-3 h-3" />
              </div>
            </Button>
            <aside className="mt-8 w-full sticky top-48 max-h-[calc(100vh-12rem)] overflow-auto">
              <OrganizationSidebar />
            </aside>
          </div>
        </div>

        <main className="w-3/4 space-y-6">
          <OverviewSection stats={MOCK_STATS} />
          <TeamsSection teams={MOCK_TEAMS} />
          <RolesSection roles={MOCK_ROLES} />
          <MembersSection members={MOCK_MEMBERS} />
          <ProjectsSection projects={MOCK_PROJECTS} />
          <BillingSection />
          <SettingsSection />
        </main>
      </div>
    </div>
  );
}
