"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import {
  ArrowRightIcon,
  ChevronDown,
  ChevronUp,
  Dot,
  GitPullRequestIcon,
  Link2,
  ListTodoIcon,
  PlusIcon,
  Settings,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

type ProjectProgress = {
  todo: number;
  inProgress: number;
  done: number;
  blocked: number;
};

type Project = {
  name: string;
  slug: string;
  role: string;
  progress: ProjectProgress;
  deadline: Date;
};

type CurrentProjectsProps = {
  projects: Project[];
};

// Mock data for tasks and PRs
const TASK_ITEMS = [
  {
    type: "Documentation",
    description: "File structure documentation",
    status: "Not started",
    bgColor: "bg-gray-50 dark:bg-gray-900",
  },
  {
    type: "Feature",
    description: "Implement authentication flow",
    status: "In progress",
    bgColor: "bg-blue-50 dark:bg-blue-900",
  },
  {
    type: "Bug",
    description: "Fix API integration issues",
    status: "Blocked",
    bgColor: "bg-red-50 dark:bg-red-900",
  },
];

const PR_ITEMS = [
  {
    type: "Approved",
    description: "Feature: Add user dashboard",
    status: "Ready for review",
    bgColor: "bg-green-50 dark:bg-green-900",
  },
  {
    type: "Pending",
    description: "Fix: Resolve navigation issues",
    status: "Changes requested",
    bgColor: "bg-yellow-50 dark:bg-yellow-900",
  },
];

export function CurrentProjects({ projects }: CurrentProjectsProps) {
  return (
    <Card className="w-full col-span-2 gap-4 p-4 shadow-xs">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Your Projects</h3>
        <Button variant="outline" size="sm">
          <PlusIcon size={16} aria-hidden="true" />
          <span className="text-xs">New Project</span>
        </Button>
      </div>

      {/* Project List */}
      <div className="flex flex-col gap-4 overflow-y-auto h-[45vh]">
        {projects.map((project, index) => (
          <CurrentProjectCard
            key={`${project.name}-${index}`}
            index={index}
            name={project.name}
            slug={project.slug}
            role={project.role}
            progress={project.progress}
            deadline={project.deadline}
          />
        ))}
      </div>
    </Card>
  );
}

type ProjectCardProps = {
  index: number;
  name: string;
  slug: string;
  role: string;
  progress: ProjectProgress;
  deadline: Date;
  className?: string;
};

function CurrentProjectCard({
  index,
  name,
  slug,
  role,
  deadline,
  className,
}: ProjectCardProps) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (index === 0) {
      setIsOpen(true);
    }
  }, [index]);

  return (
    <Collapsible
      open={isOpen}
      className={cn("w-full cursor-pointer", className)}
    >
      <Card className="gap-4 py-0">
        <CollapsibleTrigger asChild onClick={() => setIsOpen(!isOpen)}>
          <CardHeader
            id="project-header"
            className="flex flex-row items-center justify-between px-4 py-4"
          >
            <div className="flex flex-row items-center gap-2">
              {isOpen ? (
                <ChevronUp className="flex-shrink-0 w-4 h-4" />
              ) : (
                <ChevronDown className="flex-shrink-0 w-4 h-4" />
              )}
              <ProjectHeader
                name={name}
                slug={slug}
                role={role}
                deadline={deadline}
              />
            </div>
            <div onClick={(e) => e.stopPropagation()}>
              <ProjectActions role={role} />
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent className="CollapsibleContent">
          <CardContent>
            <div className="flex flex-row justify-between gap-4 pb-4">
              <TasksList />
              <PullRequestsList />
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
}

type ProjectHeaderProps = {
  name: string;
  slug: string;
  role: string;
  deadline: Date;
};

function ProjectHeader({ name, slug, role, deadline }: ProjectHeaderProps) {
  return (
    <div className="flex flex-row items-center gap-4">
      <h3 className="font-semibold text-md">{name}</h3>
      <div className="flex flex-row items-center gap-1">
        <Link2 className="w-3 h-3 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">{slug}</span>
        <Dot className="w-3 h-3 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">
          {deadline?.toDateString()}
        </span>
        <Dot className="w-3 h-3 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">Sprint 7</span>
      </div>
      <Badge variant="secondary" className="w-fit">
        {role}
      </Badge>
    </div>
  );
}

type ProjectActionsProps = {
  role: string;
};

function ProjectActions({ role }: ProjectActionsProps) {
  return (
    <div className="flex flex-shrink-0 gap-2">
      <Link href="/demo/project-settings">
        {role === "Admin" && (
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-1" />
            Administration Settings
          </Button>
        )}
      </Link>
      <Link href="/demo/dashboard">
        <Button variant="default" size="sm">
          Go to Project
          <ArrowRightIcon className="w-4 h-4 ml-1" />
        </Button>
      </Link>
    </div>
  );
}

function TasksList() {
  return (
    <div id="project-tasks" className="w-full space-y-2">
      <h4 className="flex items-center gap-1 text-sm font-medium">
        <ListTodoIcon className="w-4 h-4" />
        Assigned Tasks
      </h4>
      {TASK_ITEMS.length === 0 && (
        <p className="text-sm text-muted-foreground">No tasks assigned</p>
      )}

      {TASK_ITEMS?.map((task, index) => (
        <WorkItem
          key={`task-${index}`}
          type={task.type}
          description={task.description}
          status={task.status}
          bgColor={task.bgColor}
        />
      ))}
    </div>
  );
}

function PullRequestsList() {
  return (
    <div id="project-prs" className="w-full space-y-2">
      <h4 className="flex items-center gap-1 text-sm font-medium">
        <GitPullRequestIcon className="w-4 h-4" />
        Assigned Pull Requests
      </h4>
      {PR_ITEMS.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No pull requests assigned
        </p>
      )}
      {PR_ITEMS.map((pr, index) => (
        <WorkItem
          key={`pr-${index}`}
          type={pr.type}
          description={pr.description}
          status={pr.status}
          bgColor={pr.bgColor}
        />
      ))}
    </div>
  );
}

type WorkItemProps = {
  type: string;
  description: string;
  status: string;
  bgColor: string;
};

function WorkItem({ type, description, status, bgColor }: WorkItemProps) {
  return (
    <div className="p-2 border rounded-md">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={bgColor}>
            {type}
          </Badge>
          <span className="text-xs">{description}</span>
        </div>
        <Badge variant="outline" className="text-xs">
          {status}
        </Badge>
      </div>
    </div>
  );
}
