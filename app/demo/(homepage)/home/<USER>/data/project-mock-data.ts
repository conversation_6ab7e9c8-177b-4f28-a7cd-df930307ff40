export const MOCK_PROJECTS = [
  {
    id: "proj-1",
    name: "Cloud Migration",
    role: "admin",
    progress: {
      todo: 5,
      inProgress: 8,
      done: 12,
      blocked: 2,
    },
    deadline: new Date("2024-08-15"),
    teams: [
      {
        id: "team-1",
        name: "Backend Team",
        members: [
          { id: "user-1", name: "<PERSON>", avatar: "/avatars/alex.png" },
          { id: "user-2", name: "<PERSON>", avatar: "/avatars/sarah.png" },
          { id: "user-3", name: "<PERSON>", avatar: "/avatars/david.png" },
        ],
      },
      {
        id: "team-2",
        name: "Frontend Team",
        members: [
          { id: "user-4", name: "<PERSON>", avatar: "/avatars/emma.png" },
          { id: "user-5", name: "<PERSON>", avatar: "/avatars/james.png" },
        ],
      },
    ],
    analytics: {
      commitRate: { value: 28, change: 12, isPositive: true },
      issueResolution: { value: 85, change: 5, isPositive: true },
      deploymentFrequency: { value: 4, change: 1, isPositive: false },
      timelineAdherence: { value: 92, change: 3, isPositive: true },
    },
  },
  {
    id: "proj-2",
    name: "Data Platform",
    role: "contributor",
    progress: {
      todo: 7,
      inProgress: 4,
      done: 9,
      blocked: 1,
    },
    deadline: new Date("2024-09-30"),
    teams: [
      {
        id: "team-3",
        name: "Data Engineering",
        members: [
          { id: "user-6", name: "Michael Lee", avatar: "/avatars/michael.png" },
          { id: "user-7", name: "Lisa Wang", avatar: "/avatars/lisa.png" },
        ],
      },
      {
        id: "team-4",
        name: "ML Team",
        members: [
          { id: "user-8", name: "Robert Kim", avatar: "/avatars/robert.png" },
          { id: "user-9", name: "Olivia Davis", avatar: "/avatars/olivia.png" },
          {
            id: "user-10",
            name: "Thomas White",
            avatar: "/avatars/thomas.png",
          },
        ],
      },
    ],
    analytics: {
      commitRate: { value: 22, change: 8, isPositive: true },
      issueResolution: { value: 78, change: 2, isPositive: false },
      deploymentFrequency: { value: 3, change: 1, isPositive: true },
      timelineAdherence: { value: 88, change: 4, isPositive: true },
    },
  },
  {
    id: "proj-3",
    name: "Security Audit",
    role: "reviewer",
    progress: {
      todo: 3,
      inProgress: 6,
      done: 15,
      blocked: 0,
    },
    deadline: new Date("2024-07-20"),
    teams: [
      {
        id: "team-5",
        name: "Security Team",
        members: [
          {
            id: "user-11",
            name: "Jennifer Park",
            avatar: "/avatars/jennifer.png",
          },
          {
            id: "user-12",
            name: "Daniel Smith",
            avatar: "/avatars/daniel.png",
          },
        ],
      },
    ],
    analytics: {
      commitRate: { value: 18, change: 3, isPositive: false },
      issueResolution: { value: 92, change: 7, isPositive: true },
      deploymentFrequency: { value: 2, change: 0, isPositive: true },
      timelineAdherence: { value: 95, change: 2, isPositive: true },
    },
  },
];
