"use client";

import { AppSidebar } from "@/components/demo-navbar/app-sidebar";
import AppTopbar from "@/components/demo-navbar/app-topbar";
import GlobalNotificationBell from "@/components/origin-ui/global-notification-bell";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import React from "react";

function capitalize(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

const Layout = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();
  const url = capitalize(pathname?.split("/").pop() || "");

  if (pathname?.includes("/demo/home")) {
    return (
      <div>
        <AppTopbar />
        <main className="flex-1 h-[calc(100vh-4rem)] overflow-y-auto">{children}</main>
      </div>
    );
  }
  return (
    <>
      {/* TODO: if SignedOut -> Landing Page */}

      {/* TODO: if SignedIn -> Dashboard */}
      <SidebarProvider>
        <AppSidebar />
        <main className="flex flex-col w-full h-screen">
          <SidebarInset>
            <header className="flex h-16 shrink-0 items-center transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 pl-4 pr-6">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <SidebarTrigger />
                  <Separator
                    orientation="vertical"
                    className="data-[orientation=vertical]:h-4 mr-2"
                  />
                  <InsetBreadcrumb url={url} />
                </div>
                <GlobalNotificationBell side="bottom" align="end" />
              </div>
            </header>
          </SidebarInset>
          <div className="flex-1 h-[calc(100vh-4rem)]">{children}</div>
        </main>
      </SidebarProvider>
    </>
  );
};

export default Layout;

interface SheetBreadcrumbProps {
  url: string | undefined;
}

function InsetBreadcrumb({ url }: SheetBreadcrumbProps) {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="#">Organization</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="#">Project</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>{url}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
}
