import React from "react";
import { Button } from "@/components/ui/button";
import { FilterIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { GoalType, FilterOption } from "../_types/ideas.types";
import { goalLabels } from "../_data/goal-labels";

type FilterPopoverProps = {
  filterOptions: FilterOption;
  toggleGoalFilter: (goal: GoalType) => void;
  togglePriorityLevelFilter: (level: "high" | "medium" | "low") => void;
  toggleUserImpactLevelFilter: (level: "high" | "medium" | "low") => void;
  clearAllFilters: () => void;
  hasActiveFilters: () => boolean;
};

export function FilterPopover({
  filterOptions,
  toggleGoalFilter,
  togglePriorityLevelFilter,
  toggleUserImpact<PERSON>evelFilter,
  clearAllFilters,
  hasActiveFilters,
}: FilterPopoverProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className={hasActiveFilters() ? "border-primary" : ""}
        >
          <FilterIcon className="h-4 w-4" />
          Filter
          <Badge className="h-5 w-5 rounded-full bg-primary dark:bg-primary/80 text-primary-foreground">
            {(filterOptions.goal?.length || 0) +
              (filterOptions.priorityLevel?.length || 0) +
              (filterOptions.userImpactLevel?.length || 0)}
          </Badge>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="end">
        <div className="flex flex-row gap-6 p-4">
          <div>
            <h5 className="text-sm font-medium mb-2">Goals</h5>
            <div className="space-y-2">
              {Object.entries(goalLabels).map(([key, { text, icon }]) => (
                <div key={key} className="flex items-center">
                  <Checkbox
                    id={`goal-${key}`}
                    checked={filterOptions.goal?.includes(key as GoalType)}
                    onCheckedChange={() => toggleGoalFilter(key as GoalType)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label
                    htmlFor={`goal-${key}`}
                    className="ml-2 flex items-center"
                  >
                    <span className="mr-1">{icon}</span>
                    {text}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h5 className="text-sm font-medium mb-2">Priority Level</h5>
            <div className="space-y-2">
              <div className="flex items-center">
                <Checkbox
                  id="priority-high"
                  checked={filterOptions.priorityLevel?.includes("high")}
                  onCheckedChange={() => togglePriorityLevelFilter("high")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="priority-high" className="ml-2">
                  <Badge className="bg-green-100 dark:bg-green-950/40 text-green-700 dark:text-green-400 border-green-300 dark:border-green-800">
                    High (70+)
                  </Badge>
                </Label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="priority-medium"
                  checked={filterOptions.priorityLevel?.includes("medium")}
                  onCheckedChange={() => togglePriorityLevelFilter("medium")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="priority-medium" className="ml-2">
                  <Badge className="bg-orange-100 dark:bg-orange-950/40 text-orange-700 dark:text-orange-400 border-orange-300 dark:border-orange-800">
                    Medium (30-69)
                  </Badge>
                </Label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="priority-low"
                  checked={filterOptions.priorityLevel?.includes("low")}
                  onCheckedChange={() => togglePriorityLevelFilter("low")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="priority-low" className="ml-2">
                  <Badge className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600">
                    Low (0-29)
                  </Badge>
                </Label>
              </div>
            </div>
          </div>
          <div>
            <h5 className="text-sm font-medium mb-2">User Impact Level</h5>
            <div className="space-y-2">
              <div className="flex items-center">
                <Checkbox
                  id="impact-high"
                  checked={filterOptions.userImpactLevel?.includes("high")}
                  onCheckedChange={() => toggleUserImpactLevelFilter("high")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="impact-high" className="ml-2">
                  <Badge className="bg-blue-100 dark:bg-blue-950/40 text-blue-700 dark:text-blue-400 border-blue-300 dark:border-blue-800">
                    High (500+)
                  </Badge>
                </Label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="impact-medium"
                  checked={filterOptions.userImpactLevel?.includes("medium")}
                  onCheckedChange={() => toggleUserImpactLevelFilter("medium")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="impact-medium" className="ml-2">
                  <Badge className="bg-indigo-100 dark:bg-indigo-950/40 text-indigo-700 dark:text-indigo-400 border-indigo-300 dark:border-indigo-800">
                    Medium (200-499)
                  </Badge>
                </Label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="impact-low"
                  checked={filterOptions.userImpactLevel?.includes("low")}
                  onCheckedChange={() => toggleUserImpactLevelFilter("low")}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="impact-low" className="ml-2">
                  <Badge className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600">
                    Low (0-199)
                  </Badge>
                </Label>
              </div>
            </div>
          </div>
          <div className="p-4 flex flex-col justify-start gap-2">
            <Button variant="default" size="sm">
              Save Config
            </Button>
            <Button variant="outline" size="sm" onClick={clearAllFilters}>
              Reset
            </Button>
          </div>
        </div>
        <Separator />
      </PopoverContent>
    </Popover>
  );
}
