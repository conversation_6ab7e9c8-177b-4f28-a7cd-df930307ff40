import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronRight, Star } from "lucide-react";
import { GoalType, Idea } from "../_types/ideas.types";

const goalLabels: Record<GoalType, { label: string; color: string }> = {
  "customer-satisfaction": {
    label: "Customer Satisfaction",
    color: "bg-blue-500/10 text-blue-500 hover:bg-blue-500/20",
  },
  "market-differentiation": {
    label: "Market Differentiation",
    color: "bg-purple-500/10 text-purple-500 hover:bg-purple-500/20",
  },
  "revenue-growth": {
    label: "Revenue Growth",
    color: "bg-green-500/10 text-green-500 hover:bg-green-500/20",
  },
  "market-expansion": {
    label: "Market Expansion",
    color: "bg-orange-500/10 text-orange-500 hover:bg-orange-500/20",
  },
  "feature-rework": {
    label: "Feature Rework",
    color: "bg-red-500/10 text-red-500 hover:bg-red-500/20",
  },
};

interface GroupedByGoalsListProps {
  processedIdeas: Idea[];
}

const GroupedByGoalsList = ({ processedIdeas }: GroupedByGoalsListProps) => {
  // Group ideas by goal
  const groupedIdeas = processedIdeas.reduce<Record<GoalType, Idea[]>>(
    (acc, idea) => {
      if (!acc[idea.goal]) {
        acc[idea.goal] = [];
      }
      acc[idea.goal].push(idea);
      return acc;
    },
    {} as Record<GoalType, Idea[]>
  );

  // Sort goals by total priority score
  const sortedGoals = Object.entries(groupedIdeas).sort(
    ([, ideasA], [, ideasB]) => {
      const totalScoreA = ideasA.reduce(
        (sum, idea) => sum + idea.priorityScore,
        0
      );
      const totalScoreB = ideasB.reduce(
        (sum, idea) => sum + idea.priorityScore,
        0
      );
      return totalScoreB - totalScoreA;
    }
  );

  if (processedIdeas.length === 0) {
    return (
      <div className="h-[calc(100vh-200px)] flex items-center justify-center border rounded-md">
        <p className="text-muted-foreground">
          No ideas match your search criteria
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[calc(100vh-200px)] rounded-md border">
      <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedGoals.map(([goal, ideas]) => {
          const goalType = goal as GoalType;
          const { label, color } = goalLabels[goalType];
          const totalPriorityScore = ideas.reduce(
            (sum, idea) => sum + idea.priorityScore,
            0
          );
          const averageStrategicValue = Math.round(
            ideas.reduce((sum, idea) => sum + idea.strategicValue, 0) /
              ideas.length
          );

          return (
            <Card key={goal} className="p-6 space-y-4">
              <div className="space-y-2">
                <Badge variant="secondary" className={color}>
                  {label}
                </Badge>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{ideas.length} ideas</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-current" />
                    <span>Avg. Value: {averageStrategicValue}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                {ideas
                  .sort((a, b) => b.priorityScore - a.priorityScore)
                  .map((idea) => (
                    <div
                      key={idea.id}
                      className="group flex items-start justify-between gap-2 rounded-lg border p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium leading-none">
                          {idea.title}
                        </h4>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {idea.description}
                        </p>
                      </div>
                      <ChevronRight className="h-5 w-5 text-muted-foreground/50 group-hover:text-muted-foreground transition-colors" />
                    </div>
                  ))}
              </div>

              <div className="pt-2 border-t">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Total Priority Score
                  </span>
                  <span className="font-medium">{totalPriorityScore}</span>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </ScrollArea>
  );
};

export default GroupedByGoalsList;
