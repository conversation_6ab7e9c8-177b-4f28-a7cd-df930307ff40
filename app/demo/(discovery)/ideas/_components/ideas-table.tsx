import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowUpDown, PlusIcon } from "lucide-react";
import { FilterOption, Idea, SortField } from "../_types/ideas.types";
import { IdeaRow } from "./idea-row";

interface SortIndicatorProps {
  field: SortField;
  sortField: SortField;
  sortDirection: "asc" | "desc";
}

function SortIndicator({
  field,
  sortField,
  sortDirection,
}: SortIndicatorProps) {
  if (field === sortField) {
    return (
      <span className="inline-flex w-4 justify-center">
        {sortDirection === "asc" ? "↑" : "↓"}
      </span>
    );
  }
  return (
    <span className="inline-flex w-4 justify-center opacity-0 group-hover:opacity-50">
      <ArrowUpDown className="h-3.5 w-3.5" />
    </span>
  );
}

type IdeasTableProps = {
  ideas: Idea[];
  processedIdeas: Idea[];
  searchQuery: string;
  sortField: SortField;
  sortDirection: "asc" | "desc";
  filterOptions: FilterOption;
  handleSort: (field: SortField) => void;
  handleSelectIdea: (id: string) => void;
  handleSelectAllIdeas: (checked: boolean) => void;
};

export function IdeasTable({
  ideas,
  processedIdeas,
  searchQuery,
  sortField,
  sortDirection,
  handleSort,
  handleSelectIdea,
  handleSelectAllIdeas,
}: IdeasTableProps) {
  return (
    <div className="w-full [&>div]:max-h-[calc(100vh-200px)] rounded-md border">
      <Table className="border-separate border-spacing-0">
        <TableHeader className="h-14 bg-accent/50 dark:bg-accent/50 sticky top-0 z-10 backdrop-blur-sm">
          <TableRow>
            <TableHead className="w-[40px] pl-4 md:pl-4 lg:pl-6">
              <Checkbox
                checked={
                  ideas.length > 0 && ideas.every((idea) => idea.selected)
                }
                onCheckedChange={(checked) => handleSelectAllIdeas(!!checked)}
                aria-label="Select all ideas"
                className="border-gray-300 dark:border-gray-600"
              />
            </TableHead>
            <TableHead
              className="w-[400px] cursor-pointer group"
              onClick={() => handleSort("title")}
            >
              <div className="flex items-center gap-1">
                Summary
                <SortIndicator
                  field="title"
                  sortField={sortField}
                  sortDirection={sortDirection}
                />
              </div>
            </TableHead>
            <TableHead
              className="w-[120px] cursor-pointer group"
              onClick={() => handleSort("goal")}
            >
              <div className="flex items-center gap-1">
                Goal
                <SortIndicator
                  field="goal"
                  sortField={sortField}
                  sortDirection={sortDirection}
                />
              </div>
            </TableHead>
            <TableHead
              className="w-[100px] cursor-pointer group"
              onClick={() => handleSort("priorityScore")}
            >
              <div className="flex items-center gap-1">
                Priority score
                <SortIndicator
                  field="priorityScore"
                  sortField={sortField}
                  sortDirection={sortDirection}
                />
              </div>
            </TableHead>
            <TableHead
              className="w-[140px] cursor-pointer group"
              onClick={() => handleSort("strategicValue")}
            >
              <div className="flex items-center gap-1">
                Strategic value
                <SortIndicator
                  field="strategicValue"
                  sortField={sortField}
                  sortDirection={sortDirection}
                />
              </div>
            </TableHead>
            <TableHead
              className="w-[100px] cursor-pointer group"
              onClick={() => handleSort("userImpact")}
            >
              <div className="flex items-center gap-1">
                User impact
                <SortIndicator
                  field="userImpact"
                  sortField={sortField}
                  sortDirection={sortDirection}
                />
              </div>
            </TableHead>
            <TableHead className="w-[50px]">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-foreground"
                title="Add column"
              >
                <PlusIcon className="h-4 w-4" />
              </Button>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {processedIdeas.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={7}
                className="text-center py-10 text-muted-foreground"
              >
                {searchQuery
                  ? "No ideas match your search criteria"
                  : "No ideas yet. Create your first idea to get started!"}
              </TableCell>
            </TableRow>
          ) : (
            processedIdeas.map((idea) => (
              <IdeaRow
                key={idea.id}
                idea={idea}
                onSelectIdea={handleSelectIdea}
              />
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
