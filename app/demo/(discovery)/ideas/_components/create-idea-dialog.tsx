import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PlusIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { GoalType, IdeaScoreType } from "../_types/ideas.types";
import { goalLabels } from "../_data/goal-labels";

type NewIdeaType = {
  title: string;
  description: string;
  goal: GoalType;
  strategicValue: IdeaScoreType;
  userImpact: number;
  priorityScore: number;
};

type CreateIdeaDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  newIdea: Partial<NewIdeaType>;
  setNewIdea: (idea: Partial<NewIdeaType>) => void;
  handleAddIdea: () => void;
};

export function CreateIdeaDialog({
  open,
  onOpen<PERSON>hange,
  newIdea,
  setNewIdea,
  handleAddIdea,
}: CreateIdeaDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="h-4 w-4" />
          <span className="hidden sm:block pr-1">Create an idea</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Create New Idea</DialogTitle>
          <DialogDescription>
            Add details about your product idea for discovery and
            prioritization.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="title">Summary</Label>
            <Input
              id="title"
              value={newIdea.title || ""}
              onChange={(e) =>
                setNewIdea({ ...newIdea, title: e.target.value })
              }
              placeholder="Brief summary of the idea"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={newIdea.description || ""}
              onChange={(e) =>
                setNewIdea({
                  ...newIdea,
                  description: e.target.value,
                })
              }
              placeholder="Detailed description of your idea"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="goal">Goal</Label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(goalLabels).map(([key, { text, icon }]) => (
                <Button
                  key={key}
                  type="button"
                  variant={newIdea.goal === key ? "default" : "outline"}
                  size="sm"
                  onClick={() =>
                    setNewIdea({
                      ...newIdea,
                      goal: key as GoalType,
                    })
                  }
                  className="gap-1.5"
                >
                  <span>{icon}</span>
                  {text}
                </Button>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="strategicValue">Strategic Value (1-5)</Label>
            <div className="flex gap-1 mt-1">
              {[1, 2, 3, 4, 5].map((value) => (
                <Button
                  key={value}
                  type="button"
                  variant={
                    newIdea.strategicValue === value ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() =>
                    setNewIdea({
                      ...newIdea,
                      strategicValue: value as IdeaScoreType,
                    })
                  }
                >
                  {value}
                </Button>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddIdea}>Create Idea</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
