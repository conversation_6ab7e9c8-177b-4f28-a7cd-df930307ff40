import { Idea } from "../_types/ideas.types";

export const mockIdeas: Idea[] = [
  {
    id: "1",
    title: "AI-powered content suggestions",
    description:
      "Implement smart recommendations for content creation and editing",
    goal: "market-differentiation",
    priorityScore: 100,
    strategicValue: 5,
    userImpact: 800,
    selected: false,
  },
  {
    id: "2",
    title: "Real-time collaboration improvements",
    description:
      "Enhance multi-user editing experience with better sync and presence",
    goal: "customer-satisfaction",
    priorityScore: 90,
    strategicValue: 5,
    userImpact: 750,
    selected: false,
  },
  {
    id: "3",
    title: "Integration with design tools",
    description: "Add seamless import/export with Figma, Sketch and Adobe XD",
    goal: "market-expansion",
    priorityScore: 85,
    strategicValue: 4,
    userImpact: 600,
    selected: false,
  },
  {
    id: "4",
    title: "Component library marketplace",
    description: "Create a marketplace for sharing and selling UI components",
    goal: "revenue-growth",
    priorityScore: 80,
    strategicValue: 5,
    userImpact: 500,
    selected: false,
  },
  {
    id: "5",
    title: "Performance optimization",
    description: "Improve loading and rendering speed of large component trees",
    goal: "feature-rework",
    priorityScore: 75,
    strategicValue: 4,
    userImpact: 650,
    selected: false,
  },
  {
    id: "6",
    title: "Advanced animation tools",
    description:
      "Add comprehensive animation and transition editing capabilities",
    goal: "market-differentiation",
    priorityScore: 70,
    strategicValue: 3,
    userImpact: 400,
    selected: false,
  },
  {
    id: "7",
    title: "Interactive component previews",
    description: "Add live preview and interaction testing for components",
    goal: "market-differentiation",
    priorityScore: 65,
    strategicValue: 4,
    userImpact: 450,
    selected: false,
  },
  {
    id: "8",
    title: "Enterprise SSO integration",
    description: "Add support for enterprise authentication providers",
    goal: "revenue-growth",
    priorityScore: 60,
    strategicValue: 4,
    userImpact: 300,
    selected: false,
  },
  {
    id: "9",
    title: "Framework partnerships",
    description: "Develop integrations with major UI frameworks",
    goal: "market-expansion",
    priorityScore: 55,
    strategicValue: 3,
    userImpact: 550,
    selected: false,
  },
  {
    id: "10",
    title: "Code editor improvements",
    description:
      "Enhance code editing with better syntax highlighting and autocomplete",
    goal: "feature-rework",
    priorityScore: 50,
    strategicValue: 3,
    userImpact: 400,
    selected: false,
  },
  {
    id: "11",
    title: "Component search and discovery",
    description: "Improve component discovery with better search and filtering",
    goal: "customer-satisfaction",
    priorityScore: 45,
    strategicValue: 3,
    userImpact: 350,
    selected: false,
  },
  {
    id: "12",
    title: "Asset optimization pipeline",
    description: "Implement automatic optimization for images and assets",
    goal: "market-differentiation",
    priorityScore: 40,
    strategicValue: 2,
    userImpact: 300,
    selected: false,
  },
  {
    id: "13",
    title: "Headless CMS integration",
    description: "Add support for popular headless CMS platforms",
    goal: "market-expansion",
    priorityScore: 35,
    strategicValue: 3,
    userImpact: 250,
    selected: false,
  },
  {
    id: "14",
    title: "Visual theme builder",
    description: "Create visual tools for theme customization",
    goal: "revenue-growth",
    priorityScore: 30,
    strategicValue: 2,
    userImpact: 400,
    selected: false,
  },
  {
    id: "15",
    title: "Component analytics",
    description: "Add usage analytics and insights for components",
    goal: "feature-rework",
    priorityScore: 25,
    strategicValue: 2,
    userImpact: 200,
    selected: false,
  },
  {
    id: "16",
    title: "Responsive preview tools",
    description: "Enhance responsive design testing capabilities",
    goal: "market-differentiation",
    priorityScore: 20,
    strategicValue: 2,
    userImpact: 350,
    selected: false,
  },
  {
    id: "17",
    title: "Component versioning",
    description: "Add version control for components and libraries",
    goal: "market-differentiation",
    priorityScore: 15,
    strategicValue: 2,
    userImpact: 150,
    selected: false,
  },
  {
    id: "18",
    title: "Custom deployment options",
    description: "Support custom deployment targets and configurations",
    goal: "revenue-growth",
    priorityScore: 10,
    strategicValue: 1,
    userImpact: 100,
    selected: false,
  },
  {
    id: "19",
    title: "Design system partnerships",
    description: "Partner with major design system providers",
    goal: "market-expansion",
    priorityScore: 5,
    strategicValue: 1,
    userImpact: 200,
    selected: false,
  },
  {
    id: "20",
    title: "Documentation generator",
    description: "Automatic documentation generation from components",
    goal: "feature-rework",
    priorityScore: 5,
    strategicValue: 1,
    userImpact: 150,
    selected: false,
  },
];
