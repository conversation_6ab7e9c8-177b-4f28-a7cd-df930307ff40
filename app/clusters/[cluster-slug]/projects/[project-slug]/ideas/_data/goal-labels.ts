import { GoalLabelsMap } from "../_types/ideas.types";

export const goalLabels: GoalLabelsMap = {
  "customer-satisfaction": {
    text: "Customer Satisfaction",
    color:
      "bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-950/40 dark:text-blue-300 dark:border-blue-800",
    icon: "👍",
  },
  "market-differentiation": {
    text: "Market Differentiation",
    color:
      "bg-amber-100 text-amber-800 border-amber-300 dark:bg-amber-950/40 dark:text-amber-300 dark:border-amber-800",
    icon: "🚀",
  },
  "revenue-growth": {
    text: "Revenue Growth",
    color:
      "bg-emerald-100 text-emerald-800 border-emerald-300 dark:bg-emerald-950/40 dark:text-emerald-300 dark:border-emerald-800",
    icon: "💰",
  },
  "market-expansion": {
    text: "Market Expansion",
    color:
      "bg-violet-100 text-violet-800 border-violet-300 dark:bg-violet-950/40 dark:text-violet-300 dark:border-violet-800",
    icon: "🌱",
  },
  "feature-rework": {
    text: "Feature Rework",
    color:
      "bg-rose-100 text-rose-800 border-rose-300 dark:bg-rose-950/40 dark:text-rose-300 dark:border-rose-800",
    icon: "✨",
  },
};
