"use client";

import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Search } from "lucide-react";
import { useState } from "react";
import { CreateIdeaDialog } from "./_components/create-idea-dialog";
import { FilterPopover } from "./_components/filter-popover";
import GroupedByGoalsList from "./_components/grouped-by-goals-list";
import { IdeasTable } from "./_components/ideas-table";
import { mockIdeas } from "./_data/mock-ideas";
import {
  FilterOption,
  GoalType,
  Idea,
  IdeaScoreType,
  SortDirection,
  SortField,
} from "./_types/ideas.types";

export default function IdeasPage() {
  const [ideas, setIdeas] = useState<Idea[]>(mockIdeas);
  const [searchQuery, setSearchQuery] = useState("");
  const [newIdeaDialogOpen, setNewIdeaDialogOpen] = useState(false);
  const [newIdea, setNewIdea] = useState<Partial<Idea>>({
    title: "",
    description: "",
    goal: "customer-satisfaction",
    strategicValue: 3,
    userImpact: 0,
    priorityScore: 0,
  });

  // Sorting state
  const [sortField, setSortField] = useState<SortField>("priorityScore");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");

  // Filter state
  const [filterOptions, setFilterOptions] = useState<FilterOption>({
    goal: [],
    priorityLevel: [],
    userImpactLevel: [],
  });

  // Sort function
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Priority level classification
  const getPriorityLevel = (score: number): "high" | "medium" | "low" => {
    if (score >= 70) return "high";
    if (score >= 30) return "medium";
    return "low";
  };

  // User impact level classification
  const getUserImpactLevel = (impact: number): "high" | "medium" | "low" => {
    if (impact >= 500) return "high";
    if (impact >= 200) return "medium";
    return "low";
  };

  // Process ideas with filtering and sorting
  const processedIdeas = [...ideas]
    // First filter by search query
    .filter(
      (idea) =>
        idea.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        idea.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    // Then apply filter options
    .filter((idea) => {
      // Check if we need to filter by goal
      if (filterOptions.goal && filterOptions.goal.length > 0) {
        if (!filterOptions.goal.includes(idea.goal)) {
          return false;
        }
      }

      // Check if we need to filter by priority level
      if (filterOptions.priorityLevel && filterOptions.priorityLevel.length > 0) {
        const level = getPriorityLevel(idea.priorityScore);
        if (!filterOptions.priorityLevel.includes(level)) {
          return false;
        }
      }

      // Check if we need to filter by user impact level
      if (filterOptions.userImpactLevel && filterOptions.userImpactLevel.length > 0) {
        const level = getUserImpactLevel(idea.userImpact);
        if (!filterOptions.userImpactLevel.includes(level)) {
          return false;
        }
      }

      return true;
    })
    // Then sort by the selected field
    .sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (sortField === "goal" || sortField === "title") {
        // String comparison for text fields (goal and title)
        return sortDirection === "asc"
          ? String(aValue).localeCompare(String(bValue))
          : String(bValue).localeCompare(String(aValue));
      }

      // Numeric comparison for other fields
      return sortDirection === "asc"
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

  // Handler for idea selection
  const handleSelectIdea = (id: string) => {
    setIdeas(ideas.map((idea) => (idea.id === id ? { ...idea, selected: !idea.selected } : idea)));
  };

  // Handler for selecting all ideas
  const handleSelectAllIdeas = (checked: boolean) => {
    setIdeas(
      ideas.map((idea) => ({
        ...idea,
        selected: checked,
      }))
    );
  };

  // Handler for adding a new idea
  const handleAddIdea = () => {
    if (!newIdea.title) return;

    const idea: Idea = {
      id: Date.now().toString(),
      title: newIdea.title,
      description: newIdea.description || "",
      goal: (newIdea.goal as GoalType) || "increase-csat",
      strategicValue: (newIdea.strategicValue as IdeaScoreType) || 3,
      userImpact: newIdea.userImpact || 0,
      priorityScore: newIdea.priorityScore || 0,
      selected: false,
    };

    setIdeas([...ideas, idea]);
    setNewIdea({
      title: "",
      description: "",
      goal: "customer-satisfaction",
      strategicValue: 3,
      userImpact: 0,
      priorityScore: 0,
    });
    setNewIdeaDialogOpen(false);
  };

  // Toggle filters
  const toggleGoalFilter = (goal: GoalType) => {
    setFilterOptions((prev) => {
      const currentGoals = prev.goal || [];
      return {
        ...prev,
        goal: currentGoals.includes(goal)
          ? currentGoals.filter((g) => g !== goal)
          : [...currentGoals, goal],
      };
    });
  };

  const togglePriorityLevelFilter = (level: "high" | "medium" | "low") => {
    setFilterOptions((prev) => {
      const currentLevels = prev.priorityLevel || [];
      return {
        ...prev,
        priorityLevel: currentLevels.includes(level)
          ? currentLevels.filter((l) => l !== level)
          : [...currentLevels, level],
      };
    });
  };

  const toggleUserImpactLevelFilter = (level: "high" | "medium" | "low") => {
    setFilterOptions((prev) => {
      const currentLevels = prev.userImpactLevel || [];
      return {
        ...prev,
        userImpactLevel: currentLevels.includes(level)
          ? currentLevels.filter((l) => l !== level)
          : [...currentLevels, level],
      };
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilterOptions({
      goal: [],
      priorityLevel: [],
      userImpactLevel: [],
    });
  };

  // Check if any filters are active
  const hasActiveFilters = (): boolean => {
    return (
      (filterOptions.goal?.length || 0) > 0 ||
      (filterOptions.priorityLevel?.length || 0) > 0 ||
      (filterOptions.userImpactLevel?.length || 0) > 0
    );
  };

  return (
    <div className="h-full w-full p-6">
      <Tabs defaultValue="table" className="w-full">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
          <div>
            <h1 className="text-2xl font-semibold">Ideas Discovery</h1>
            <p className="text-sm text-muted-foreground">Manage and prioritize product ideas</p>
          </div>

          <TabsList className="mb-4 sm:mb-0">
            <TabsTrigger value="table">Ideas Table</TabsTrigger>
            <TabsTrigger value="list-grouped-by-goals">Grouped by Goals</TabsTrigger>
            {/* <TabsTrigger value="tab-3">Content for tab 3</TabsTrigger> */}
          </TabsList>

          <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-end">
            <div className="relative w-full sm:w-auto">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search ideas..."
                className="pl-9 w-full sm:w-[200px] h-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <FilterPopover
                filterOptions={filterOptions}
                toggleGoalFilter={toggleGoalFilter}
                togglePriorityLevelFilter={togglePriorityLevelFilter}
                toggleUserImpactLevelFilter={toggleUserImpactLevelFilter}
                clearAllFilters={clearAllFilters}
                hasActiveFilters={hasActiveFilters}
              />

              <CreateIdeaDialog
                open={newIdeaDialogOpen}
                onOpenChange={setNewIdeaDialogOpen}
                newIdea={newIdea}
                setNewIdea={setNewIdea}
                handleAddIdea={handleAddIdea}
              />
            </div>
          </div>
        </div>

        <TabsContent value="table" className="mt-0">
          <IdeasTable
            ideas={ideas}
            processedIdeas={processedIdeas}
            searchQuery={searchQuery}
            sortField={sortField}
            sortDirection={sortDirection}
            filterOptions={filterOptions}
            handleSort={handleSort}
            handleSelectIdea={handleSelectIdea}
            handleSelectAllIdeas={handleSelectAllIdeas}
          />
        </TabsContent>

        <TabsContent value="list-grouped-by-goals">
          <GroupedByGoalsList processedIdeas={processedIdeas} />
        </TabsContent>

        {/* <TabsContent value="tab-3">
          <div className="h-[calc(100vh-200px)] flex items-center justify-center border rounded-md bg-muted/20">
            <p className="text-muted-foreground">
              Calendar view implementation placeholder
            </p>
          </div>
        </TabsContent> */}
      </Tabs>
    </div>
  );
}
