// Types for our idea entries
export type GoalType =
  | "customer-satisfaction"
  | "market-differentiation"
  | "revenue-growth"
  | "market-expansion"
  | "feature-rework";

export type IdeaScoreType = 1 | 2 | 3 | 4 | 5;

export type Idea = {
  id: string;
  title: string;
  description: string;
  goal: GoalType;
  priorityScore: number;
  strategicValue: IdeaScoreType;
  userImpact: number;
  selected: boolean;
};

// Sort types
export type SortField =
  | "title"
  | "goal"
  | "priorityScore"
  | "strategicValue"
  | "userImpact";
export type SortDirection = "asc" | "desc";

// Add FilterOption type after the other type definitions
export type FilterOption = {
  goal?: GoalType[];
  priorityLevel?: ("high" | "medium" | "low")[];
  userImpactLevel?: ("high" | "medium" | "low")[];
};

export type GoalLabel = {
  text: string;
  color: string;
  icon: string;
};

export type GoalLabelsMap = Record<GoalType, GoalLabel>;
