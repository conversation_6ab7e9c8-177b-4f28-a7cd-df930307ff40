import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Rating, RatingButton } from "@/components/ui/rating";
import { TableCell, TableRow } from "@/components/ui/table";
import { CircleIcon, MoreHorizontal } from "lucide-react";
import { goalLabels } from "../_data/goal-labels";
import { GoalType, Idea } from "../_types/ideas.types";
type IdeaRowProps = {
  idea: Idea;
  onSelectIdea: (id: string) => void;
};

export function IdeaRow({ idea, onSelectIdea }: IdeaRowProps) {
  const getStrategicValueStars = (value: number) => {
    return (
      <Rating defaultValue={value} className="text-blue-500/70">
        {Array.from({ length: 5 }).map((_, index) => (
          <RatingButton
            key={index}
            size={14}
            color="amber"
            icon={<CircleIcon />}
          />
        ))}
      </Rating>
    );
  };

  // Generate goal badge
  const getGoalBadge = (goal: GoalType) => {
    const { text, color, icon } = goalLabels[goal];
    return (
      <Badge className={`${color} font-medium border text-xs px-2 py-0.5`}>
        <span className="mr-1">{icon}</span>
        {text}
      </Badge>
    );
  };

  return (
    <TableRow
      key={idea.id}
      className={`rounded-md hover:inset-ring-1 hover:inset-ring-gray-500 dark:hover:inset-ring-gray-500 ${
        idea.selected ? "bg-primary/5 dark:bg-primary/10" : ""
      }`}
      data-state={idea.selected ? "selected" : ""}
    >
      <TableCell className="min-w-[40px] pl-4 max-md:pl-4 lg:pl-6">
        <Checkbox
          checked={idea.selected}
          onCheckedChange={() => onSelectIdea(idea.id)}
          aria-label={`Select ${idea.title}`}
          className="border-gray-300 dark:border-gray-600"
        />
      </TableCell>
      <TableCell className="font-medium">
        <div>
          <div className="font-medium">{idea.title}</div>
          <div className="text-sm text-muted-foreground truncate max-w-[350px]">
            {idea.description}
          </div>
        </div>
      </TableCell>
      <TableCell>{getGoalBadge(idea.goal)}</TableCell>
      <TableCell>
        <Badge
          variant="outline"
          className={`font-medium ${
            idea.priorityScore >= 75
              ? "border-green-500 text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-950/30"
              : idea.priorityScore >= 30
              ? "border-orange-500 text-orange-700 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/30"
              : "border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50"
          }`}
        >
          {idea.priorityScore}
        </Badge>
      </TableCell>
      <TableCell>{getStrategicValueStars(idea.strategicValue)}</TableCell>
      <TableCell>
        <div className="font-medium">{idea.userImpact}</div>
      </TableCell>
      <TableCell>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
