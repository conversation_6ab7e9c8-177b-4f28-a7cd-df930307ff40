import { TasksPageSkeleton } from "@/components/skeletons";
import { getProjectBySlugAndTenantId } from "@/db/actions/project.action";
import { getTenantId } from "@/db/tenant.db";
import { PageParams } from "@/types/next.types";
import { Suspense } from "react";
import { ClientWrapper } from "./_components/client-wrapper";
import { TaskHeader } from "./_components/task-header";

// Minimal server component that only fetches essential data for navigation
export default async function TasksPage(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
) {
  // Only fetch the absolute minimum required for page routing
  const tenantId = await getTenantId();
  if (!tenantId) {
    throw new Error("Tenant ID not found");
  }

  const params = await props.params;
  const project = await getProjectBySlugAndTenantId(params["project-slug"], tenantId);

  if (!project) {
    throw new Error("Project not found");
  }

  return (
    <div className="flex flex-col w-full h-full px-6">
      <TaskHeader />
      <Suspense fallback={<TasksPageSkeleton />}>
        <ClientWrapper
          projectId={project.id}
          clusterId={params["cluster-slug"]}
          tenantId={tenantId}
          projectSlug={params["project-slug"]}
        />
      </Suspense>
    </div>
  );
}
