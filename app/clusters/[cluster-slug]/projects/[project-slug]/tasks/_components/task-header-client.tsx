"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { useState } from "react";

interface TaskHeaderClientProps {
  onAnalyticsToggle?: (show: boolean) => void;
}

export function TaskHeaderClient({ onAnalyticsToggle }: TaskHeaderClientProps = {}) {
  const [showAnalytics, setShowAnalytics] = useState(false);

  const handleToggle = () => {
    const newState = !showAnalytics;
    setShowAnalytics(newState);
    onAnalyticsToggle?.(newState);
  };

  return (
    <Button variant="outline" size="sm" onClick={handleToggle}>
      {showAnalytics ? (
        <>
          <ChevronUpIcon className="w-4 h-4" /> Hide tasks analytics
        </>
      ) : (
        <>
          <ChevronDownIcon className="w-4 h-4" /> See tasks analytics
        </>
      )}
    </Button>
  );
}
