"use client";

import { TaskModel } from "@/db/schemas/tasks.schema";
import { createContext, useContext, useMemo } from "react";
import { useFilteredTasks, useTaskFilters } from "../_hooks/use-task-filters";
import { useTasksData } from "../_hooks/use-tasks-data";
import { useTasksContext } from "./tasks-provider";

interface FilteredTasksContextType {
  filteredTasks: TaskModel[];
  allTasks: TaskModel[];
  isLoading: boolean;
}

const FilteredTasksContext = createContext<FilteredTasksContextType | null>(null);

export function FilteredTasksProvider({ children }: { children: React.ReactNode }) {
  const { projectId } = useTasksContext();
  const { filters } = useTaskFilters();
  const { tasks: allTasks, isLoading } = useTasksData(projectId);

  // Apply filters to tasks
  const filteredTasks = useFilteredTasks(allTasks, filters);

  const value = useMemo(
    () => ({
      filteredTasks,
      allTasks,
      isLoading,
    }),
    [filteredTasks, allTasks, isLoading]
  );

  return <FilteredTasksContext.Provider value={value}>{children}</FilteredTasksContext.Provider>;
}

export function useFilteredTasksContext() {
  const context = useContext(FilteredTasksContext);
  if (!context) {
    throw new Error("useFilteredTasksContext must be used within FilteredTasksProvider");
  }
  return context;
}
