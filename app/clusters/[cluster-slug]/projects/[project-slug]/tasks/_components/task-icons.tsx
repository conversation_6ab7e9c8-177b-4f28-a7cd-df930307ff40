"use client";

import { cn } from "@/lib/utils";
import {
  AlignLeft,
  Calendar,
  ChartGantt,
  Clock,
  Filter,
  LayoutGridIcon,
  LayoutPanelLeft,
  MoreHorizontal,
  Search,
  SortDesc,
  Tag,
  Users,
} from "lucide-react";
import React from "react";

// Icon button props type
type IconButtonProps = {
  icon: React.ReactNode;
  onClick?: () => void;
  className?: string;
  title?: string;
  isGroup?: boolean;
};

// Icon button component
export function IconButton(props: IconButtonProps) {
  return (
    <button
      onClick={props.onClick}
      title={props.title}
      className={cn(
        "p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100",
        props.isGroup && "rounded-l-md rounded-r-md",
        props.className
      )}
    >
      {props.icon}
    </button>
  );
}

export function IconButtonGroup(props: {
  children: React.ReactNode[];
  onClick?: () => void;
  className?: string;
  title?: string;
}) {
  // Clone children to apply proper styling
  const childrenCount = React.Children.count(props.children);

  const styledChildren = React.Children.map(props.children, (child, index) => {
    if (React.isValidElement<IconButtonProps>(child)) {
      // Apply specific styles based on position
      return React.cloneElement(child, {
        ...child.props,
        className: cn(
          child.props.className || "",
          index === 0 ? "rounded-l-md rounded-r-none" : "",
          index === childrenCount - 1 ? "rounded-r-md rounded-l-none" : "",
          index > 0 && index < childrenCount - 1 ? "rounded-none" : "",
          "border-r last:border-r-0"
        ),
        onClick: child.props.onClick || props.onClick,
      });
    }
    return child;
  });

  return (
    <div className={cn("flex border rounded-md overflow-hidden", props.className)}>
      {styledChildren}
    </div>
  );
}

// List view icon
export function ListIcon(props: { className?: string }) {
  return <AlignLeft className={cn("w-4 h-4", props.className)} />;
}

// Kanban view icon
export function KanbanIcon(props: { className?: string }) {
  return <LayoutPanelLeft className={cn("w-4 h-4", props.className)} />;
}

// Timeline view icon
export function TimelineIcon(props: { className?: string }) {
  return <ChartGantt className={cn("w-4 h-4", props.className)} />;
}

// Calendar view icon
export function CalendarIcon(props: { className?: string }) {
  return <Calendar className={cn("w-4 h-4", props.className)} />;
}

// Dashboard view icon
export function DashboardIcon(props: { className?: string }) {
  return <LayoutGridIcon className={cn("w-4 h-4", props.className)} />;
}

// Sort icon
export function SortIcon(props: { className?: string }) {
  return <SortDesc className={cn("w-5 h-5", props.className)} />;
}

// Filter icon
export function FilterIcon(props: { className?: string }) {
  return <Filter className={cn("w-5 h-5", props.className)} />;
}

// Search icon
export function SearchIcon(props: { className?: string }) {
  return <Search className={cn("w-5 h-5", props.className)} />;
}

// More icon (three dots)
export function MoreIcon(props: { className?: string }) {
  return <MoreHorizontal className={cn("w-5 h-5", props.className)} />;
}

// Clock icon for due date
export function ClockIcon(props: { className?: string }) {
  return <Clock className={cn("w-4 h-4", props.className)} />;
}

// Group icon for task groups
export function GroupIcon(props: { className?: string }) {
  return <Users className={cn("w-5 h-5", props.className)} />;
}

// Tag icon
export function TagIcon(props: { className?: string }) {
  return <Tag className={cn("w-4 h-4", props.className)} />;
}

// Get icon by name
export function getIconByName(name: string) {
  switch (name) {
    case "list":
      return <ListIcon />;
    case "kanban":
      return <KanbanIcon />;
    case "timeline":
      return <TimelineIcon />;
    case "calendar":
      return <CalendarIcon />;
    case "dashboard":
      return <DashboardIcon />;
    case "sort":
      return <SortIcon />;
    case "filter":
      return <FilterIcon />;
    case "search":
      return <SearchIcon />;
    case "more":
      return <MoreIcon />;
    case "clock":
      return <ClockIcon />;
    case "group":
      return <GroupIcon />;
    case "tag":
      return <TagIcon />;
    default:
      return <ListIcon />;
  }
}
