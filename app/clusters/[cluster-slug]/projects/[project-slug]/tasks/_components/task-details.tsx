import { Sheet, SheetContent, SheetDescription } from "@/components/ui/sheet";
import { memo } from "react";
import { useProjectAssignees, useTags } from "../_hooks/use-crud-tasks";
import { useTaskStatusInfo } from "../utils/task-status-styles";
import {
  TaskDetailsFields,
  TaskDetailsFooter,
  TaskDetailsHeader,
  useTaskDetailsEffects,
  useTaskDetailsHandlers,
  useTaskDetailsState,
  type TaskDetailsProps,
} from "./_task_details";

const TaskDetails = memo(function TaskDetails({
  task,
  isSheetOpen,
  setIsSheetOpen,
  projectId,
}: TaskDetailsProps) {
  // Consolidated state management
  const { state, setters } = useTaskDetailsState(task);

  // Consolidated event handlers
  const handlers = useTaskDetailsHandlers(task, projectId, setters);

  // Consolidated effects (URL state, keyboard events, state sync)
  useTaskDetailsEffects(task, isSheetOpen, state, setters);

  // Hook for fetching available tags (only when editing tags)
  const tagsQuery = useTags(projectId || "", state.isEditingTags);
  const availableTags = tagsQuery.data || [];

  // Hook for fetching available assignees (only when editing assignees)
  const assigneesQuery = useProjectAssignees(projectId || "", state.isEditingAssignee);
  const availableAssignees = assigneesQuery.data || [];

  // Get dynamic status info (used by ColumnStatusDropdown)
  useTaskStatusInfo(projectId);

  return (
    <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
      <SheetDescription className="hidden">Opening task details</SheetDescription>
      <SheetContent className="overflow-y-auto rounded-l-lg sm:max-w-2xl gap-2">
        <TaskDetailsHeader task={task} state={state} setters={setters} handlers={handlers} />
        <div className="flex flex-col h-full px-12 gap-6">
          <TaskDetailsFields
            task={task}
            projectId={projectId}
            state={state}
            setters={setters}
            handlers={handlers}
            availableTags={availableTags}
            availableAssignees={availableAssignees}
          />
          <TaskDetailsFooter />
        </div>
      </SheetContent>
    </Sheet>
  );
});

export default TaskDetails;
