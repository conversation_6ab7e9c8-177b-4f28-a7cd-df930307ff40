"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TagModel, UserModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import {
  CheckIcon,
  ChevronDown,
  LayoutGridIcon,
  PlusIcon,
  SortAscIcon,
  SortDescIcon,
  TableIcon,
} from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { TaskFiltersState } from "../_types/filter.types";
import { GroupByOption, ViewType, viewOptions } from "../_types/list-group-options.types";
import { TaskFiltersPopover } from "./task-filters-popover";
import { SearchIcon, getIconByName } from "./task-icons";

// Format the group option for display
function formatGroupOption(option: GroupByOption): string {
  switch (option) {
    case "status":
      return "Column Status";
    case "lifecycle":
      return "Lifecycle Status";
    case "tag":
      return "Tag";
    case "assignee":
      return "Assignee";
    case "due-date":
      return "Due Date";
    case "effort":
      return "Effort";
    case "value":
      return "Value";
    default:
      return option;
  }
}

// Check if a grouping option is active
function activeGroupingCheck(activeGrouping: GroupByOption, grouping: GroupByOption) {
  return (
    <>
      {activeGrouping === grouping ? (
        <div className="flex items-center justify-between w-full">
          {formatGroupOption(grouping)}
          <CheckIcon className="w-3 h-3" />
        </div>
      ) : (
        formatGroupOption(grouping)
      )}
    </>
  );
}

// Props for the TasksTabsHeader component
export interface TasksTabsHeaderProps {
  activeView: ViewType;
  activeGrouping?: GroupByOption;
  onGroupingChange?: (grouping: GroupByOption) => void;
  onSortChange?: (isSortReversed: boolean) => void;
  isCompactMode?: boolean;
  onCompactModeChange?: (isCompact: boolean) => void;
  onCreateTask?: () => void;
  filtersState?: TaskFiltersState;
  availableTags?: TagModel[];
  availableAssignees?: UserModel[];
}

// Main tabs header component
export function TasksTabsHeader({
  activeView,
  activeGrouping = "status",
  onGroupingChange,
  onSortChange,
  isCompactMode = false,
  onCompactModeChange,
  onCreateTask,
  filtersState,
  availableTags = [],
  availableAssignees = [],
}: TasksTabsHeaderProps) {
  const [isSortReversed, setIsSortReversed] = useState(false);

  // Handle sort button click
  const handleSort = useCallback(() => {
    const newReversed = !isSortReversed;
    setIsSortReversed(newReversed);
    if (onSortChange) {
      onSortChange(newReversed);
    }
    toast.info(`Sorted in ${newReversed ? "descending" : "ascending"} order`);
  }, [isSortReversed, onSortChange]);

  // Handle grouping change
  const handleGroupingChange = useCallback(
    (grouping: GroupByOption) => {
      if (onGroupingChange) {
        onGroupingChange(grouping);
      }
    },
    [onGroupingChange]
  );

  // Handle compact mode toggle
  const handleCompactModeToggle = useCallback(() => {
    if (onCompactModeChange) {
      onCompactModeChange(!isCompactMode);
      toast.info(`Switched to ${!isCompactMode ? "compact" : "normal"} view`);
    }
  }, [isCompactMode, onCompactModeChange]);

  return (
    <div className="flex items-center justify-between">
      {/* Tabs list for view selection */}
      <TabsList className="flex items-center p-0 mt-4 overflow-hidden bg-transparent border rounded-md">
        {viewOptions.map((option) => (
          <TabsTrigger
            key={option.id}
            value={option.id}
            className="h-8 px-3 rounded-none border-r last:border-r-0 shadow-none text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 bg-transparent data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-gray-800 data-[state=active]:text-gray-900 dark:data-[state=active]:text-gray-50 data-[state=active]:shadow-sm data-[state=active]:font-medium data-[state=active]:border-b-1 data-[state=active]:border-b-gray-500 data-[state=active]:transition-all data-[state=active]:duration-200"
          >
            <span className="flex items-center gap-1.5 text-sm">
              {getIconByName(option.icon)}
              <span>{option.name}</span>
            </span>
          </TabsTrigger>
        ))}
      </TabsList>

      <div className="flex items-center gap-2">
        {/* Group by selector - Only show in List view */}
        {activeView === "list" && (
          <div className="flex items-center">
            <span className="mr-1 text-xs">Group by:</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="clean-focus">
                <Button variant="outline" size="sm" className="flex items-center h-8 gap-1 text-xs">
                  {formatGroupOption(activeGrouping)}
                  <ChevronDown className="w-3 h-3 ml-1 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[180px]">
                <DropdownMenuItem
                  className={cn(activeGrouping === "status" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("status")}
                >
                  {activeGroupingCheck(activeGrouping, "status")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "lifecycle" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("lifecycle")}
                >
                  {activeGroupingCheck(activeGrouping, "lifecycle")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "tag" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("tag")}
                >
                  {activeGroupingCheck(activeGrouping, "tag")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "assignee" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("assignee")}
                >
                  {activeGroupingCheck(activeGrouping, "assignee")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "due-date" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("due-date")}
                >
                  {activeGroupingCheck(activeGrouping, "due-date")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "effort" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("effort")}
                >
                  {activeGroupingCheck(activeGrouping, "effort")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={cn(activeGrouping === "value" && "bg-gray-100 dark:bg-gray-800")}
                  onClick={() => handleGroupingChange("value")}
                >
                  {activeGroupingCheck(activeGrouping, "value")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Sort and filter buttons */}
        <div className="flex overflow-visible border rounded-md">
          {activeView === "list" && (
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 px-3 rounded-none border-r last:border-r-0",
                isSortReversed && "bg-gray-100 dark:bg-gray-800"
              )}
              onClick={handleSort}
            >
              {isSortReversed ? (
                <SortAscIcon className="w-4 h-4" />
              ) : (
                <SortDescIcon className="w-4 h-4" />
              )}
            </Button>
          )}
          {activeView === "kanban" && onCompactModeChange && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCompactModeToggle}
              className={cn(
                "h-8 px-3 rounded-none border-r last:border-r-0",
                isCompactMode && "bg-gray-100 dark:bg-gray-800"
              )}
            >
              {isCompactMode ? (
                <>
                  <TableIcon className="w-4 h-4" />
                </>
              ) : (
                <>
                  <LayoutGridIcon className="w-4 h-4" />
                </>
              )}
            </Button>
          )}

          {filtersState && (
            <TaskFiltersPopover
              filtersState={filtersState}
              availableTags={availableTags}
              availableAssignees={availableAssignees}
            />
          )}
        </div>

        {/* Search bar */}
        <div className="relative">
          <SearchIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 w-3.5 h-3.5" />
          <input
            type="text"
            placeholder="Search tasks..."
            className="pl-8 pr-3 py-1.5 border rounded-md w-48 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 h-8"
            onChange={(e) => {
              if (e.target.value.length > 2) {
                toast.info(`Searching for: ${e.target.value}`);
              }
            }}
          />
        </div>

        {/* Add task button */}
        <Button
          size="sm"
          className="h-8 gap-1 px-3 text-sm"
          onClick={onCreateTask || (() => toast.success("Add task"))}
        >
          <PlusIcon className="w-3.5 h-3.5" />
          <span>Add Task</span>
        </Button>
      </div>
    </div>
  );
}

// Export as default as well for flexibility
export default TasksTabsHeader;
