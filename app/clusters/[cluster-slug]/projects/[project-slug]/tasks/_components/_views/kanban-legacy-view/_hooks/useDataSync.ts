import type { ColumnStatus, TaskModel } from "@/db/schemas/tasks.schema";
import { useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useRef } from "react";
import { toast } from "sonner";
import { taskKeys, useMoveTask } from "../../../../_hooks/use-crud-tasks";

interface UseDataSyncProps {
  projectId?: string;
  enableOptimisticUpdates?: boolean;
  enableErrorRecovery?: boolean;
  enableRealTimeSync?: boolean;
}

interface UseDataSyncReturn {
  syncTaskMove: (taskId: string, targetColumn: string, targetPosition: number) => Promise<void>;
  rollbackOptimisticUpdate: (taskId: string) => void;
  invalidateAndRefresh: () => void;
  isOnline: boolean;
  hasPendingChanges: boolean;
}

/**
 * Hook for managing data synchronization between local state and server
 *
 * Features:
 * - Optimistic updates with rollback capability
 * - Error recovery mechanisms
 * - Offline support with queue management
 * - Real-time sync capabilities
 * - Data validation and sanitization
 */
export function useDataSync({
  projectId,
  enableOptimisticUpdates = true,
  enableErrorRecovery = true,
}: Omit<UseDataSyncProps, "enableRealTimeSync">): UseDataSyncReturn {
  const queryClient = useQueryClient();
  const moveTaskMutation = useMoveTask();

  // Track pending changes and network status
  const pendingChangesRef = useRef<Set<string>>(new Set());
  const isOnlineRef = useRef(navigator.onLine);
  const rollbackDataRef = useRef<Map<string, TaskModel>>(new Map());

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => {
      isOnlineRef.current = true;
      if (pendingChangesRef.current.size > 0) {
        toast.info("Connection restored. Syncing pending changes...");
        // TODO: Implement pending changes sync
      }
    };

    const handleOffline = () => {
      isOnlineRef.current = false;
      toast.warning("Connection lost. Changes will be synced when online.");
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Sanitize task data
  const sanitizeTaskData = useCallback((task: TaskModel): TaskModel => {
    return {
      ...task,
      title: task.title.trim(),
      description: task.description?.trim() || "",
      // Ensure position is a positive integer
      positionInColumn: Math.max(1, Math.floor(task.positionInColumn || 1)),
    };
  }, []);

  // Rollback optimistic update
  const rollbackOptimisticUpdate = useCallback(
    (taskId: string) => {
      if (!projectId) return;

      const originalTask = rollbackDataRef.current.get(taskId);
      if (!originalTask) return;

      queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
        if (!oldData) return [];

        return oldData.map((task) => (task.id === taskId ? originalTask : task));
      });

      // Clean up tracking
      rollbackDataRef.current.delete(taskId);
      pendingChangesRef.current.delete(taskId);

      toast.error("Change reverted due to sync error");
    },
    [projectId, queryClient]
  );

  // Enhanced optimistic update that actually rearranges tasks in the UI
  const applyTaskMoveOptimisticUpdate = useCallback(
    (taskId: string, targetColumn: string, targetPosition: number) => {
      if (!projectId || !enableOptimisticUpdates) return;

      // Store original data for rollback
      const currentData = queryClient.getQueryData(taskKeys.list(projectId)) as
        | TaskModel[]
        | undefined;
      const originalTask = currentData?.find((task) => task.id === taskId);

      if (originalTask) {
        rollbackDataRef.current.set(taskId, originalTask);
      }

      // Apply optimistic update with proper array manipulation
      queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
        if (!oldData) return [];

        // Find the task being moved
        const taskToMove = oldData.find((task) => task.id === taskId);
        if (!taskToMove) return oldData;

        // Remove task from current position
        const tasksWithoutMoved = oldData.filter((task) => task.id !== taskId);

        // Group tasks by status/column
        const tasksByColumn = tasksWithoutMoved.reduce(
          (acc, task) => {
            const column = task.status || "unknown";
            if (!acc[column]) acc[column] = [];
            acc[column].push(task);
            return acc;
          },
          {} as Record<string, TaskModel[]>
        );

        // Sort tasks in target column by position
        const targetColumnTasks = (tasksByColumn[targetColumn] || []).sort(
          (a, b) => (a.positionInColumn || 1) - (b.positionInColumn || 1)
        );

        // Insert moved task at the correct position
        const updatedTask = sanitizeTaskData({
          ...taskToMove,
          status: targetColumn,
          positionInColumn: targetPosition,
          isOptimistic: true,
          updatedAt: new Date().toISOString(),
        });

        // Insert at target position (1-based to 0-based conversion)
        const insertIndex = Math.min(targetPosition - 1, targetColumnTasks.length);
        targetColumnTasks.splice(insertIndex, 0, updatedTask);

        // Update positions for all tasks in target column, preserving the moved task's intended position
        targetColumnTasks.forEach((task, index) => {
          if (task.id === taskId) {
            // Keep the moved task at its intended position
            task.positionInColumn = targetPosition;
          } else {
            // Update other tasks' positions based on their array index
            task.positionInColumn = index + 1;
          }
        });

        // Rebuild the full task list
        tasksByColumn[targetColumn] = targetColumnTasks;

        return Object.values(tasksByColumn).flat();
      });

      // Track pending change
      pendingChangesRef.current.add(taskId);
    },
    [projectId, enableOptimisticUpdates, queryClient, sanitizeTaskData]
  );

  // Sync task move to server
  const syncTaskMove = useCallback(
    async (taskId: string, targetColumn: string, targetPosition: number) => {
      if (!projectId) {
        throw new Error("Project ID is required for sync");
      }

      // Validate inputs
      if (!taskId || !targetColumn || targetPosition < 1) {
        throw new Error("Invalid sync parameters");
      }

      // Apply enhanced optimistic update
      applyTaskMoveOptimisticUpdate(taskId, targetColumn, targetPosition);

      try {
        // Perform server sync
        await moveTaskMutation.mutateAsync({
          taskId,
          projectId,
          newStatus: targetColumn as ColumnStatus,
          targetPosition,
        });

        // Success - clean up tracking
        rollbackDataRef.current.delete(taskId);
        pendingChangesRef.current.delete(taskId);
      } catch (error) {
        console.error("Sync error:", error);

        if (enableErrorRecovery) {
          // Rollback on error
          rollbackOptimisticUpdate(taskId);
        } else {
          // Just remove from pending if not using error recovery
          pendingChangesRef.current.delete(taskId);
        }

        throw error;
      }
    },
    [
      projectId,
      applyTaskMoveOptimisticUpdate,
      moveTaskMutation,
      enableErrorRecovery,
      rollbackOptimisticUpdate,
    ]
  );

  // Force invalidate and refresh data (used for manual refresh)
  const invalidateAndRefresh = useCallback(() => {
    if (!projectId) return;

    // Use a gentler invalidation that doesn't immediately clear the cache
    // This prevents tasks from disappearing during the refresh
    queryClient.invalidateQueries({
      queryKey: taskKeys.list(projectId),
      refetchType: "active", // Keep this for manual refresh as user expects immediate update
    });

    // Clear all pending changes and rollback data
    pendingChangesRef.current.clear();
    rollbackDataRef.current.clear();

    toast.info("Data refreshed from server");
  }, [projectId, queryClient]);

  // TODO: Implement WebSocket or Server-Sent Events for real-time updates
  // Real-time sync would be initialized here when enableRealTimeSync is true

  return {
    syncTaskMove,
    rollbackOptimisticUpdate,
    invalidateAndRefresh,
    isOnline: isOnlineRef.current,
    hasPendingChanges: pendingChangesRef.current.size > 0,
  };
}
