"use client";

import { TaskGroupModel } from "@/db/schemas/tasks.schema";
import { DragDropContext } from "@hello-pangea/dnd";
import { useEffect, useState } from "react";
import { useTasksData } from "../../../_hooks/use-tasks-data";
import { useTasksContext } from "../../tasks-provider";
import { useKanbanDrag } from "./_actions/kanban.action";
import { KanbanColumn } from "./_components/kanban-column";

export default function KanbanBoard() {
  const { projectId } = useTasksContext();

  // Get initial data from cache
  const { getKanbanColumns, isLoading } = useTasksData(projectId);
  const serverTaskGroups = getKanbanColumns();

  // State isolation: local state is initialized once and never updated externally
  const [taskGroups, setTaskGroups] = useState<TaskGroupModel[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize state only once when data becomes available
  useEffect(() => {
    if (!isInitialized && serverTaskGroups.length > 0) {
      setTaskGroups(serverTaskGroups);
      setIsInitialized(true);
    }
  }, [serverTaskGroups, isInitialized]);

  // Simple drag hook - no external interference
  const { onDragEnd, isUpdating } = useKanbanDrag({
    taskGroups,
    setTaskGroups,
    projectId: projectId!,
  });

  // Show loading only if we haven't initialized and are still loading
  if (isLoading && !isInitialized) {
    return (
      <div className="flex items-center justify-center h-64 p-6">
        <div className="text-lg text-gray-600">Loading kanban board...</div>
      </div>
    );
  }

  if (!taskGroups || taskGroups.length === 0) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-14rem)] border rounded-md bg-gray-50 text-gray-500">
        <div className="text-center">
          <h3 className="mb-2 text-xl font-medium">No columns available</h3>
          <p>Please check your task configuration</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <DragDropContext onDragEnd={onDragEnd}>
        <div className="flex flex-col md:flex-row gap-4 md:gap-6">
          {taskGroups.map((taskGroup) => (
            <KanbanColumn key={taskGroup.id} taskGroup={taskGroup} isUpdating={isUpdating} />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}
