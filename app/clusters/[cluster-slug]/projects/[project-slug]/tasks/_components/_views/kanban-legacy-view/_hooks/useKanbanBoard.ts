import type { TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import { useDataSync } from "./useDataSync";
import { useDragAndDropLegacy } from "./useDragAndDropLegacy";

/**
 * Simplified useKanbanBoard hook using cache-only approach
 *
 * Key Changes:
 * - No local state management (columns state removed)
 * - Uses TanStack Query cache as single source of truth via useTasksData
 * - Reactive columns that update when cache changes
 * - Optimistic updates handle UI feedback
 * - Preview columns show drag-over effects during drag operations
 * - Eliminates redundancy between local state and cache updates
 *
 * Flow:
 * 1. User drags task
 * 2. useDragAndDrop shows preview in target column (drag-over effect)
 * 3. useDragAndDrop calculates position and calls onTaskMove
 * 4. useDataSync applies optimistic update to cache (instant UI feedback)
 * 5. useTasksData.getKanbanColumns() re-runs due to cache change
 * 6. UI re-renders with new positions immediately
 * 7. useDataSync syncs to server
 * 8. On error: rollback cache, on success: clear rollback data
 * 9. Meaningful toast notifications show success/error from server sync
 */
export function useKanbanBoard(
  initialColumnsData: TaskGroupModel[],
  projectId?: string,
  allTasks?: TaskModel[] // Add allTasks parameter for filter handling
) {
  // Use data sync for optimistic updates
  const { syncTaskMove } = useDataSync({
    projectId,
    enableOptimisticUpdates: true,
    enableErrorRecovery: true,
  });

  // Handle task move with optimistic updates - simple async function doesn't need memoization
  const handleTaskMove = async (taskId: string, targetColumn: string, targetPosition: number) => {
    try {
      await syncTaskMove(taskId, targetColumn, targetPosition);
    } catch (error) {
      console.error("Failed to move task:", error);
      // Error handling and rollback is managed by useDataSync
    }
  };

  // Use drag and drop hook with current filtered data and allTasks for fallback
  const { activeTask, sensors, handlers, previewColumns } = useDragAndDropLegacy({
    columns: initialColumnsData, // Use current filtered data for drag operations
    allTasks: allTasks || [], // Provide allTasks for filter fallback scenarios
    onTaskMove: handleTaskMove,
  });

  // Return initial columns when not dragging, preview columns when dragging
  // Simple comparison - no need for complex memoization
  const finalColumns = previewColumns !== initialColumnsData ? previewColumns : initialColumnsData;

  return {
    columns: finalColumns,
    originalColumns: initialColumnsData, // Provide original columns for stable position calculations
    activeTask,
    sensors,
    handlers,
  };
}
