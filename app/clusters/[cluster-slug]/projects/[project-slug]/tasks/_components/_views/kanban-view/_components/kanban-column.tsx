import { Badge } from "@/components/ui/badge";
import { TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { Droppable } from "@hello-pangea/dnd";
import { LoaderCircleIcon } from "lucide-react";
import { memo, useEffect, useState } from "react";
import TaskDetails from "../../../task-details";
import { useTasksContext } from "../../../tasks-provider";
import { getColumnStyle } from "../_styles/column-styles";
import { KanbanCard } from "./kanban-card";

interface KanbanColumnProps {
  taskGroup: TaskGroupModel;
  isUpdating?: boolean;
}

// Define the component function first
function KanbanColumnComponent({ taskGroup, isUpdating = false }: KanbanColumnProps) {
  const { projectId } = useTasksContext();
  const style = getColumnStyle(taskGroup.name);
  const IconComponent = style.icon;
  const columnHeaderIconSize = "h-3.5 w-3.5";

  // Global task selection state
  const [selectedTask, setSelectedTask] = useState<TaskModel | null>(null);
  const [isTaskDetailsOpen, setIsTaskDetailsOpen] = useState(false);

  const handleTaskClick = (task: TaskModel) => {
    setSelectedTask(task);
    setIsTaskDetailsOpen(true);
  };

  // DEBUG: Render counter - DO NOT REMOVE
  const [renderCount, setRenderCount] = useState(0);
  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [taskGroup]);

  return (
    <>
      <div className="rounded-lg border shadow-2xs hover:shadow-sm transition-all duration-200 w-80 flex-shrink-0 flex flex-col h-[calc(100vh-12.5rem)]">
        <header
          className={cn(
            "py-3 px-4 flex items-center justify-between border-b flex-shrink-0",
            style.header
          )}
        >
          <div className="flex items-center gap-2">
            {isUpdating ? (
              <LoaderCircleIcon
                className={cn(columnHeaderIconSize, style.iconColor, "animate-spin")}
              />
            ) : (
              <IconComponent className={cn(columnHeaderIconSize, style.iconColor)} />
            )}
            <h2 className={cn("text-sm font-medium", style.title)}>{taskGroup.name}</h2>
          </div>
          {renderCount}
          <Badge className="text-xs px-1.5 py-0 bg-gray-200/60 text-gray-500 dark:bg-gray-800/60 dark:text-gray-400">
            {taskGroup.tasks.length}
          </Badge>
        </header>

        {/* Let the Droppable handle scrolling directly - no nested scroll containers */}
        <Droppable droppableId={taskGroup.id}>
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={cn(
                "flex-1 overflow-y-auto bg-gray-50/20 dark:bg-background p-3 md:p-4",
                "min-h-0" // Essential for flex child to shrink
              )}
            >
              {taskGroup.tasks.map((task, index) => (
                <KanbanCard
                  key={task.id}
                  task={task}
                  index={index}
                  projectId={projectId}
                  onTaskClick={handleTaskClick}
                />
              ))}
              {provided.placeholder}
              {taskGroup.tasks.length === 0 && (
                <div className="flex items-center justify-center h-32 text-sm text-muted-foreground border-2 border-dashed border-gray-200 rounded-lg">
                  Drop tasks here
                </div>
              )}
            </div>
          )}
        </Droppable>
      </div>

      {/* Global TaskDetails component */}
      {selectedTask && (
        <TaskDetails
          task={selectedTask}
          isSheetOpen={isTaskDetailsOpen}
          setIsSheetOpen={setIsTaskDetailsOpen}
          projectId={projectId}
        />
      )}
    </>
  );
}

// Memoize the column to prevent unnecessary re-renders
export const KanbanColumn = memo(KanbanColumnComponent, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.taskGroup.id === nextProps.taskGroup.id &&
    prevProps.taskGroup.name === nextProps.taskGroup.name &&
    prevProps.taskGroup.tasks.length === nextProps.taskGroup.tasks.length &&
    prevProps.isUpdating === nextProps.isUpdating &&
    // Efficient task comparison - only check if tasks array reference has changed
    // Since we maintain stable references in the parent, this is sufficient
    prevProps.taskGroup.tasks === nextProps.taskGroup.tasks
  );
});
