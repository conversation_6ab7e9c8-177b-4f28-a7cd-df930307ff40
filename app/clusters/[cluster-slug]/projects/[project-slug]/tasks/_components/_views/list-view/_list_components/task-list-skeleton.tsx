import { Skeleton } from "@/components/ui/skeleton";

// Optimized skeleton for task list to prevent Suspense fallback flashing
export function TaskListSkeleton() {
  return (
    <div className="space-y-4">
      {/* Task group headers */}
      {Array.from({ length: 3 }).map((_, groupIndex) => (
        <div key={groupIndex} className="mb-4">
          {/* Group header skeleton */}
          <div className="flex items-center justify-between gap-2 px-4 py-2 rounded-lg mb-1 bg-gray-50 dark:bg-gray-800/30">
            <div className="flex items-center gap-1.5">
              <Skeleton className="w-4 h-4 rounded-full" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-8 rounded-full" />
            </div>
            <div className="flex items-center gap-6">
              <Skeleton className="h-5 w-12 rounded-full" />
              <Skeleton className="w-4 h-4" />
            </div>
          </div>

          {/* Task list container */}
          <div className="overflow-hidden border rounded-md dark:border-gray-800/60 dark:bg-gray-900/20">
            {/* Task list header */}
            <div className="grid grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(180px,1.8fr)_minmax(140px,1fr)_minmax(90px,0.7fr)_minmax(80px,0.5fr)] gap-x-3 items-center px-4 py-2 bg-gray-50 dark:bg-gray-800/40 border-b dark:border-gray-800/60">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-8" />
            </div>

            {/* Task rows */}
            {Array.from({ length: Math.floor(Math.random() * 5) + 2 }).map((_, taskIndex) => (
              <div
                key={taskIndex}
                className="grid grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(180px,1.8fr)_minmax(140px,1fr)_minmax(90px,0.7fr)] gap-x-3 items-center px-4 py-2.5 border-b h-12 dark:border-gray-800/60"
              >
                {/* ID */}
                <div className="flex items-center gap-1.5">
                  <Skeleton className="h-3 w-12" />
                  <Skeleton className="w-3.5 h-3.5 rounded-full" />
                </div>

                {/* Name */}
                <Skeleton className="h-4 w-full max-w-[200px]" />

                {/* Life Cycle */}
                <Skeleton className="h-6 w-16 rounded-full" />

                {/* Value */}
                <Skeleton className="h-6 w-12 rounded-full" />

                {/* Tags */}
                <div className="flex gap-1">
                  <Skeleton className="h-5 w-12 rounded-full" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>

                {/* Due date */}
                <Skeleton className="h-4 w-20" />

                {/* Effort */}
                <Skeleton className="h-4 w-8" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

// Lightweight skeleton for individual task rows
export function TaskRowSkeleton() {
  return (
    <div className="grid grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(180px,1.8fr)_minmax(140px,1fr)_minmax(90px,0.7fr)] gap-x-3 items-center px-4 py-2.5 border-b h-12 dark:border-gray-800/60 animate-pulse">
      <div className="flex items-center gap-1.5">
        <Skeleton className="h-3 w-12" />
        <Skeleton className="w-3.5 h-3.5 rounded-full" />
      </div>
      <Skeleton className="h-4 w-full max-w-[200px]" />
      <Skeleton className="h-6 w-16 rounded-full" />
      <Skeleton className="h-6 w-12 rounded-full" />
      <div className="flex gap-1">
        <Skeleton className="h-5 w-12 rounded-full" />
        <Skeleton className="h-5 w-16 rounded-full" />
      </div>
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-8" />
    </div>
  );
}

// Skeleton for empty state
export function TaskListEmptySkeleton() {
  return (
    <div className="flex flex-col items-center justify-center h-80 p-12 border border-dashed rounded-md">
      <Skeleton className="w-16 h-16 rounded-full mb-4" />
      <Skeleton className="h-4 w-48 mb-2" />
      <Skeleton className="h-3 w-64" />
    </div>
  );
}
