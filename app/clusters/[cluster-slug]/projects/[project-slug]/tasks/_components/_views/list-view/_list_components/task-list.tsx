"use client";

// Re-export all components from the new modular architecture
export {
  ColumnStatusDropdown,
  EffortEditor,
  EmptyState,
  TaskGroup,
  TaskListEmptySkeleton,
  TaskListHeader,
  TaskListSkeleton,
  TaskRow,
  TaskRowSkeleton,
  TaskStatusIcon,
  TaskTag,
  ValueBadge,
} from "./index";

// Re-export types for backward compatibility
export type {
  BadgeStyleMap,
  ColorClasses,
  ColorMap,
  ColumnStatusDropdownProps,
  EffortChangeHandler,
  EffortEditorProps,
  StatusChangeHandler,
  StatusColorMap,
  TagClickHandler,
  TaskClickHandler,
  TaskColor,
  TaskGroupProps,
  TaskRowProps,
  TaskStatusIconProps,
  TaskTagProps,
  ValueBadgeProps,
} from "./index";

// Re-export constants for backward compatibility
export {
  ALL_COLUMN_STATUSES,
  BADGE_STYLES,
  EFFORT_VALIDATION_REGEX,
  MAX_VISIBLE_TAGS,
  STATUS_COLORS,
  TASK_COLOR_HOVER_CLASSES,
  TASK_GRID_COLS,
  TASK_ROW_BASE_CLASSES,
  TASK_ROW_HEIGHT,
} from "./index";

// Re-export utilities for backward compatibility
export {
  createBadgeClasses,
  createTaskGroupMemoKey,
  createTaskRowClasses,
  generateTagKey,
  generateTaskRowKey,
  getBadgeStyles,
  getTaskColorHoverClass,
  hasTasksChanged,
  isInteractiveElement,
  isValidEffortFormat,
} from "./index";

// Import the main components for default exports
import TaskGroupComponent from "./task-group";

// Default exports for backward compatibility
export default TaskGroupComponent;
