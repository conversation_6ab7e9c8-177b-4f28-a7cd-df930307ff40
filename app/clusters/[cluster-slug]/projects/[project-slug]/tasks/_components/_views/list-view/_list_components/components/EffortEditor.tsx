import { cn } from "@/lib/utils";
import { formatEffort, parseEffort } from "@/lib/utils-tasks";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { type EffortEditorProps } from "../types";
import { isValidEffortFormat } from "../utils";

/**
 * Inline effort editor component
 * Memoized to prevent unnecessary re-renders during editing
 */
export const EffortEditor = memo(function EffortEditor({
  value,
  onSave,
  onCancel,
  isTaskDetails = false,
}: EffortEditorProps) {
  const [inputValue, setInputValue] = useState(String(value));
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    inputRef.current?.focus();
    inputRef.current?.select();
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault();
        const days = parseEffort(inputValue);
        if (!isNaN(days)) {
          const formattedValue = formatEffort(days);
          setInputValue(formattedValue);
          onSave(days);
        }
      } else if (e.key === "Escape") {
        onCancel();
      }
    },
    [inputValue, onSave, onCancel]
  );

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // If it's just a number or a valid format, update the input
    if (isValidEffortFormat(value)) {
      setInputValue(value);
    }
  }, []);

  const handleBlur = useCallback(() => {
    const days = parseEffort(inputValue);
    if (!isNaN(days)) {
      const formattedValue = formatEffort(days);
      setInputValue(formattedValue);
      onSave(days);
    }
  }, [inputValue, onSave]);

  return (
    <input
      ref={inputRef}
      type="text"
      value={inputValue}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
      className={cn(
        "w-20 px-1 py-0.5 border rounded bg-white dark:bg-gray-800 dark:border-gray-700",
        isTaskDetails ? "text-sm" : "text-xs"
      )}
    />
  );
});

export default EffortEditor;
