import { memo } from "react";
import { TASK_GRID_COLS } from "../constants";

/**
 * Task list header component
 * Memoized as it never changes
 */
export const TaskListHeader = memo(function TaskListHeader() {
  return (
    <div
      className={`grid ${TASK_GRID_COLS} gap-x-3 items-center px-4 py-2 bg-gray-50 dark:bg-gray-800/40 border-b dark:border-gray-800/60`}
    >
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">ID</div>
      <div className="flex flex-1 text-xs font-medium text-gray-500 dark:text-gray-400">Name</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Life Cycle</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Value</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Tags</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Due date</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Assignee</div>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Effort</div>
    </div>
  );
});

export default TaskListHeader;
