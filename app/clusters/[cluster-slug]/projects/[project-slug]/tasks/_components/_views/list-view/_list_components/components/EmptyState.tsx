import { memo } from "react";
import { TASK_GRID_COLS, TASK_ROW_HEIGHT } from "../constants";

/**
 * Empty state component for when no tasks are available
 * Matches the exact height of a task row with discrete "add a task" text
 */
export const EmptyState = memo(function EmptyState() {
  return (
    <div
      className={`grid ${TASK_GRID_COLS} gap-x-3 items-center px-4 py-2.5 border-b ${TASK_ROW_HEIGHT} dark:border-gray-800/60`}
    >
      <div className="text-sm text-gray-400 dark:text-gray-500 col-span-full text-center">
        add a task
      </div>
    </div>
  );
});

export default EmptyState;
