import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui";
import { type ColumnStatus } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import { memo, useCallback, useEffect, useState } from "react";
import { useTaskStatusInfo } from "../../../../../utils/task-status-styles";
import { type ColumnStatusDropdownProps } from "../types";
import TaskStatusIcon from "./TaskStatusIcon";

/**
 * Column status dropdown component for changing task status
 * Memoized to prevent unnecessary re-renders
 */
export const ColumnStatusDropdown = memo(function ColumnStatusDropdown({
  initialStatus,
  onStatusChange,
  projectId,
  taskGroups,
  withLabel = false,
}: ColumnStatusDropdownProps) {
  const [currentStatus, setCurrentStatus] = useState<string>(initialStatus || "Backlog");

  // Get dynamic status info
  const { getStatusInfo, getAllStatusOptions } = useTaskStatusInfo(projectId);
  const statusOptions = getAllStatusOptions();

  useEffect(() => {
    if (initialStatus) {
      setCurrentStatus(initialStatus);
    }
  }, [initialStatus]);

  const handleStatusChange = useCallback(
    (status: string) => {
      if (status !== currentStatus) {
        setCurrentStatus(status);
        if (onStatusChange) {
          onStatusChange(status as ColumnStatus);
        }
      }
    },
    [currentStatus, onStatusChange]
  );

  const handleItemClick = useCallback(
    (e: React.MouseEvent, status: string) => {
      e.preventDefault();
      e.stopPropagation();
      handleStatusChange(status);
    },
    [handleStatusChange]
  );

  // Get current status info for display
  const currentStatusInfo = getStatusInfo(currentStatus);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div
          className={cn(
            "cursor-pointer group hover:bg-gray-100 dark:hover:bg-gray-800 p-1.5 rounded-md",
            withLabel && "flex items-center gap-2"
          )}
        >
          <TaskStatusIcon
            color={currentStatusInfo.color}
            name={currentStatus}
            taskGroups={taskGroups}
            icon={currentStatusInfo.icon}
          />
          {withLabel && <span className="text-sm text-gray-600">{currentStatus}</span>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-[140px]">
        {statusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption.name}
            className={cn(
              "flex items-center justify-between text-sm",
              statusOption.name === currentStatus ? "bg-gray-50 dark:bg-gray-800/60" : ""
            )}
            onClick={(e) => handleItemClick(e, statusOption.name)}
          >
            <div className="flex items-center gap-2">
              <TaskStatusIcon
                color={statusOption.color}
                name={statusOption.name}
                taskGroups={taskGroups}
                icon={statusOption.icon}
              />
              <span>{statusOption.name}</span>
            </div>
            {statusOption.name === currentStatus && (
              <Check className="w-3 h-3 text-gray-500 dark:text-gray-400" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});

export default ColumnStatusDropdown;
