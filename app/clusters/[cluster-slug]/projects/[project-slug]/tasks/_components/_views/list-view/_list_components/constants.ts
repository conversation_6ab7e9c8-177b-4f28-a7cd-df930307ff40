import { type ColumnStatus } from "@/db/schemas/tasks.schema";
import { type BadgeStyleMap, type StatusColorMap, type TaskColor } from "./types";

// Task color hover classes for Tailwind JIT - expanded for comprehensive color support
export const TASK_COLOR_HOVER_CLASSES: Record<TaskColor, string> = {
  gray: "hover:bg-gray-50 hover:dark:bg-gray-900/40",
  blue: "hover:bg-blue-50 hover:dark:bg-blue-900/40",
  amber: "hover:bg-amber-50 hover:dark:bg-amber-900/40",
  green: "hover:bg-green-50 hover:dark:bg-green-900/40",
  red: "hover:bg-red-50 hover:dark:bg-red-900/40",
  purple: "hover:bg-purple-50 hover:dark:bg-purple-900/40",
  indigo: "hover:bg-indigo-50 hover:dark:bg-indigo-900/40",
  pink: "hover:bg-pink-50 hover:dark:bg-pink-900/40",
  orange: "hover:bg-orange-50 hover:dark:bg-orange-900/40",
  teal: "hover:bg-teal-50 hover:dark:bg-teal-900/40",
  cyan: "hover:bg-cyan-50 hover:dark:bg-cyan-900/40",
  lime: "hover:bg-lime-50 hover:dark:bg-lime-900/40",
  violet: "hover:bg-violet-50 hover:dark:bg-violet-900/40",
  emerald: "hover:bg-emerald-50 hover:dark:bg-emerald-900/40",
  rose: "hover:bg-rose-50 hover:dark:bg-rose-900/40",
  sky: "hover:bg-sky-50 hover:dark:bg-sky-900/40",
};

// All column statuses in order
export const ALL_COLUMN_STATUSES: ColumnStatus[] = [
  "Backlog",
  "To do",
  "In progress",
  "In review",
  "Done",
];

// Status to color mapping
export const STATUS_COLORS: StatusColorMap = {
  Backlog: "gray",
  "To do": "blue",
  "In progress": "amber",
  "In review": "purple",
  Done: "green",
};

// Badge styles for enhanced dark mode visibility and accessibility
export const BADGE_STYLES: BadgeStyleMap = {
  blue: "bg-blue-100 text-blue-800 dark:bg-blue-500/25 dark:text-blue-200 dark:border-blue-400/50",
  amber:
    "bg-amber-100 text-amber-800 dark:bg-amber-500/25 dark:text-amber-200 dark:border-amber-400/50",
  purple:
    "bg-purple-100 text-purple-800 dark:bg-purple-500/25 dark:text-purple-200 dark:border-purple-400/50",
  green:
    "bg-green-100 text-green-800 dark:bg-green-500/25 dark:text-green-200 dark:border-green-400/50",
  orange:
    "bg-orange-100 text-orange-800 dark:bg-orange-500/25 dark:text-orange-200 dark:border-orange-400/50",
  red: "bg-red-100 text-red-800 dark:bg-red-500/25 dark:text-red-200 dark:border-red-400/50",
  gray: "bg-gray-100 text-gray-800 dark:bg-gray-500/25 dark:text-gray-200 dark:border-gray-400/50",
  indigo:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-500/25 dark:text-indigo-200 dark:border-indigo-400/50",
  pink: "bg-pink-100 text-pink-800 dark:bg-pink-500/25 dark:text-pink-200 dark:border-pink-400/50",
  teal: "bg-teal-100 text-teal-800 dark:bg-teal-500/25 dark:text-teal-200 dark:border-teal-400/50",
  cyan: "bg-cyan-100 text-cyan-800 dark:bg-cyan-500/25 dark:text-cyan-200 dark:border-cyan-400/50",
  lime: "bg-lime-100 text-lime-800 dark:bg-lime-500/25 dark:text-lime-200 dark:border-lime-400/50",
  violet:
    "bg-violet-100 text-violet-800 dark:bg-violet-500/25 dark:text-violet-200 dark:border-violet-400/50",
  emerald:
    "bg-emerald-100 text-emerald-800 dark:bg-emerald-500/25 dark:text-emerald-200 dark:border-emerald-400/50",
  rose: "bg-rose-100 text-rose-800 dark:bg-rose-500/25 dark:text-rose-200 dark:border-rose-400/50",
  sky: "bg-sky-100 text-sky-800 dark:bg-sky-500/25 dark:text-sky-200 dark:border-sky-400/50",
};

// Grid layout classes for task list (includes Assignee and Actions columns)
export const TASK_GRID_COLS =
  "grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(90px,1.4fr)_minmax(140px,1fr)_minmax(90px,0.5fr)_minmax(90px,0.7fr)]";

// Task row height
export const TASK_ROW_HEIGHT = "h-12";

// Common CSS classes
export const TASK_ROW_BASE_CLASSES = `grid ${TASK_GRID_COLS} gap-x-3 items-center px-4 py-2.5 border-b cursor-pointer ${TASK_ROW_HEIGHT} dark:border-gray-800/60 dark:bg-gray-950/10`;

// Tag display limits
export const MAX_VISIBLE_TAGS = 2;

// Effort input validation regex
export const EFFORT_VALIDATION_REGEX = /^\d+$|^\d+w(\d+d)?$|^\d+d$/;
