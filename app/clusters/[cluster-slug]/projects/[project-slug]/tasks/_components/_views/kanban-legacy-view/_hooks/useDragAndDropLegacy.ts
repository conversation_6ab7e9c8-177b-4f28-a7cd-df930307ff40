import type { TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { useCallback, useState } from "react";

interface UseDragAndDropProps {
  columns: TaskGroupModel[];
  allTasks: TaskModel[]; // Add allTasks to handle filter scenarios
  onTaskMove: (taskId: string, targetColumn: string, targetPosition: number) => void;
}

interface UseDragAndDropReturn {
  activeTask: TaskModel | null;
  sensors: ReturnType<typeof useSensors>;
  handlers: {
    handleDragStart: (event: DragStartEvent) => void;
    handleDragOver: (event: DragOverEvent) => void;
    handleDragEnd: (event: DragEndEvent) => void;
  };
  isDragging: boolean;
  previewColumns: TaskGroupModel[]; // Preview columns with drag-over effects
}

/**
 * Hook for managing drag-and-drop functionality in the Kanban board
 *
 * Features:
 * - Optimized sensors with activation constraints
 * - Keyboard navigation support
 * - Memoized handlers for performance
 * - Cross-column and same-column reordering
 * - Preview columns with drag-over effects for visual feedback
 * - Early return optimization: prevents server updates when task is dropped at original position
 */
export function useDragAndDropLegacy({
  columns,
  allTasks,
  onTaskMove,
}: UseDragAndDropProps): UseDragAndDropReturn {
  const [activeTask, setActiveTask] = useState<TaskModel | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Track original position and column for early return optimization
  const [originalPosition, setOriginalPosition] = useState<{
    taskId: string;
    columnName: string;
    position: number;
  } | null>(null);

  // Use columns directly for preview when not dragging, or maintain preview state during drag
  const [dragPreview, setDragPreview] = useState<TaskGroupModel[] | null>(null);

  // Return either the drag preview or the base columns
  const previewColumns = isDragging && dragPreview ? dragPreview : columns;

  // Optimized sensors with better activation constraints
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 2, // Reduced from 8px for better responsiveness
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates, // Proper keyboard navigation
    })
  );

  // Enhanced helper to find container for a given ID, with fallback to allTasks
  const findContainer = useCallback(
    (id: string): string | undefined => {
      // Check if it's a task ID in filtered columns first
      const taskContainer = columns.find((col) => col.tasks.some((task) => task.id === id))?.id;
      if (taskContainer) return taskContainer;

      // Fallback: check in allTasks for tasks that might be filtered out
      const taskInAllTasks = allTasks.find((task) => task.id === id);
      if (taskInAllTasks) {
        // Find the column that should contain this task based on its status
        const targetColumn = columns.find((col) => col.name === taskInAllTasks.status);
        if (targetColumn) return targetColumn.id;
      }

      // Check if it's a drop zone ID (format: columnId-empty or columnId-bottom)
      if (id.includes("-empty") || id.includes("-bottom")) {
        const columnId = id.replace("-empty", "").replace("-bottom", "");
        return columns.find((col) => col.id === columnId)?.id;
      }

      // Check if it's a direct column ID
      return columns.find((col) => col.id === id)?.id;
    },
    [columns, allTasks]
  );

  // Enhanced drag start handler with fallback to allTasks
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const id = active.id.toString();

      setIsDragging(true);

      // First try to find task in filtered columns
      const taskContainer = columns.find((col) => col.tasks.some((t) => t.id === id));
      let task = taskContainer?.tasks.find((t) => t.id === id);

      // Fallback: find task in allTasks if not found in filtered columns
      // This handles cases where optimistic updates moved the task out of current filter
      if (!task) {
        task = allTasks.find((t) => t.id === id);
      }

      if (!task || task.isDummy) {
        return;
      }

      setActiveTask(task);

      // Track original position for early return optimization
      setOriginalPosition({
        taskId: task.id,
        columnName: task.status || "",
        position: task.positionInColumn || 1,
      });

      // Clear any previous drag preview when starting new drag
      setDragPreview(null);
    },
    [columns, allTasks]
  );

  // Memoized drag over handler
  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { active, over } = event;
      if (!over) return;

      const activeId = active.id.toString();
      const overId = over.id.toString();

      // If we're over the same task we're dragging, no update needed
      if (activeId === overId) {
        return;
      }

      const activeContainer = findContainer(activeId);
      let overContainer = findContainer(overId);

      // If we can't find the over container, check if it's a column
      if (!overContainer) {
        for (const col of columns) {
          if (col.id === overId) {
            overContainer = col.id;
            break;
          }
        }
      }

      if (!activeContainer || !overContainer) {
        return;
      }

      // Get the active task for preview with fallback to allTasks
      let activeTask = columns
        .find((col) => col.tasks.some((task) => task.id === activeId))
        ?.tasks.find((task) => task.id === activeId);

      // Fallback to allTasks if task not found in filtered columns
      if (!activeTask) {
        activeTask = allTasks.find((task) => task.id === activeId);
      }

      if (!activeTask || activeTask.isDummy) return;

      if (activeContainer === overContainer) {
        // Dragging back to original column - clear preview to show base columns
        setDragPreview(null);
        return;
      }

      // Cross-column preview: show task in target column temporarily
      setDragPreview(
        columns.map((column) => {
          if (column.id === activeContainer) {
            // Remove task from source column in preview
            return {
              ...column,
              tasks: column.tasks.filter((task) => task.id !== activeId),
            };
          } else if (column.id === overContainer) {
            // Add task to target column in preview
            return {
              ...column,
              tasks: [...column.tasks.filter((t) => !t.isDummy), activeTask],
            };
          }
          return column;
        })
      );
    },
    [findContainer, columns, allTasks]
  );

  // Memoized drag end handler
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      setIsDragging(false);
      setActiveTask(null);
      // Clear drag preview when drag ends
      setDragPreview(null);

      if (!over) {
        // Clear original position tracking when drag is cancelled
        setOriginalPosition(null);
        return;
      }

      const activeId = active.id.toString();
      const overId = over.id.toString();

      // Quick exit for same element
      if (activeId === overId) return;

      const activeContainer = findContainer(activeId);
      const overContainer = findContainer(overId);

      if (!activeContainer || !overContainer) return;

      // Find active task with fallback to allTasks
      let activeTask = columns
        .find((col) => col.tasks.some((task) => task.id === activeId))
        ?.tasks.find((task) => task.id === activeId);

      // Fallback to allTasks if task not found in filtered columns
      if (!activeTask) {
        activeTask = allTasks.find((task) => task.id === activeId);
      }

      if (!activeTask || activeTask.isDummy) return;

      // Calculate new position
      let newPosition = 1;
      const targetColumn = columns.find((col) => col.id === overContainer);

      if (targetColumn) {
        const isDropZone = overId.includes("-empty") || overId.includes("-bottom");
        const overTask = columns
          .find((col) => col.tasks.some((task) => task.id === overId))
          ?.tasks.find((task) => task.id === overId);

        if (activeContainer === overContainer) {
          // Same column reordering
          if (isDropZone || !overTask || overTask.isDummy) {
            const realTasks = targetColumn.tasks.filter((t) => !t.isDummy && t.id !== activeId);
            newPosition = realTasks.length + 1;
          } else {
            // Active task always takes the target's position
            // When moving down: target and tasks between move UP
            // When moving up: target and tasks between move DOWN
            newPosition = overTask.positionInColumn || 1;
          }
        } else {
          // Cross-column move
          if (isDropZone || !overTask || overTask.isDummy) {
            // Drop on empty area or drop zone - add to end
            const realTasks = targetColumn.tasks.filter((t) => !t.isDummy);
            newPosition = realTasks.length + 1;
          } else {
            // Drop on specific task - insert at that position
            // The active task will take the target's position
            // All tasks at that position and below will shift down (+1)
            newPosition = overTask.positionInColumn || 1;
          }
        }
      }

      // Early return: Check if task is dropped at its original position
      if (
        originalPosition &&
        originalPosition.taskId === activeId &&
        originalPosition.columnName === targetColumn?.name &&
        originalPosition.position === newPosition
      ) {
        // Task dropped at original position - no update needed
        setOriginalPosition(null);
        return;
      }

      // Clear original position tracking
      setOriginalPosition(null);

      // Note: Local state updates removed - optimistic updates in cache will handle UI feedback

      // Notify parent about the move for server persistence
      if (onTaskMove && targetColumn) {
        onTaskMove(activeId, targetColumn.name, newPosition);
      }
    },
    [findContainer, columns, allTasks, onTaskMove, originalPosition]
  );

  return {
    activeTask,
    sensors,
    handlers: {
      handleDragStart,
      handleDragOver,
      handleDragEnd,
    },
    isDragging,
    previewColumns,
  };
}
