import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { memo } from "react";
import { getTagListViewStyles } from "../../../../../utils/tag-styles";
import { type TaskTagProps } from "../types";

/**
 * Individual task tag component
 * Memoized to prevent unnecessary re-renders when tag list changes
 */
export const TaskTag = memo(function TaskTag({ tag, onClick }: TaskTagProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick(e, tag.name);
    }
  };

  return (
    <Badge
      variant="outline"
      className={cn(
        "border font-normal text-[11px] py-0.5 px-2 rounded-full whitespace-nowrap overflow-hidden text-ellipsis max-w-[90px] cursor-pointer transition-all duration-200",
        getTagListViewStyles(tag.name),
        // Enhanced hover states for better dark mode interaction
        "hover:shadow-sm",
        "hover:bg-opacity-80 dark:hover:bg-opacity-30",
        "hover:border-opacity-100 dark:hover:border-opacity-80"
      )}
      title={tag.name}
      onClick={handleClick}
    >
      {tag.name}
    </Badge>
  );
});

export default TaskTag;
