import {
  type ColumnStatus,
  type TagModel,
  type TaskGroupModel,
  type TaskModel,
  type Value,
} from "@/db/schemas/tasks.schema";
import { LucideIcon } from "lucide-react";

// Component-specific interfaces
export interface TaskRowProps {
  task: TaskModel;
  color: string;
  columnStatus?: React.ReactNode;
  onStatusChange?: (taskId: string, newStatus: ColumnStatus) => void;
  onEffortChange?: (taskId: string, effort: number) => void;
  onTaskClick?: (task: TaskModel) => void;
  openTaskDetails?: () => void;
  projectId?: string;
}

export interface TaskGroupProps {
  taskGroup: TaskGroupModel;
  onStatusChange?: (taskId: string, newStatus: ColumnStatus) => void;
  onEffortChange?: (taskId: string, effort: number) => void;
  onTaskClick?: (task: TaskModel) => void;
  openTaskDetails?: () => void;
  projectId?: string;
}

export interface ValueBadgeProps {
  taskValue: Value;
}

export interface TaskTagProps {
  tag: TagModel;
  onClick?: (e: React.MouseEvent, tagName: string) => void;
}

export interface TaskStatusIconProps {
  color: string;
  name: string;
  taskGroups?: TaskGroupModel[];
  icon?: LucideIcon;
  projectId?: string;
}

export interface ColumnStatusDropdownProps {
  initialStatus: string;
  onStatusChange?: (status: ColumnStatus) => void;
  projectId?: string;
  taskGroups?: TaskGroupModel[];
  withLabel?: boolean;
}

export interface EffortEditorProps {
  value: number;
  onSave: (value: number) => void;
  onCancel: () => void;
  isTaskDetails?: boolean;
}

// Event handler types
export type TaskClickHandler = (task: TaskModel) => void;
export type StatusChangeHandler = (taskId: string, newStatus: ColumnStatus) => void;
export type EffortChangeHandler = (taskId: string, effort: number) => void;
export type TagClickHandler = (e: React.MouseEvent, tagName: string) => void;

// Color mapping types - expanded to include all tag colors for comprehensive dark mode support
export type TaskColor =
  | "gray"
  | "blue"
  | "amber"
  | "green"
  | "red"
  | "purple"
  | "indigo"
  | "pink"
  | "orange"
  | "teal"
  | "cyan"
  | "lime"
  | "violet"
  | "emerald"
  | "rose"
  | "sky";

export interface ColorClasses {
  bg: string;
  text: string;
  border: string;
}

export type ColorMap = Record<TaskColor, ColorClasses>;

// Status color mapping
export type StatusColorMap = Record<ColumnStatus, TaskColor>;

// Badge style types
export type BadgeStyleMap = Record<TaskColor, string>;
