"use client";

import { type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { type DropResult } from "@hello-pangea/dnd";
import { useMutation } from "@tanstack/react-query";
import { useCallback } from "react";
import { toast } from "sonner";

interface UseKanbanDragProps {
  taskGroups: TaskGroupModel[];
  setTaskGroups: (taskGroups: TaskGroupModel[]) => void;
  projectId: string;
}

interface KanbanDragResult {
  onDragEnd: (result: DropResult) => void;
  isUpdating: boolean;
}

/**
 * Fractional positioning system for efficient kanban updates
 */
class FractionalPositioning {
  private static readonly INITIAL_POSITION = 1000;
  private static readonly MIN_DISTANCE = 1;

  static calculatePosition(beforePos: number | null, afterPos: number | null): number {
    if (beforePos === null && afterPos === null) return this.INITIAL_POSITION;
    if (beforePos === null && afterPos !== null) return Math.max(afterPos / 2, this.MIN_DISTANCE);
    if (beforePos !== null && afterPos === null) return beforePos + this.INITIAL_POSITION;

    if (beforePos !== null && afterPos !== null) {
      const midpoint = (beforePos + afterPos) / 2;
      if (Math.abs(afterPos - beforePos) < this.MIN_DISTANCE * 2) {
        return beforePos + (afterPos - beforePos) / 2; // Rebalance
      }
      return midpoint;
    }

    return this.INITIAL_POSITION;
  }

  static getNewPosition(tasks: TaskModel[], targetIndex: number, sourceIndex?: number): number {
    const sortedTasks = [...tasks].sort(
      (a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)
    );
    const availableTasks =
      sourceIndex !== undefined
        ? sortedTasks.filter((_, index) => index !== sourceIndex)
        : sortedTasks;
    const beforeTask = targetIndex > 0 ? availableTasks[targetIndex - 1] : null;
    const afterTask = targetIndex < availableTasks.length ? availableTasks[targetIndex] : null;
    return this.calculatePosition(
      beforeTask?.positionInColumn || null,
      afterTask?.positionInColumn || null
    );
  }
}

/**
 * Update task positions in the database
 */
async function updateTaskPositionsInDB(
  taskId: string,
  status: string,
  positionInColumn: number,
  projectId: string
): Promise<void> {
  const { updateTask } = await import("../../../../_actions/tasks.actions");
  const result = await updateTask(taskId, {
    status,
    positionInColumn,
    project_id: projectId,
    tenant_id: "",
  });

  if (result.error) throw result.error;
}

/**
 * Simple kanban drag hook following the original hello-pangea/dnd pattern
 */
export function useKanbanDrag({
  taskGroups,
  setTaskGroups,
  projectId,
}: UseKanbanDragProps): KanbanDragResult {
  const syncPositionsMutation = useMutation({
    mutationFn: (variables: {
      taskId: string;
      status: string;
      positionInColumn: number;
      taskTitle: string;
    }) =>
      updateTaskPositionsInDB(
        variables.taskId,
        variables.status,
        variables.positionInColumn,
        projectId
      ),

    onSuccess: () => {
      toast.success(`Task moved successfully`);
    },

    onError: (error: Error, variables) => {
      toast.error("Failed to sync task position", {
        description: `Task "${variables.taskTitle}": ${error.message}`,
      });
      // Note: We don't revert the UI state since user experience is more important
      // The next refresh will show the correct state if there was a real conflict
    },
  });

  const onDragEndCallback = useCallback(
    (result: DropResult) => {
      const { destination, source, draggableId } = result;

      // If dropped outside a droppable area or in the same position
      if (
        !destination ||
        (destination.droppableId === source.droppableId && destination.index === source.index)
      ) {
        return;
      }

      // Convert taskGroups to a columns object for easier manipulation
      const columns: Record<string, TaskGroupModel> = {};
      taskGroups.forEach((group) => {
        columns[group.id] = group;
      });

      const sourceColumn = columns[source.droppableId];
      const destinationColumn = columns[destination.droppableId];

      if (!sourceColumn || !destinationColumn) return;

      // Moving within the same column
      if (sourceColumn === destinationColumn) {
        const newTasks = Array.from(sourceColumn.tasks);
        const [movedTask] = newTasks.splice(source.index, 1);

        // Calculate new position using fractional positioning
        const newPosition = FractionalPositioning.getNewPosition(
          newTasks,
          destination.index,
          source.index
        );

        // Update the task with new position
        const updatedTask = {
          ...movedTask,
          positionInColumn: newPosition,
        };

        newTasks.splice(destination.index, 0, updatedTask);

        const newColumn = {
          ...sourceColumn,
          tasks: newTasks,
        };

        const newTaskGroups = taskGroups.map((group) =>
          group.id === newColumn.id ? newColumn : group
        );

        setTaskGroups(newTaskGroups);

        // Sync with database (fire and forget)
        syncPositionsMutation.mutate({
          taskId: movedTask.id,
          status: sourceColumn.name,
          positionInColumn: newPosition,
          taskTitle: movedTask.title,
        });
      } else {
        // Moving between different columns
        const sourceTasks = Array.from(sourceColumn.tasks);
        const destinationTasks = Array.from(destinationColumn.tasks);
        const [movedTask] = sourceTasks.splice(source.index, 1);

        // Calculate new position using fractional positioning
        const newPosition = FractionalPositioning.getNewPosition(
          destinationTasks,
          destination.index
        );

        // Update the task with new status and position
        const updatedTask = {
          ...movedTask,
          status: destinationColumn.name,
          positionInColumn: newPosition,
        };

        destinationTasks.splice(destination.index, 0, updatedTask);

        const newSourceColumn = {
          ...sourceColumn,
          tasks: sourceTasks,
        };

        const newDestinationColumn = {
          ...destinationColumn,
          tasks: destinationTasks,
        };

        const newTaskGroups = taskGroups.map((group) => {
          if (group.id === newSourceColumn.id) return newSourceColumn;
          if (group.id === newDestinationColumn.id) return newDestinationColumn;
          return group;
        });

        setTaskGroups(newTaskGroups);

        // Sync with database (fire and forget)
        syncPositionsMutation.mutate({
          taskId: movedTask.id,
          status: destinationColumn.name,
          positionInColumn: newPosition,
          taskTitle: movedTask.title,
        });
      }
    },
    [taskGroups, setTaskGroups, syncPositionsMutation]
  );

  return {
    onDragEnd: onDragEndCallback,
    isUpdating: syncPositionsMutation.isPending,
  };
}
