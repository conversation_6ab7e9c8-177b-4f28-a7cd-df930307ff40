"use client";

import { Badge } from "@/components/ui";
import { Skeleton } from "@/components/ui/skeleton";
import { type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { closestCorners, DndContext, DragOverlay, type CollisionDetection } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { lazy, memo, Suspense, useMemo, useState } from "react";
import { getColumnStyle } from "../../../utils/column-styles";
import TaskDetails from "../../task-details";
import { useKanbanBoard } from "./_hooks/useKanbanBoard";
import { DropZone } from "./_kanban_components/drop-zone";
import { DroppableColumn } from "./_kanban_components/droppable-column";
import { TaskCard } from "./_kanban_components/task-card";

const SortableTask = lazy(() => import("./_kanban_components/sortable-task"));

// Memoized column component to prevent unnecessary re-renders
const KanbanColumn = memo(function KanbanColumn({
  column,
  originalColumn,
  isCompactMode,
  projectId,
  onTaskClick,
}: {
  column: TaskGroupModel;
  originalColumn?: TaskGroupModel;
  isCompactMode: boolean;
  projectId?: string;
  onTaskClick: (task: TaskModel) => void;
}) {
  const style = getColumnStyle(column.name);
  const IconComponent = style.icon;
  const columnHeaderIconSize = "h-3.5 w-3.5";

  // Memoize the filtered tasks to prevent recreation on every render
  const realTasks = useMemo(
    () => column.tasks.filter((task: TaskModel) => !task.isDummy),
    [column.tasks]
  );

  // Create a stable sortableItems array
  const sortableItems = useMemo(() => {
    return realTasks.map((task) => task.id);
  }, [realTasks]);

  // Create stable task position mapping using original column data to prevent re-renders during drag preview
  // This ensures isFirstTask/isLastTask don't change during drag operations
  const taskPositions = useMemo(() => {
    const positions = new Map<string, { isFirst: boolean; isLast: boolean }>();
    // Use original column data if available, otherwise fall back to current column
    const sourceColumn = originalColumn || column;
    const sourceTasks = sourceColumn.tasks.filter((task: TaskModel) => !task.isDummy);

    sourceTasks.forEach((task, index) => {
      positions.set(task.id, {
        isFirst: index === 0,
        isLast: index === sourceTasks.length - 1,
      });
    });
    return positions;
  }, [originalColumn, column]);

  return (
    <div className="rounded-lg border shadow-2xs hover:shadow-sm transition-all duration-200 w-full flex flex-col overflow-hidden h-[calc(100vh-16rem)]">
      <header
        className={cn(
          "py-3 px-4 flex items-center justify-between border-b flex-shrink-0",
          style.header
        )}
      >
        <div className="flex items-center gap-2">
          <IconComponent className={cn(columnHeaderIconSize, style.iconColor)} />
          <h2 className={cn("text-sm font-medium", style.title)}>{column.name}</h2>
        </div>
        <Badge className="text-xs px-1.5 py-0 bg-gray-200/60 text-gray-500 dark:bg-gray-800/60 dark:text-gray-400">
          {realTasks.length}
        </Badge>
      </header>
      <DroppableColumn
        id={column.id}
        className="flex-1 overflow-y-auto bg-gray-50/20 dark:bg-background min-h-0"
      >
        <div className="p-3 md:p-4">
          <SortableContext items={sortableItems} strategy={verticalListSortingStrategy}>
            <div className={cn("min-h-[50px]", isCompactMode ? "space-y-0" : "space-y-2")}>
              {realTasks.length === 0 ? (
                // Empty column drop zone
                <DropZone id={`${column.id}-empty`} isEmpty={true} />
              ) : (
                realTasks.map((task: TaskModel) => {
                  // Use stable position mapping instead of index-based calculation
                  const position = taskPositions.get(task.id);
                  const isFirstTask = position?.isFirst ?? false;
                  const isLastTask = position?.isLast ?? false;

                  return (
                    <Suspense key={task.id} fallback={<Skeleton className="w-full h-20 px-6" />}>
                      <SortableTask
                        key={task.id}
                        task={task}
                        columnId={column.id}
                        isFirstTask={isFirstTask}
                        isLastTask={isLastTask}
                        isCompactMode={isCompactMode}
                        projectId={projectId}
                        onTaskClick={onTaskClick}
                      />
                    </Suspense>
                  );
                })
              )}
            </div>
          </SortableContext>

          {/* Bottom drop zone - only show if column has tasks */}
          {realTasks.length > 0 && (
            <DropZone id={`${column.id}-bottom`} isActive={false} className="mt-2" />
          )}
        </div>
      </DroppableColumn>
    </div>
  );
});

// Simplified collision detection that handles tasks, drop zones, and columns
const customCollisionDetection: CollisionDetection = (args) => {
  const closestIntersection = closestCorners(args);

  if (closestIntersection.length > 0) {
    // Prioritize collisions in this order: task > drop-zone > column
    for (const collision of closestIntersection) {
      const container = args.droppableContainers.find((c) => c.id === collision.id);

      if (!container) continue;

      const containerType = container.data.current?.type;

      // Skip if this is the active task (can't drop on itself)
      if (collision.id === args.active.id) continue;

      // Prioritize task collisions for precise positioning
      if (containerType === "task") {
        return [{ id: collision.id }];
      }

      // Then drop zones for end-of-column drops
      if (containerType === "drop-zone") {
        return [{ id: collision.id }];
      }

      // Finally column collisions as fallback
      if (containerType === "column") {
        return [{ id: collision.id }];
      }
    }
  }

  return [];
};

// KanbanBoard component with memo applied at export with custom comparison
function KanbanLegacyBoard({
  isCompactMode,
  taskGroups = [],
  projectId,
  allTasks,
}: KanbanBoardProps) {
  const { columns, originalColumns, activeTask, sensors, handlers } = useKanbanBoard(
    taskGroups,
    projectId,
    allTasks
  );

  // Global task selection state
  const [selectedTask, setSelectedTask] = useState<TaskModel | null>(null);
  const [isTaskDetailsOpen, setIsTaskDetailsOpen] = useState(false);

  const handleTaskClick = (task: TaskModel) => {
    setSelectedTask(task);
    setIsTaskDetailsOpen(true);
  };

  if (!columns || columns.length === 0) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-14rem)] border rounded-md bg-gray-50 text-gray-500">
        <div className="text-center">
          <h3 className="mb-2 text-xl font-medium">No columns available</h3>
          <p>Please check your task configuration</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={customCollisionDetection}
        onDragStart={handlers.handleDragStart}
        onDragOver={handlers.handleDragOver}
        // onDragEnd={handlers.handleDragEnd}
      >
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 justify-evenly overflow-x-auto">
          {columns.map((column) => {
            // Find the corresponding original column for stable position calculations
            const originalColumn = originalColumns.find((orig) => orig.id === column.id);
            return (
              <KanbanColumn
                key={column.id}
                column={column}
                originalColumn={originalColumn}
                isCompactMode={isCompactMode}
                projectId={projectId}
                onTaskClick={handleTaskClick}
              />
            );
          })}
        </div>
        <DragOverlay>
          {activeTask ? (
            <div>
              <TaskCard
                task={activeTask}
                isCompactMode={isCompactMode}
                isDragOverlay
                projectId={projectId}
              />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Global TaskDetails component */}
      {selectedTask && (
        <TaskDetails
          task={selectedTask}
          isSheetOpen={isTaskDetailsOpen}
          setIsSheetOpen={setIsTaskDetailsOpen}
          projectId={projectId}
        />
      )}
    </>
  );
}

interface KanbanBoardProps {
  isCompactMode: boolean;
  taskGroups: TaskGroupModel[];
  projectId: string;
  allTasks?: TaskModel[]; // Add allTasks for filter handling
}

// Use standard React.memo() - React's built-in comparison is more efficient
// than our custom deep comparison for this use case
export default memo(KanbanLegacyBoard);
