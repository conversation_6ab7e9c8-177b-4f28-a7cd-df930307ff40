"use client";

import { AssigneeDisplay } from "@/components/tasks/tasks-assignee-display";
import TaskDeletionConfirmDialog from "@/components/tasks/tasks-deletion-confirm-dialog";
import { TaskAdditionalMenu } from "@/components/tasks/tasks-menu-dropdown";
import { Badge } from "@/components/ui/badge";
import { TagModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { formatEffort, getValueIcon } from "@/lib/utils-tasks";
import { Draggable } from "@hello-pangea/dnd";
import { Ellipsis } from "lucide-react";
import { memo, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { useDeleteTask } from "../../../../_hooks/use-crud-tasks";
import { getTagKanbanViewStyles } from "../../../../utils/tag-styles";
import { SeperatorVertical } from "./separator-vertical";

interface KanbanCardProps {
  task: TaskModel;
  index: number;
  isDragOverlay?: boolean;
  projectId?: string;
  onTaskClick?: (task: TaskModel) => void;
}

export const KanbanCard = memo(
  function KanbanCard({
    task,
    index,
    isDragOverlay = false,
    projectId,
    onTaskClick,
  }: KanbanCardProps) {
    // Debug: Render counter
    const renderCount = useRef(0);
    useEffect(() => {
      renderCount.current += 1;
    });

    // Local delete dialog state
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [taskToDelete, setTaskToDelete] = useState<TaskModel | null>(null);
    const deleteTaskMutation = useDeleteTask();

    const handleCardClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      onTaskClick?.(task);
    };

    // Task deletion logic
    const showDeleteDialog = (taskToDelete: TaskModel) => {
      setTaskToDelete(taskToDelete);
      setIsDeleteDialogOpen(true);
    };

    const handleDeleteTask = async () => {
      if (!projectId || !taskToDelete) {
        toast.error("Cannot delete task: Missing required information");
        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
        return;
      }

      try {
        await deleteTaskMutation.mutateAsync({
          taskId: taskToDelete.id,
          projectId,
        });

        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
      } catch {
        toast.error("Failed to delete task. Please try again.");
        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
      }
    };

    const { lucideIcon: ValueIcon } = getValueIcon(task.value);

    return (
      <Draggable draggableId={task.id} index={index}>
        {(provided, snapshot) => (
          <>
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              className={cn(
                "flex flex-col justify-between backdrop-blur-md border-b border-x border-[#e5e7eb] dark:border-[#30363d] p-2 shadow-none hover:shadow-xs transition-shadow duration-200 cursor-pointer group relative mb-2",
                // Add consistent spacing - replace parent's space-y-2 with individual margins
                "border-t rounded-lg",
                isDragOverlay && "border rounded-lg ring-gray-200"
                // Disable interactions when dragging to prevent max rerender issues
                // snapshot.isDragging && "pointer-events-none"
              )}
              onClick={handleCardClick}
            >
              <header className="flex flex-row items-center justify-between">
                <div className="flex flex-row flex-wrap gap-1">
                  {task?.tags?.map((tag: TagModel, index: number) => (
                    <Badge
                      key={tag.id || `tag-${tag.name}-${index}`}
                      variant="outline"
                      className={cn(
                        "w-fit px-1.5 py-0.2 text-tiny! font-medium border-[.08rem]",
                        getTagKanbanViewStyles(tag.name)
                      )}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
                <div className="flex flex-row items-center gap-1">
                  <span className="text-xs text-gray-400">{formatEffort(task.effort)}</span>

                  <TaskAdditionalMenu
                    task={task}
                    projectId={projectId}
                    onDelete={(taskToDelete) =>
                      !snapshot.isDragging && showDeleteDialog(taskToDelete || task)
                    }
                  >
                    <Ellipsis className="text-gray-400 size-3" />
                  </TaskAdditionalMenu>
                </div>
              </header>

              {/* Compact mode only affects padding - simple CSS change */}
              <div className={cn("flex-1 pt-2 cursor-pointer pb-3")}>
                <span className="text-[#374151] dark:text-[#c9d1d9] text-sm font-medium line-clamp-2">
                  {task.title}
                </span>
              </div>

              <footer className="flex flex-row items-center justify-between gap-2 text-xs">
                <div className="flex flex-row items-center gap-2">
                  <span className="text-[11px]">{task.key}</span>

                  <SeperatorVertical height="4" />
                  <Badge variant="outline" className="text-tiny! capitalize">
                    {task.lifeCycleStatus?.replace("-", " ") || "created"}
                  </Badge>
                  <Badge variant="outline" className="text-tiny!">
                    <ValueIcon className="w-3 h-3" />
                    <span className="text-[10px]">{task.value}</span>
                  </Badge>
                </div>
                {/* Debug: Render counter badge */}
                {process.env.NODE_ENV === "development" && (
                  <Badge variant="secondary" className="h-4 w-4 text-[9px] font-mono font-bold">
                    {renderCount.current}
                  </Badge>
                )}
                <AssigneeDisplay assignees={task.assignees || []} variant="card" maxVisible={2} />
              </footer>
            </div>

            {/* Use the shared TaskDeletionConfirmDialog component */}
            <TaskDeletionConfirmDialog
              task={taskToDelete}
              isOpen={isDeleteDialogOpen}
              isDeleting={deleteTaskMutation.isPending}
              onOpenChange={setIsDeleteDialogOpen}
              onConfirm={handleDeleteTask}
              variant="danger"
            />
          </>
        )}
      </Draggable>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison to prevent unnecessary re-renders
    // Include all properties that affect rendering to ensure proper updates
    return (
      prevProps.task.id === nextProps.task.id &&
      prevProps.task.title === nextProps.task.title &&
      prevProps.task.status === nextProps.task.status &&
      prevProps.task.effort === nextProps.task.effort &&
      prevProps.task.value === nextProps.task.value &&
      prevProps.task.positionInColumn === nextProps.task.positionInColumn &&
      prevProps.task.lifeCycleStatus === nextProps.task.lifeCycleStatus &&
      prevProps.task.updatedAt === nextProps.task.updatedAt &&
      prevProps.task.isOptimistic === nextProps.task.isOptimistic &&
      prevProps.index === nextProps.index &&
      prevProps.isDragOverlay === nextProps.isDragOverlay &&
      prevProps.projectId === nextProps.projectId &&
      // Efficient tags comparison - check length first, then IDs
      prevProps.task.tags?.length === nextProps.task.tags?.length &&
      (prevProps.task.tags?.every((tag, i) => tag.id === nextProps.task.tags?.[i]?.id) ?? true) &&
      // Efficient assignees comparison - check length first, then IDs
      prevProps.task.assignees?.length === nextProps.task.assignees?.length &&
      (prevProps.task.assignees?.every(
        (assignee, i) => assignee.id === nextProps.task.assignees?.[i]?.id
      ) ??
        true)
    );
  }
);
