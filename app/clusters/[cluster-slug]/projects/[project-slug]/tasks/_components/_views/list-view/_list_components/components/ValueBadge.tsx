import { Badge } from "@/components/ui/badge";
import { getValueIcon } from "@/lib/utils-tasks";
import { memo } from "react";
import { type ValueBadgeProps } from "../types";

/**
 * Value badge component for displaying task value/priority
 * Memoized to prevent unnecessary re-renders
 */
export const ValueBadge = memo(function ValueBadge({ taskValue = "Not set" }: ValueBadgeProps) {
  const { value, lucideIcon: ValueIcon } = getValueIcon(taskValue);

  return (
    <Badge
      variant="outline"
      className="font-normal text-xs py-0.5 px-2.5 rounded-full border flex items-center gap-1.5"
    >
      <ValueIcon className="w-3 h-3" />
      {value}
    </Badge>
  );
});

export default ValueBadge;
