import { type TaskModel } from "@/db/schemas/tasks.schema";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { memo, useMemo } from "react";
import { TaskCard } from "./task-card";

interface SortableTaskProps {
  task: TaskModel;
  columnId: string;
  disabled?: boolean;
  isFirstTask?: boolean;
  isLastTask?: boolean;
  isCompactMode?: boolean;
  projectId?: string;
  onTaskClick?: (task: TaskModel) => void;
}

function SortableTask({
  task,
  columnId,
  disabled = false,
  isFirstTask,
  isLastTask,
  isCompactMode = false,
  projectId,
  onTaskClick,
}: SortableTaskProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: task.id,
    disabled,
    data: {
      type: "task",
      columnId,
      task,
    },
  });

  // Memoized style calculations for better performance
  const style = useMemo(
    () => ({
      transform: transform ? CSS.Transform.toString(transform) : undefined,
      transition,
      opacity: isDragging ? 0.5 : 1,
      position: "relative" as const,
      zIndex: isDragging ? 10 : 0,
    }),
    [transform, transition, isDragging]
  );

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative touch-manipulation focus:outline-none ${
        isDragging ? "pointer-events-none" : ""
      }`}
      {...attributes}
      {...listeners}
    >
      <TaskCard
        task={task}
        isFirstTask={isFirstTask}
        isLastTask={isLastTask}
        isCompactMode={isCompactMode}
        projectId={projectId}
        isDragging={isDragging}
        onTaskClick={onTaskClick}
      />
    </div>
  );
}

// Optimized memo with custom comparison for drag performance
export default memo(SortableTask, (prevProps, nextProps) => {
  // Compare all props that affect rendering
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.title === nextProps.task.title &&
    prevProps.task.status === nextProps.task.status &&
    prevProps.task.effort === nextProps.task.effort &&
    prevProps.task.value === nextProps.task.value &&
    prevProps.task.positionInColumn === nextProps.task.positionInColumn &&
    prevProps.task.lifeCycleStatus === nextProps.task.lifeCycleStatus &&
    prevProps.task.updatedAt === nextProps.task.updatedAt &&
    prevProps.task.isOptimistic === nextProps.task.isOptimistic &&
    prevProps.columnId === nextProps.columnId &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.isFirstTask === nextProps.isFirstTask &&
    prevProps.isLastTask === nextProps.isLastTask &&
    prevProps.isCompactMode === nextProps.isCompactMode &&
    prevProps.projectId === nextProps.projectId &&
    // Efficient tags comparison - check length first, then IDs
    prevProps.task.tags?.length === nextProps.task.tags?.length &&
    (prevProps.task.tags?.every((tag, i) => tag.id === nextProps.task.tags?.[i]?.id) ?? true)
  );
});
