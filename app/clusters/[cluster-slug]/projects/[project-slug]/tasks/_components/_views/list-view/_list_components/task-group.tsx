import { Badge } from "@/components/ui/badge";
import { type TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { formatEffort } from "@/lib/utils-tasks";
import { MoreHorizontal } from "lucide-react";
import { memo, useCallback, useMemo } from "react";
import {
  getTaskGroupBadgeStyles,
  getTaskGroupHeaderStyles,
} from "../../../../utils/task-group-styles";
import EmptyState from "./components/EmptyState";
import TaskListHeader from "./components/TaskListHeader";
import TaskStatusIcon from "./components/TaskStatusIcon";
import TaskRow from "./task-row";
import { type TaskGroupProps } from "./types";

/**
 * Task group component with header and task list
 * Optimized with memoization to prevent unnecessary re-renders
 */
export const TaskGroup = memo(
  function TaskGroup({
    taskGroup: initialTaskGroup,
    onStatusChange,
    onEffortChange,
    onTaskClick,
    openTaskDetails,
    projectId,
  }: TaskGroupProps) {
    // Memoize tasks to prevent unnecessary re-renders
    const tasks = useMemo(() => initialTaskGroup.tasks, [initialTaskGroup.tasks]);
    const { name, count, color } = initialTaskGroup;

    // Memoize task group header styles using the dynamic color from database
    const taskGroupHeaderStyles = useMemo(() => getTaskGroupHeaderStyles(color), [color]);

    const isEmpty = tasks.length === 0;

    // Memoize total effort calculation
    const totalEffort = useMemo(
      () => tasks.reduce((sum: number, task: TaskModel) => sum + (task.effort || 0), 0),
      [tasks]
    );

    // Memoize effort change handler
    const handleEffortChange = useCallback(
      (taskId: string, effort: number) => {
        // Call parent callback directly - no local state needed
        if (onEffortChange) {
          onEffortChange(taskId, effort);
        }
      },
      [onEffortChange]
    );

    // Memoize badge styles using the dynamic color from database
    const badgeStyles = useMemo(() => getTaskGroupBadgeStyles(color), [color]);

    return (
      <div className="mb-4">
        <div
          className={cn(
            "flex items-center justify-between gap-2 px-4 py-2 rounded-lg mb-1 border",
            taskGroupHeaderStyles
          )}
        >
          <h3 className="text-sm font-medium flex items-center gap-1.5">
            <TaskStatusIcon color={color} name={name} projectId={projectId} />
            <span>{name}</span>
            <Badge className={cn("ml-1.5 text-xs px-1.5 py-0 font-medium", badgeStyles)}>
              {count}
            </Badge>
          </h3>
          <div className="flex items-center gap-6">
            <Badge variant="outline" className={cn("ml-1.5 text-xs px-1.5 py-0", badgeStyles)}>
              {formatEffort(totalEffort)}
            </Badge>
            <MoreHorizontal className="w-4 h-4 text-gray-300 cursor-pointer hover:text-gray-700 dark:text-gray-600 dark:hover:text-gray-400" />
          </div>
        </div>
        <div className="overflow-hidden border rounded-md dark:border-gray-800/60 dark:bg-gray-900/20">
          {!isEmpty && <TaskListHeader />}
          {isEmpty && <EmptyState />}
          {/* Simplified rendering to reduce re-renders */}
          {tasks.map((task: TaskModel) => (
            <TaskRow
              key={task.id}
              task={task}
              color={color}
              onStatusChange={onStatusChange}
              onEffortChange={handleEffortChange}
              onTaskClick={onTaskClick}
              openTaskDetails={openTaskDetails}
              projectId={projectId}
            />
          ))}
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function to prevent unnecessary re-renders
    const prevGroup = prevProps.taskGroup;
    const nextGroup = nextProps.taskGroup;

    // Check if the task group structure has changed
    if (
      prevGroup.id !== nextGroup.id ||
      prevGroup.name !== nextGroup.name ||
      prevGroup.count !== nextGroup.count ||
      prevGroup.color !== nextGroup.color ||
      prevGroup.tasks.length !== nextGroup.tasks.length
    ) {
      return false; // Re-render
    }

    // Deep comparison of task effort values and other properties
    for (let i = 0; i < prevGroup.tasks.length; i++) {
      const prevTask = prevGroup.tasks[i];
      const nextTask = nextGroup.tasks[i];

      if (
        prevTask.id !== nextTask.id ||
        prevTask.title !== nextTask.title ||
        prevTask.status !== nextTask.status ||
        prevTask.effort !== nextTask.effort ||
        prevTask.lifeCycleStatus !== nextTask.lifeCycleStatus ||
        prevTask.updatedAt !== nextTask.updatedAt ||
        prevTask.isOptimistic !== nextTask.isOptimistic
      ) {
        return false; // Re-render
      }
    }

    // Check if callbacks have changed
    if (
      prevProps.onStatusChange !== nextProps.onStatusChange ||
      prevProps.onEffortChange !== nextProps.onEffortChange ||
      prevProps.onTaskClick !== nextProps.onTaskClick ||
      prevProps.projectId !== nextProps.projectId
    ) {
      return false; // Re-render
    }

    return true; // Skip re-render
  }
);

export default TaskGroup;
