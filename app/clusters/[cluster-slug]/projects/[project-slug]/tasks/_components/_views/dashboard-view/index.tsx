"use client";

import { viewOptions } from "../../../_types/list-group-options.types";

export function DashboardView(props: { viewId: string }) {
  const { viewId } = props;
  // Helper to get view name
  const getViewName = (viewId: string): string => {
    const view = viewOptions.find((option) => option.id === viewId);
    return view ? view.name : "Unknown";
  };

  return (
    <div className="flex items-center justify-center h-[calc(100vh-14rem)] border rounded-md bg-gray-50 text-gray-500">
      <div className="text-center">
        <h3 className="mb-2 text-xl font-medium">{getViewName(viewId)} View</h3>
        <p>Coming Soon</p>
      </div>
    </div>
  );
}

export default DashboardView;
