import type { <PERSON>umnStatus, TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { useCallback, useEffect, useRef, useState } from "react";
import { useMoveTask } from "../../../../_hooks/use-crud-tasks";

/**
 * BACKUP: Original useKanbanBoard implementation before refactoring
 * This file serves as a rollback point if needed during the refactor process
 * 
 * Date: 2024-01-XX
 * Commit: [commit-hash]
 * 
 * DO NOT MODIFY THIS FILE - IT'S A BACKUP
 */

export function useKanbanBoard(initialColumnsData: TaskGroupModel[], projectId?: string) {
  // Move task mutation
  const moveTaskMutation = useMoveTask();

  // Use ref to track if we're currently dragging to prevent updates during drag
  const isDraggingRef = useRef(false);

  // Use the initial data directly as state - no need for complex memoization
  const [columns, setColumns] = useState<TaskGroupModel[]>(() =>
    initialColumnsData.map((col) => ({
      ...col,
      tasks: col.tasks || [],
    }))
  );
  const [activeTask, setActiveTask] = useState<TaskModel | null>(null);

  // Update columns when initialColumnsData changes, but only if not dragging
  useEffect(() => {
    // Don't update columns while dragging
    if (isDraggingRef.current) {
      return;
    }

    const newColumns = initialColumnsData.map((col) => ({
      ...col,
      tasks: col.tasks || [],
    }));

    // Simple check - just update if the data reference changed
    setColumns(newColumns);
  }, [initialColumnsData]);

  // Create sensors with optimized activation constraints
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: () => {
        return { x: 0, y: 0 };
      },
    })
  );

  // Stable function to find a task's container or extract column from drop zone
  const findContainer = useCallback(
    (id: string) => {
      // Check if it's a task ID
      const taskContainer = columns.find((col) => col.tasks.some((task) => task.id === id))?.id;
      if (taskContainer) return taskContainer;

      // Check if it's a drop zone ID (format: columnId-empty or columnId-bottom)
      if (id.includes("-empty") || id.includes("-bottom")) {
        const columnId = id.replace("-empty", "").replace("-bottom", "");
        return columns.find((col) => col.id === columnId)?.id;
      }

      // Check if it's a direct column ID
      return columns.find((col) => col.id === id)?.id;
    },
    [columns]
  );

  // Handle drag start
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const id = active.id.toString();

      // Set dragging flag
      isDraggingRef.current = true;

      const taskContainer = columns.find((col) => col.tasks.some((t) => t.id === id));
      const task = taskContainer?.tasks.find((t) => t.id === id);

      if (!task || task.isDummy) {
        return;
      }

      setActiveTask(task);
    },
    [columns]
  );

  // Handle drag over - simplified for demo
  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { active, over } = event;
      if (!over) return;

      const activeId = active.id.toString();
      const overId = over.id.toString();

      // If we're over the same task we're dragging, no update needed
      if (activeId === overId) {
        return;
      }

      const activeContainer = findContainer(activeId);
      let overContainer = findContainer(overId);

      // If we can't find the over container, check if it's a column
      if (!overContainer) {
        for (const col of columns) {
          if (col.id === overId) {
            overContainer = col.id;
            break;
          }
        }
      }

      if (!activeContainer || !overContainer) {
        return;
      }

      // Only handle cross-column moves during drag over
      if (activeContainer === overContainer) {
        return;
      }

      // Simple cross-column move for demo
      setColumns((prevColumns) => {
        const activeContainerIndex = prevColumns.findIndex((col) => col.id === activeContainer);
        const overContainerIndex = prevColumns.findIndex((col) => col.id === overContainer);

        if (activeContainerIndex === -1 || overContainerIndex === -1) return prevColumns;

        const activeTaskIndex = prevColumns[activeContainerIndex].tasks.findIndex(
          (task) => task.id === activeId
        );

        if (activeTaskIndex === -1) return prevColumns;

        const activeTask = prevColumns[activeContainerIndex].tasks[activeTaskIndex];
        if (activeTask.isDummy) return prevColumns;

        // Create new columns array
        const newColumns = [...prevColumns];

        // Remove task from source column
        newColumns[activeContainerIndex] = {
          ...newColumns[activeContainerIndex],
          tasks: newColumns[activeContainerIndex].tasks.filter((_, i) => i !== activeTaskIndex),
        };

        // Add task to target column at the end
        newColumns[overContainerIndex] = {
          ...newColumns[overContainerIndex],
          tasks: [...newColumns[overContainerIndex].tasks.filter((t) => !t.isDummy), activeTask],
        };

        return newColumns;
      });
    },
    [findContainer, columns]
  );

  // Handle drag end - with server persistence
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // Clear dragging flag
      isDraggingRef.current = false;
      setActiveTask(null);

      if (!over) return;

      const activeId = active.id.toString();
      const overId = over.id.toString();

      // Quick exit for same element
      if (activeId === overId) return;

      const activeContainer = findContainer(activeId);
      const overContainer = findContainer(overId);

      if (!activeContainer || !overContainer) return;

      const activeTask = columns
        .find((col) => col.tasks.some((task) => task.id === activeId))
        ?.tasks.find((task) => task.id === activeId);

      if (!activeTask || activeTask.isDummy) return;

      // Calculate new position - simplified approach
      let newPosition = 1;
      const targetColumn = columns.find((col) => col.id === overContainer);

      if (targetColumn) {
        const isDropZone = overId.includes("-empty") || overId.includes("-bottom");
        const overTask = columns
          .find((col) => col.tasks.some((task) => task.id === overId))
          ?.tasks.find((task) => task.id === overId);

        if (activeContainer === overContainer) {
          // Same column reordering
          if (isDropZone || !overTask || overTask.isDummy) {
            // Move to end of column
            const realTasks = targetColumn.tasks.filter((t) => !t.isDummy && t.id !== activeId);
            newPosition = realTasks.length + 1;
          } else {
            // Insert relative to target task
            const targetTaskPosition = overTask.positionInColumn || 1;
            const activeTaskPosition = activeTask.positionInColumn || 1;

            // If moving down, insert after target; if moving up, insert before
            if (activeTaskPosition < targetTaskPosition) {
              newPosition = targetTaskPosition + 1;
            } else {
              newPosition = targetTaskPosition;
            }
          }
        } else {
          // Cross-column move - add to end
          const realTasks = targetColumn.tasks.filter((t) => !t.isDummy);
          newPosition = realTasks.length + 1;
        }
      }

      // Update local state immediately for UI responsiveness
      setColumns((prevColumns) => {
        if (activeContainer === overContainer) {
          // Same column reordering
          return prevColumns.map((column) => {
            if (column.id === activeContainer) {
              const tasksWithoutMoved = column.tasks.filter((t) => t.id !== activeId);
              const isDropZone = overId.includes("-empty") || overId.includes("-bottom");
              const overTask = column.tasks.find((task) => task.id === overId);

              if (isDropZone || !overTask || overTask.isDummy) {
                return {
                  ...column,
                  tasks: [...tasksWithoutMoved.filter((t) => !t.isDummy), activeTask],
                };
              } else {
                const originalActiveIndex = column.tasks.findIndex((t) => t.id === activeId);
                const originalOverIndex = column.tasks.findIndex((t) => t.id === overId);
                const targetIndex = tasksWithoutMoved.findIndex((t) => t.id === overId);
                const newTasks = [...tasksWithoutMoved];

                if (originalActiveIndex < originalOverIndex) {
                  newTasks.splice(targetIndex + 1, 0, activeTask);
                } else {
                  newTasks.splice(targetIndex, 0, activeTask);
                }

                return { ...column, tasks: newTasks };
              }
            }
            return column;
          });
        } else {
          // Cross-column move
          const newColumns = [...prevColumns];
          const activeContainerIndex = newColumns.findIndex((col) => col.id === activeContainer);
          const overContainerIndex = newColumns.findIndex((col) => col.id === overContainer);

          if (activeContainerIndex !== -1 && overContainerIndex !== -1) {
            // Remove from source
            newColumns[activeContainerIndex] = {
              ...newColumns[activeContainerIndex],
              tasks: newColumns[activeContainerIndex].tasks.filter((t) => t.id !== activeId),
            };

            // Add to target
            newColumns[overContainerIndex] = {
              ...newColumns[overContainerIndex],
              tasks: [
                ...newColumns[overContainerIndex].tasks.filter((t) => !t.isDummy),
                activeTask,
              ],
            };
          }

          return newColumns;
        }
      });

      // Persist to server if projectId is available
      if (projectId && targetColumn) {
        moveTaskMutation.mutate({
          taskId: activeId,
          projectId,
          newStatus: targetColumn.name as ColumnStatus,
          targetPosition: newPosition,
        });
      }
    },
    [findContainer, columns, projectId, moveTaskMutation]
  );

  return {
    columns,
    activeTask,
    sensors,
    handlers: {
      handleDragStart,
      handleDragOver,
      handleDragEnd,
    },
    activeTaskExists: !!activeTask,
  };
}
