import { KanbanViewSkeleton } from "@/components/skeletons";
import { memo } from "react";
import { useFilteredTaskGroupsByStatusKanban } from "../../../_hooks/use-filtered-task-groups";
import { useFilteredTasksContext } from "../../filtered-tasks-provider";
import { useTasksContext } from "../../tasks-provider";
import KanbanLegacyBoard from "./kanban-legacy-board";

interface KanbanViewProps {
  isCompactMode?: boolean;
}

const KanbanView = memo(function KanbanView({ isCompactMode = false }: KanbanViewProps) {
  // Get projectId from context instead of discovering from cache
  const { projectId } = useTasksContext();

  // Use filtered tasks context
  const { filteredTasks, allTasks, isLoading } = useFilteredTasksContext();

  // Get kanban columns using filtered tasks (grouped by status for kanban, excludes Backlog)
  // Force re-calculation when allTasks change (includes optimistic updates)
  const taskGroups = useFilteredTaskGroupsByStatusKanban(filteredTasks);

  if (isLoading) {
    return <KanbanViewSkeleton />;
  }

  if (taskGroups.length === 0) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-14rem)] border rounded-md bg-gray-50 text-gray-500">
        <div className="text-center">
          <h3 className="mb-2 text-xl font-medium">No tasks found</h3>
          <p>Create some tasks to get started with the Kanban board</p>
        </div>
      </div>
    );
  }

  return (
    <KanbanLegacyBoard
      isCompactMode={isCompactMode}
      taskGroups={taskGroups}
      projectId={projectId}
      allTasks={allTasks} // Pass allTasks for filter handling
    />
  );
});

export default KanbanView;
