"use client";

import { cn } from "@/lib/utils";
import { useDroppable } from "@dnd-kit/core";

interface DropZoneProps {
  id: string;
  className?: string;
  isActive?: boolean;
  isEmpty?: boolean;
}

export function DropZone({ id, className, isActive = false, isEmpty = false }: DropZoneProps) {
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      type: "drop-zone",
      columnId: id,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "transition-all duration-200",
        // Base styles for drop zone
        isEmpty
          ? "min-h-28 rounded-md border-1 border-dashed border-gray-300 dark:border-gray-700"
          : "min-h-0.5 max-w-1/3 mx-auto",
        // Active drop styles
        isOver || isActive
          ? "border-gray-50 bg-gray-50 dark:bg-gray-950/20"
          : isEmpty
            ? "border-gray-300 dark:border-gray-700"
            : "border-transparent",
        className
      )}
    >
      {isEmpty && (
        <div className="flex items-center justify-center h-28 text-sm text-gray-500 dark:text-gray-400">
          Drop a task here
        </div>
      )}
      {!isEmpty && (isOver || isActive) && (
        <div className="flex items-center justify-center h-full">
          <div className="w-full h-0.5 bg-gray-200 rounded"></div>
        </div>
      )}
    </div>
  );
}
