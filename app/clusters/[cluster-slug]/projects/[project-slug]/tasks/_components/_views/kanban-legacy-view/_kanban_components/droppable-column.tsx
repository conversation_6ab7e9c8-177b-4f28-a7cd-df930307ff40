"use client";

import { cn } from "@/lib/utils";
import { useDroppable } from "@dnd-kit/core";

interface DroppableColumnProps {
  id: string;
  children: React.ReactNode;
  className?: string;
}

export function DroppableColumn({ id, children, className }: DroppableColumnProps) {
  const { setNodeRef } = useDroppable({
    id,
    data: {
      type: "column",
      columnId: id,
    },
  });

  return (
    <div ref={setNodeRef} className={cn(className)}>
      {children}
    </div>
  );
}
