import { LucideIcon } from "lucide-react";
import { memo } from "react";
import { getTaskStatusInfo, useTaskStatusInfo } from "../../../../../utils/task-status-styles";
import { type TaskStatusIconProps } from "../types";

/**
 * Task status icon component with dynamic icon support
 * Memoized to prevent unnecessary re-renders when status doesn't change
 */
export const TaskStatusIcon = memo(function TaskStatusIcon({
  color,
  name,
  taskGroups,
  icon: providedIcon,
  projectId,
}: TaskStatusIconProps) {
  // Always call the hook (rules of hooks)
  const { getStatusInfo } = useTaskStatusInfo(projectId);

  // Use provided icon or get from dynamic system
  let IconComponent: LucideIcon;
  let iconColor: string;

  if (providedIcon) {
    // Use provided icon directly
    IconComponent = providedIcon;
    iconColor = color;
  } else {
    // Get dynamic status info - use hook result if projectId exists, otherwise fallback
    const statusInfo =
      projectId && getStatusInfo ? getStatusInfo(name) : getTaskStatusInfo(name, taskGroups);
    IconComponent = statusInfo.icon;
    iconColor = color || statusInfo.color;
  }

  const className = `w-3.5 h-3.5 text-${iconColor}-500 dark:text-${iconColor}-400`;

  return <IconComponent className={className} />;
});

export default TaskStatusIcon;
