import { AssigneeDisplay } from "@/components/tasks/tasks-assignee-display";
import { TaskDeletionConfirmDialog } from "@/components/tasks/tasks-deletion-confirm-dialog";
import { TaskLifeCycleStatusBadge } from "@/components/tasks/tasks-lifecycle-status-style";
import { TaskAdditionalMenu } from "@/components/tasks/tasks-menu-dropdown";
import {
  Badge,
  Button,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui";
import { type ColumnStatus, type LifeCycleStatus, type TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Ellipsis } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { useDeleteTask, useUpdateTask } from "../../../../_hooks/use-crud-tasks";
import { ClockIcon } from "../../../task-icons";
import ColumnStatusDropdown from "./components/ColumnStatusDropdown";
import EffortEditor from "./components/EffortEditor";
import TaskTag from "./components/TaskTag";
import ValueBadge from "./components/ValueBadge";
import { MAX_VISIBLE_TAGS, TASK_ROW_BASE_CLASSES } from "./constants";
import { type TaskRowProps } from "./types";
import {
  createTaskRowClasses,
  generateTagKey,
  getTaskColorHoverClass,
  isInteractiveElement,
} from "./utils";

/**
 * Individual task row component with optimized memoization
 * Handles all task interactions including status changes, effort editing, and deletion
 */
export const TaskRow = memo(
  function TaskRow({
    task: initialTask,
    color,
    onStatusChange,
    onEffortChange,
    onTaskClick,
    openTaskDetails,
    projectId,
  }: TaskRowProps) {
    const [task, setTask] = useState({ ...initialTask });
    const [dueDate, setDueDate] = useState<Date | undefined>(
      task.dueDate ? new Date(task.dueDate) : undefined
    );
    const [isEditingEffort, setIsEditingEffort] = useState(false);
    const [isDatePopoverOpen, setIsDatePopoverOpen] = useState(false);

    // Local delete dialog state
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [taskToDelete, setTaskToDelete] = useState<TaskModel | null>(null);

    const updateTaskMutation = useUpdateTask();
    const deleteTaskMutation = useDeleteTask();

    // Update local task when initial task changes
    useEffect(() => {
      setTask(initialTask);
    }, [initialTask]);

    // Sync local dueDate state with task changes
    useEffect(() => {
      setDueDate(task.dueDate ? new Date(task.dueDate) : undefined);
    }, [task.dueDate]);

    // Memoize task row classes
    const taskRowClasses = useMemo(
      () => createTaskRowClasses(TASK_ROW_BASE_CLASSES, getTaskColorHoverClass(color)),
      [color]
    );

    const handleTaskClick = useCallback(
      (e: React.MouseEvent) => {
        // Check if the click is on an interactive element - if so, don't handle the task click
        const target = e.target as HTMLElement;
        if (isInteractiveElement(target)) {
          return;
        }

        // Open task details sheet
        if (onTaskClick) {
          onTaskClick(task);
        }
        if (openTaskDetails) {
          openTaskDetails();
        }
      },
      [task, onTaskClick, openTaskDetails]
    );

    const handleStatusChange = useCallback(
      async (status: ColumnStatus) => {
        // If we have projectId, use the update hook for real persistence
        if (projectId) {
          try {
            await updateTaskMutation.mutateAsync({
              taskId: task.id,
              projectId,
              updates: { status },
            });
          } catch (error) {
            // Error handling is done in the hook
            console.error("Status update failed:", error);
          }
        }

        // Still call the callback for backward compatibility
        if (onStatusChange) {
          onStatusChange(task.id, status);
        }
      },
      [task.id, projectId, updateTaskMutation, onStatusChange]
    );

    const handleEffortChange = useCallback(
      async (effort: number) => {
        // Update local state immediately for responsive UI
        setTask((prev) => ({ ...prev, effort }));

        // If we have projectId, use the update hook for real persistence
        if (projectId) {
          try {
            await updateTaskMutation.mutateAsync({
              taskId: task.id,
              projectId,
              updates: { effort },
            });
          } catch (error) {
            // Rollback local state on error
            setTask((prev) => ({ ...prev, effort: initialTask.effort }));
            console.error("Effort update failed:", error);
          }
        }

        // Still call the callback for backward compatibility
        if (onEffortChange) {
          onEffortChange(task.id, effort);
        }
      },
      [task.id, projectId, updateTaskMutation, onEffortChange, initialTask.effort]
    );

    const handleDateChange = useCallback(
      async (date: Date | undefined) => {
        setDueDate(date);
        setIsDatePopoverOpen(false); // Close the popover when date is selected

        // If we have projectId, use the update hook for real persistence
        if (projectId) {
          try {
            await updateTaskMutation.mutateAsync({
              taskId: task.id,
              projectId,
              updates: { dueDate: date ? date.toISOString() : null },
            });

            toast.success(
              `Due date for task ${task.key} ${date ? "set to " + format(date, "PPP") : "removed"}`
            );
          } catch (error) {
            // Rollback local state on error
            setDueDate(task.dueDate ? new Date(task.dueDate) : undefined);
            console.error("Due date update failed:", error);
          }
        } else {
          // Fallback toast for when there's no projectId
          toast.info(`Due date for task ${task.key} set to ${date ? format(date, "PPP") : "none"}`);
        }
      },
      [task.id, task.key, task.dueDate, projectId, updateTaskMutation]
    );

    // Handle lifecycle status change - wire to actual update hook
    const handleLifeCycleChange = useCallback(
      async (lifeCycleStatus: LifeCycleStatus) => {
        // If we have projectId, use the update hook for real persistence
        if (projectId) {
          try {
            await updateTaskMutation.mutateAsync({
              taskId: task.id,
              projectId,
              updates: { lifeCycleStatus },
            });

            toast.success(`Updated task ${task.key} lifecycle status to ${lifeCycleStatus}`);
          } catch (error) {
            console.error("Lifecycle status update failed:", error);
          }
        } else {
          // Fallback toast for when there's no projectId
          toast.info(`Updated task ${task.key} lifecycle status to ${lifeCycleStatus}`);
        }
      },
      [task.id, task.key, projectId, updateTaskMutation]
    );

    // Handle tag click - open task details
    const handleTagClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        if (onTaskClick) {
          onTaskClick(task);
        }
        if (openTaskDetails) {
          openTaskDetails();
        }
      },
      [task, onTaskClick, openTaskDetails]
    );

    // Handle title click - open task details
    const handleTitleClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        if (onTaskClick) {
          onTaskClick(task);
        }
        if (openTaskDetails) {
          openTaskDetails();
        }
      },
      [task, onTaskClick, openTaskDetails]
    );

    // Task deletion logic
    const showDeleteDialog = useCallback((taskToDelete: TaskModel) => {
      setTaskToDelete(taskToDelete);
      setIsDeleteDialogOpen(true);
    }, []);

    const handleDeleteTask = useCallback(async () => {
      if (!projectId || !taskToDelete) {
        toast.error("Cannot delete task: Missing required information");
        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
        return;
      }

      try {
        await deleteTaskMutation.mutateAsync({
          taskId: taskToDelete.id,
          projectId,
        });

        // Close the dialog
        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
      } catch {
        toast.error("Failed to delete task. Please try again.");

        // Make sure the dialog is closed even if there was an error
        setIsDeleteDialogOpen(false);
        setTaskToDelete(null);
      }
    }, [projectId, taskToDelete, deleteTaskMutation]);

    // Memoize additional tags for tooltip
    const additionalTags = useMemo(() => {
      if (task.tags.length <= MAX_VISIBLE_TAGS) return [];
      return task.tags.slice(MAX_VISIBLE_TAGS);
    }, [task.tags]);

    // Memoize tooltip content for additional tags
    const tooltipContent = useMemo(() => {
      if (additionalTags.length === 0) return "";
      return additionalTags.map((tag) => tag.name).join(", ");
    }, [additionalTags]);

    return (
      <>
        <div className={cn(taskRowClasses, "group")} onClick={handleTaskClick}>
          {/* ID */}
          <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400 font-mono whitespace-nowrap">
            <span className="min-w-[58px]">{task.key}</span>
            <div data-no-propagation>
              <ColumnStatusDropdown
                initialStatus={task.status}
                onStatusChange={handleStatusChange}
                projectId={projectId}
              />
            </div>
          </div>

          {/* Name */}
          <div
            className="text-sm truncate task-row-content cursor-pointer"
            onClick={handleTitleClick}
          >
            {task.title}
          </div>

          {/* Life Cycle */}
          <div data-no-propagation>
            <TaskLifeCycleStatusBadge
              taskId={task.id}
              initialStatus={task.lifeCycleStatus}
              onStatusChange={handleLifeCycleChange}
            />
          </div>

          {/* Value */}
          <div className="task-row-content">
            <ValueBadge taskValue={task.value} />
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-1 overflow-hidden task-row-content">
            {task.tags.slice(0, MAX_VISIBLE_TAGS).map((tag, index) => (
              <div key={generateTagKey(tag.id, index)}>
                <TaskTag tag={tag} onClick={handleTagClick} />
              </div>
            ))}
            {task.tags.length > MAX_VISIBLE_TAGS && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="outline"
                    className="border-0 bg-gray-50 dark:bg-gray-800/40 text-gray-500 dark:text-gray-400 font-normal text-[10px] py-0.5 px-1.5 rounded-full cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/60 transition-colors"
                    onClick={handleTagClick}
                  >
                    +{task.tags.length - MAX_VISIBLE_TAGS}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="text-xs">{tooltipContent}</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {/* Due date */}
          <div
            className="flex items-center text-sm text-gray-600 truncate dark:text-gray-400"
            data-no-propagation
          >
            {dueDate ? (
              <Popover open={isDatePopoverOpen} onOpenChange={setIsDatePopoverOpen}>
                <PopoverTrigger asChild className="-ml-3">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-normal flex items-center gap-1.5 hover:bg-transparent"
                  >
                    <ClockIcon className="flex-shrink-0 w-3 h-3 text-gray-400 dark:text-gray-500" />
                    <span className="text-xs truncate">{format(dueDate, "PPP")}</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="single" selected={dueDate} onSelect={handleDateChange} />
                </PopoverContent>
              </Popover>
            ) : (
              <Popover open={isDatePopoverOpen} onOpenChange={setIsDatePopoverOpen}>
                <PopoverTrigger asChild className="-ml-3">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-normal flex items-center gap-1.5 hover:bg-transparent text-gray-400 dark:text-gray-500"
                  >
                    <ClockIcon className="flex-shrink-0 w-3 h-3" />
                    <span className="text-xs">Set due date</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="single" selected={dueDate} onSelect={handleDateChange} />
                </PopoverContent>
              </Popover>
            )}
          </div>

          {/* Assignee */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <AssigneeDisplay
              assignees={task.assignees || []}
              variant="row"
              maxVisible={2}
              showNames={false}
            />
          </div>

          {/* Effort */}
          <div
            className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400"
            data-no-propagation
          >
            {isEditingEffort ? (
              <EffortEditor
                value={task.effort || 0}
                onSave={(effort) => {
                  handleEffortChange(effort);
                  setIsEditingEffort(false);
                }}
                onCancel={() => setIsEditingEffort(false)}
              />
            ) : (
              <button
                className="text-xs hover:bg-gray-100 dark:hover:bg-gray-800 px-1 py-0.5 rounded transition-colors"
                onClick={() => setIsEditingEffort(true)}
              >
                {task.effort ? `${task.effort}d` : "0d"}
              </button>
            )}

            {/* Actions */}
            <TaskAdditionalMenu
              task={task}
              projectId={projectId}
              onDelete={(taskToDelete) => showDeleteDialog(taskToDelete || task)}
            >
              <Ellipsis className="text-gray-400 size-3 opacity-0 group-hover:opacity-100 transition-opacity" />
            </TaskAdditionalMenu>
          </div>
        </div>

        {/* Use the shared TaskDeletionConfirmDialog component */}
        <TaskDeletionConfirmDialog
          task={taskToDelete}
          isOpen={isDeleteDialogOpen}
          isDeleting={deleteTaskMutation.isPending}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={handleDeleteTask}
          variant="danger"
        />
      </>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function to prevent unnecessary re-renders
    const prevTask = prevProps.task;
    const nextTask = nextProps.task;

    // Check if task properties that affect rendering have changed
    if (
      prevTask.id !== nextTask.id ||
      prevTask.title !== nextTask.title ||
      prevTask.status !== nextTask.status ||
      prevTask.effort !== nextTask.effort ||
      prevTask.lifeCycleStatus !== nextTask.lifeCycleStatus ||
      prevTask.value !== nextTask.value ||
      prevTask.dueDate !== nextTask.dueDate ||
      prevTask.tags.length !== nextTask.tags.length ||
      prevTask.assignees?.length !== nextTask.assignees?.length ||
      prevProps.color !== nextProps.color ||
      prevProps.projectId !== nextProps.projectId
    ) {
      return false; // Re-render
    }

    // Check if callbacks have changed (they should be stable with useCallback)
    if (
      prevProps.onStatusChange !== nextProps.onStatusChange ||
      prevProps.onEffortChange !== nextProps.onEffortChange ||
      prevProps.onTaskClick !== nextProps.onTaskClick ||
      prevProps.openTaskDetails !== nextProps.openTaskDetails
    ) {
      return false; // Re-render
    }

    return true; // Skip re-render
  }
);

export default TaskRow;
