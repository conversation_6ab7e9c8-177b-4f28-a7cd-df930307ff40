import { type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { BADGE_STYLES, TASK_COLOR_HOVER_CLASSES } from "./constants";
import { type TaskColor } from "./types";

/**
 * Get hover classes for a task color
 */
export function getTaskColorHoverClass(color: string): string {
  return TASK_COLOR_HOVER_CLASSES[color as TaskColor] || TASK_COLOR_HOVER_CLASSES.gray;
}

/**
 * Get badge styles for a color
 */
export function getBadgeStyles(color: TaskColor): string {
  return BADGE_STYLES[color] || BADGE_STYLES.gray;
}

/**
 * Check if a click target is an interactive element
 */
export function isInteractiveElement(target: HTMLElement): boolean {
  return !!(
    target.closest("button") ||
    target.closest('[role="button"]') ||
    target.closest("input") ||
    target.closest(".interactive") ||
    target.closest("[data-no-propagation]")
  );
}

/**
 * Create stable class names for task rows
 */
export function createTaskRowClasses(baseClasses: string, colorHoverClass: string): string {
  return cn(baseClasses, colorHoverClass);
}

/**
 * Validate effort input format
 */
export function isValidEffortFormat(value: string): boolean {
  return /^\d+$/.test(value) || /^\d+w(\d+d)?$|^\d+d$/.test(value);
}

/**
 * Create stable badge class names
 */
export function createBadgeClasses(baseClasses: string, colorStyles: string): string {
  return cn(baseClasses, colorStyles);
}

/**
 * Generate stable key for task row
 */
export function generateTaskRowKey(taskId: string): string {
  return `task-row-${taskId}`;
}

/**
 * Generate stable key for tag
 */
export function generateTagKey(tagId: string, index: number): string {
  return `tag-${tagId}-${index}`;
}

/**
 * Check if tasks array has changed (deep comparison for effort-related properties)
 */
export function hasTasksChanged(prevTasks: TaskModel[], nextTasks: TaskModel[]): boolean {
  if (prevTasks.length !== nextTasks.length) return true;

  for (let i = 0; i < prevTasks.length; i++) {
    const prevTask = prevTasks[i];
    const nextTask = nextTasks[i];

    if (
      prevTask.id !== nextTask.id ||
      prevTask.title !== nextTask.title ||
      prevTask.status !== nextTask.status ||
      prevTask.effort !== nextTask.effort ||
      prevTask.lifeCycleStatus !== nextTask.lifeCycleStatus ||
      prevTask.updatedAt !== nextTask.updatedAt ||
      prevTask.isOptimistic !== nextTask.isOptimistic
    ) {
      return true;
    }
  }

  return false;
}

/**
 * Create a hash of task effort values for efficient comparison
 */
export function createTaskEffortHash(tasks: TaskModel[]): string {
  return tasks.map((task) => `${task.id}:${task.effort || 0}:${task.updatedAt}`).join("|");
}

/**
 * Check if task group has changed including effort values
 */
export function hasTaskGroupChanged(prevGroup: TaskGroupModel, nextGroup: TaskGroupModel): boolean {
  // Basic group properties
  if (
    prevGroup.id !== nextGroup.id ||
    prevGroup.name !== nextGroup.name ||
    prevGroup.count !== nextGroup.count ||
    prevGroup.color !== nextGroup.color ||
    prevGroup.tasks.length !== nextGroup.tasks.length
  ) {
    return true;
  }

  // Deep comparison of task effort values
  return hasTasksChanged(prevGroup.tasks, nextGroup.tasks);
}

/**
 * Create memoization key for task group
 */
export function createTaskGroupMemoKey(taskGroup: TaskGroupModel): string {
  return `${taskGroup.id}-${taskGroup.count}-${taskGroup.tasks.length}`;
}
