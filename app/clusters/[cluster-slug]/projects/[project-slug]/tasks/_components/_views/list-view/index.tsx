/**
 * @deprecated This file is deprecated as of the GroupBy Hub Architecture refactor.
 *
 * The TaskListView component has been replaced by direct routing to grouped view components
 * in tasks-view-tabs.tsx to eliminate the double-skeleton loading issue and improve performance.
 *
 * All grouped views are now loaded directly:
 * - StatusGroupedView
 * - LifecycleGroupedView
 * - TagGroupedView
 * - AssigneeGroupedView
 * - DueDateGroupedView
 * - EffortGroupedView
 * - ValueGroupedView
 *
 * This file can be safely removed in a future cleanup.
 */

"use client";

import { AnimatePresence, motion } from "framer-motion";
import { lazy, memo, useMemo } from "react";
import { GroupByOption } from "../../../_types/list-group-options.types";

// Define interface for grouped view components
interface GroupedViewProps {
  isSortReversed?: boolean;
}

// Lazy load the specialized views
const StatusGroupedView = lazy(
  () => import("../../_grouping-views/status-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const LifecycleGroupedView = lazy(
  () => import("../../_grouping-views/lifecycle-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const TagGroupedView = lazy(
  () => import("../../_grouping-views/tag-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const AssigneeGroupedView = lazy(
  () => import("../../_grouping-views/assignee-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const DueDateGroupedView = lazy(
  () => import("../../_grouping-views/due-date-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const EffortGroupedView = lazy(
  () => import("../../_grouping-views/effort-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;
const ValueGroupedView = lazy(
  () => import("../../_grouping-views/value-grouped-view")
) as unknown as React.ComponentType<GroupedViewProps>;

// Props interface
interface ListViewProps {
  groupBy?: GroupByOption;
  isSortReversed?: boolean;
}

// Memoized component map to prevent recreation
const COMPONENT_MAP = {
  status: StatusGroupedView,
  lifecycle: LifecycleGroupedView,
  tag: TagGroupedView,
  assignee: AssigneeGroupedView,
  "due-date": DueDateGroupedView,
  effort: EffortGroupedView,
  value: ValueGroupedView,
} as const;

// List view component with proper memoization
export const ListView = memo(function ListView({
  groupBy = "status",
  isSortReversed = false,
}: ListViewProps) {
  // Memoize the component to render
  const GroupedViewComponent = useMemo(() => {
    return COMPONENT_MAP[groupBy] || StatusGroupedView;
  }, [groupBy]);

  // Memoize the rendered component with stable props
  const renderedComponent = useMemo(
    () => <GroupedViewComponent isSortReversed={isSortReversed} />,
    [GroupedViewComponent, isSortReversed]
  );

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={groupBy}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className="w-full"
      >
        {renderedComponent}
      </motion.div>
    </AnimatePresence>
  );
});

export default ListView;
