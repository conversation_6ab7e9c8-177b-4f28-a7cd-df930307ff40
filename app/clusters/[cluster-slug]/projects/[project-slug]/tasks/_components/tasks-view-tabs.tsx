"use client";

import { KanbanViewSkeleton, TaskListSkeleton } from "@/components/skeletons";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { AnimatePresence, motion } from "framer-motion";
import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { lazy, memo, Suspense, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { useTags } from "../_hooks/use-crud-tasks";
import { useAvailableAssignees, useTaskFilters } from "../_hooks/use-task-filters";
import { useTasksData } from "../_hooks/use-tasks-data";
import { GroupByOption, viewOptions, ViewType } from "../_types/list-group-options.types";
import { FilteredTasksProvider } from "./filtered-tasks-provider";
import { TasksTabsHeader } from "./tabs-header";
import { useTasksContext } from "./tasks-provider";

// Lazy load the main view types
// const TaskKanbanLegacyView = lazy(() => import("./_views/kanban-legacy-view/index"));
const KanbanView = lazy(() => import("./_views/kanban-view/index"));
const TaskTimelineView = lazy(() => import("./_views/timeline-view/index"));
const TaskCalendarView = lazy(() => import("./_views/calendar-view/index"));
const TaskDashboardView = lazy(() => import("./_views/dashboard-view/index"));

// Lazy load the grouped views directly (GroupBy Hub Architecture)
const StatusGroupedView = lazy(() => import("./_grouping-views/status-grouped-view"));
const LifecycleGroupedView = lazy(() => import("./_grouping-views/lifecycle-grouped-view"));
const TagGroupedView = lazy(() => import("./_grouping-views/tag-grouped-view"));
const AssigneeGroupedView = lazy(() => import("./_grouping-views/assignee-grouped-view"));
const DueDateGroupedView = lazy(() => import("./_grouping-views/due-date-grouped-view"));
const EffortGroupedView = lazy(() => import("./_grouping-views/effort-grouped-view"));
const ValueGroupedView = lazy(() => import("./_grouping-views/value-grouped-view"));

// Create a motion wrapper for tab content
const MotionTabsContent = motion.create(TabsContent);

// Valid options for validation
const VALID_VIEWS = viewOptions.map((option) => option.id);
const VALID_GROUP_BY_OPTIONS: GroupByOption[] = [
  "status",
  "lifecycle",
  "tag",
  "assignee",
  "due-date",
  "effort",
  "value",
];

// Memoized tabs container for tasks views
const TasksViewTabs = memo(function TasksViewTabs() {
  const { openCreateTaskDialog, projectId } = useTasksContext();

  // Initialize filter system
  const filtersState = useTaskFilters();

  // Fetch tasks data to get assignees
  const { tasks } = useTasksData(projectId);

  // Fetch tags for filter options
  const { data: availableTags = [] } = useTags(projectId, true);

  // Get available assignees from tasks data
  const availableAssignees = useAvailableAssignees(tasks);

  // Use nuqs for URL state management with proper typing
  const [activeView, setActiveView] = useQueryState(
    "view",
    parseAsString.withDefault("list").withOptions({
      clearOnDefault: true,
    })
  );

  const [activeGrouping, setActiveGrouping] = useQueryState(
    "groupBy",
    parseAsString.withDefault("status").withOptions({
      clearOnDefault: true,
    })
  );

  const [isSortReversed, setIsSortReversed] = useQueryState(
    "sortReversed",
    parseAsBoolean.withDefault(false).withOptions({
      clearOnDefault: true,
    })
  );

  // Simple local state for compact mode - just a boolean, nothing fancy
  const [isCompactMode, setIsCompactMode] = useState(false);

  // Validate URL parameters and redirect to clean URL if invalid
  useEffect(() => {
    const isValidView = VALID_VIEWS.includes(activeView);
    const isValidGroupBy = VALID_GROUP_BY_OPTIONS.includes(activeGrouping as GroupByOption);

    if (!isValidView || !isValidGroupBy) {
      console.warn("Invalid URL parameters detected:", {
        activeView,
        activeGrouping,
        isValidView,
        isValidGroupBy,
      });

      // Reset to defaults (this will clear the URL due to clearOnDefault: true)
      setActiveView("list");
      setActiveGrouping("status");
      setIsSortReversed(false);
      setIsCompactMode(false); // Reset local state as well

      toast.info("Invalid URL parameters detected. Redirected to default view.");
    }
  }, [activeView, activeGrouping, setActiveView, setActiveGrouping, setIsSortReversed]);

  // Animation variants for tab content transitions
  const tabContentVariants = useMemo(
    () => ({
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.3, ease: "easeOut" } },
      exit: { opacity: 0, y: -10, transition: { duration: 0.2 } },
    }),
    []
  );

  const handleViewChange = useMemo(
    () => (viewId: string) => {
      setActiveView(viewId);

      // Clear groupBy parameter when switching to views other than list
      // since groupBy is only relevant for list view
      if (viewId !== "list" && activeGrouping !== "status") {
        setActiveGrouping("status"); // Reset to default
      }

      toast.info(`Switched to ${viewId.charAt(0).toUpperCase() + viewId.slice(1)} view`);
    },
    [setActiveView, activeGrouping, setActiveGrouping]
  );

  const handleGroupingChange = useMemo(
    () => (grouping: GroupByOption) => {
      setActiveGrouping(grouping);
      toast.info(`Grouped by ${grouping}`);
    },
    [setActiveGrouping]
  );

  const handleSortChange = useMemo(
    () => (isSortReversed: boolean) => {
      setIsSortReversed(isSortReversed);
    },
    [setIsSortReversed]
  );

  // Simple function to toggle compact mode
  const handleCompactModeChange = useMemo(
    () => (isCompact: boolean) => {
      setIsCompactMode(isCompact);
    },
    []
  );

  // GroupBy Hub Architecture - Direct component mapping
  const GROUPED_VIEW_COMPONENTS = useMemo(
    () => ({
      status: StatusGroupedView,
      lifecycle: LifecycleGroupedView,
      tag: TagGroupedView,
      assignee: AssigneeGroupedView,
      "due-date": DueDateGroupedView,
      effort: EffortGroupedView,
      value: ValueGroupedView,
    }),
    []
  );

  // Memoize the list view component (now directly routes to grouped views)
  const ListViewComponent = useMemo(() => {
    const GroupedViewComponent =
      GROUPED_VIEW_COMPONENTS[activeGrouping as GroupByOption] || StatusGroupedView;
    return <GroupedViewComponent isSortReversed={isSortReversed} />;
  }, [activeGrouping, isSortReversed, GROUPED_VIEW_COMPONENTS]);

  // Legacy Kanban View
  // const KanbanLegacyViewComponent = useMemo(
  //   () => <TaskKanbanLegacyView isCompactMode={isCompactMode} />,
  //   [isCompactMode]
  // );

  // Memoize kanban view with stable props
  const KanbanViewComponent = useMemo(() => <KanbanView />, []);

  const TimelineViewComponent = useMemo(
    () => <TaskTimelineView viewId={activeView} />,
    [activeView]
  );

  const CalendarViewComponent = useMemo(
    () => <TaskCalendarView viewId={activeView} />,
    [activeView]
  );

  const DashboardViewComponent = useMemo(
    () => <TaskDashboardView viewId={activeView} />,
    [activeView]
  );

  return (
    <FilteredTasksProvider>
      <div className="space-y-4">
        <Tabs value={activeView} onValueChange={handleViewChange} className="flex flex-col gap-4">
          <TasksTabsHeader
            activeView={activeView as ViewType}
            activeGrouping={activeGrouping as GroupByOption}
            onGroupingChange={handleGroupingChange}
            onSortChange={handleSortChange}
            isCompactMode={isCompactMode}
            onCompactModeChange={handleCompactModeChange}
            onCreateTask={openCreateTaskDialog}
            filtersState={filtersState}
            availableTags={availableTags}
            availableAssignees={availableAssignees}
          />

          {/* Animated tab contents with AnimatePresence */}
          <AnimatePresence mode="popLayout">
            <MotionTabsContent
              key="list"
              value="list"
              className="mt-0 overflow-auto"
              initial="hidden"
              animate={activeView === "list" ? "visible" : "hidden"}
              exit="exit"
              variants={tabContentVariants}
            >
              <Suspense fallback={<TaskListSkeleton />}>{ListViewComponent}</Suspense>
            </MotionTabsContent>

            <MotionTabsContent
              key="kanban"
              value="kanban"
              className="mt-0"
              initial="hidden"
              animate={activeView === "kanban" ? "visible" : "hidden"}
              exit="exit"
              variants={tabContentVariants}
            >
              <Suspense fallback={<KanbanViewSkeleton />}>{KanbanViewComponent}</Suspense>
            </MotionTabsContent>

            <MotionTabsContent
              key="timeline"
              value="timeline"
              className="mt-0"
              initial="hidden"
              animate={activeView === "timeline" ? "visible" : "hidden"}
              exit="exit"
              variants={tabContentVariants}
            >
              <Suspense fallback={<Skeleton className="h-[calc(100vh-14rem)]" />}>
                {TimelineViewComponent}
              </Suspense>
            </MotionTabsContent>

            <MotionTabsContent
              key="calendar"
              value="calendar"
              className="mt-0"
              initial="hidden"
              animate={activeView === "calendar" ? "visible" : "hidden"}
              exit="exit"
              variants={tabContentVariants}
            >
              <Suspense fallback={<Skeleton className="h-[calc(100vh-14rem)]" />}>
                {CalendarViewComponent}
              </Suspense>
            </MotionTabsContent>

            <MotionTabsContent
              key="dashboard"
              value="dashboard"
              className="mt-0"
              initial="hidden"
              animate={activeView === "dashboard" ? "visible" : "hidden"}
              exit="exit"
              variants={tabContentVariants}
            >
              <Suspense fallback={<Skeleton className="h-[calc(100vh-14rem)]" />}>
                {DashboardViewComponent}
              </Suspense>
            </MotionTabsContent>
          </AnimatePresence>
        </Tabs>
      </div>
    </FilteredTasksProvider>
  );
});

export { TasksViewTabs };
export default TasksViewTabs;
