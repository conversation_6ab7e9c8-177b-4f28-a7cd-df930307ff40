import MenuTabs1 from "@/components/origin-ui/menu-tabs-1";
import { Button } from "@/components/ui/button";
import { PaperclipIcon } from "lucide-react";
import { memo } from "react";

/**
 * TaskDetails footer component with attachments and tabs
 * Memoized to prevent unnecessary re-renders
 */
export const TaskDetailsFooter = memo(function TaskDetailsFooter() {
  return (
    <>
      {/* Attachments */}
      <div className="flex flex-col justify-between gap-2">
        <div className="flex flex-row items-center justify-between gap-2">
          <div className="flex items-center gap-3">
            <PaperclipIcon className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-600">Attachments</span>
            <span className="text-sm text-gray-600">(2)</span>
          </div>
          <Button variant="outline" size="sm">
            Download All
          </Button>
        </div>
      </div>

      {/* Tabs - Subtasks, Comments, Activity */}
      <div className="">
        <MenuTabs1 />
      </div>
    </>
  );
});

export default TaskDetailsFooter;
