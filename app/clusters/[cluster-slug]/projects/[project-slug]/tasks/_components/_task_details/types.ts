import { TagModel, TaskModel, UserModel } from "@/db/schemas/tasks.schema";

/**
 * Props for the main TaskDetails component
 */
export interface TaskDetailsProps {
  task: TaskModel;
  isSheetOpen: boolean;
  setIsSheetOpen: (isOpen: boolean) => void;
  projectId?: string;
}

/**
 * State for all inline editing modes
 */
export interface EditingState {
  isEditingTitle: boolean;
  isEditingEffort: boolean;
  isEditingTags: boolean;
  isEditingAssignee: boolean;
  isDatePopoverOpen: boolean;
}

/**
 * Local state for form fields
 */
export interface LocalFieldState {
  dueDate: Date | undefined;
  localEffort: number;
  showCopyCheck: boolean;
}

/**
 * Event handler function types
 */
export interface TaskDetailsHandlers {
  handleStatusChange: (status: string) => Promise<void>;
  handleTitleChange: (newTitle: string) => Promise<void>;
  handleDateChange: (date: Date | undefined) => Promise<void>;
  handleEffortChange: (effort: number) => Promise<void>;
  handleTagsChange: (newTags: TagModel[]) => Promise<void>;
  handleAssigneeChange: (newAssignees: UserModel[]) => Promise<void>;
  handleLifeCycleStatusChange: (lifeCycleStatus: string) => Promise<void>;
  copyTaskId: () => void;
}

/**
 * Editing state setters
 */
export interface EditingStateSetters {
  setIsEditingTitle: (editing: boolean) => void;
  setIsEditingEffort: (editing: boolean) => void;
  setIsEditingTags: (editing: boolean) => void;
  setIsEditingAssignee: (editing: boolean) => void;
  setIsDatePopoverOpen: (open: boolean) => void;
}

/**
 * Local field state setters
 */
export interface LocalFieldStateSetters {
  setDueDate: (date: Date | undefined) => void;
  setLocalEffort: (effort: number) => void;
  setShowCopyCheck: (show: boolean) => void;
}

/**
 * Combined state management interface
 */
export interface TaskDetailsState extends EditingState, LocalFieldState {
  hasActiveEditor: boolean;
}

/**
 * Combined setters interface
 */
export interface TaskDetailsStateSetters extends EditingStateSetters, LocalFieldStateSetters {}

/**
 * Hook return type for useTaskDetailsState
 */
export interface UseTaskDetailsStateReturn {
  state: TaskDetailsState;
  setters: TaskDetailsStateSetters;
}

/**
 * Hook return type for useTaskDetailsHandlers
 */
export type UseTaskDetailsHandlersReturn = TaskDetailsHandlers;

/**
 * Props for field components
 */
export interface TaskFieldProps {
  task: TaskModel;
  projectId?: string;
  state: TaskDetailsState;
  setters: TaskDetailsStateSetters;
  handlers: TaskDetailsHandlers;
}
