import { Button } from "@/components/ui/button";
import { CheckIcon } from "@/components/ui/check";
import { SeperatorVertical } from "@/components/ui/separator-vertical";
import { SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { TaskModel } from "@/db/schemas/tasks.schema";
import { memo } from "react";
import TitleEditor from "../../TitleEditor";
import type { TaskDetailsHandlers, TaskDetailsState, TaskDetailsStateSetters } from "../types";

interface TaskDetailsHeaderProps {
  task: TaskModel;
  state: TaskDetailsState;
  setters: TaskDetailsStateSetters;
  handlers: TaskDetailsHandlers;
}

/**
 * TaskDetails header component with copy functionality and title editing
 * Memoized to prevent unnecessary re-renders
 */
export const TaskDetailsHeader = memo(function TaskDetailsHeader({
  task,
  state,
  setters,
  handlers,
}: TaskDetailsHeaderProps) {
  const { isEditingTitle, showCopyCheck } = state;
  const { setIsEditingTitle, setIsEditingEffort, setIsEditingTags, setIsDatePopoverOpen } = setters;
  const { handleTitleChange, copyTaskId } = handlers;

  return (
    <>
      {/* Header */}
      <div className="flex flex-row items-center gap-1 px-8 pt-4">
        {showCopyCheck && (
          <span className="w-16 h-7 text-green-500 flex items-center justify-center">
            <CheckIcon size={22} />
          </span>
        )}
        {!showCopyCheck && (
          <Button
            variant="ghost"
            size="icon"
            className="py-1 size-auto hover:bg-gray-100 dark:hover:bg-gray-800"
            onClick={copyTaskId}
          >
            <div className="flex items-center justify-center px-2 font-mono text-sm text-neutral-600 w-16 h-5">
              {task.key}
            </div>
          </Button>
        )}
        <SeperatorVertical height="5 mx-2 mr-3" />
      </div>

      {/* Title Section */}
      <div className="flex flex-col h-full px-4">
        <SheetHeader>
          {isEditingTitle ? (
            <TitleEditor
              value={task.title}
              onSave={handleTitleChange}
              onCancel={() => setIsEditingTitle(false)}
            />
          ) : (
            <SheetTitle
              className="text-4xl font-medium text-neutral-800 dark:text-neutral-200 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 px-2 py-1 rounded transition-colors"
              onClick={() => {
                // Cancel other active editors
                setIsEditingEffort(false);
                setIsEditingTags(false);
                setIsDatePopoverOpen(false);
                setIsEditingTitle(true);
              }}
            >
              {task.title}
            </SheetTitle>
          )}
        </SheetHeader>
      </div>
    </>
  );
});

export default TaskDetailsHeader;
