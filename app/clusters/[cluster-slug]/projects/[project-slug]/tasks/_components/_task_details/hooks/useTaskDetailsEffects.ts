import { TaskModel } from "@/db/schemas/tasks.schema";
import { parseAsString, useQueryState } from "nuqs";
import { useEffect, useRef } from "react";
import type { TaskDetailsState, TaskDetailsStateSetters } from "../types";

/**
 * Consolidated effects hook for TaskDetails component
 * Manages URL state, keyboard events, and state synchronization
 */
export function useTaskDetailsEffects(
  task: TaskModel,
  isSheetOpen: boolean,
  state: TaskDetailsState,
  setters: TaskDetailsStateSetters
) {
  // URL state management - add/remove task ID when component mounts/unmounts
  const [, setTaskId] = useQueryState("id", parseAsString.withOptions({ clearOnDefault: true }));

  // Track if this instance has ever been opened to avoid cleanup from inactive instances
  const hasBeenOpenedRef = useRef(false);

  // Manage URL state based on sheet open/close state
  useEffect(() => {
    if (isSheetOpen) {
      // Add task ID to URL when sheet opens
      setTaskId(task.id);
      hasBeenOpenedRef.current = true;
    } else if (hasBeenOpenedRef.current) {
      // Only remove task ID if this instance was previously opened
      setTaskId(null);
      hasBeenOpenedRef.current = false;
    }
  }, [isSheetOpen, task.id, setTaskId]);

  // Cleanup: only remove task ID from URL if this instance was opened
  useEffect(() => {
    return () => {
      if (hasBeenOpenedRef.current) {
        setTaskId(null);
      }
    };
  }, [setTaskId]);

  // Sync local dueDate state with task prop changes
  useEffect(() => {
    setters.setDueDate(task.dueDate ? new Date(task.dueDate) : undefined);
  }, [task.dueDate, setters]);

  // Sync local effort state with task prop changes
  useEffect(() => {
    setters.setLocalEffort(task.effort || 0);
  }, [task.effort, setters]);

  // Hierarchical Escape key behavior
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        // If any inline editor is active, cancel it first
        if (state.hasActiveEditor) {
          e.preventDefault();
          e.stopPropagation();

          // Cancel active editors in priority order
          if (state.isEditingTitle) {
            setters.setIsEditingTitle(false);
          } else if (state.isEditingEffort) {
            setters.setIsEditingEffort(false);
          } else if (state.isEditingTags) {
            setters.setIsEditingTags(false);
          } else if (state.isEditingAssignee) {
            setters.setIsEditingAssignee(false);
          } else if (state.isDatePopoverOpen) {
            setters.setIsDatePopoverOpen(false);
          }
        }
        // If no editors are active, let the Sheet handle the escape (close the panel)
        // This is handled by the Sheet component's default behavior
      }
    };

    // Add event listener to the document to catch all escape key events
    document.addEventListener("keydown", handleKeyDown, true); // Use capture phase

    return () => {
      document.removeEventListener("keydown", handleKeyDown, true);
    };
  }, [
    state.hasActiveEditor,
    state.isEditingTitle,
    state.isEditingEffort,
    state.isEditingTags,
    state.isEditingAssignee,
    state.isDatePopoverOpen,
    setters,
  ]);
}
