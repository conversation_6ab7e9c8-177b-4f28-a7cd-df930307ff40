import { TagModel, TaskModel, UserModel } from "@/db/schemas/tasks.schema";
import { format } from "date-fns";
import { useCallback } from "react";
import { toast } from "sonner";
import { useUpdateTask } from "../../../_hooks/use-crud-tasks";
import type { TaskDetailsStateSetters, UseTaskDetailsHandlersReturn } from "../types";

/**
 * Consolidated event handlers hook for TaskDetails component
 * Contains all async handlers for task updates and UI interactions
 */
export function useTaskDetailsHandlers(
  task: TaskModel,
  projectId: string | undefined,
  setters: TaskDetailsStateSetters
): UseTaskDetailsHandlersReturn {
  const updateTaskMutation = useUpdateTask();

  // Handle status change
  const handleStatusChange = useCallback(
    async (status: string) => {
      if (projectId && status !== task.status) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { status },
          });
          // Note: Success toast is handled by the useUpdateTask hook
        } catch (error) {
          console.error("Status update failed:", error);
          // Note: Error toast is handled by the useUpdateTask hook
        }
      }
    },
    [task.id, task.status, projectId, updateTaskMutation]
  );

  // Handle title change
  const handleTitleChange = useCallback(
    async (newTitle: string) => {
      if (projectId && newTitle.trim() !== task.title) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { title: newTitle.trim() },
          });
          toast.success(`Task title updated successfully`);
        } catch (error) {
          console.error("Title update failed:", error);
          toast.error("Failed to update task title");
        }
      }
      setters.setIsEditingTitle(false);
    },
    [task.id, task.title, projectId, updateTaskMutation, setters]
  );

  // Handle due date change
  const handleDateChange = useCallback(
    async (date: Date | undefined) => {
      setters.setDueDate(date);
      setters.setIsDatePopoverOpen(false); // Close the popover when date is selected

      if (projectId) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { dueDate: date ? date.toISOString() : null },
          });

          toast.success(
            `Due date for task ${task.key} ${date ? "set to " + format(date, "PPP") : "removed"}`
          );
        } catch (error) {
          // Rollback local state on error
          setters.setDueDate(task.dueDate ? new Date(task.dueDate) : undefined);
          console.error("Due date update failed:", error);
          toast.error("Failed to update due date");
        }
      }
    },
    [task.id, task.key, task.dueDate, projectId, updateTaskMutation, setters]
  );

  // Handle effort change
  const handleEffortChange = useCallback(
    async (effort: number) => {
      // Early return if effort hasn't changed to avoid unnecessary API calls
      if (effort === (task.effort || 0)) {
        setters.setIsEditingEffort(false);
        return;
      }

      // Update local state immediately for responsive UI
      setters.setLocalEffort(effort);

      if (projectId) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { effort },
          });
          toast.success(`Effort updated to ${effort}d`);
        } catch (error) {
          // Rollback local state on error
          setters.setLocalEffort(task.effort || 0);
          console.error("Effort update failed:", error);
          toast.error("Failed to update effort");
        }
      }
      setters.setIsEditingEffort(false);
    },
    [task.id, task.effort, projectId, updateTaskMutation, setters]
  );

  // Handle tag change
  const handleTagsChange = useCallback(
    async (newTags: TagModel[]) => {
      // Check if tags have actually changed to avoid unnecessary API calls
      const currentTagIds = new Set(task.tags.map((tag) => tag.id));
      const newTagIds = new Set(newTags.map((tag) => tag.id));

      const hasChanged =
        currentTagIds.size !== newTagIds.size ||
        !Array.from(currentTagIds).every((id) => newTagIds.has(id));

      if (!hasChanged) {
        setters.setIsEditingTags(false);
        return;
      }

      if (projectId) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { tags: newTags },
          });
          toast.success(`Tags updated successfully`);
        } catch (error) {
          console.error("Tags update failed:", error);
          toast.error("Failed to update tags");
        }
      }
      setters.setIsEditingTags(false);
    },
    [task.id, task.tags, projectId, updateTaskMutation, setters]
  );

  // Handle assignee change
  const handleAssigneeChange = useCallback(
    async (newAssignees: UserModel[]) => {
      // Check if assignees have actually changed to avoid unnecessary API calls
      const currentAssigneeIds = new Set(task.assignees?.map((assignee) => assignee.id) || []);
      const newAssigneeIds = new Set(newAssignees.map((assignee) => assignee.id));

      const hasChanged =
        currentAssigneeIds.size !== newAssigneeIds.size ||
        !Array.from(currentAssigneeIds).every((id) => newAssigneeIds.has(id));

      if (!hasChanged) {
        setters.setIsEditingAssignee(false);
        return;
      }

      if (projectId) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { assignees: newAssignees },
          });
          toast.success(`Assignees updated successfully`);
        } catch (error) {
          console.error("Assignees update failed:", error);
          toast.error("Failed to update assignees");
        }
      }
      setters.setIsEditingAssignee(false);
    },
    [task.id, task.assignees, projectId, updateTaskMutation, setters]
  );

  // Handle lifecycle status change
  const handleLifeCycleStatusChange = useCallback(
    async (lifeCycleStatus: string) => {
      if (projectId && lifeCycleStatus !== task.lifeCycleStatus) {
        try {
          await updateTaskMutation.mutateAsync({
            taskId: task.id,
            projectId,
            updates: { lifeCycleStatus },
          });

          // Special message for "dev done" status
          if (lifeCycleStatus === "dev done") {
            toast.success(`Task ${task.key} marked as dev done! Done date has been set.`);
          } else {
            toast.success(`Task ${task.key} lifecycle status updated to ${lifeCycleStatus}`);
          }
        } catch (error) {
          console.error("Lifecycle status update failed:", error);
          toast.error("Failed to update lifecycle status");
        }
      }
    },
    [task.id, task.key, task.lifeCycleStatus, projectId, updateTaskMutation]
  );

  // Handle copy task ID
  const copyTaskId = useCallback(() => {
    const currentUrl = window.location.href;
    navigator.clipboard.writeText(currentUrl);

    // Show check mark
    setters.setShowCopyCheck(true);

    // Reset after 3 seconds
    setTimeout(() => {
      setters.setShowCopyCheck(false);
    }, 3000);

    toast.success(`Task URL copied to clipboard`);
  }, [setters]);

  return {
    handleStatusChange,
    handleTitleChange,
    handleDateChange,
    handleEffortChange,
    handleTagsChange,
    handleAssigneeChange,
    handleLifeCycleStatusChange,
    copyTaskId,
  };
}
