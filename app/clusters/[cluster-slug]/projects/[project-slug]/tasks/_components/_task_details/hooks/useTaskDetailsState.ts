import { TaskModel } from "@/db/schemas/tasks.schema";
import { useMemo, useState } from "react";
import type { UseTaskDetailsStateReturn } from "../types";

/**
 * Consolidated state management hook for TaskDetails component
 * Manages all editing states and local field states in one place
 */
export function useTaskDetailsState(task: TaskModel): UseTaskDetailsStateReturn {
  // Editing states
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingEffort, setIsEditingEffort] = useState(false);
  const [isEditingTags, setIsEditingTags] = useState(false);
  const [isEditingAssignee, setIsEditingAssignee] = useState(false);
  const [isDatePopoverOpen, setIsDatePopoverOpen] = useState(false);

  // Local field states
  const [dueDate, setDueDate] = useState<Date | undefined>(
    task.dueDate ? new Date(task.dueDate) : undefined
  );
  const [localEffort, setLocalEffort] = useState(task.effort || 0);
  const [showCopyCheck, setShowCopyCheck] = useState(false);

  // Computed state
  const hasActiveEditor =
    isEditingTitle || isEditingEffort || isEditingTags || isEditingAssignee || isDatePopoverOpen;

  // Memoize setters to prevent infinite re-renders in useEffect dependencies
  const setters = useMemo(
    () => ({
      // Editing state setters
      setIsEditingTitle,
      setIsEditingEffort,
      setIsEditingTags,
      setIsEditingAssignee,
      setIsDatePopoverOpen,
      // Local field state setters
      setDueDate,
      setLocalEffort,
      setShowCopyCheck,
    }),
    [] // Empty dependency array since setter functions are stable
  );

  return {
    state: {
      // Editing states
      isEditingTitle,
      isEditingEffort,
      isEditingTags,
      isEditingAssignee,
      isDatePopoverOpen,
      // Local field states
      dueDate,
      localEffort,
      showCopyCheck,
      // Computed state
      hasActiveEditor,
    },
    setters,
  };
}
