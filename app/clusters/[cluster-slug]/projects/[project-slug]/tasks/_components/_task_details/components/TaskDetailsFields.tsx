import { AssigneeDisplay } from "@/components/tasks/tasks-assignee-display";
import { TaskLifeCycleStatusBadge } from "@/components/tasks/tasks-lifecycle-status-style";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { TagModel, TaskModel, UserModel } from "@/db/schemas/tasks.schema";
import { formatEffort, getValueIcon } from "@/lib/utils-tasks";
import { format } from "date-fns";
import {
  CalendarClockIcon,
  CircleDotIcon,
  ClockIcon,
  FileTextIcon,
  TagIcon,
  UsersIcon,
} from "lucide-react";
import { memo } from "react";
import { getTagKanbanViewStyles } from "../../../utils/tag-styles";
import ColumnStatusDropdown from "../../_views/list-view/_list_components/components/ColumnStatusDropdown";
import EffortEditor from "../../_views/list-view/_list_components/components/EffortEditor";
import AssigneeEditor from "../../AssigneeEditor";
import TagEditor from "../../TagEditor";
import type { TaskDetailsHandlers, TaskDetailsState, TaskDetailsStateSetters } from "../types";

interface TaskDetailsFieldsProps {
  task: TaskModel;
  projectId?: string;
  state: TaskDetailsState;
  setters: TaskDetailsStateSetters;
  handlers: TaskDetailsHandlers;
  availableTags: TagModel[];
  availableAssignees: UserModel[];
}

/**
 * TaskDetails fields component containing all form fields
 * Memoized to prevent unnecessary re-renders
 */
export const TaskDetailsFields = memo(function TaskDetailsFields({
  task,
  projectId,
  state,
  setters,
  handlers,
  availableTags,
  availableAssignees,
}: TaskDetailsFieldsProps) {
  const {
    isEditingEffort,
    isEditingTags,
    isEditingAssignee,
    isDatePopoverOpen,
    dueDate,
    localEffort,
  } = state;
  const {
    setIsEditingTitle,
    setIsEditingEffort,
    setIsEditingTags,
    setIsEditingAssignee,
    setIsDatePopoverOpen,
  } = setters;
  const {
    handleStatusChange,
    handleDateChange,
    handleEffortChange,
    handleTagsChange,
    handleAssigneeChange,
    handleLifeCycleStatusChange,
  } = handlers;

  const { value, lucideIcon: ValueIcon } = getValueIcon(task.value);

  return (
    <div className="w-full">
      {/* Status */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <CircleDotIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Status</span>
        </div>
        <div
          data-no-propagation
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          <ColumnStatusDropdown
            initialStatus={task.status}
            onStatusChange={handleStatusChange}
            projectId={projectId}
            withLabel
          />
        </div>
      </div>

      {/* Lifecycle Status */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10 ">
        <div className="flex items-center gap-3">
          <CircleDotIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Lifecycle Status</span>
        </div>
        <TaskLifeCycleStatusBadge
          taskId={task.id}
          initialStatus={task.lifeCycleStatus}
          onStatusChange={handleLifeCycleStatusChange}
        />
      </div>

      {/* Value */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <CircleDotIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Value</span>
        </div>
        <Badge
          variant="outline"
          className="font-normal text-xs py-0.5 px-2.5 rounded-full border flex items-center gap-1.5"
        >
          <ValueIcon className="w-1 h-3" />
          {value}
        </Badge>
      </div>

      {/* Assignees */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <UsersIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Assignee</span>
        </div>
        <div
          data-no-propagation
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {isEditingAssignee ? (
            <AssigneeEditor
              value={task.assignees || []}
              availableAssignees={availableAssignees}
              onSave={handleAssigneeChange}
              onCancel={() => setIsEditingAssignee(false)}
            />
          ) : (
            <div
              className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 px-2 py-1 rounded transition-colors"
              onClick={() => {
                // Cancel other active editors
                setIsEditingTitle(false);
                setIsEditingEffort(false);
                setIsEditingTags(false);
                setIsDatePopoverOpen(false);
                setIsEditingAssignee(true);
              }}
            >
              {task.assignees && task.assignees.length > 0 ? (
                <AssigneeDisplay
                  assignees={task.assignees}
                  variant="row"
                  maxVisible={2}
                  showNames
                />
              ) : (
                <span className="text-sm text-gray-400 dark:text-gray-500">Click to assign</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Tags */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <TagIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Tags</span>
        </div>
        <div
          data-no-propagation
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {isEditingTags ? (
            <TagEditor
              value={task.tags || []}
              availableTags={availableTags}
              onSave={handleTagsChange}
              onCancel={() => setIsEditingTags(false)}
            />
          ) : (
            <div
              className="flex flex-wrap gap-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 px-2 py-1 rounded transition-colors"
              onClick={() => {
                // Cancel other active editors
                setIsEditingTitle(false);
                setIsEditingEffort(false);
                setIsDatePopoverOpen(false);
                setIsEditingTags(true);
              }}
            >
              {task.tags && task.tags.length > 0 ? (
                task.tags.map((tag: TagModel, index: number) => (
                  <Badge
                    key={tag.id || `tag-${tag.name}-${index}`}
                    variant="outline"
                    className={`px-2 py-0.5 text-xs rounded-full ${
                      getTagKanbanViewStyles(tag.name) ||
                      "text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-300 dark:bg-gray-500/10 dark:border-gray-300"
                    }`}
                  >
                    {tag.name}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-gray-400 dark:text-gray-500">Click to add tags</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Due date */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <CalendarClockIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Due date</span>
        </div>
        <div
          data-no-propagation
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
          className="w-full"
        >
          {dueDate ? (
            <Popover open={isDatePopoverOpen} onOpenChange={setIsDatePopoverOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-auto px-2 py-1 font-normal flex items-center gap-1.5 hover:bg-gray-100 dark:hover:bg-gray-800 text-sm w-full justify-start"
                >
                  <ClockIcon className="flex-shrink-0 w-3 h-3 text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium">{format(dueDate, "PPP")}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar mode="single" selected={dueDate} onSelect={handleDateChange} />
              </PopoverContent>
            </Popover>
          ) : (
            <Popover open={isDatePopoverOpen} onOpenChange={setIsDatePopoverOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-auto px-2 py-1 font-normal flex items-center gap-1.5 hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-400 dark:text-gray-500 w-full justify-start"
                >
                  <ClockIcon className="flex-shrink-0 w-3 h-3" />
                  <span className="text-sm">Set due date</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar mode="single" selected={dueDate} onSelect={handleDateChange} />
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>

      {/* Effort */}
      <div className="grid grid-cols-[150px_1fr] items-center gap-2 h-10">
        <div className="flex items-center gap-3">
          <ClockIcon className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Effort</span>
        </div>
        <div
          data-no-propagation
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {isEditingEffort ? (
            <EffortEditor
              value={localEffort}
              onSave={(effort: number) => {
                handleEffortChange(effort);
                setIsEditingEffort(false);
              }}
              onCancel={() => setIsEditingEffort(false)}
              isTaskDetails
            />
          ) : (
            <button
              className="text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-1 rounded transition-colors w-full justify-start flex"
              onClick={() => {
                // Cancel other active editors
                setIsEditingTitle(false);
                setIsEditingTags(false);
                setIsDatePopoverOpen(false);
                setIsEditingEffort(true);
              }}
            >
              {formatEffort(localEffort)}
            </button>
          )}
        </div>
      </div>

      {/* Description */}
      <div className="space-y-4 pt-2">
        <div className="flex items-center gap-3">
          <FileTextIcon className="w-4 h-4 text-gray-600" />
          <h3 className="text-sm text-gray-600">Description</h3>
        </div>
        <Textarea
          value={task.description || ""}
          placeholder="Add a description to this task..."
          className="w-full min-h-[80px]"
          onChange={(e) => e.stopPropagation()}
          rows={4}
        />
      </div>
    </div>
  );
});

export default TaskDetailsFields;
