import { Button } from "@/components/ui/button";
import MultipleSelector, { type Option } from "@/components/ui/multiselect";
import { TagModel } from "@/db/schemas/tasks.schema";
import { CheckIcon, XIcon } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

interface TagEditorProps {
  value: TagModel[];
  availableTags: TagModel[];
  onSave: (value: TagModel[]) => void;
  onCancel: () => void;
  projectId?: string;
}

/**
 * Inline tag editor component using MultipleSelector
 * Memoized to prevent unnecessary re-renders during editing
 */
export const TagEditor = memo(function TagEditor({
  value,
  availableTags,
  onSave,
  onCancel,
}: TagEditorProps) {
  // Convert TagModel[] to Option[] for MultipleSelector
  const selectedOptions: Option[] = useMemo(
    () =>
      value.map((tag) => ({
        value: tag.id,
        label: tag.name,
      })),
    [value]
  );

  // Convert available tags to options
  const tagOptions: Option[] = useMemo(
    () =>
      availableTags.map((tag) => ({
        value: tag.id,
        label: tag.name,
      })),
    [availableTags]
  );

  const [currentValue, setCurrentValue] = useState<Option[]>(selectedOptions);
  const selectorRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Focus the selector when component mounts
    const focusSelector = () => {
      const input = selectorRef.current?.querySelector("input");
      if (input) {
        input.focus();
      }
    };

    // Small delay to ensure component is fully rendered
    const timeoutId = setTimeout(focusSelector, 50);
    return () => clearTimeout(timeoutId);
  }, []);

  // Check if values have changed
  const hasChanged = useMemo(() => {
    if (currentValue.length !== selectedOptions.length) return true;

    const currentIds = new Set(currentValue.map((opt) => opt.value));
    const originalIds = new Set(selectedOptions.map((opt) => opt.value));

    return !Array.from(currentIds).every((id) => originalIds.has(id));
  }, [currentValue, selectedOptions]);

  const handleSave = useCallback(() => {
    if (!hasChanged) {
      onCancel(); // No change, just cancel
      return;
    }

    // Convert Option[] back to TagModel[]
    const selectedTags: TagModel[] = currentValue.map((option) => {
      const originalTag = availableTags.find((tag) => tag.id === option.value);
      return originalTag || { id: option.value, name: option.label };
    });

    onSave(selectedTags);
  }, [currentValue, hasChanged, availableTags, onSave, onCancel]);

  // Handle clicks outside the TagEditor component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        // Don't auto-save if user clicked on action buttons (they handle their own logic)
        if (!(event.target as Element)?.closest("[data-action-button]")) {
          handleSave();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleSave]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleSave();
      } else if (e.key === "Escape") {
        e.preventDefault();
        onCancel();
      }
    },
    [handleSave, onCancel]
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent) => {
      // Don't auto-save on blur if user clicked on action buttons
      if (e.relatedTarget?.closest("[data-action-button]")) {
        return;
      }
      handleSave();
    },
    [handleSave]
  );

  return (
    <div ref={containerRef} className="flex gap-2 items-center" data-no-propagation>
      {/* Tag selector with reserved space for buttons */}
      <div ref={selectorRef} className="flex-1 min-w-0">
        <MultipleSelector
          value={currentValue}
          defaultOptions={tagOptions}
          placeholder="Select tags..."
          hideClearAllButton
          hidePlaceholderWhenSelected
          creatable
          onChange={setCurrentValue}
          emptyIndicator={
            <p className="text-center text-sm text-gray-500">
              {availableTags.length === 0
                ? "No tags available for this project yet."
                : "No results found"}
            </p>
          }
          className="min-h-[2.5rem]"
          commandProps={{
            label: "Select tags",
          }}
          inputProps={{
            onKeyDown: handleKeyDown,
            onBlur: handleBlur,
          }}
        />
      </div>

      {/* Action buttons stacked vertically and centered */}
      <div className="flex flex-col gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
          onClick={handleSave}
          data-action-button
          data-no-propagation
        >
          <CheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
          onClick={onCancel}
          data-action-button
          data-no-propagation
        >
          <XIcon className="h-3 w-3 text-red-600 dark:text-red-400" />
        </Button>
      </div>
    </div>
  );
});

export default TagEditor;
