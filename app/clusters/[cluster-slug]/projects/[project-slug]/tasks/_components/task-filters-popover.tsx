"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  ColumnStatus,
  LifeCycleStatus,
  TagModel,
  UserModel,
  Value,
} from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import { useMemo } from "react";
import { TaskFiltersState } from "../_types/filter.types";
import { FilterIcon } from "./task-icons";

interface TaskFiltersPopoverProps {
  filtersState: TaskFiltersState;
  availableTags: TagModel[];
  availableAssignees: UserModel[];
}

// Status options with labels
const STATUS_OPTIONS: { value: ColumnStatus; label: string; color: string }[] = [
  { value: "Backlog", label: "Backlog", color: "gray" },
  { value: "To do", label: "To Do", color: "blue" },
  { value: "In progress", label: "In Progress", color: "amber" },
  { value: "In review", label: "In Review", color: "purple" },
  { value: "Done", label: "Done", color: "green" },
];

// Lifecycle status options with labels
const LIFECYCLE_OPTIONS: { value: LifeCycleStatus; label: string; color: string }[] = [
  { value: "created", label: "Created", color: "gray" },
  { value: "ready", label: "Ready", color: "blue" },
  { value: "started", label: "Started", color: "amber" },
  { value: "dev done", label: "Dev Done", color: "green" },
  { value: "test done", label: "Test Done", color: "emerald" },
  { value: "deploy ready", label: "Deploy Ready", color: "purple" },
  { value: "delivered", label: "Delivered", color: "green" },
];

// Value options with labels
const VALUE_OPTIONS: { value: Value; label: string; color: string }[] = [
  { value: "Huge", label: "Huge", color: "red" },
  { value: "High", label: "High", color: "orange" },
  { value: "Normal", label: "Normal", color: "blue" },
  { value: "Low", label: "Low", color: "gray" },
  { value: "Not set", label: "Not Set", color: "gray" },
];

export function TaskFiltersPopover({
  filtersState,
  availableTags,
  availableAssignees,
}: TaskFiltersPopoverProps) {
  const {
    filters,
    hasActiveFilters,
    getActiveFilterCount,
    clearAllFilters,
    toggleStatusFilter,
    toggleLifecycleFilter,
    toggleTagFilter,
    toggleAssigneeFilter,
    toggleValueFilter,
    setDateRange,
    toggleNoEffortFilter,
  } = filtersState;

  // Format date range for display
  const formatDateRange = useMemo(() => {
    if (!filters.dateRange?.from && !filters.dateRange?.to) return "Select date range";
    if (filters.dateRange.from && filters.dateRange.to) {
      return `${filters.dateRange.from.toLocaleDateString()} - ${filters.dateRange.to.toLocaleDateString()}`;
    }
    if (filters.dateRange.from) {
      return `From ${filters.dateRange.from.toLocaleDateString()}`;
    }
    if (filters.dateRange.to) {
      return `Until ${filters.dateRange.to.toLocaleDateString()}`;
    }
    return "Select date range";
  }, [filters.dateRange]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-8 px-3 rounded-none relative overflow-visible",
            hasActiveFilters() &&
              "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800"
          )}
        >
          <FilterIcon className="w-4 h-4" />
          {hasActiveFilters() && (
            <Badge
              variant="default"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full text-xs font-bold flex items-center justify-center p-0 border border-background z-10"
            >
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 max-w-6xl" align="end">
        <div className="flex flex-row gap-4 p-4">
          {/* Status Filter */}
          <div className="min-w-[140px]">
            <h5 className="text-sm font-medium mb-3">Status</h5>
            <div className="space-y-2">
              {STATUS_OPTIONS.map(({ value, label, color }) => (
                <div key={value} className="flex items-center">
                  <Checkbox
                    id={`status-${value}`}
                    checked={filters.status?.includes(value) || false}
                    onCheckedChange={() => toggleStatusFilter(value)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label htmlFor={`status-${value}`} className="ml-2 flex items-center">
                    <Badge
                      className={cn(
                        "text-xs",
                        color === "gray" &&
                          "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300",
                        color === "blue" &&
                          "bg-blue-100 dark:bg-blue-950/40 text-blue-700 dark:text-blue-400",
                        color === "amber" &&
                          "bg-amber-100 dark:bg-amber-950/40 text-amber-700 dark:text-amber-400",
                        color === "purple" &&
                          "bg-purple-100 dark:bg-purple-950/40 text-purple-700 dark:text-purple-400",
                        color === "green" &&
                          "bg-green-100 dark:bg-green-950/40 text-green-700 dark:text-green-400"
                      )}
                    >
                      {label}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Lifecycle Status Filter */}
          <div className="min-w-[140px]">
            <h5 className="text-sm font-medium mb-3">Lifecycle Status</h5>
            <div className="space-y-2">
              {LIFECYCLE_OPTIONS.map(({ value, label, color }) => (
                <div key={value} className="flex items-center">
                  <Checkbox
                    id={`lifecycle-${value}`}
                    checked={filters.lifecycleStatus?.includes(value) || false}
                    onCheckedChange={() => toggleLifecycleFilter(value)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label htmlFor={`lifecycle-${value}`} className="ml-2 flex items-center">
                    <Badge
                      className={cn(
                        "text-xs",
                        color === "gray" &&
                          "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300",
                        color === "blue" &&
                          "bg-blue-100 dark:bg-blue-950/40 text-blue-700 dark:text-blue-400",
                        color === "amber" &&
                          "bg-amber-100 dark:bg-amber-950/40 text-amber-700 dark:text-amber-400",
                        color === "purple" &&
                          "bg-purple-100 dark:bg-purple-950/40 text-purple-700 dark:text-purple-400",
                        color === "green" &&
                          "bg-green-100 dark:bg-green-950/40 text-green-700 dark:text-green-400",
                        color === "emerald" &&
                          "bg-emerald-100 dark:bg-emerald-950/40 text-emerald-700 dark:text-emerald-400"
                      )}
                    >
                      {label}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Tags Filter */}
          <div className="min-w-[140px]">
            <h5 className="text-sm font-medium mb-3">Tags</h5>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableTags.map((tag) => (
                <div key={tag.id} className="flex items-center">
                  <Checkbox
                    id={`tag-${tag.id}`}
                    checked={filters.tags?.includes(tag.name) || false}
                    onCheckedChange={() => toggleTagFilter(tag.name)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label htmlFor={`tag-${tag.id}`} className="ml-2 flex items-center">
                    <Badge variant="outline" className="text-xs">
                      {tag.name}
                    </Badge>
                  </Label>
                </div>
              ))}
              {availableTags.length === 0 && (
                <p className="text-sm text-gray-500">No tags available</p>
              )}
            </div>
          </div>

          {/* Assignees Filter */}
          <div className="min-w-[140px]">
            <h5 className="text-sm font-medium mb-3">Assignees</h5>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableAssignees.map((assignee) => (
                <div key={assignee.id} className="flex items-center">
                  <Checkbox
                    id={`assignee-${assignee.id}`}
                    checked={filters.assignees?.includes(assignee.id) || false}
                    onCheckedChange={() => toggleAssigneeFilter(assignee.id)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label
                    htmlFor={`assignee-${assignee.id}`}
                    className="ml-2 flex items-center gap-2"
                  >
                    <Avatar className="h-5 w-5">
                      <AvatarImage src={assignee.avatar} />
                      <AvatarFallback className="text-xs">
                        {assignee.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{assignee.name}</span>
                  </Label>
                </div>
              ))}
              {availableAssignees.length === 0 && (
                <p className="text-sm text-gray-500">No assignees available</p>
              )}
            </div>
          </div>

          {/* Value & Special Filters */}
          <div className="min-w-[140px]">
            <h5 className="text-sm font-medium mb-3">Value</h5>
            <div className="space-y-2 mb-4">
              {VALUE_OPTIONS.map(({ value, label, color }) => (
                <div key={value} className="flex items-center">
                  <Checkbox
                    id={`value-${value}`}
                    checked={filters.value?.includes(value) || false}
                    onCheckedChange={() => toggleValueFilter(value)}
                    className="border-gray-300 dark:border-gray-600"
                  />
                  <Label htmlFor={`value-${value}`} className="ml-2 flex items-center">
                    <Badge
                      className={cn(
                        "text-xs",
                        color === "red" &&
                          "bg-red-100 dark:bg-red-950/40 text-red-700 dark:text-red-400",
                        color === "orange" &&
                          "bg-orange-100 dark:bg-orange-950/40 text-orange-700 dark:text-orange-400",
                        color === "blue" &&
                          "bg-blue-100 dark:bg-blue-950/40 text-blue-700 dark:text-blue-400",
                        color === "gray" &&
                          "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                      )}
                    >
                      {label}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>

            {/* No Effort Filter */}
            <div className="border-t pt-3">
              <div className="flex items-center">
                <Checkbox
                  id="no-effort"
                  checked={filters.noEffort || false}
                  onCheckedChange={toggleNoEffortFilter}
                  className="border-gray-300 dark:border-gray-600"
                />
                <Label htmlFor="no-effort" className="ml-2 text-sm">
                  No effort assigned
                </Label>
              </div>
            </div>
          </div>

          {/* Date Range Filter */}
          <div className="min-w-[180px]">
            <h5 className="text-sm font-medium mb-3">Due Date Range</h5>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "w-full justify-start text-left font-normal text-xs",
                    !filters.dateRange?.from && !filters.dateRange?.to && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-1 h-3 w-3" />
                  <span className="truncate">{formatDateRange}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  mode="range"
                  selected={{
                    from: filters.dateRange?.from,
                    to: filters.dateRange?.to,
                  }}
                  onSelect={(range) => {
                    setDateRange({
                      from: range?.from,
                      to: range?.to,
                    });
                  }}
                  numberOfMonths={1}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="border-t p-4 flex justify-between items-center">
          <Button variant="outline" size="sm" onClick={clearAllFilters}>
            Reset
          </Button>
          <div className="text-sm text-gray-500">
            {getActiveFilterCount()} filter{getActiveFilterCount() !== 1 ? "s" : ""} active
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
