"use client";

import { type ColumnStatus, type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { useVirtualizer } from "@tanstack/react-virtual";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useFilteredTaskGroupsByStatus } from "../../_hooks/use-filtered-task-groups";
import { TaskGroup, TaskListSkeleton } from "../_views/list-view/_list_components/task-list";
import { useFilteredTasksContext } from "../filtered-tasks-provider";
import TaskDetails from "../task-details";
import { useTasksContext } from "../tasks-provider";

// Create a memoized task group component without individual Suspense boundaries
const MemoizedTaskGroup = memo(
  ({
    taskGroup,
    onStatusChange,
    onTaskClick,
    projectId,
  }: {
    taskGroup: TaskGroupModel;
    onStatusChange?: (taskId: string, newStatus: ColumnStatus) => void;
    onTaskClick?: (task: TaskModel) => void;
    projectId?: string;
  }) => (
    <TaskGroup
      taskGroup={taskGroup}
      onStatusChange={onStatusChange}
      onTaskClick={onTaskClick}
      projectId={projectId}
    />
  ),
  (prevProps, nextProps) => {
    // Custom comparison to prevent unnecessary re-renders
    const prevGroup = prevProps.taskGroup;
    const nextGroup = nextProps.taskGroup;

    // Check basic group properties
    if (
      prevGroup.id !== nextGroup.id ||
      prevGroup.count !== nextGroup.count ||
      prevGroup.tasks.length !== nextGroup.tasks.length ||
      prevProps.projectId !== nextProps.projectId
    ) {
      return false; // Re-render
    }

    // Deep comparison of task effort values and other properties
    for (let i = 0; i < prevGroup.tasks.length; i++) {
      const prevTask = prevGroup.tasks[i];
      const nextTask = nextGroup.tasks[i];

      if (
        prevTask.id !== nextTask.id ||
        prevTask.effort !== nextTask.effort ||
        prevTask.updatedAt !== nextTask.updatedAt ||
        prevTask.isOptimistic !== nextTask.isOptimistic
      ) {
        return false; // Re-render
      }
    }

    return true; // Skip re-render
  }
);

// Add display name to the memoized component
MemoizedTaskGroup.displayName = "MemoizedTaskGroup";

// Export the component as a named export for better DX
export default function StatusGroupedView({ isSortReversed = false }) {
  // Get projectId from context instead of discovering from cache
  const { projectId } = useTasksContext();

  // Use filtered tasks context
  const { filteredTasks, isLoading } = useFilteredTasksContext();

  // Global task selection state
  const [selectedTask, setSelectedTask] = useState<TaskModel | null>(null);
  const [isTaskDetailsOpen, setIsTaskDetailsOpen] = useState(false);

  const handleTaskClick = useCallback((task: TaskModel) => {
    setSelectedTask(task);
    setIsTaskDetailsOpen(true);
  }, []);

  const [isReversed, setIsReversed] = useState(isSortReversed);
  const parentRef = useRef<HTMLDivElement>(null);

  // Get task groups using filtered tasks
  const taskGroups = useFilteredTaskGroupsByStatus(filteredTasks);

  // Update isReversed when prop changes
  useEffect(() => {
    setIsReversed(isSortReversed);
  }, [isSortReversed]);

  // Stable reference to virtualizer options
  const virtualizerOptions = useMemo(
    () => ({
      count: taskGroups.length,
      getScrollElement: () => parentRef.current,
      overscan: 2,
      initialMeasureCache: {
        has: () => false,
        set: () => {},
        get: () => undefined,
      },
    }),
    [taskGroups.length]
  );

  // Memoized size estimator function
  const estimateSize = useCallback(
    (index: number) => {
      const adjustedIndex = isReversed ? taskGroups.length - 1 - index : index;
      const taskCount = taskGroups[adjustedIndex]?.tasks.length || 0;

      const groupHeaderHeight = 40;
      const tableHeaderHeight = taskCount > 0 ? 32 : 0;
      const taskRowHeight = 48;
      const emptyStateHeight = taskCount === 0 ? 48 : 0;
      const paddingAndBorders = 16;
      const bottomMargin = taskCount === 0 ? 4 : 0;

      return (
        groupHeaderHeight +
        tableHeaderHeight +
        (taskCount * taskRowHeight || emptyStateHeight) +
        paddingAndBorders +
        bottomMargin
      );
    },
    [taskGroups, isReversed]
  );

  // Set up the virtualizer with optimized dependencies
  const rowVirtualizer = useVirtualizer({
    ...virtualizerOptions,
    estimateSize,
  });

  // Extract taskCounts as a separate variable for dependency tracking
  const taskCountString = useMemo(
    () => taskGroups.map((group) => group.tasks.length).join(","),
    [taskGroups]
  );

  // Force recalculation when task count changes or sort order changes
  useEffect(() => {
    rowVirtualizer.measure();
  }, [taskCountString, rowVirtualizer, isReversed]);

  // Use a stable reference to virtual items and total size
  const virtualItems = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  // Create a toggle function and expose it for the parent component
  const toggleSortOrder = useCallback(() => {
    setIsReversed((prev) => !prev);
    return !isReversed;
  }, [isReversed]);

  // Expose the sort state and toggle function as properties
  (StatusGroupedView as unknown as { isReversed: boolean }).isReversed = isReversed;
  (StatusGroupedView as unknown as { toggleSortOrder: () => void }).toggleSortOrder =
    toggleSortOrder;

  // Show loading state with single skeleton
  if (isLoading) {
    return <TaskListSkeleton />;
  }

  return (
    <>
      <div
        ref={parentRef}
        className="overflow-auto h-[calc(100vh-14rem)] scroll-smooth"
        onScroll={(e) => e.currentTarget.addEventListener("scroll", () => {}, { passive: true })}
      >
        <div
          style={{
            height: `${totalSize}px`,
            width: "100%",
            position: "relative",
            willChange: "contents",
          }}
        >
          {virtualItems.map((virtualRow) => {
            const adjustedIndex = isReversed
              ? taskGroups.length - 1 - virtualRow.index
              : virtualRow.index;
            const taskGroup = taskGroups[adjustedIndex];

            if (!taskGroup) return null;

            return (
              <div
                key={taskGroup.id}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: virtualRow.size,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
                data-index={virtualRow.index}
              >
                <MemoizedTaskGroup
                  taskGroup={taskGroup}
                  onTaskClick={handleTaskClick}
                  projectId={projectId || undefined}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Global TaskDetails component */}
      {selectedTask && (
        <TaskDetails
          task={selectedTask}
          isSheetOpen={isTaskDetailsOpen}
          setIsSheetOpen={setIsTaskDetailsOpen}
          projectId={projectId}
        />
      )}
    </>
  );
}
