"use client";

import { type ColumnStatus, type TaskModel } from "@/db/schemas/tasks.schema";
import { useVirtualizer } from "@tanstack/react-virtual";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useFilteredTaskGroupsByEffort } from "../../_hooks/use-filtered-task-groups";
import { TaskGroup } from "../_views/list-view/_list_components/task-list";
import { TaskListSkeleton } from "../_views/list-view/_list_components/task-list-skeleton";
import { useFilteredTasksContext } from "../filtered-tasks-provider";
import TaskDetails from "../task-details";
import { useTasksContext } from "../tasks-provider";

// Create a memoized task group component without individual Suspense boundaries
const MemoizedTaskGroup = memo(
  ({
    taskGroup,
    onStatusChange,
    onTaskClick,
    projectId,
  }: {
    taskGroup: {
      id: string;
      name: string;
      count: number;
      tasks: TaskModel[];
      color: string;
      icon: string;
    };
    onStatusChange: (taskId: string, newStatus: ColumnStatus) => void;
    onTaskClick?: (task: TaskModel) => void;
    projectId?: string;
  }) => (
    <TaskGroup
      taskGroup={taskGroup}
      onStatusChange={onStatusChange}
      onTaskClick={onTaskClick}
      projectId={projectId}
    />
  ),
  (prevProps, nextProps) => {
    // Custom comparison for performance
    return (
      prevProps.taskGroup.id === nextProps.taskGroup.id &&
      prevProps.taskGroup.count === nextProps.taskGroup.count &&
      prevProps.taskGroup.tasks.length === nextProps.taskGroup.tasks.length &&
      prevProps.projectId === nextProps.projectId
    );
  }
);

MemoizedTaskGroup.displayName = "MemoizedTaskGroup";

export default function EffortGroupedView({ isSortReversed = false }) {
  // Get projectId from context instead of discovering from cache
  const { projectId } = useTasksContext();

  // Use filtered tasks context
  const { filteredTasks, isLoading } = useFilteredTasksContext();

  // Global task selection state
  const [selectedTask, setSelectedTask] = useState<TaskModel | null>(null);
  const [isTaskDetailsOpen, setIsTaskDetailsOpen] = useState(false);

  const handleTaskClick = useCallback((task: TaskModel) => {
    setSelectedTask(task);
    setIsTaskDetailsOpen(true);
  }, []);

  const [isReversed, setIsReversed] = useState(isSortReversed);
  const parentRef = useRef<HTMLDivElement>(null);

  // Get task groups using filtered tasks
  const taskGroups = useFilteredTaskGroupsByEffort(filteredTasks);

  // Update isReversed when prop changes
  useEffect(() => {
    setIsReversed(isSortReversed);
  }, [isSortReversed]);

  // Memoized size estimator function
  const estimateSize = useCallback(
    (index: number) => {
      const adjustedIndex = isReversed ? taskGroups.length - 1 - index : index;
      const taskCount = taskGroups[adjustedIndex]?.tasks.length || 0;

      const groupHeaderHeight = 40;
      const tableHeaderHeight = taskCount > 0 ? 32 : 0;
      const taskRowHeight = 48;
      const emptyStateHeight = taskCount === 0 ? 48 : 0;
      const paddingAndBorders = 16;
      const bottomMargin = 4;

      return (
        groupHeaderHeight +
        tableHeaderHeight +
        (taskCount * taskRowHeight || emptyStateHeight) +
        paddingAndBorders +
        bottomMargin
      );
    },
    [taskGroups, isReversed]
  );

  // Virtual scrolling with proper size estimation
  const virtualizer = useVirtualizer({
    count: taskGroups.length,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 2,
  });

  const virtualItems = virtualizer.getVirtualItems();
  const totalSize = virtualizer.getTotalSize();

  // Extract taskCounts as a separate variable for dependency tracking
  const taskCountString = useMemo(
    () => taskGroups.map((group) => group.tasks.length).join(","),
    [taskGroups]
  );

  // Force recalculation when task count changes or sort order changes
  useEffect(() => {
    virtualizer.measure();
  }, [taskCountString, virtualizer, isReversed]);

  // Handle status change with optimistic updates
  const handleStatusChange = useCallback(async (taskId: string, newStatus: ColumnStatus) => {
    try {
      // Update the task status
      // This will be handled by the task context or mutation
      console.log(`Updating task ${taskId} to status ${newStatus}`);
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  }, []);

  if (isLoading) {
    return <TaskListSkeleton />;
  }

  if (!taskGroups || taskGroups.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        No tasks found.
      </div>
    );
  }

  return (
    <>
      <div
        ref={parentRef}
        className="overflow-auto h-[calc(100vh-14rem)] scroll-smooth"
        onScroll={(e) => e.currentTarget.addEventListener("scroll", () => {}, { passive: true })}
      >
        <div
          style={{
            height: `${totalSize}px`,
            width: "100%",
            position: "relative",
            willChange: "contents",
          }}
        >
          {virtualItems.map((virtualRow) => {
            const adjustedIndex = isReversed
              ? taskGroups.length - 1 - virtualRow.index
              : virtualRow.index;
            const taskGroup = taskGroups[adjustedIndex];

            if (!taskGroup) return null;

            return (
              <div
                key={taskGroup.id}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: virtualRow.size,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
                data-index={virtualRow.index}
              >
                <MemoizedTaskGroup
                  taskGroup={taskGroup}
                  onStatusChange={handleStatusChange}
                  onTaskClick={handleTaskClick}
                  projectId={projectId}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Global TaskDetails component */}
      {selectedTask && (
        <TaskDetails
          task={selectedTask}
          isSheetOpen={isTaskDetailsOpen}
          setIsSheetOpen={setIsTaskDetailsOpen}
          projectId={projectId}
        />
      )}
    </>
  );
}
