import { Badge } from "@/components/ui/badge";
import { Card, CardDescription, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";
import {
  BugIcon,
  ClockIcon,
  LightbulbIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  WrenchIcon,
} from "lucide-react";
import { memo, useMemo } from "react";
import { TaskAnalyticsData } from "../_mockdata/tasks-analytics.data";
import { TaskAnalyticsModel } from "../_types/tasks-analytics.types";

// Skeleton component for loading state
export function TaskAnalyticsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-2">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="p-4 border space-y-2 rounded-lg animate-pulse">
          <Skeleton className="h-4 rounded" />
          <Skeleton className="h-8 rounded" />
          <Skeleton className="h-3 rounded" />
        </div>
      ))}
    </div>
  );
}

type StatCardProps = {
  variant: "bugs" | "features" | "improvements" | "overall";
  data: TaskAnalyticsModel;
  index: number;
};

const getTrendIcon = (trend: string) => {
  return trend.startsWith("+") ? (
    <TrendingUpIcon className="size-3" />
  ) : (
    <TrendingDownIcon className="size-3" />
  );
};

const getCategoryIcon = (variant: StatCardProps["variant"]) => {
  switch (variant) {
    case "bugs":
      return <BugIcon className="size-4" />;
    case "features":
      return <LightbulbIcon className="size-4" />;
    case "improvements":
      return <WrenchIcon className="size-4" />;
    default:
      return <ClockIcon className="size-4" />;
  }
};

// Pre-define animation variants for better performance
const cardAnimationVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (index: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.25,
      delay: index * 0.08, // Slightly faster delay
      ease: [0.2, 0.65, 0.3, 0.9], // Optimized ease curve
    },
  }),
};

const BoardStatCard = memo(({ variant, data, index }: StatCardProps) => {
  // Use useMemo to prevent recalculations
  const content = useMemo(() => {
    switch (variant) {
      case "bugs":
        return {
          title: "Active Bugs",
          value: data.categories.bugs.count,
          trend: data.categories.bugs.trend,
          description: `${data.categories.bugs.priority.high} high priority`,
          footer: "Bug resolution metrics",
        };
      case "features":
        return {
          title: "Feature Requests",
          value: data.categories.features.count,
          trend: data.categories.features.trend,
          description: `${data.categories.features.priority.high} high priority`,
          footer: "Feature development tracking",
        };
      case "improvements":
        return {
          title: "Improvements",
          value: data.categories.improvements.count,
          trend: data.categories.improvements.trend,
          description: `${data.categories.improvements.priority.high} high priority`,
          footer: "Improvement initiatives",
        };
      default:
        return {
          title: "Total Board",
          value: data.totalItems,
          trend: data.teamMetrics.backlogGrowthRate,
          description: `Health Score: ${data.teamMetrics.healthScore}%`,
          footer: `Avg. Resolution: ${data.teamMetrics.averageTimeToResolution}`,
        };
    }
  }, [variant, data]);

  // Memoize the icon
  const icon = useMemo(() => getCategoryIcon(variant), [variant]);

  // Memoize the trend icon
  const trendIcon = useMemo(() => getTrendIcon(content.trend), [content.trend]);

  return (
    <motion.div
      custom={index}
      initial="hidden"
      animate="visible"
      variants={cardAnimationVariants}
      style={{
        willChange: "transform, opacity",
        transform: "translateZ(0)", // Hardware acceleration
        backfaceVisibility: "hidden", // Additional hardware acceleration
      }}
    >
      <Card className="p-4 shadow-2xs transform-gpu">
        {" "}
        {/* Force GPU acceleration */}
        <div className="relative flex flex-col gap-1">
          <CardDescription className="flex items-center gap-2 text-sm">
            {icon} {content.title}
          </CardDescription>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-semibold tabular-nums">{content.value}</CardTitle>
            <Badge
              variant="outline"
              className={`flex gap-1 rounded-lg text-xs ${
                content.trend.startsWith("+") ? "text-green-600" : "text-red-600"
              }`}
            >
              {trendIcon}
              {content.trend}
            </Badge>
          </div>
          <div className="mt-1 flex flex-col gap-0.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">{content.description}</div>
            <div className="text-muted-foreground">{content.footer}</div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
});

BoardStatCard.displayName = "BoardStatCard";

// Export the variants to be used in the page
export const BoardStatsCards = memo(() => {
  // Use useMemo to prevent recreation on each render
  const variants = useMemo(
    () =>
      [
        { variant: "overall", index: 0 },
        { variant: "bugs", index: 1 },
        { variant: "features", index: 2 },
        { variant: "improvements", index: 3 },
      ] as const,
    []
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-2">
      {variants.map((item) => (
        <BoardStatCard
          key={item.variant}
          variant={item.variant}
          data={TaskAnalyticsData}
          index={item.index}
        />
      ))}
    </div>
  );
});

BoardStatsCards.displayName = "BoardStatsCards";

export default BoardStatsCards;
