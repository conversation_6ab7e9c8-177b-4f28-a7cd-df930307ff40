import { Button } from "@/components/ui/button";
import MultipleSelector, { type Option } from "@/components/ui/multiselect";
import { UserModel } from "@/db/schemas/tasks.schema";
import { CheckIcon, XIcon } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

interface AssigneeEditorProps {
  value: UserModel[];
  availableAssignees: UserModel[];
  onSave: (value: UserModel[]) => void;
  onCancel: () => void;
  projectId?: string;
}

/**
 * Inline assignee editor component using MultipleSelector
 * Memoized to prevent unnecessary re-renders during editing
 */
export const AssigneeEditor = memo(function AssigneeEditor({
  value,
  availableAssignees,
  onSave,
  onCancel,
}: AssigneeEditorProps) {
  // Convert UserModel[] to Option[] for MultipleSelector
  const selectedOptions: Option[] = useMemo(
    () =>
      value.map((assignee) => ({
        value: assignee.id,
        label: assignee.name,
      })),
    [value]
  );

  // Convert available assignees to options
  const assigneeOptions: Option[] = useMemo(
    () =>
      availableAssignees.map((assignee) => ({
        value: assignee.id,
        label: assignee.name,
      })),
    [availableAssignees]
  );

  const [currentValue, setCurrentValue] = useState<Option[]>(selectedOptions);
  const containerRef = useRef<HTMLDivElement>(null);
  const selectorRef = useRef<HTMLDivElement>(null);

  // Update current value when value prop changes
  useEffect(() => {
    setCurrentValue(selectedOptions);
  }, [selectedOptions]);

  const handleSave = useCallback(() => {
    // Convert Option[] back to UserModel[]
    const newAssignees: UserModel[] = currentValue.map((option) => {
      const existingAssignee = availableAssignees.find((assignee) => assignee.id === option.value);
      return (
        existingAssignee || {
          id: option.value,
          name: option.label,
        }
      );
    });

    onSave(newAssignees);
  }, [currentValue, availableAssignees, onSave]);

  // Handle clicks outside the AssigneeEditor component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        // Don't auto-save if user clicked on action buttons (they handle their own logic)
        if (!(event.target as Element)?.closest("[data-action-button]")) {
          handleSave();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleSave]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleSave();
      } else if (e.key === "Escape") {
        e.preventDefault();
        onCancel();
      }
    },
    [handleSave, onCancel]
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent) => {
      // Don't auto-save on blur if user clicked on action buttons
      if (e.relatedTarget?.closest("[data-action-button]")) {
        return;
      }
      handleSave();
    },
    [handleSave]
  );

  return (
    <div ref={containerRef} className="flex gap-2 items-center" data-no-propagation>
      {/* Assignee selector with reserved space for buttons */}
      <div ref={selectorRef} className="flex-1 min-w-0">
        <MultipleSelector
          value={currentValue}
          defaultOptions={assigneeOptions}
          placeholder="Select assignees..."
          hideClearAllButton
          hidePlaceholderWhenSelected
          onChange={setCurrentValue}
          emptyIndicator={
            <p className="text-center text-sm text-gray-500">
              {availableAssignees.length === 0
                ? "No assignees available for this project yet."
                : "No results found"}
            </p>
          }
          className="min-h-[2.5rem]"
          commandProps={{
            label: "Select assignees",
          }}
          inputProps={{
            onKeyDown: handleKeyDown,
            onBlur: handleBlur,
          }}
        />
      </div>

      {/* Action buttons stacked vertically and centered */}
      <div className="flex flex-col gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
          onClick={handleSave}
          data-action-button
          data-no-propagation
        >
          <CheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
          onClick={onCancel}
          data-action-button
          data-no-propagation
        >
          <XIcon className="h-3 w-3 text-red-600 dark:text-red-400" />
        </Button>
      </div>
    </div>
  );
});

export default AssigneeEditor;
