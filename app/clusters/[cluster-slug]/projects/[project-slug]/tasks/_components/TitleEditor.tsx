import { But<PERSON> } from "@/components/ui/button";
import { CheckIcon, XIcon } from "lucide-react";
import { memo, useCallback, useEffect, useRef, useState } from "react";

interface TitleEditorProps {
  value: string;
  onSave: (value: string) => void;
  onCancel: () => void;
}

/**
 * Inline title editor component with multi-line support
 * Memoized to prevent unnecessary re-renders during editing
 */
export const TitleEditor = memo(function TitleEditor({
  value,
  onSave,
  onCancel,
}: TitleEditorProps) {
  const [inputValue, setInputValue] = useState(value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.select();
      // Auto-resize textarea to fit content
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = textareaRef.current.scrollHeight + "px";
    }
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault(); // Prevent new line in textarea
        const trimmedValue = inputValue.trim();
        if (trimmedValue && trimmedValue !== value) {
          onSave(trimmedValue);
        } else if (trimmedValue === value) {
          onCancel(); // No change, just cancel
        } else {
          // Empty title, don't save
          onCancel();
        }
      } else if (e.key === "Escape") {
        onCancel();
      }
    },
    [inputValue, value, onSave, onCancel]
  );

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    // Auto-resize textarea to fit content
    e.target.style.height = "auto";
    e.target.style.height = e.target.scrollHeight + "px";
  }, []);

  const handleSave = useCallback(() => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && trimmedValue !== value) {
      onSave(trimmedValue);
    } else if (trimmedValue === value) {
      onCancel(); // No change, just cancel
    } else {
      // Empty title, don't save
      onCancel();
    }
  }, [inputValue, value, onSave, onCancel]);

  const handleBlur = useCallback(
    (e: React.FocusEvent) => {
      // Don't auto-save on blur if user clicked on action buttons
      if (e.relatedTarget?.closest("[data-action-button]")) {
        return;
      }
      handleSave();
    },
    [handleSave]
  );

  return (
    <div className="flex gap-2 items-start">
      {/* Title textarea with reserved space for buttons */}
      <textarea
        ref={textareaRef}
        value={inputValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        className="flex-1 min-w-0 px-2 py-1 text-4xl font-medium text-neutral-800 dark:text-neutral-200 bg-white dark:bg-gray-800 ring-0 border-0 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none overflow-hidden"
        placeholder="Enter task title..."
        rows={1}
      />

      {/* Action buttons stacked vertically and centered */}
      <div className="flex flex-col gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
          onClick={handleSave}
          data-action-button
        >
          <CheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
          onClick={onCancel}
          data-action-button
        >
          <XIcon className="h-3 w-3 text-red-600 dark:text-red-400" />
        </Button>
      </div>
    </div>
  );
});

export default TitleEditor;
