"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import { TaskHeaderClient } from "./task-header-client";
import { BoardStatsCards } from "./tasks-analytics";

export function TaskHeader() {
  const [showAnalytics, setShowAnalytics] = useState(false);

  return (
    <div className="flex flex-col flex-shrink-0 pb-0 w-full">
      <header className="flex items-center justify-between w-full">
        <div className="flex flex-col">
          <h1 className="text-2xl font-semibold">Tasks</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Create, organize, and track your tasks
          </p>
        </div>
        <div className="flex-shrink-0">
          <TaskHeaderClient onAnalyticsToggle={setShowAnalytics} />
        </div>
      </header>

      {/* Analytics section in document flow - spacing handled by AnimatePresence */}
      <AnimatePresence>
        {showAnalytics && (
          <motion.div
            layout={false} // Disable layout animation which can be expensive
            initial={{
              height: 0,
              opacity: 0,
              marginTop: 0,
            }}
            animate={{
              height: "auto",
              opacity: 1,
              marginTop: 16, // 16px = mt-4
              transition: {
                height: { type: "spring", stiffness: 500, damping: 40 },
                opacity: { duration: 0.15, delay: 0.05 },
                marginTop: { type: "spring", stiffness: 500, damping: 40 },
              },
            }}
            exit={{
              height: 0,
              opacity: 0,
              marginTop: 0,
              transition: {
                height: { type: "spring", stiffness: 500, damping: 40 },
                opacity: { duration: 0.15 },
                marginTop: { type: "spring", stiffness: 500, damping: 40 },
              },
            }}
            style={{
              willChange: "transform, opacity, height, margin-top",
              transform: "translateZ(0)", // Hardware acceleration
              backfaceVisibility: "hidden", // Additional hardware acceleration
              perspective: 1000, // Improve performance for 3D transforms
              contain: "paint layout", // Optimize repaints
              marginTop: 0,
            }}
            className="overflow-hidden transform-gpu"
          >
            <BoardStatsCards />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
