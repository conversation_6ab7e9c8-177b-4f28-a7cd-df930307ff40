"use client";

import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TagModel,
  UserModel,
  type TaskGroupModel,
  type TaskModel,
} from "@/db/schemas/tasks.schema";
import { useQueryClient } from "@tanstack/react-query";
import {
  createContext,
  lazy,
  Suspense,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { taskKeys } from "../_hooks/use-crud-tasks";

// Lazy load the heavy CreateTaskForm component
const CreateTaskForm = lazy(() => import("@/components/tasks/create-task-form"));

// Skeleton for create task form
function CreateTaskFormSkeleton() {
  return (
    <div className="space-y-6 p-6">
      <div className="space-y-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  );
}

interface TasksContextType {
  openCreateTaskDialog: () => void;
  closeCreateTaskDialog: () => void;
  isCreateTaskOpen: boolean;
  projectId: string;
  clusterId: string;
  tenantId: string;
  teams: { id: string; name: string }[];
}

// Create the context
const TasksContext = createContext<TasksContextType | undefined>(undefined);

export function useTasksContext() {
  const context = useContext(TasksContext);
  if (context === undefined) {
    throw new Error("useTasksContext must be used within a TasksProvider");
  }

  return context;
}

interface TasksProviderProps {
  projectId: string;
  clusterId: string;
  tenantId: string;
  teams: { id: string; name: string }[];
  initialTasks: TaskModel[];
  initialTaskGroups: TaskGroupModel[];
  initialTags?: TagModel[];
  initialProjectAssignees?: UserModel[];
  initialCurrentUser?: UserModel | null;
  children: React.ReactNode;
}

export function TasksProvider({
  projectId,
  clusterId,
  tenantId,
  teams,
  initialTasks = [],
  initialTaskGroups = [],
  initialTags = [],
  initialProjectAssignees = [],
  initialCurrentUser = null,
  children,
}: TasksProviderProps) {
  const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
  const queryClient = useQueryClient();

  // Use refs to track initialization state - this prevents infinite loops
  const hasInitializedTasks = useRef(false);
  const hasInitializedTaskGroups = useRef(false);
  const hasInitializedTags = useRef(false);
  const hasInitializedAssignees = useRef(false);
  const hasInitializedCurrentUser = useRef(false);

  // Create stable references for arrays to prevent infinite re-renders
  const stableInitialTasks = useMemo(() => initialTasks, [initialTasks]);
  const stableInitialTaskGroups = useMemo(() => initialTaskGroups, [initialTaskGroups]);
  const stableInitialTags = useMemo(() => initialTags, [initialTags]);
  const stableInitialProjectAssignees = useMemo(
    () => initialProjectAssignees,
    [initialProjectAssignees]
  );
  const stableInitialCurrentUser = useMemo(() => initialCurrentUser, [initialCurrentUser]);

  // Initialize the query cache with all provided data in a single effect
  useEffect(() => {
    const initializeQueryCache = () => {
      // Initialize tasks data
      if (stableInitialTasks.length > 0 && !hasInitializedTasks.current) {
        const tasksQueryKey = taskKeys.list(projectId);
        queryClient.setQueryData(tasksQueryKey, stableInitialTasks);
        hasInitializedTasks.current = true;
      }

      // Initialize task groups data
      if (stableInitialTaskGroups.length > 0 && !hasInitializedTaskGroups.current) {
        const taskGroupsQueryKey = ["tasks", "groups", projectId];
        queryClient.setQueryData(taskGroupsQueryKey, stableInitialTaskGroups);
        hasInitializedTaskGroups.current = true;
      }

      // Initialize tags data
      if (stableInitialTags.length > 0 && !hasInitializedTags.current) {
        const tagsQueryKey = ["project", "tags", projectId];
        queryClient.setQueryData(tagsQueryKey, stableInitialTags);
        hasInitializedTags.current = true;
      }

      // Initialize project assignees data
      if (stableInitialProjectAssignees.length > 0 && !hasInitializedAssignees.current) {
        const assigneesQueryKey = ["project", "assignees", projectId];
        queryClient.setQueryData(assigneesQueryKey, stableInitialProjectAssignees);
        hasInitializedAssignees.current = true;
      }

      // Initialize current user data
      if (stableInitialCurrentUser && !hasInitializedCurrentUser.current) {
        const currentUserQueryKey = ["user", "profile"];
        queryClient.setQueryData(currentUserQueryKey, stableInitialCurrentUser);
        hasInitializedCurrentUser.current = true;
      }
    };

    initializeQueryCache();
  }, [
    queryClient,
    projectId,
    stableInitialTasks,
    stableInitialTaskGroups,
    stableInitialTags,
    stableInitialProjectAssignees,
    stableInitialCurrentUser,
  ]);

  const openCreateTaskDialog = () => setIsCreateTaskOpen(true);
  const closeCreateTaskDialog = () => setIsCreateTaskOpen(false);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      projectId,
      clusterId,
      tenantId,
      teams,
      isCreateTaskOpen,
      openCreateTaskDialog,
      closeCreateTaskDialog,
    }),
    [projectId, clusterId, tenantId, teams, isCreateTaskOpen]
  );

  return (
    <TasksContext.Provider value={contextValue}>
      {children}

      {/* Create Task Form Dialog */}
      <Dialog open={isCreateTaskOpen} onOpenChange={setIsCreateTaskOpen}>
        <DialogTitle className="hidden">Create a new task</DialogTitle>
        <DialogDescription className="hidden">Create a new task</DialogDescription>
        <DialogContent className="min-w-3xl">
          <Suspense fallback={<CreateTaskFormSkeleton />}>
            <CreateTaskForm
              projectId={projectId}
              clusterId={clusterId}
              tenantId={tenantId}
              teams={teams}
              onSuccess={closeCreateTaskDialog}
            />
          </Suspense>
        </DialogContent>
      </Dialog>
    </TasksContext.Provider>
  );
}
