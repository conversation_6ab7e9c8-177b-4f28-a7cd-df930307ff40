"use client";

import { TaskListSkeleton } from "@/components/skeletons";
import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery } from "@tanstack/react-query";
import { TasksProvider } from "./tasks-provider";
import { TasksViewTabs } from "./tasks-view-tabs";

interface ClientWrapperProps {
  projectId: string;
  clusterId: string;
  tenantId: string;
  projectSlug: string;
}

export function ClientWrapper({ projectId, clusterId, tenantId, projectSlug }: ClientWrapperProps) {
  // Fetch all data using React Query for instant navigation
  const {
    data: tasksData,
    isLoading: isTasksLoading,
    error: tasksError,
  } = useQuery({
    queryKey: ["tasks", "list", projectId],
    queryFn: async () => {
      const { getTasksAndGroupsAction } = await import("@/db/actions/tasks.action");
      const props = {
        params: Promise.resolve({ "cluster-slug": clusterId, "project-slug": projectSlug }),
      };
      return await getTasksAndGroupsAction(props);
    },
    ...cacheConfigs.tasks,
    ...commonQueryOptions,
  });

  const { data: tags = [] } = useQuery({
    queryKey: ["project", "tags", projectId],
    queryFn: async () => {
      const { getTagsByProjectAction } = await import("@/db/actions/tasks.action");
      return await getTagsByProjectAction(projectId);
    },
    ...cacheConfigs.projectMeta,
    ...commonQueryOptions,
  });

  const { data: projectAssignees = [] } = useQuery({
    queryKey: ["project", "assignees", projectId],
    queryFn: async () => {
      const { getProjectAssigneesAction } = await import("@/db/actions/tasks.action");
      return await getProjectAssigneesAction(projectId);
    },
    ...cacheConfigs.projectMeta,
    ...commonQueryOptions,
  });

  const { data: userProfile } = useQuery({
    queryKey: ["user", "profile"],
    queryFn: async () => {
      const { getUserProfileAction } = await import("@/db/actions/user-profile.action");
      const { getUserId } = await import("@/db/user.db");

      try {
        const [profile, userId] = await Promise.all([getUserProfileAction(), getUserId(false)]);

        return profile && userId
          ? {
              id: userId,
              name: profile.name,
              email: profile.email,
              avatar: profile.avatar,
            }
          : null;
      } catch {
        return null;
      }
    },
    ...cacheConfigs.user,
    ...commonQueryOptions,
  });

  // Show loading state while essential data is loading
  if (isTasksLoading) {
    return <TaskListSkeleton />;
  }

  // Show error state if tasks failed to load
  if (tasksError || !tasksData) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <p className="text-muted-foreground mb-2">Failed to load tasks</p>
        <p className="text-xs text-muted-foreground">Please try refreshing the page</p>
      </div>
    );
  }

  const teams: { id: string; name: string }[] = [];

  return (
    <TasksProvider
      projectId={projectId}
      clusterId={clusterId}
      tenantId={tenantId}
      teams={teams}
      initialTasks={tasksData.tasks}
      initialTaskGroups={tasksData.taskGroups}
      initialTags={tags}
      initialProjectAssignees={projectAssignees}
      initialCurrentUser={userProfile}
    >
      <TasksViewTabs />
    </TasksProvider>
  );
}

export default ClientWrapper;
