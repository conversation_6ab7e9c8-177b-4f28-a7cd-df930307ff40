"use client";

import { TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import { useMemo } from "react";

/**
 * Hook to group filtered tasks by status (for list view - includes Backlog)
 */
export function useFilteredTaskGroupsByStatus(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by status
    const tasksByStatus = filteredTasks.reduce(
      (acc, task) => {
        const status = task.status || "Backlog";
        if (!acc[status]) {
          acc[status] = [];
        }
        acc[status].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define status order for list view (includes Backlog)
    const statusOrder = ["Backlog", "To do", "In progress", "In review", "Done"];

    // Create task groups
    return statusOrder.map((status) => {
      const tasks = tasksByStatus[status] || [];
      return {
        id: status.toLowerCase().replace(" ", "-"),
        name: status,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForStatus(status),
        icon: getIconForStatus(status),
      };
    });
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by status for Kanban view (excludes Backlog)
 */
export function useFilteredTaskGroupsByStatusKanban(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by status
    const tasksByStatus = filteredTasks.reduce(
      (acc, task) => {
        const status = task.status || "Backlog";
        if (!acc[status]) {
          acc[status] = [];
        }
        acc[status].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define status order for kanban view (excludes Backlog)
    const statusOrder = ["To do", "In progress", "In review", "Done"];

    // Create task groups
    return statusOrder.map((status) => {
      const tasks = tasksByStatus[status] || [];
      return {
        id: status.toLowerCase().replace(" ", "-"),
        name: status,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForStatus(status),
        icon: getIconForStatus(status),
      };
    });
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by lifecycle status
 */
export function useFilteredTaskGroupsByLifecycle(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by lifecycle status
    const tasksByLifecycle = filteredTasks.reduce(
      (acc, task) => {
        const lifecycle = task.lifeCycleStatus || "created";
        if (!acc[lifecycle]) {
          acc[lifecycle] = [];
        }
        acc[lifecycle].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define lifecycle order (matching database values - lowercase)
    const lifecycleOrder = [
      { id: "created", name: "Created" },
      { id: "ready", name: "Ready" },
      { id: "started", name: "Started" },
      { id: "dev done", name: "Dev Done" },
      { id: "test done", name: "Test Done" },
      { id: "deploy ready", name: "Deploy Ready" },
      { id: "delivered", name: "Delivered" },
    ];

    // Create task groups
    return lifecycleOrder.map((lifecycle) => {
      const tasks = tasksByLifecycle[lifecycle.id] || [];
      return {
        id: lifecycle.id,
        name: lifecycle.name,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForLifecycle(lifecycle.name),
        icon: getIconForLifecycle(lifecycle.name),
      };
    });
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by assignee
 */
export function useFilteredTaskGroupsByAssignee(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by assignee
    const tasksByAssignee = filteredTasks.reduce(
      (acc, task) => {
        if (!task.assignees || task.assignees.length === 0) {
          // Unassigned tasks
          if (!acc["unassigned"]) {
            acc["unassigned"] = [];
          }
          acc["unassigned"].push(task);
        } else {
          // Tasks with assignees
          task.assignees.forEach((assignee) => {
            if (!acc[assignee.id]) {
              acc[assignee.id] = [];
            }
            acc[assignee.id].push(task);
          });
        }
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Create task groups
    const groups: TaskGroupModel[] = [];

    // Add unassigned group first
    if (tasksByAssignee["unassigned"]) {
      groups.push({
        id: "unassigned",
        name: "Unassigned",
        count: tasksByAssignee["unassigned"].length,
        tasks: tasksByAssignee["unassigned"].sort(
          (a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)
        ),
        color: "gray",
        icon: "user-x",
      });
    }

    // Add assignee groups
    Object.entries(tasksByAssignee).forEach(([assigneeId, tasks]) => {
      if (assigneeId !== "unassigned" && tasks.length > 0) {
        const assignee = tasks[0].assignees?.find((a) => a.id === assigneeId);
        if (assignee) {
          groups.push({
            id: assigneeId,
            name: assignee.name,
            count: tasks.length,
            tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
            color: "blue",
            icon: "user",
          });
        }
      }
    });

    return groups;
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by tag
 */
export function useFilteredTaskGroupsByTag(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by tag
    const tasksByTag = filteredTasks.reduce(
      (acc, task) => {
        if (!task.tags || task.tags.length === 0) {
          // Untagged tasks
          if (!acc["untagged"]) {
            acc["untagged"] = [];
          }
          acc["untagged"].push(task);
        } else {
          // Tasks with tags
          task.tags.forEach((tag) => {
            if (!acc[tag.id]) {
              acc[tag.id] = [];
            }
            acc[tag.id].push(task);
          });
        }
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Create task groups
    const groups: TaskGroupModel[] = [];

    // Add untagged group first
    if (tasksByTag["untagged"]) {
      groups.push({
        id: "untagged",
        name: "Untagged",
        count: tasksByTag["untagged"].length,
        tasks: tasksByTag["untagged"].sort(
          (a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)
        ),
        color: "gray",
        icon: "tag",
      });
    }

    // Add tag groups
    Object.entries(tasksByTag).forEach(([tagId, tasks]) => {
      if (tagId !== "untagged" && tasks.length > 0) {
        const tag = tasks[0].tags?.find((t) => t.id === tagId);
        if (tag) {
          groups.push({
            id: tagId,
            name: tag.name,
            count: tasks.length,
            tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
            color: "blue", // Default blue color for tags
            icon: "tag",
          });
        }
      }
    });

    return groups;
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by effort
 */
export function useFilteredTaskGroupsByEffort(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by effort
    const tasksByEffort = filteredTasks.reduce(
      (acc, task) => {
        const effort = task.effort || 0;
        const effortGroup = getEffortGroup(effort);
        if (!acc[effortGroup]) {
          acc[effortGroup] = [];
        }
        acc[effortGroup].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define effort order
    const effortOrder = ["No Effort", "Low (1-3)", "Medium (4-6)", "High (7-10)"];

    // Create task groups
    return effortOrder.map((effortGroup) => {
      const tasks = tasksByEffort[effortGroup] || [];
      return {
        id: effortGroup.toLowerCase().replace(/[^a-z0-9]/g, "-"),
        name: effortGroup,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForEffort(effortGroup),
        icon: getIconForEffort(effortGroup),
      };
    });
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by value
 */
export function useFilteredTaskGroupsByValue(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by value
    const tasksByValue = filteredTasks.reduce(
      (acc, task) => {
        const value = task.value || "Not set";
        if (!acc[value]) {
          acc[value] = [];
        }
        acc[value].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define value order
    const valueOrder = ["Huge", "High", "Normal", "Low", "Not set"];

    // Create task groups
    return valueOrder.map((value) => {
      const tasks = tasksByValue[value] || [];
      return {
        id: value.toLowerCase().replace(" ", "-"),
        name: value,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForValue(value),
        icon: getIconForValue(value),
      };
    });
  }, [filteredTasks]);
}

/**
 * Hook to group filtered tasks by due date
 */
export function useFilteredTaskGroupsByDueDate(filteredTasks: TaskModel[]): TaskGroupModel[] {
  return useMemo(() => {
    // Group tasks by due date
    const tasksByDueDate = filteredTasks.reduce(
      (acc, task) => {
        const dueDateGroup = getDueDateGroup(task.dueDate);
        if (!acc[dueDateGroup]) {
          acc[dueDateGroup] = [];
        }
        acc[dueDateGroup].push(task);
        return acc;
      },
      {} as Record<string, TaskModel[]>
    );

    // Define due date order
    const dueDateOrder = [
      "Overdue",
      "Today",
      "This Week",
      "Next Week",
      "This Month",
      "Later",
      "No Due Date",
    ];

    // Create task groups
    return dueDateOrder.map((dueDateGroup) => {
      const tasks = tasksByDueDate[dueDateGroup] || [];
      return {
        id: dueDateGroup.toLowerCase().replace(" ", "-"),
        name: dueDateGroup,
        count: tasks.length,
        tasks: tasks.sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)),
        color: getColorForDueDate(dueDateGroup),
        icon: getIconForDueDate(dueDateGroup),
      };
    });
  }, [filteredTasks]);
}

// Helper functions for colors and icons
function getColorForStatus(status: string): string {
  const colorMap: Record<string, string> = {
    Backlog: "gray",
    "To do": "blue",
    "In progress": "amber",
    "In review": "purple",
    Done: "green",
  };
  return colorMap[status] || "gray";
}

function getIconForStatus(status: string): string {
  const iconMap: Record<string, string> = {
    Backlog: "inbox",
    "To do": "circle",
    "In progress": "clock",
    "In review": "eye",
    Done: "check-circle",
  };
  return iconMap[status] || "circle";
}

function getColorForLifecycle(lifecycle: string): string {
  const colorMap: Record<string, string> = {
    Created: "gray",
    Ready: "blue",
    Started: "amber",
    "Dev Done": "purple",
    "Test Done": "violet",
    "Deploy Ready": "indigo",
    Delivered: "green",
  };
  return colorMap[lifecycle] || "gray";
}

function getIconForLifecycle(lifecycle: string): string {
  const iconMap: Record<string, string> = {
    Created: "plus",
    Ready: "play",
    Started: "zap",
    "Dev Done": "code",
    "Test Done": "check",
    "Deploy Ready": "rocket",
    Delivered: "check-circle",
  };
  return iconMap[lifecycle] || "circle";
}

// Helper functions for effort grouping
function getEffortGroup(effort: number): string {
  if (effort === 0) return "No Effort";
  if (effort <= 3) return "Low (1-3)";
  if (effort <= 6) return "Medium (4-6)";
  return "High (7-10)";
}

function getColorForEffort(effortGroup: string): string {
  const colorMap: Record<string, string> = {
    "No Effort": "gray",
    "Low (1-3)": "green",
    "Medium (4-6)": "amber",
    "High (7-10)": "red",
  };
  return colorMap[effortGroup] || "gray";
}

function getIconForEffort(effortGroup: string): string {
  const iconMap: Record<string, string> = {
    "No Effort": "minus",
    "Low (1-3)": "zap",
    "Medium (4-6)": "flame",
    "High (7-10)": "fire",
  };
  return iconMap[effortGroup] || "zap";
}

// Helper functions for value
function getColorForValue(value: string): string {
  const colorMap: Record<string, string> = {
    Huge: "red",
    High: "amber",
    Normal: "blue",
    Low: "gray",
    "Not set": "gray",
  };
  return colorMap[value] || "gray";
}

function getIconForValue(value: string): string {
  const iconMap: Record<string, string> = {
    Huge: "star",
    High: "trending-up",
    Normal: "circle",
    Low: "trending-down",
    "Not set": "help-circle",
  };
  return iconMap[value] || "circle";
}

// Helper functions for due date grouping
function getDueDateGroup(dueDate: string | null): string {
  if (!dueDate) return "No Due Date";

  const today = new Date();
  const due = new Date(dueDate);
  const diffTime = due.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) return "Overdue";
  if (diffDays === 0) return "Today";
  if (diffDays <= 7) return "This Week";
  if (diffDays <= 14) return "Next Week";
  if (diffDays <= 30) return "This Month";
  return "Later";
}

function getColorForDueDate(dueDateGroup: string): string {
  const colorMap: Record<string, string> = {
    Overdue: "red",
    Today: "amber",
    "This Week": "blue",
    "Next Week": "green",
    "This Month": "purple",
    Later: "gray",
    "No Due Date": "gray",
  };
  return colorMap[dueDateGroup] || "gray";
}

function getIconForDueDate(dueDateGroup: string): string {
  const iconMap: Record<string, string> = {
    Overdue: "alert-triangle",
    Today: "clock",
    "This Week": "calendar",
    "Next Week": "calendar-plus",
    "This Month": "calendar-days",
    Later: "calendar-range",
    "No Due Date": "calendar-x",
  };
  return iconMap[dueDateGroup] || "calendar";
}
