"use client";

import { ColumnStatus, LifeCycleStatus, TaskModel, Value } from "@/db/schemas/tasks.schema";
import {
  parseAsArrayOf,
  parseAsBoolean,
  parseAsIsoDate,
  parseAsString,
  useQueryStates,
} from "nuqs";
import { useCallback, useMemo } from "react";
import { TaskFilterOptions, TaskFiltersState } from "../_types/filter.types";

/**
 * Hook for managing task filters with URL state persistence
 */
export function useTaskFilters(): TaskFiltersState {
  // Use nuqs for URL state management with individual parameters
  const [urlState, setUrlState] = useQueryStates(
    {
      status: parseAsArrayOf(parseAsString).withDefault([]),
      lifecycle: parseAsArrayOf(parseAsString).withDefault([]),
      tags: parseAsArrayOf(parseAsString).withDefault([]),
      assignees: parseAsArrayOf(parseAsString).withDefault([]),
      value: parseAsArrayOf(parseAsString).withDefault([]),
      noEffort: parseAsBoolean.withDefault(false),
      from: parseAsIsoDate,
      to: parseAsIsoDate,
    },
    {
      clearOnDefault: true,
    }
  );

  // Convert URL state to filter format
  const filters = useMemo(
    (): TaskFilterOptions => ({
      status: urlState.status as ColumnStatus[],
      lifecycleStatus: urlState.lifecycle as LifeCycleStatus[],
      tags: urlState.tags,
      assignees: urlState.assignees,
      value: urlState.value as Value[],
      noEffort: urlState.noEffort,
      dateRange: {
        from: urlState.from || undefined,
        to: urlState.to || undefined,
      },
    }),
    [urlState]
  );

  // Check if any filters are active
  const hasActiveFilters = useCallback((): boolean => {
    return (
      (filters.status?.length || 0) > 0 ||
      (filters.lifecycleStatus?.length || 0) > 0 ||
      (filters.tags?.length || 0) > 0 ||
      (filters.assignees?.length || 0) > 0 ||
      (filters.value?.length || 0) > 0 ||
      filters.noEffort === true ||
      filters.dateRange?.from !== undefined ||
      filters.dateRange?.to !== undefined
    );
  }, [filters]);

  // Get count of active filters
  const getActiveFilterCount = useCallback((): number => {
    let count = 0;
    if (filters.status?.length) count += filters.status.length;
    if (filters.lifecycleStatus?.length) count += filters.lifecycleStatus.length;
    if (filters.tags?.length) count += filters.tags.length;
    if (filters.assignees?.length) count += filters.assignees.length;
    if (filters.value?.length) count += filters.value.length;
    if (filters.noEffort) count += 1;
    if (filters.dateRange?.from || filters.dateRange?.to) count += 1;
    return count;
  }, [filters]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setUrlState({
      status: [],
      lifecycle: [],
      tags: [],
      assignees: [],
      value: [],
      noEffort: false,
      from: null,
      to: null,
    });
  }, [setUrlState]);

  // Update filters
  const updateFilters = useCallback(
    (newFilters: Partial<TaskFilterOptions>) => {
      const updates: Partial<typeof urlState> = {};

      if (newFilters.status !== undefined) {
        updates.status = newFilters.status;
      }
      if (newFilters.lifecycleStatus !== undefined) {
        updates.lifecycle = newFilters.lifecycleStatus;
      }
      if (newFilters.tags !== undefined) {
        updates.tags = newFilters.tags;
      }
      if (newFilters.assignees !== undefined) {
        updates.assignees = newFilters.assignees;
      }
      if (newFilters.value !== undefined) {
        updates.value = newFilters.value;
      }
      if (newFilters.noEffort !== undefined) {
        updates.noEffort = newFilters.noEffort;
      }
      if (newFilters.dateRange !== undefined) {
        updates.from = newFilters.dateRange.from || null;
        updates.to = newFilters.dateRange.to || null;
      }

      setUrlState(updates);
    },
    [setUrlState]
  );

  // Toggle status filter
  const toggleStatusFilter = useCallback(
    (status: ColumnStatus) => {
      const currentStatuses = filters.status || [];
      const newStatuses = currentStatuses.includes(status)
        ? currentStatuses.filter((s) => s !== status)
        : [...currentStatuses, status];
      updateFilters({ status: newStatuses });
    },
    [filters.status, updateFilters]
  );

  // Toggle lifecycle filter
  const toggleLifecycleFilter = useCallback(
    (lifecycle: LifeCycleStatus) => {
      const currentLifecycles = filters.lifecycleStatus || [];
      const newLifecycles = currentLifecycles.includes(lifecycle)
        ? currentLifecycles.filter((l) => l !== lifecycle)
        : [...currentLifecycles, lifecycle];
      updateFilters({ lifecycleStatus: newLifecycles });
    },
    [filters.lifecycleStatus, updateFilters]
  );

  // Toggle tag filter (now using tag names instead of IDs)
  const toggleTagFilter = useCallback(
    (tagName: string) => {
      const currentTags = filters.tags || [];
      const newTags = currentTags.includes(tagName)
        ? currentTags.filter((t) => t !== tagName)
        : [...currentTags, tagName];
      updateFilters({ tags: newTags });
    },
    [filters.tags, updateFilters]
  );

  // Toggle assignee filter (using IDs for privacy)
  const toggleAssigneeFilter = useCallback(
    (assigneeId: string) => {
      const currentAssignees = filters.assignees || [];
      const newAssignees = currentAssignees.includes(assigneeId)
        ? currentAssignees.filter((a) => a !== assigneeId)
        : [...currentAssignees, assigneeId];
      updateFilters({ assignees: newAssignees });
    },
    [filters.assignees, updateFilters]
  );

  // Toggle value filter
  const toggleValueFilter = useCallback(
    (value: Value) => {
      const currentValues = filters.value || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter((v) => v !== value)
        : [...currentValues, value];
      updateFilters({ value: newValues });
    },
    [filters.value, updateFilters]
  );

  // Set date range
  const setDateRange = useCallback(
    (range: { from?: Date; to?: Date }) => {
      updateFilters({ dateRange: range });
    },
    [updateFilters]
  );

  // Toggle no effort filter
  const toggleNoEffortFilter = useCallback(() => {
    updateFilters({ noEffort: !filters.noEffort });
  }, [filters.noEffort, updateFilters]);

  return useMemo(
    () => ({
      filters,
      hasActiveFilters,
      getActiveFilterCount,
      clearAllFilters,
      updateFilters,
      toggleStatusFilter,
      toggleLifecycleFilter,
      toggleTagFilter,
      toggleAssigneeFilter,
      toggleValueFilter,
      setDateRange,
      toggleNoEffortFilter,
    }),
    [
      filters,
      hasActiveFilters,
      getActiveFilterCount,
      clearAllFilters,
      updateFilters,
      toggleStatusFilter,
      toggleLifecycleFilter,
      toggleTagFilter,
      toggleAssigneeFilter,
      toggleValueFilter,
      setDateRange,
      toggleNoEffortFilter,
    ]
  );
}

/**
 * Hook to get unique assignees from tasks data
 */
export function useAvailableAssignees(tasks: TaskModel[] | undefined) {
  return useMemo(() => {
    // Handle case where tasks might be undefined or not an array
    if (!tasks || !Array.isArray(tasks)) {
      return [];
    }

    const assigneeMap = new Map<string, { id: string; name: string; avatar?: string }>();

    tasks.forEach((task) => {
      task.assignees?.forEach((assignee) => {
        if (!assigneeMap.has(assignee.id)) {
          assigneeMap.set(assignee.id, {
            id: assignee.id,
            name: assignee.name,
            avatar: assignee.avatar,
          });
        }
      });
    });

    return Array.from(assigneeMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [tasks]);
}

/**
 * Hook for applying filters to task data
 */
export function useFilteredTasks(
  tasks: TaskModel[] | undefined,
  filters: TaskFilterOptions
): TaskModel[] {
  return useMemo(() => {
    if (!tasks || !Array.isArray(tasks) || tasks.length === 0) return [];

    return tasks.filter((task) => {
      // Status filter
      if (filters.status?.length && !filters.status.includes(task.status as ColumnStatus)) {
        return false;
      }

      // Lifecycle status filter
      if (
        filters.lifecycleStatus?.length &&
        !filters.lifecycleStatus.includes(task.lifeCycleStatus)
      ) {
        return false;
      }

      // Tags filter (now using tag names)
      if (filters.tags?.length) {
        const taskTagNames = task.tags?.map((tag) => tag.name) || [];
        const hasMatchingTag = filters.tags.some((tagName) => taskTagNames.includes(tagName));
        if (!hasMatchingTag) return false;
      }

      // Assignees filter
      if (filters.assignees?.length) {
        const taskAssigneeIds = task.assignees?.map((assignee) => assignee.id) || [];
        const hasMatchingAssignee = filters.assignees.some((assigneeId) =>
          taskAssigneeIds.includes(assigneeId)
        );
        if (!hasMatchingAssignee) return false;
      }

      // Value filter
      if (filters.value?.length && !filters.value.includes(task.value)) {
        return false;
      }

      // No effort filter
      if (filters.noEffort && task.effort > 0) {
        return false;
      }

      // Date range filter
      if (filters.dateRange?.from || filters.dateRange?.to) {
        if (!task.dueDate) return false;
        const taskDate = new Date(task.dueDate);

        if (filters.dateRange.from && taskDate < filters.dateRange.from) {
          return false;
        }

        if (filters.dateRange.to && taskDate > filters.dateRange.to) {
          return false;
        }
      }

      return true;
    });
  }, [tasks, filters]);
}
