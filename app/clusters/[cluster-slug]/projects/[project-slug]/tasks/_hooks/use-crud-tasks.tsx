"use client";

import { ColumnStatus, TaskModel } from "@/db/schemas/tasks.schema";
import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { CreateTaskInput } from "../_actions/tasks.actions";
import { deleteTask as deleteTaskAction } from "../_actions/tasks.actions";

// Define query keys as constants to ensure consistency
export const taskKeys = {
  all: ["tasks"] as const,
  lists: () => [...taskKeys.all, "list"] as const,
  list: (projectId: string) => [...taskKeys.lists(), projectId] as const,
  details: () => [...taskKeys.all, "detail"] as const,
  detail: (id: string) => [...taskKeys.details(), id] as const,
};

// Hook to create a new task
export function useCreateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTaskInput) => {
      const { createTask } = await import("../_actions/tasks.actions");
      return createTask(data);
    },
    onSuccess: (result, data) => {
      if (result.error) {
        toast.error(result.error.message);
        return;
      }

      if (result.task) {
        toast.success(`Task ${result.task.key} created successfully`);

        // Instead of invalidating, optimistically update the cache
        const queryKey = taskKeys.list(data.project_id);
        queryClient.setQueryData(queryKey, (oldData: TaskModel[]) => {
          if (!oldData) return [result.task];
          return [...oldData, result.task];
        });

        // Mark data as stale but don't force refetch
        queryClient.invalidateQueries({
          queryKey,
          refetchType: "none", // Don't automatically refetch
        });
      }
    },
    onError: (error: Error) => {
      console.error("Error creating task:", error);
      toast.error("Failed to create task");
    },
  });
}

// Type for delete task params
export interface DeleteTaskParams {
  taskId: string;
  projectId: string;
}

// Add UpdateTask types and params
export interface UpdateTaskParams {
  taskId: string;
  projectId: string;
  updates: Partial<CreateTaskInput>;
}

// Hook to delete a task
export function useDeleteTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ taskId, projectId }: DeleteTaskParams) => {
      const result = await deleteTaskAction(taskId);
      if (!result.success && result.error) {
        throw result.error;
      }
      return { result, taskId, projectId };
    },
    onSuccess: ({ taskId, projectId }: DeleteTaskParams) => {
      // Optimistically remove the task from cache
      const queryKey = taskKeys.list(projectId);
      queryClient.setQueryData(queryKey, (oldData: TaskModel[]) => {
        if (!oldData) return [];
        return oldData.filter((task: TaskModel) => task.id !== taskId);
      });

      // Mark data as stale but don't force refetch
      queryClient.invalidateQueries({
        queryKey,
        refetchType: "none", // Don't automatically refetch
      });

      toast.success(`Task ${taskId} deleted successfully`);
    },
    onError: (error: Error, { taskId }) => {
      console.error("Failed to delete task:", taskId, error);
      toast.error(`Failed to delete task: ${error.message}`);
    },
  });
}

// Hook to update a task - follows the same pattern as useCreateTask and useDeleteTask
export function useUpdateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ taskId, updates }: Omit<UpdateTaskParams, "projectId">) => {
      const { updateTask } = await import("../_actions/tasks.actions");
      return updateTask(taskId, updates);
    },
    onMutate: async ({ taskId, projectId, updates }: UpdateTaskParams) => {
      // Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: taskKeys.list(projectId) });

      // Snapshot previous value for rollback
      const previousTasks = queryClient.getQueryData(taskKeys.list(projectId)) as
        | TaskModel[]
        | undefined;

      // Optimistically update the cache
      queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
        if (!oldData) return [];
        return oldData.map((task) =>
          task.id === taskId
            ? {
                ...task,
                ...updates,
                // Mark as optimistic so UI can show loading states if needed
                isOptimistic: true,
                // Update timestamp
                updatedAt: new Date().toISOString(),
              }
            : task
        );
      });

      return { previousTasks, taskId, projectId };
    },
    onError: (err: Error, { taskId, projectId }: UpdateTaskParams, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(taskKeys.list(projectId), context.previousTasks);
      }
      console.error("Failed to update task:", taskId, err);
      toast.error(`Failed to update task: ${err.message}`);
    },
    onSuccess: (result, { taskId, projectId }: UpdateTaskParams) => {
      if (result.error) {
        toast.error(result.error.message);
        return;
      }

      if (result.task) {
        // Update cache with server response (removes optimistic flag)
        queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
          if (!oldData) return [result.task];
          return oldData.map((task) =>
            task.id === taskId ? { ...result.task, isOptimistic: false } : task
          );
        });

        // Mark data as stale but don't force refetch
        queryClient.invalidateQueries({
          queryKey: taskKeys.list(projectId),
          refetchType: "none", // Don't automatically refetch
        });

        toast.success(`Task ${result.task.key} updated successfully`);
      }
    },
  });
}

// Type for move task params
export interface MoveTaskParams {
  taskId: string;
  projectId: string;
  newStatus: ColumnStatus;
  targetPosition: number;
}

// Hook to move a task between columns or within a column
export function useMoveTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ taskId, newStatus, targetPosition, projectId }: MoveTaskParams) => {
      const { moveTaskToPosition } = await import("../_actions/tasks.actions");
      return moveTaskToPosition(taskId, newStatus, targetPosition, projectId);
    },
    onMutate: async ({ taskId, projectId, newStatus }: MoveTaskParams) => {
      // Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: taskKeys.list(projectId) });

      // Snapshot previous value for rollback
      const previousTasks = queryClient.getQueryData(taskKeys.list(projectId)) as
        | TaskModel[]
        | undefined;

      // Optimistically update the cache - only update status, let server handle position
      queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
        if (!oldData) return [];

        // Find the task to move
        const taskToMove = oldData.find((task) => task.id === taskId);
        if (!taskToMove) return oldData;

        // Update the task with new status only - let server determine final position
        const updatedTask = {
          ...taskToMove,
          status: newStatus,
          isOptimistic: true,
          updatedAt: new Date().toISOString(),
        };

        // Return updated array with the modified task
        return oldData.map((task) => (task.id === taskId ? updatedTask : task));
      });

      // Return context for rollback
      return { previousTasks };
    },
    onError: (error: Error, variables, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(taskKeys.list(variables.projectId), context.previousTasks);
      }
      console.error("Error moving task:", error);
      toast.error(`Failed to move task: ${error.message}`);
    },
    onSuccess: (result, { taskId, projectId, newStatus }: MoveTaskParams) => {
      if (!result.success && result.error) {
        toast.error(result.error.message);
        return;
      }

      // Get task title for toast before any cache operations
      let taskTitle = "";
      const currentData = queryClient.getQueryData(taskKeys.list(projectId)) as
        | TaskModel[]
        | undefined;
      if (currentData) {
        const task = currentData.find((t) => t.id === taskId);
        taskTitle = task?.title || "";
      }

      // Remove optimistic flag from the moved task to indicate server sync is complete
      queryClient.setQueryData(taskKeys.list(projectId), (oldData: TaskModel[] | undefined) => {
        if (!oldData) return [];
        return oldData.map((task) =>
          task.id === taskId ? { ...task, isOptimistic: false } : task
        );
      });

      // Mark data as stale for background refresh, but don't force immediate refetch
      // This prevents tasks from disappearing while maintaining data freshness
      queryClient.invalidateQueries({
        queryKey: taskKeys.list(projectId),
        refetchType: "none", // Don't force immediate refetch to prevent task disappearance
      });

      // Show success toast with task and column info
      if (taskTitle) {
        toast.success(`Task moved to ${newStatus}`, {
          description: `"${taskTitle}" has been moved successfully`,
          duration: 2000,
        });
      }
    },
  });
}

/**
 * Hook to fetch tags for a project - only when explicitly needed
 */
export function useTags(projectId: string, enabled: boolean = false) {
  return useQuery({
    queryKey: ["project", "tags", projectId],
    queryFn: async () => {
      const { getTagsByProjectAction } = await import("@/db/actions/tasks.action");
      return getTagsByProjectAction(projectId);
    },
    enabled: enabled && Boolean(projectId), // Only run when explicitly enabled and projectId exists
    ...cacheConfigs.projectMeta,
    ...commonQueryOptions,
  });
}

/**
 * Hook to fetch potential assignees for a project - only when explicitly needed
 */
export function useProjectAssignees(projectId: string, enabled: boolean = false) {
  return useQuery({
    queryKey: ["project", "assignees", projectId],
    queryFn: async () => {
      const { getProjectAssigneesAction } = await import("@/db/actions/tasks.action");
      return getProjectAssigneesAction(projectId);
    },
    enabled: enabled && Boolean(projectId), // Only run when explicitly enabled and projectId exists
    ...cacheConfigs.projectMeta,
    ...commonQueryOptions,
  });
}

/**
 * Hook to get current user info - only when explicitly needed
 */
export function useCurrentUser(enabled: boolean = false) {
  return useQuery({
    queryKey: ["user", "profile"],
    queryFn: async () => {
      const { createClient } = await import("@/lib/supabase/client");
      const supabase = createClient();

      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error("Not authenticated");
      }

      // Get profile data
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("user_id, first_name, last_name, full_name, email, avatar_url")
        .eq("user_id", user.id)
        .single();

      if (profileError) {
        throw profileError;
      }

      return {
        id: profile.user_id,
        name: profile.full_name || `${profile.first_name} ${profile.last_name}`.trim(),
        email: profile.email,
        avatar: profile.avatar_url,
      };
    },
    enabled: enabled, // Only run when explicitly enabled
    ...cacheConfigs.user,
    ...commonQueryOptions,
  });
}
