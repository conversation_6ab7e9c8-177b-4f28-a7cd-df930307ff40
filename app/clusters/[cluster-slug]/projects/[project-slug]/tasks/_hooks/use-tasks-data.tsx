"use client";

import { type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useMemo } from "react";
import { getTagColorByName } from "../utils/tag-styles";
import { taskKeys } from "./use-crud-tasks";

interface UseTasksDataReturn {
  tasks: TaskModel[];
  taskGroups: TaskGroupModel[];
  projectId: string | undefined;
  isLoading: boolean;
  // Grouping methods
  groupByStatus: () => TaskGroupModel[];
  groupByAssignee: () => TaskGroupModel[];
  groupByDueDate: () => TaskGroupModel[];
  groupByEffort: () => TaskGroupModel[];
  groupByLifecycle: () => TaskGroupModel[];
  groupByTag: () => TaskGroupModel[];
  groupByValue: () => TaskGroupModel[];
  // Kanban specific
  getKanbanColumns: () => TaskGroupModel[];
}

/**
 * Centralized hook to manage all tasks data
 * @param projectId - The project ID to fetch tasks for
 */
export function useTasksData(projectId?: string): UseTasksDataReturn {
  const queryClient = useQueryClient();

  // Use React Query hooks only when we have a valid projectId
  const tasksQuery = useQuery({
    queryKey: taskKeys.list(projectId!),
    queryFn: async () => {
      const { getTasks } = await import("../_actions/tasks.actions");
      const result = await getTasks(projectId!);

      if (result.error) {
        throw result.error;
      }

      return result.tasks || [];
    },
    enabled: Boolean(projectId && projectId.length > 0), // Ensure projectId is valid
    ...cacheConfigs.tasks,
    ...commonQueryOptions,
    // Use cached data if available and not stale
    placeholderData: (previousData) => {
      const cachedData = queryClient.getQueryData(taskKeys.list(projectId!));
      return (cachedData as TaskModel[]) || previousData;
    },
  });

  // Get data from queries, falling back to empty arrays
  const tasks = useMemo(() => tasksQuery.data || [], [tasksQuery.data]);
  const isLoading = tasksQuery.isLoading && !tasksQuery.data; // Only show loading if no cached data

  // Get task groups from cache - these contain the database colors and icons
  const taskGroupsQuery = useQuery({
    queryKey: ["tasks", "groups", projectId],
    queryFn: () => {
      // This should already be cached by the provider
      const cachedData = queryClient.getQueryData(["tasks", "groups", projectId]);
      return (cachedData as TaskGroupModel[]) || [];
    },
    enabled: Boolean(projectId),
    staleTime: Infinity, // Use cached data indefinitely since it's set by provider
    gcTime: Infinity,
  });

  const taskGroups: TaskGroupModel[] = useMemo(
    () => taskGroupsQuery.data || [],
    [taskGroupsQuery.data]
  );

  // Grouping methods - memoized to prevent unnecessary recalculations
  const groupByStatus = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    const statusOrder = ["Backlog", "To do", "In progress", "In review", "Done"];

    return statusOrder.map((status) => {
      const statusTasks = tasks
        .filter((task) => task.status === status)
        .sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)); // ✅ Sort by position!

      // Try to find the task group from database first
      const dbTaskGroup = taskGroups.find(
        (group) => group.name.toLowerCase() === status.toLowerCase()
      );

      return {
        id: status.toLowerCase().replace(" ", "-"),
        name: status,
        count: statusTasks.length,
        tasks: statusTasks,
        // Use database color/icon if available, otherwise fallback to hardcoded
        color: dbTaskGroup?.color || getColorForStatus(status),
        icon: dbTaskGroup?.icon || getIconForStatus(status),
      };
    });
  }, [tasks, taskGroups]);

  const groupByAssignee = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    // Get unique assignees
    const uniqueAssignees = new Map<string, { id: string; name: string; avatar?: string }>();
    tasks.forEach((task) => {
      if (Array.isArray(task.assignees)) {
        task.assignees.forEach((assignee) => uniqueAssignees.set(assignee.id, assignee));
      }
    });

    // Group tasks by assignee
    const taskMap = new Map<string, TaskModel[]>();
    Array.from(uniqueAssignees.keys()).forEach((assigneeId) => {
      taskMap.set(assigneeId, []);
    });

    const unassigned: TaskModel[] = [];

    tasks.forEach((task) => {
      if (!Array.isArray(task.assignees) || task.assignees.length === 0) {
        unassigned.push(task);
      } else {
        task.assignees.forEach((assignee) => {
          const assigneeTasks = taskMap.get(assignee.id) || [];
          assigneeTasks.push(task);
          taskMap.set(assignee.id, assigneeTasks);
        });
      }
    });

    const groups = [];

    // Add unassigned group first if it has tasks
    if (unassigned.length > 0) {
      groups.push({
        id: "unassigned",
        name: "Unassigned",
        count: unassigned.length,
        tasks: unassigned,
        color: "gray",
        icon: "user",
      });
    }

    // Add assigned groups
    Array.from(uniqueAssignees.entries()).forEach(([assigneeId, assigneeData]) => {
      const assigneeTasks = taskMap.get(assigneeId) || [];
      if (assigneeTasks.length > 0) {
        groups.push({
          id: assigneeId,
          name: assigneeData.name || `User ${assigneeId}`,
          count: assigneeTasks.length,
          tasks: assigneeTasks,
          color: "blue",
          icon: "user",
        });
      }
    });

    return groups;
  }, [tasks]);

  const groupByDueDate = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const getEndOfWeek = (date: Date) => {
      const result = new Date(date);
      const day = date.getDay();
      result.setDate(result.getDate() + (6 - day));
      result.setHours(23, 59, 59, 999);
      return result;
    };

    const getEndOfNextWeek = (date: Date) => {
      const result = new Date(date);
      const day = date.getDay();
      result.setDate(result.getDate() + (13 - day));
      result.setHours(23, 59, 59, 999);
      return result;
    };

    const endOfToday = new Date(today);
    endOfToday.setHours(23, 59, 59, 999);
    const endOfThisWeek = getEndOfWeek(today);
    const endOfNextWeek = getEndOfNextWeek(today);

    const overdue: TaskModel[] = [];
    const dueToday: TaskModel[] = [];
    const dueThisWeek: TaskModel[] = [];
    const dueNextWeek: TaskModel[] = [];
    const dueLater: TaskModel[] = [];
    const noDueDate: TaskModel[] = [];

    tasks.forEach((task) => {
      if (!task.dueDate) {
        noDueDate.push(task);
        return;
      }

      const dueDate = new Date(task.dueDate);

      if (dueDate < today) {
        overdue.push(task);
      } else if (dueDate <= endOfToday) {
        dueToday.push(task);
      } else if (dueDate <= endOfThisWeek) {
        dueThisWeek.push(task);
      } else if (dueDate <= endOfNextWeek) {
        dueNextWeek.push(task);
      } else {
        dueLater.push(task);
      }
    });

    return [
      {
        id: "overdue",
        name: "Overdue",
        count: overdue.length,
        tasks: overdue,
        color: "red",
        icon: "clock",
      },
      {
        id: "due-today",
        name: "Due Today",
        count: dueToday.length,
        tasks: dueToday,
        color: "amber",
        icon: "calendar",
      },
      {
        id: "due-this-week",
        name: "Due This Week",
        count: dueThisWeek.length,
        tasks: dueThisWeek,
        color: "blue",
        icon: "calendar",
      },
      {
        id: "due-next-week",
        name: "Due Next Week",
        count: dueNextWeek.length,
        tasks: dueNextWeek,
        color: "purple",
        icon: "calendar",
      },
      {
        id: "due-later",
        name: "Due Later",
        count: dueLater.length,
        tasks: dueLater,
        color: "orange",
        icon: "calendar",
      },
      {
        id: "no-due-date",
        name: "No Due Date",
        count: noDueDate.length,
        tasks: noDueDate,
        color: "gray",
        icon: "calendar",
      },
    ].filter((group) => group.count > 0);
  }, [tasks]);

  const groupByEffort = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    const noEffort: TaskModel[] = [];
    const lowEffort: TaskModel[] = [];
    const mediumEffort: TaskModel[] = [];
    const highEffort: TaskModel[] = [];

    tasks.forEach((task) => {
      const effort = task.effort || 0;
      if (effort === 0) {
        noEffort.push(task);
      } else if (effort <= 2) {
        lowEffort.push(task);
      } else if (effort <= 5) {
        mediumEffort.push(task);
      } else {
        highEffort.push(task);
      }
    });

    return [
      {
        id: "no-effort",
        name: "No Effort Set",
        count: noEffort.length,
        tasks: noEffort,
        color: "gray",
        icon: "clock",
      },
      {
        id: "low-effort",
        name: "Low Effort (1-2 days)",
        count: lowEffort.length,
        tasks: lowEffort,
        color: "green",
        icon: "clock",
      },
      {
        id: "medium-effort",
        name: "Medium Effort (3-5 days)",
        count: mediumEffort.length,
        tasks: mediumEffort,
        color: "amber",
        icon: "clock",
      },
      {
        id: "high-effort",
        name: "High Effort (6+ days)",
        count: highEffort.length,
        tasks: highEffort,
        color: "red",
        icon: "clock",
      },
    ].filter((group) => group.count > 0);
  }, [tasks]);

  const groupByLifecycle = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    // Define the correct lifecycle stages with their display properties
    // Using colors that match the lifecycle status badge component
    const lifecycleStages: { id: string; name: string; color: string; icon: string }[] = [
      { id: "created", name: "Created", color: "gray", icon: "circle-dashed" },
      { id: "ready", name: "Ready", color: "blue", icon: "circle" },
      { id: "started", name: "Started", color: "amber", icon: "circle-dot" },
      { id: "dev done", name: "Dev Done", color: "green", icon: "circle-check" },
      { id: "test done", name: "Test Done", color: "purple", icon: "circle-check" },
      { id: "deploy ready", name: "Deploy Ready", color: "indigo", icon: "circle-check" },
      { id: "delivered", name: "Delivered", color: "green", icon: "check-circle" },
    ];

    const lifecycleGroups = lifecycleStages.map((stage) => {
      // Use the actual lifecycle status from tasks, not artificially modified versions
      const lifecycleTasks = tasks.filter((task) => task.lifeCycleStatus === stage.id);
      return {
        id: stage.id,
        name: stage.name,
        count: lifecycleTasks.length,
        tasks: lifecycleTasks,
        color: stage.color,
        icon: stage.icon,
      };
    });

    // Return all groups, including empty ones, to show the complete lifecycle
    return lifecycleGroups;
  }, [tasks]);

  const groupByTag = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    // Get unique tags
    const uniqueTags = new Map<string, { id: string; name: string }>();
    tasks.forEach((task) => {
      if (Array.isArray(task.tags)) {
        task.tags.forEach((tag) => uniqueTags.set(tag.id, tag));
      }
    });

    // Group tasks by tag
    const taskMap = new Map<string, TaskModel[]>();
    Array.from(uniqueTags.keys()).forEach((tagId) => {
      taskMap.set(tagId, []);
    });

    const untagged: TaskModel[] = [];

    tasks.forEach((task) => {
      if (!Array.isArray(task.tags) || task.tags.length === 0) {
        untagged.push(task);
      } else {
        task.tags.forEach((tag) => {
          const tagTasks = taskMap.get(tag.id) || [];
          tagTasks.push(task);
          taskMap.set(tag.id, tagTasks);
        });
      }
    });

    const groups = [];

    // Add untagged group first if it has tasks
    if (untagged.length > 0) {
      groups.push({
        id: "untagged",
        name: "Untagged",
        count: untagged.length,
        tasks: untagged,
        color: "gray",
        icon: "tag",
      });
    }

    // Add tagged groups
    Array.from(uniqueTags.entries()).forEach(([tagId, tagData]) => {
      const tagTasks = taskMap.get(tagId) || [];
      if (tagTasks.length > 0) {
        groups.push({
          id: tagId,
          name: tagData.name || `Tag ${tagId}`,
          count: tagTasks.length,
          tasks: tagTasks,
          color: getTagColorByName(tagData.name || `Tag ${tagId}`),
          icon: "tag",
        });
      }
    });

    return groups;
  }, [tasks]);

  const groupByValue = useCallback((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    const valueOrder = ["Critical", "High", "Normal", "Low", "Not set"];

    return valueOrder.map((value) => {
      const valueTasks = tasks.filter((task) => task.value === value);
      return {
        id: value.toLowerCase().replace(" ", "-"),
        name: value,
        count: valueTasks.length,
        tasks: valueTasks,
        color: getColorForValue(value),
        icon: getIconForValue(value),
      };
    });
  }, [tasks]);

  // Memoize the kanban columns to prevent recreation on every call
  const kanbanColumns = useMemo((): TaskGroupModel[] => {
    if (!tasks.length) return [];

    const statusOrder = ["To do", "In progress", "In review", "Done"];

    return statusOrder.map((status) => {
      const statusTasks = tasks
        .filter((task) => task.status === status)
        .sort((a, b) => (a.positionInColumn || 0) - (b.positionInColumn || 0)); // ✅ Sort by position!

      // Try to find the task group from database first
      const dbTaskGroup = taskGroups.find(
        (group) => group.name.toLowerCase() === status.toLowerCase()
      );

      return {
        id: status.toLowerCase().replace(" ", "-"),
        name: status,
        count: statusTasks.length,
        tasks: statusTasks,
        // Use database color/icon if available, otherwise fallback to hardcoded
        color: dbTaskGroup?.color || getColorForStatus(status),
        icon: dbTaskGroup?.icon || getIconForStatus(status),
      };
    });
  }, [tasks, taskGroups]);

  const getKanbanColumns = useCallback((): TaskGroupModel[] => {
    return kanbanColumns;
  }, [kanbanColumns]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      tasks,
      taskGroups,
      projectId,
      isLoading,
      groupByStatus,
      groupByAssignee,
      groupByDueDate,
      groupByEffort,
      groupByLifecycle,
      groupByTag,
      groupByValue,
      getKanbanColumns,
    }),
    [
      tasks,
      taskGroups,
      projectId,
      isLoading,
      groupByStatus,
      groupByAssignee,
      groupByDueDate,
      groupByEffort,
      groupByLifecycle,
      groupByTag,
      groupByValue,
      getKanbanColumns,
    ]
  );
}

// Helper functions for status colors and icons
function getColorForStatus(status: string): string {
  switch (status) {
    case "To do":
      return "blue";
    case "In progress":
      return "amber";
    case "In review":
      return "purple";
    case "Done":
      return "green";
    case "Backlog":
    default:
      return "gray";
  }
}

function getIconForStatus(status: string): string {
  switch (status) {
    case "To do":
      return "circle";
    case "In progress":
      return "circle-dot";
    case "In review":
      return "circle-ellipsis";
    case "Done":
      return "check-circle";
    case "Backlog":
    default:
      return "circle-dashed";
  }
}

// Helper functions for value colors and icons
function getColorForValue(value: string): string {
  switch (value) {
    case "Critical":
      return "red";
    case "High":
      return "orange";
    case "Normal":
      return "blue";
    case "Low":
      return "green";
    case "Not set":
    default:
      return "gray";
  }
}

function getIconForValue(value: string): string {
  switch (value) {
    case "Critical":
      return "chevrons-up";
    case "High":
      return "chevron-up";
    case "Normal":
      return "minus";
    case "Low":
      return "chevron-down";
    case "Not set":
    default:
      return "dot";
  }
}
