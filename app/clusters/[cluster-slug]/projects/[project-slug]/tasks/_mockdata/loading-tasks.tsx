import { Skeleton } from "@/components/ui/skeleton";

export default function TasksLoading() {
  return (
    <div className="flex flex-col gap-6 px-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-56" />
        </div>
        <div className="flex items-center gap-3">
          <Skeleton className="h-9 w-[140px]" />
          <Skeleton className="h-9 w-9" />
          <Skeleton className="h-9 w-9" />
          <Skeleton className="h-9 w-[100px]" />
        </div>
      </div>

      {/* View Tabs */}
      <div className="flex items-center gap-4">
        {["List", "Kanban", "Timeline", "Calendar", "Dashboard"].map((_, i) => (
          <Skeleton key={i} className="h-9 w-24" />
        ))}
      </div>

      {/* Task Groups */}
      {["Backlog", "To do", "In progress"].map((_, groupIndex) => (
        <div key={groupIndex} className="flex flex-col gap-2">
          {/* Group Header */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-6 ml-2" />
            <Skeleton className="h-4 w-12 ml-auto" />
          </div>

          {/* Task List Headers */}
          <div className="grid grid-cols-7 gap-4 px-4 py-2 text-sm text-muted-foreground">
            <div className="col-span-2 flex items-center gap-2">
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-16" />
          </div>

          {/* Task Items */}
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="grid grid-cols-7 gap-4 px-4 py-3 hover:bg-muted/50 rounded-lg">
              <div className="col-span-2 flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-48" />
              </div>
              <div className="flex items-center">
                <Skeleton className="h-6 w-24" />
              </div>
              <div className="flex items-center">
                <Skeleton className="h-4 w-16" />
              </div>
              <div className="flex items-center gap-1">
                {Array.from({ length: 3 }).map((_, j) => (
                  <Skeleton key={j} className="h-6 w-16" />
                ))}
              </div>
              <div className="flex items-center gap-1">
                {Array.from({ length: 2 }).map((_, j) => (
                  <Skeleton key={j} className="h-6 w-6 rounded-full" />
                ))}
              </div>
              <div className="flex items-center">
                <Skeleton className="h-4 w-8" />
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}
