import { TaskAnalyticsModel } from "../_types/tasks-analytics.types";

export const TaskAnalyticsData: TaskAnalyticsModel = {
  totalItems: 13, // Total non-dummy tasks across all columns
  categories: {
    bugs: {
      count: 2,
      trend: "-15%", // Decreasing bugs is good
      priority: {
        high: 1,
        medium: 1,
        low: 0,
      },
    },
    features: {
      count: 3,
      trend: "+10%",
      priority: {
        high: 2,
        medium: 1,
        low: 0,
      },
    },
    improvements: {
      count: 8,
      trend: "+5%",
      priority: {
        high: 3,
        medium: 3,
        low: 2,
      },
    },
  },
  recentActivity: {
    lastUpdated: "2024-03-26T10:30:00Z",
    addedLast7Days: 5,
    completedLast7Days: 4, // Tasks in "Done" column
  },
  teamMetrics: {
    averageTimeToResolution: "3.5 days", // Average duration of completed tasks
    backlogGrowthRate: "-2.5%", // Decreasing backlog is good
    healthScore: 85, // High due to good task distribution and completion rate
  },
};
