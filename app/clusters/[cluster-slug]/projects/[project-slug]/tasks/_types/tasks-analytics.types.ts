interface TaskValue {
  high: number;
  medium: number;
  low: number;
}

interface TaskStatsType {
  count: number;
  trend: string;
  priority: TaskValue;
}

export interface TaskAnalyticsModel {
  totalItems: number;
  categories: {
    bugs: TaskStatsType;
    features: TaskStatsType;
    improvements: TaskStatsType;
  };
  recentActivity: {
    lastUpdated: string;
    addedLast7Days: number;
    completedLast7Days: number;
  };
  teamMetrics: {
    averageTimeToResolution: string;
    backlogGrowthRate: string;
    healthScore: number;
  };
}
