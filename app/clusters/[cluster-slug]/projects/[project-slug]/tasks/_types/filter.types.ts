import { ColumnStatus, LifeCycleStatus, Value } from "@/db/schemas/tasks.schema";

// Filter option types for tasks
export type TaskFilterOptions = {
  status?: ColumnStatus[];
  lifecycleStatus?: LifeCycleStatus[];
  tags?: string[]; // Tag names (for user-friendly URLs)
  assignees?: string[]; // User IDs (kept as IDs for privacy/confidentiality)
  value?: Value[];
  noEffort?: boolean; // Filter for tasks with no effort assigned
  dateRange?: {
    from?: Date;
    to?: Date;
  };
};

// Individual filter categories
export type StatusFilter = ColumnStatus[];
export type LifecycleFilter = LifeCycleStatus[];
export type TagFilter = string[];
export type AssigneeFilter = string[];
export type ValueFilter = Value[];
export type DateRangeFilter = {
  from?: Date;
  to?: Date;
};

// Filter state management
export interface TaskFiltersState {
  filters: TaskFilterOptions;
  hasActiveFilters: () => boolean;
  getActiveFilterCount: () => number;
  clearAllFilters: () => void;
  updateFilters: (newFilters: Partial<TaskFilterOptions>) => void;
  toggleStatusFilter: (status: ColumnStatus) => void;
  toggleLifecycleFilter: (lifecycle: LifeCycleStatus) => void;
  toggleTagFilter: (tagId: string) => void;
  toggleAssigneeFilter: (assigneeId: string) => void;
  toggleValueFilter: (value: Value) => void;
  setDateRange: (range: DateRangeFilter) => void;
  toggleNoEffortFilter: () => void;
}

// Available filter options from database
export interface AvailableFilterOptions {
  statuses: ColumnStatus[];
  lifecycleStatuses: LifeCycleStatus[];
  tags: { id: string; name: string }[];
  assignees: { id: string; name: string; avatar?: string }[];
  values: Value[];
}

// Filter utility functions
export interface FilterUtils {
  getStatusLabel: (status: ColumnStatus) => string;
  getLifecycleLabel: (lifecycle: LifeCycleStatus) => string;
  getValueLabel: (value: Value) => string;
  formatDateRange: (range: DateRangeFilter) => string;
}
