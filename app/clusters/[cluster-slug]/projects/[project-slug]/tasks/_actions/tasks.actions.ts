"use server";

import { ColumnStatus, LifeCycleStatus, TaskModel, Value } from "@/db/schemas/tasks.schema";
import { createClient } from "@/lib/supabase/server";

// Type for creating a new task
export type CreateTaskInput = {
  title: string;
  description?: string;
  value?: string;
  status?: string;
  lifeCycleStatus?: string;
  tags?: { id: string; name: string }[];
  sprint?: string;
  week?: number;
  dueDate?: string | null;
  assignees?: { id: string; name: string; avatar?: string }[];
  effort?: number;
  positionInColumn?: number;
  visibleToTeams?: string[];
  project_id: string;
  tenant_id: string;
};

export type DbTaskModel = {
  id: string;
  key: string;
  title: string;
  description: string;
  value: Value;
  status: ColumnStatus;
  life_cycle_status: LifeCycleStatus;
  tags: string;
  sprint: string;
  week: number;
  due_date: string | null;
  done_date: string | null;
  delivered_date: string | null;
  assignees: string;
  effort: number;
  position_in_column: number;
  created_at: string;
  updated_at: string;
  subtasks: string;
  visible_to_team_ids: string[];
};

/**
 * Get tasks for a project - now uses the clean transformation from tasks.db.ts
 */
export async function getTasks(projectId: string): Promise<{ tasks: TaskModel[]; error?: Error }> {
  try {
    const supabase = await createClient();

    // Use the database function that properly JOINs tasks with tags
    const { data, error } = await supabase.rpc("get_tasks_with_tags", {
      project_id_param: projectId,
    });

    if (error) {
      console.error("Database error fetching tasks:", error);
      throw error;
    }

    // Transform the data to match TaskModel structure
    const transformedTasks = (data || []).map((dbTask: DbTaskModel) => {
      // Parse tags if they exist
      const tags = dbTask.tags ? JSON.parse(dbTask.tags) : [];

      // Parse assignees
      const assignees =
        typeof dbTask.assignees === "string"
          ? JSON.parse(dbTask.assignees)
          : dbTask.assignees || [];

      // Ensure assignees is always an array
      const safeAssignees = Array.isArray(assignees) ? assignees : [];

      // Parse subtasks
      const subtasks =
        typeof dbTask.subtasks === "string" ? JSON.parse(dbTask.subtasks) : dbTask.subtasks || [];

      return {
        id: dbTask.id,
        key: dbTask.key,
        title: dbTask.title,
        description: dbTask.description,
        value: dbTask.value,
        status: dbTask.status,
        lifeCycleStatus: dbTask.life_cycle_status,
        tags: tags,
        sprint: dbTask.sprint,
        week: dbTask.week,
        dueDate: dbTask.due_date,
        doneDate: dbTask.done_date,
        deliveredDate: dbTask.delivered_date,
        assignees: safeAssignees,
        effort: dbTask.effort || 0,
        positionInColumn: dbTask.position_in_column || 1,
        attachments: [],
        createdAt: dbTask.created_at,
        updatedAt: dbTask.updated_at,
        subtasks: subtasks,
        visibleToTeams: Array.isArray(dbTask.visible_to_team_ids) ? dbTask.visible_to_team_ids : [],
        isDummy: false,
        isOptimistic: false,
      } as TaskModel;
    });

    return { tasks: transformedTasks };
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return { tasks: [], error: error as Error };
  }
}

/**
 * Create a new task
 */
export async function createTask(
  task: CreateTaskInput
): Promise<{ task?: TaskModel; error?: Error }> {
  try {
    const supabase = await createClient();

    // Validate required fields
    if (!task.title) {
      console.error("Task creation failed: Missing required title field");
      return { error: new Error("Title is required") };
    }

    if (!task.project_id) {
      console.error("Task creation failed: Missing project_id");
      return { error: new Error("Project ID is required") };
    }

    if (!task.tenant_id) {
      console.error("Task creation failed: Missing tenant_id");
      return { error: new Error("Tenant ID is required") };
    }

    // Validate teams are selected
    if (!task.visibleToTeams || task.visibleToTeams.length === 0) {
      console.error("Task creation failed: No teams selected");
      return { error: new Error("At least one team must be selected") };
    }

    // Extract tag IDs from tag objects
    const tagIds = Array.isArray(task.tags) ? task.tags.map((tag) => tag.id) : [];
    const visibleTeams = Array.isArray(task.visibleToTeams) ? task.visibleToTeams : [];

    // Generate a task key (e.g., "TASK-123")
    const { data: maxKeyData, error: keyError } = await supabase
      .from("tasks")
      .select("key")
      .eq("project_id", task.project_id)
      .order("key", { ascending: false })
      .limit(1);

    if (keyError) {
      console.error("Error fetching task keys:", keyError);
      return { error: keyError };
    }

    let nextKey = "TASK-001";
    if (maxKeyData && maxKeyData.length > 0) {
      const lastKey = maxKeyData[0].key;
      const keyParts = lastKey.split("-");
      if (keyParts.length > 1) {
        const numPart = parseInt(keyParts[1], 10);
        if (!isNaN(numPart)) {
          nextKey = `${keyParts[0]}-${String(numPart + 1).padStart(3, "0")}`;
        }
      }
    }

    // Get the next position in the target column
    const { data: nextPositionData, error: positionError } = await supabase.rpc(
      "get_next_position_in_column",
      {
        column_status: task.status || "Backlog",
        project_id_param: task.project_id,
      }
    );

    if (positionError) {
      console.error("Error getting next position:", positionError);
      return { error: positionError };
    }

    const nextPosition = nextPositionData || 1;

    // Prepare task data for database
    const newTask = {
      key: nextKey,
      title: task.title,
      description: task.description || "",
      value: task.value || "Normal",
      status: task.status || "Backlog",
      life_cycle_status: "created",
      tags: tagIds, // Use UUID array instead of JSON
      sprint: task.sprint || "",
      week: task.week || 0,
      due_date: task.dueDate || null,
      done_date: null,
      delivered_date: null,
      assignees: JSON.stringify(task.assignees || []),
      effort: task.effort || 0,
      position_in_column: task.positionInColumn || nextPosition,
      project_id: task.project_id,
      tenant_id: task.tenant_id,
      visible_to_team_ids: visibleTeams,
    };

    const { data, error } = await supabase.from("tasks").insert(newTask).select().single();

    if (error) {
      console.error("Database error creating task:", error);
      // Handle potential database constraint violations
      if (error.code === "23505") {
        return { error: new Error("A task with this information already exists") };
      }
      if (error.code === "23503") {
        return { error: new Error("Referenced entity not found (foreign key violation)") };
      }
      throw error;
    }

    if (!data) {
      console.error("No data returned after task creation");
      return { error: new Error("Task creation failed: No data returned") };
    }

    // Use getTasks to fetch the created task with proper tag data
    const { tasks } = await getTasks(task.project_id);
    const createdTask = tasks.find((t) => t.id === data.id);

    if (!createdTask) {
      console.error("Created task not found in results");
      return {
        error: new Error("Task creation succeeded but could not retrieve the created task"),
      };
    }

    return { task: createdTask };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    const errorCode = error instanceof Error ? error.name : "no_code";
    console.error(`Error creating task (${errorCode}): ${errorMessage}`, error);
    return {
      error: new Error(
        `Task creation failed: ${errorMessage}${error instanceof Error && error.name ? " - " + error.name : ""}`
      ),
    };
  }
}

/**
 * Update an existing task
 */
export async function updateTask(
  taskId: string,
  updates: Partial<CreateTaskInput>
): Promise<{ task?: TaskModel; error?: Error }> {
  try {
    const supabase = await createClient();

    // Transform updates to match database schema
    const dbUpdates: { [key: string]: string | number | string[] | number[] | null } = {};

    // Only include fields that are actually being updated
    if (updates.title !== undefined) dbUpdates.title = updates.title;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.value !== undefined) dbUpdates.value = updates.value;
    if (updates.status !== undefined) dbUpdates.status = updates.status;
    if (updates.lifeCycleStatus !== undefined)
      dbUpdates.life_cycle_status = updates.lifeCycleStatus;
    if (updates.effort !== undefined) dbUpdates.effort = updates.effort;
    if (updates.positionInColumn !== undefined)
      dbUpdates.position_in_column = updates.positionInColumn;
    if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate;
    if (updates.sprint !== undefined) dbUpdates.sprint = updates.sprint;
    if (updates.week !== undefined) dbUpdates.week = updates.week;

    // Handle array/object fields
    if (updates.tags !== undefined) {
      // Extract tag IDs from tag objects
      dbUpdates.tags = Array.isArray(updates.tags) ? updates.tags.map((tag) => tag.id) : [];
    }
    if (updates.assignees !== undefined) {
      dbUpdates.assignees = JSON.stringify(updates.assignees || []);
    }
    if (updates.visibleToTeams !== undefined) {
      dbUpdates.visible_to_team_ids = updates.visibleToTeams;
    }

    // Always update the timestamp
    dbUpdates.updated_at = new Date().toISOString();

    // If status is being updated, also update lifecycle status (unless explicitly set)
    if (updates.status !== undefined && updates.lifeCycleStatus === undefined) {
      const { mapStatusToLifeCycle } = await import("@/lib/utils-tasks");
      dbUpdates.life_cycle_status = mapStatusToLifeCycle(updates.status);
    }

    // If lifecycle status is being updated to "dev done", set the done_date
    const finalLifeCycleStatus =
      updates.lifeCycleStatus ||
      (updates.status !== undefined ? dbUpdates.life_cycle_status : undefined);

    if (finalLifeCycleStatus === "dev done") {
      // Only set done_date if it's not already set (to preserve original completion time)
      const currentTask = await supabase
        .from("tasks")
        .select("done_date")
        .eq("id", taskId)
        .single();

      if (!currentTask.error && !currentTask.data?.done_date) {
        dbUpdates.done_date = new Date().toISOString();
      }
    }

    const { data, error } = await supabase
      .from("tasks")
      .update(dbUpdates)
      .eq("id", taskId)
      .select()
      .single();

    if (error) {
      console.error("Database error updating task:", error);
      // Handle potential database constraint violations
      if (error.code === "23505") {
        return { error: new Error("A task with this information already exists") };
      }
      if (error.code === "23503") {
        return { error: new Error("Referenced entity not found (foreign key violation)") };
      }
      throw error;
    }

    if (!data) {
      console.error("No data returned after task update");
      return { error: new Error("Task update failed: No data returned") };
    }

    // Get the project_id to fetch the updated task with proper tag data
    const projectId = data.project_id;
    if (!projectId) {
      return { error: new Error("Project ID not found in updated task") };
    }

    // Use getTasks to fetch the updated task with proper tag data
    const { tasks } = await getTasks(projectId);
    const updatedTask = tasks.find((t) => t.id === taskId);

    if (!updatedTask) {
      return { error: new Error("Updated task not found") };
    }

    return { task: updatedTask };
  } catch (error) {
    console.error("Error updating task:", error);
    return { error: error as Error };
  }
}

/**
 * Delete a task
 */
export async function deleteTask(taskId: string): Promise<{ success: boolean; error?: Error }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase.from("tasks").delete().eq("id", taskId);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error("Error deleting task:", error);
    return { success: false, error: error as Error };
  }
}

/**
 * Move a task to a specific position within a column or between columns
 */
export async function moveTaskToPosition(
  taskId: string,
  newStatus: ColumnStatus,
  targetPosition: number,
  projectId: string
): Promise<{ success: boolean; error?: Error }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase.rpc("move_task_to_position", {
      task_id: taskId,
      new_status: newStatus,
      target_position: targetPosition,
      project_id_param: projectId,
    });

    if (error) {
      console.error("Error moving task:", error);
      return { success: false, error };
    }

    return { success: true };
  } catch (error) {
    console.error("Error moving task:", error);
    return { success: false, error: error as Error };
  }
}

/**
 * Bulk update task positions after drag and drop
 */
export async function updateTaskPositions(
  updates: Array<{ taskId: string; status: ColumnStatus; positionInColumn: number }>
): Promise<{ success: boolean; error?: Error }> {
  try {
    const supabase = await createClient();

    // Use a transaction to update all positions atomically
    const { error } = await supabase.rpc("bulk_update_task_positions", {
      position_updates: updates.map((update) => ({
        task_id: update.taskId,
        new_status: update.status,
        new_position: update.positionInColumn,
      })),
    });

    if (error) {
      console.error("Error bulk updating task positions:", error);
      return { success: false, error };
    }

    return { success: true };
  } catch (error) {
    console.error("Error bulk updating task positions:", error);
    return { success: false, error: error as Error };
  }
}
