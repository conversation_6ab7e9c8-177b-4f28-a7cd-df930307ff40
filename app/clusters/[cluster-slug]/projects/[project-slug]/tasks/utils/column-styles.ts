import { ColumnStyle, getStatusStyle } from "@/db/schemas/tasks.schema";
import { CheckCircle2, Circle, CircleFadingArrowUp, CircleGauge, ListChecks } from "lucide-react";

// Icon mapping function to get the appropriate Lucide icon based on iconName
function getIconComponent(iconName: string) {
  switch (iconName) {
    case "circle":
      return Circle;
    case "circle-dot":
      return CircleGauge;
    case "circle-ellipsis":
      return CircleFadingArrowUp;
    case "check-circle":
      return CheckCircle2;
    case "circle-dashed":
    default:
      return ListChecks;
  }
}

// Get column style based on column title
export const getColumnStyle = (title: string): ColumnStyle => {
  const statusStyle = getStatusStyle(title);
  const iconComponent = getIconComponent(statusStyle.iconName);

  return {
    header: `${statusStyle.text.light} ${statusStyle.text.dark} ${statusStyle.bg.light} ${statusStyle.bg.dark} border-${statusStyle.color}-100 dark:border-${statusStyle.color}-950/30`,
    title: `${statusStyle.text.light} ${statusStyle.text.dark}`,
    icon: iconComponent,
    iconColor: `text-${statusStyle.color}-500 dark:text-${statusStyle.color}-500`,
  };
};
