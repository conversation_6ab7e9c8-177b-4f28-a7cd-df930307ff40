/**
 * Task group color type - matches the colors that can be stored in the database
 */
export type TaskGroupColor =
  | "blue"
  | "green"
  | "amber"
  | "purple"
  | "red"
  | "gray"
  | "indigo"
  | "pink"
  | "orange"
  | "teal"
  | "cyan"
  | "lime"
  | "emerald"
  | "violet"
  | "fuchsia"
  | "rose";

/**
 * Get task group styles for group view headers
 * Returns Tailwind classes optimized for task group headers with enhanced dark mode contrast
 * Takes the color from the task group object and generates appropriate styles
 */
export function getTaskGroupHeaderStyles(color: string): string {
  // Normalize color to ensure it matches our supported colors
  const normalizedColor = (color?.toLowerCase() || "gray") as TaskGroupColor;

  // Define color-specific styling for group headers with improved dark mode contrast
  switch (normalizedColor) {
    case "blue":
      return "bg-blue-50/80 border-blue-200/80 text-blue-800 dark:bg-blue-500/20 dark:border-blue-400/50 dark:text-blue-200";
    case "green":
      return "bg-green-50/80 border-green-200/80 text-green-800 dark:bg-green-500/20 dark:border-green-400/50 dark:text-green-200";
    case "amber":
      return "bg-amber-50/80 border-amber-200/80 text-amber-800 dark:bg-amber-500/20 dark:border-amber-400/50 dark:text-amber-200";
    case "purple":
      return "bg-purple-50/80 border-purple-200/80 text-purple-800 dark:bg-purple-500/20 dark:border-purple-400/50 dark:text-purple-200";
    case "red":
      return "bg-red-50/80 border-red-200/80 text-red-800 dark:bg-red-500/20 dark:border-red-400/50 dark:text-red-200";
    case "indigo":
      return "bg-indigo-50/80 border-indigo-200/80 text-indigo-800 dark:bg-indigo-500/20 dark:border-indigo-400/50 dark:text-indigo-200";
    case "pink":
      return "bg-pink-50/80 border-pink-200/80 text-pink-800 dark:bg-pink-500/20 dark:border-pink-400/50 dark:text-pink-200";
    case "orange":
      return "bg-orange-50/80 border-orange-200/80 text-orange-800 dark:bg-orange-500/20 dark:border-orange-400/50 dark:text-orange-200";
    case "teal":
      return "bg-teal-50/80 border-teal-200/80 text-teal-800 dark:bg-teal-500/20 dark:border-teal-400/50 dark:text-teal-200";
    case "cyan":
      return "bg-cyan-50/80 border-cyan-200/80 text-cyan-800 dark:bg-cyan-500/20 dark:border-cyan-400/50 dark:text-cyan-200";
    case "lime":
      return "bg-lime-50/80 border-lime-200/80 text-lime-800 dark:bg-lime-500/20 dark:border-lime-400/50 dark:text-lime-200";
    case "emerald":
      return "bg-emerald-50/80 border-emerald-200/80 text-emerald-800 dark:bg-emerald-500/20 dark:border-emerald-400/50 dark:text-emerald-200";
    case "violet":
      return "bg-violet-50/80 border-violet-200/80 text-violet-800 dark:bg-violet-500/20 dark:border-violet-400/50 dark:text-violet-200";
    case "fuchsia":
      return "bg-fuchsia-50/80 border-fuchsia-200/80 text-fuchsia-800 dark:bg-fuchsia-500/20 dark:border-fuchsia-400/50 dark:text-fuchsia-200";
    case "rose":
      return "bg-rose-50/80 border-rose-200/80 text-rose-800 dark:bg-rose-500/20 dark:border-rose-400/50 dark:text-rose-200";
    case "gray":
    default:
      return "bg-gray-50/80 border-gray-200/80 text-gray-800 dark:bg-gray-500/20 dark:border-gray-400/50 dark:text-gray-200";
  }
}

/**
 * Get task group badge styles for count badges
 * Returns Tailwind classes optimized for count badges with enhanced dark mode contrast
 * Takes the color from the task group object and generates appropriate badge styles
 */
export function getTaskGroupBadgeStyles(color: string): string {
  // Normalize color to ensure it matches our supported colors
  const normalizedColor = (color?.toLowerCase() || "gray") as TaskGroupColor;

  // Define color-specific styling for badges with improved dark mode contrast
  switch (normalizedColor) {
    case "blue":
      return "bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/70 dark:text-blue-200 dark:hover:bg-blue-900/70";
    case "green":
      return "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/70 dark:text-green-200 dark:hover:bg-green-900/70";
    case "amber":
      return "bg-amber-100 text-amber-700 hover:bg-amber-200 dark:bg-amber-900/70 dark:text-amber-200 dark:hover:bg-amber-900/70";
    case "purple":
      return "bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/70 dark:text-purple-200 dark:hover:bg-purple-900/70";
    case "red":
      return "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/70 dark:text-red-200 dark:hover:bg-red-900/70";
    case "indigo":
      return "bg-indigo-100 text-indigo-700 hover:bg-indigo-200 dark:bg-indigo-900/70 dark:text-indigo-200 dark:hover:bg-indigo-900/70";
    case "pink":
      return "bg-pink-100 text-pink-700 hover:bg-pink-200 dark:bg-pink-900/70 dark:text-pink-200 dark:hover:bg-pink-900/70";
    case "orange":
      return "bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/70 dark:text-orange-200 dark:hover:bg-orange-900/70";
    case "teal":
      return "bg-teal-100 text-teal-700 hover:bg-teal-200 dark:bg-teal-900/70 dark:text-teal-200 dark:hover:bg-teal-900/70";
    case "cyan":
      return "bg-cyan-100 text-cyan-700 hover:bg-cyan-200 dark:bg-cyan-900/70 dark:text-cyan-200 dark:hover:bg-cyan-900/70";
    case "lime":
      return "bg-lime-100 text-lime-700 hover:bg-lime-200 dark:bg-lime-900/70 dark:text-lime-200 dark:hover:bg-lime-900/70";
    case "emerald":
      return "bg-emerald-100 text-emerald-700 hover:bg-emerald-200 dark:bg-emerald-900/70 dark:text-emerald-200 dark:hover:bg-emerald-900/70";
    case "violet":
      return "bg-violet-100 text-violet-700 hover:bg-violet-200 dark:bg-violet-900/70 dark:text-violet-200 dark:hover:bg-violet-900/70";
    case "fuchsia":
      return "bg-fuchsia-100 text-fuchsia-700 hover:bg-fuchsia-200 dark:bg-fuchsia-900/70 dark:text-fuchsia-200 dark:hover:bg-fuchsia-900/70";
    case "rose":
      return "bg-rose-100 text-rose-700 hover:bg-rose-200 dark:bg-rose-900/70 dark:text-rose-200 dark:hover:bg-rose-900/70";
    case "gray":
    default:
      return "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-900/70 dark:text-gray-200 dark:hover:bg-gray-900/70";
  }
}

/**
 * Get task group styles for any context
 * Universal function that can be used for different styling contexts
 */
export function getTaskGroupStyles(color: string, context: "header" | "badge" = "header"): string {
  switch (context) {
    case "badge":
      return getTaskGroupBadgeStyles(color);
    case "header":
    default:
      return getTaskGroupHeaderStyles(color);
  }
}
