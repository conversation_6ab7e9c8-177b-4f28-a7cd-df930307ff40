# Tag Color System Guide

## Overview

This document outlines the comprehensive tag color system implemented across all views in the application, with enhanced dark mode support and accessibility compliance.

## Color Palette

The tag system uses a consistent 16-color palette that provides excellent contrast in both light and dark modes:

- `blue` - Primary blue tones
- `green` - Success/positive states  
- `amber` - Warning/in-progress states
- `purple` - Review/special states
- `red` - Error/critical states
- `indigo` - Deep blue variants
- `pink` - Accent colors
- `teal` - Balanced blue-green
- `orange` - Warm accent colors
- `cyan` - Light blue variants
- `lime` - Bright green variants
- `violet` - Purple variants
- `emerald` - Rich green variants
- `rose` - Pink-red variants
- `sky` - Light blue variants
- `gray` - Neutral/default states

## Color Assignment

Tags are assigned colors using a deterministic hash function based on the tag name, ensuring:
- Consistent colors across sessions
- Even distribution across the color palette
- Same tag name always gets the same color

## View-Specific Styling

### List View (`getTagListViewStyles`)
- **Background**: Semi-transparent with 60% opacity
- **Border**: 80% opacity for better definition
- **Text**: High contrast (900 weight in light, 200 weight in dark)
- **Dark Mode**: 25% background opacity, 60% border opacity

### Kanban View (`getTagKanbanViewStyles`)  
- **Background**: Semi-transparent with 80% opacity
- **Border**: Full opacity for card definition
- **Text**: Medium contrast (700 weight in light, 200 weight in dark)
- **Dark Mode**: 25% background opacity, 60% border opacity

### Group Headers (`getTagGroupViewStyles`)
- **Background**: Semi-transparent with 80% opacity
- **Border**: 80% opacity for section definition
- **Text**: High contrast (800 weight in light, 200 weight in dark)
- **Dark Mode**: 20% background opacity, 50% border opacity

## Dark Mode Enhancements

### Contrast Improvements
- Increased text contrast from 300 to 200 weight
- Enhanced border visibility from 50% to 60% opacity
- Improved background opacity from 20% to 25%

### Accessibility Compliance
- All color combinations meet WCAG AA contrast requirements
- Enhanced border visibility for better shape definition
- Consistent opacity levels across all views

## Usage Examples

```typescript
// List view tags
import { getTagListViewStyles } from './tag-styles';
const className = getTagListViewStyles('feature');

// Kanban view tags  
import { getTagKanbanViewStyles } from './tag-styles';
const className = getTagKanbanViewStyles('bug');

// Group headers
import { getTagGroupViewStyles } from './tag-styles';
const className = getTagGroupViewStyles('performance');

// Universal function
import { getTagStyles } from './tag-styles';
const listClassName = getTagStyles('feature', 'list');
const kanbanClassName = getTagStyles('feature', 'kanban');
const groupClassName = getTagStyles('feature', 'group');
```

## Component Integration

### TaskTag Component
- Enhanced hover states with scale and shadow effects
- Improved opacity transitions for dark mode
- Better border visibility on hover

### TaskGroup Component  
- Uses badge styles from constants for count badges
- Integrates with color map for consistent theming
- Supports all tag colors through expanded type definitions

### Badge Styles
- Comprehensive BADGE_STYLES mapping for all 16 colors
- Enhanced dark mode visibility with 25% background opacity
- Consistent 50% border opacity for definition

## Migration Notes

### Breaking Changes
- Expanded `TaskColor` type to include all 16 colors
- Updated `TASK_COLOR_HOVER_CLASSES` for comprehensive coverage
- Enhanced `BADGE_STYLES` with new color variants

### Backward Compatibility
- Legacy `getTagClassNameByName` function maintained
- Existing API signatures preserved
- Default fallbacks to gray color for unknown tags

## Testing Recommendations

1. **Light Mode Testing**
   - Verify all 16 colors display correctly
   - Check contrast ratios meet accessibility standards
   - Test hover states and interactions

2. **Dark Mode Testing**  
   - Confirm enhanced contrast improvements
   - Verify border visibility in all contexts
   - Test color differentiation between similar hues

3. **Cross-View Consistency**
   - Compare tag appearance across list, kanban, and group views
   - Ensure consistent color assignment for same tag names
   - Verify group header styling matches tag colors

## Performance Considerations

- Color assignment uses memoized hash function
- Consistent class name generation prevents style recalculation
- Optimized opacity values for GPU acceleration
- Minimal CSS specificity for fast rendering
