import { TagModel } from "@/db/schemas/tasks.schema";

// Color palette for tags - predefined colors that work well in both light and dark modes
const TAG_COLOR_PALETTE = [
  "blue",
  "green",
  "amber",
  "purple",
  "red",
  "indigo",
  "pink",
  "teal",
  "orange",
  "cyan",
  "lime",
  "violet",
  "emerald",
  "rose",
  "sky",
  "gray",
] as const;

export type TagColor = (typeof TAG_COLOR_PALETTE)[number];

/**
 * Generate a consistent color for a tag based on its name
 * Uses a simple hash function to ensure the same tag name always gets the same color
 */
export function getTagColorByName(tagName: string): TagColor {
  if (!tagName) return "gray";

  // Simple hash function for consistent color assignment
  let hash = 0;
  for (let i = 0; i < tagName.length; i++) {
    const char = tagName.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Get absolute value and map to color palette
  const index = Math.abs(hash) % TAG_COLOR_PALETTE.length;
  return TAG_COLOR_PALETTE[index];
}

/**
 * Get tag styles for list view
 * Returns Tailwind classes optimized for the task list table with enhanced dark mode contrast
 */
export function getTagListViewStyles(tagName: string): string {
  const color = getTagColorByName(tagName);

  // Define color-specific styling for list view with improved dark mode contrast and accessibility
  switch (color) {
    case "blue":
      return "bg-blue-100/60 border-blue-200/80 text-blue-900 dark:bg-blue-500/25 dark:border-blue-400/60 dark:text-blue-200";
    case "green":
      return "bg-green-100/60 border-green-200/80 text-green-900 dark:bg-green-500/25 dark:border-green-400/60 dark:text-green-200";
    case "amber":
      return "bg-amber-100/60 border-amber-200/80 text-amber-900 dark:bg-amber-500/25 dark:border-amber-400/60 dark:text-amber-200";
    case "purple":
      return "bg-purple-100/60 border-purple-200/80 text-purple-900 dark:bg-purple-500/25 dark:border-purple-400/60 dark:text-purple-200";
    case "red":
      return "bg-red-100/60 border-red-200/80 text-red-900 dark:bg-red-500/25 dark:border-red-400/60 dark:text-red-200";
    case "indigo":
      return "bg-indigo-100/60 border-indigo-200/80 text-indigo-900 dark:bg-indigo-500/25 dark:border-indigo-400/60 dark:text-indigo-200";
    case "pink":
      return "bg-pink-100/60 border-pink-200/80 text-pink-900 dark:bg-pink-500/25 dark:border-pink-400/60 dark:text-pink-200";
    case "teal":
      return "bg-teal-100/60 border-teal-200/80 text-teal-900 dark:bg-teal-500/25 dark:border-teal-400/60 dark:text-teal-200";
    case "orange":
      return "bg-orange-100/60 border-orange-200/80 text-orange-900 dark:bg-orange-500/25 dark:border-orange-400/60 dark:text-orange-200";
    case "cyan":
      return "bg-cyan-100/60 border-cyan-200/80 text-cyan-900 dark:bg-cyan-500/25 dark:border-cyan-400/60 dark:text-cyan-200";
    case "lime":
      return "bg-lime-100/60 border-lime-200/80 text-lime-900 dark:bg-lime-500/25 dark:border-lime-400/60 dark:text-lime-200";
    case "violet":
      return "bg-violet-100/60 border-violet-200/80 text-violet-900 dark:bg-violet-500/25 dark:border-violet-400/60 dark:text-violet-200";
    case "emerald":
      return "bg-emerald-100/60 border-emerald-200/80 text-emerald-900 dark:bg-emerald-500/25 dark:border-emerald-400/60 dark:text-emerald-200";
    case "rose":
      return "bg-rose-100/60 border-rose-200/80 text-rose-900 dark:bg-rose-500/25 dark:border-rose-400/60 dark:text-rose-200";
    case "sky":
      return "bg-sky-100/60 border-sky-200/80 text-sky-900 dark:bg-sky-500/25 dark:border-sky-400/60 dark:text-sky-200";
    case "gray":
    default:
      return "bg-gray-100/60 border-gray-200/80 text-gray-900 dark:bg-gray-500/25 dark:border-gray-400/60 dark:text-gray-200";
  }
}

/**
 * Get tag styles for kanban view
 * Returns Tailwind classes optimized for kanban cards with enhanced dark mode contrast
 */
export function getTagKanbanViewStyles(tagName: string): string {
  const color = getTagColorByName(tagName);

  // Define color-specific styling for kanban view with improved dark mode contrast and accessibility
  switch (color) {
    case "blue":
      return "text-blue-700 bg-blue-50/80 border-blue-200 dark:text-blue-200 dark:bg-blue-500/25 dark:border-blue-400/60";
    case "green":
      return "text-green-700 bg-green-50/80 border-green-200 dark:text-green-200 dark:bg-green-500/25 dark:border-green-400/60";
    case "amber":
      return "text-amber-700 bg-amber-50/80 border-amber-200 dark:text-amber-200 dark:bg-amber-500/25 dark:border-amber-400/60";
    case "purple":
      return "text-purple-700 bg-purple-50/80 border-purple-200 dark:text-purple-200 dark:bg-purple-500/25 dark:border-purple-400/60";
    case "red":
      return "text-red-700 bg-red-50/80 border-red-200 dark:text-red-200 dark:bg-red-500/25 dark:border-red-400/60";
    case "indigo":
      return "text-indigo-700 bg-indigo-50/80 border-indigo-200 dark:text-indigo-200 dark:bg-indigo-500/25 dark:border-indigo-400/60";
    case "pink":
      return "text-pink-700 bg-pink-50/80 border-pink-200 dark:text-pink-200 dark:bg-pink-500/25 dark:border-pink-400/60";
    case "teal":
      return "text-teal-700 bg-teal-50/80 border-teal-200 dark:text-teal-200 dark:bg-teal-500/25 dark:border-teal-400/60";
    case "orange":
      return "text-orange-700 bg-orange-50/80 border-orange-200 dark:text-orange-200 dark:bg-orange-500/25 dark:border-orange-400/60";
    case "cyan":
      return "text-cyan-700 bg-cyan-50/80 border-cyan-200 dark:text-cyan-200 dark:bg-cyan-500/25 dark:border-cyan-400/60";
    case "lime":
      return "text-lime-700 bg-lime-50/80 border-lime-200 dark:text-lime-200 dark:bg-lime-500/25 dark:border-lime-400/60";
    case "violet":
      return "text-violet-700 bg-violet-50/80 border-violet-200 dark:text-violet-200 dark:bg-violet-500/25 dark:border-violet-400/60";
    case "emerald":
      return "text-emerald-700 bg-emerald-50/80 border-emerald-200 dark:text-emerald-200 dark:bg-emerald-500/25 dark:border-emerald-400/60";
    case "rose":
      return "text-rose-700 bg-rose-50/80 border-rose-200 dark:text-rose-200 dark:bg-rose-500/25 dark:border-rose-400/60";
    case "sky":
      return "text-sky-700 bg-sky-50/80 border-sky-200 dark:text-sky-200 dark:bg-sky-500/25 dark:border-sky-400/60";
    case "gray":
    default:
      return "text-gray-700 bg-gray-50/80 border-gray-200 dark:text-gray-200 dark:bg-gray-500/25 dark:border-gray-400/60";
  }
}

/**
 * Get the base color name for a tag (useful for grouping)
 */
export function getTagColor(tag: TagModel): string {
  return getTagColorByName(tag.name);
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use getTagListViewStyles() instead
 */
export function getTagClassNameByName(tagName: string): string {
  return getTagListViewStyles(tagName);
}

/**
 * Get tag styles for group-by view headers
 * Returns Tailwind classes optimized for tag group headers with enhanced dark mode contrast
 */
export function getTagGroupViewStyles(tagName: string): string {
  const color = getTagColorByName(tagName);

  // Define color-specific styling for group headers with improved dark mode contrast
  switch (color) {
    case "blue":
      return "bg-blue-50/80 border-blue-200/80 text-blue-800 dark:bg-blue-500/20 dark:border-blue-400/50 dark:text-blue-200";
    case "green":
      return "bg-green-50/80 border-green-200/80 text-green-800 dark:bg-green-500/20 dark:border-green-400/50 dark:text-green-200";
    case "amber":
      return "bg-amber-50/80 border-amber-200/80 text-amber-800 dark:bg-amber-500/20 dark:border-amber-400/50 dark:text-amber-200";
    case "purple":
      return "bg-purple-50/80 border-purple-200/80 text-purple-800 dark:bg-purple-500/20 dark:border-purple-400/50 dark:text-purple-200";
    case "red":
      return "bg-red-50/80 border-red-200/80 text-red-800 dark:bg-red-500/20 dark:border-red-400/50 dark:text-red-200";
    case "indigo":
      return "bg-indigo-50/80 border-indigo-200/80 text-indigo-800 dark:bg-indigo-500/20 dark:border-indigo-400/50 dark:text-indigo-200";
    case "pink":
      return "bg-pink-50/80 border-pink-200/80 text-pink-800 dark:bg-pink-500/20 dark:border-pink-400/50 dark:text-pink-200";
    case "teal":
      return "bg-teal-50/80 border-teal-200/80 text-teal-800 dark:bg-teal-500/20 dark:border-teal-400/50 dark:text-teal-200";
    case "orange":
      return "bg-orange-50/80 border-orange-200/80 text-orange-800 dark:bg-orange-500/20 dark:border-orange-400/50 dark:text-orange-200";
    case "cyan":
      return "bg-cyan-50/80 border-cyan-200/80 text-cyan-800 dark:bg-cyan-500/20 dark:border-cyan-400/50 dark:text-cyan-200";
    case "lime":
      return "bg-lime-50/80 border-lime-200/80 text-lime-800 dark:bg-lime-500/20 dark:border-lime-400/50 dark:text-lime-200";
    case "violet":
      return "bg-violet-50/80 border-violet-200/80 text-violet-800 dark:bg-violet-500/20 dark:border-violet-400/50 dark:text-violet-200";
    case "emerald":
      return "bg-emerald-50/80 border-emerald-200/80 text-emerald-800 dark:bg-emerald-500/20 dark:border-emerald-400/50 dark:text-emerald-200";
    case "rose":
      return "bg-rose-50/80 border-rose-200/80 text-rose-800 dark:bg-rose-500/20 dark:border-rose-400/50 dark:text-rose-200";
    case "sky":
      return "bg-sky-50/80 border-sky-200/80 text-sky-800 dark:bg-sky-500/20 dark:border-sky-400/50 dark:text-sky-200";
    case "gray":
    default:
      return "bg-gray-50/80 border-gray-200/80 text-gray-800 dark:bg-gray-500/20 dark:border-gray-400/50 dark:text-gray-200";
  }
}

/**
 * Get consistent tag styles for any context
 */
export function getTagStyles(
  tagName: string,
  context: "list" | "kanban" | "group" = "list"
): string {
  switch (context) {
    case "kanban":
      return getTagKanbanViewStyles(tagName);
    case "group":
      return getTagGroupViewStyles(tagName);
    case "list":
    default:
      return getTagListViewStyles(tagName);
  }
}
