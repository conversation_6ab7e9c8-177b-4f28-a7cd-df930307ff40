import { TaskGroupModel } from "@/db/schemas/tasks.schema";
import { useQueryClient } from "@tanstack/react-query";
import {
  CheckCircle2Icon,
  CircleDashedIcon,
  CircleFadingArrowUpIcon,
  CircleGaugeIcon,
  CircleIcon,
  LucideIcon,
} from "lucide-react";

// Icon mapping for common icon names from database
const ICON_MAP: Record<string, LucideIcon> = {
  "circle-dashed": CircleDashedIcon,
  circle: CircleIcon,
  "circle-gauge": CircleGaugeIcon,
  "circle-fading-arrow-up": CircleFadingArrowUpIcon,
  "check-circle": CheckCircle2Icon,
  "check-circle-2": CheckCircle2Icon,
  // Add more mappings as needed
};

// Fallback icons for common status names
const STATUS_FALLBACK_ICONS: Record<string, LucideIcon> = {
  backlog: CircleDashedIcon,
  "to do": CircleIcon,
  "in progress": CircleGaugeIcon,
  "in review": CircleFadingArrowUpIcon,
  done: CheckCircle2Icon,
};

// Fallback colors for common status names
const STATUS_FALLBACK_COLORS: Record<string, string> = {
  backlog: "gray",
  "to do": "blue",
  "in progress": "amber",
  "in review": "purple",
  done: "green",
};

export interface TaskStatusInfo {
  icon: LucideIcon;
  color: string;
  name: string;
}

/**
 * Get task status information dynamically from task groups cache
 * Falls back to hardcoded values if no task group data is available
 */
export function useTaskStatusInfo(projectId?: string) {
  const queryClient = useQueryClient();

  const getStatusInfo = (statusName: string): TaskStatusInfo => {
    if (!projectId || !statusName) {
      return {
        icon: CircleDashedIcon,
        color: "gray",
        name: statusName || "Unknown",
      };
    }

    // Get task groups from cache
    const taskGroups = queryClient.getQueryData(["tasks", "groups", projectId]) as
      | TaskGroupModel[]
      | undefined;

    if (taskGroups && taskGroups.length > 0) {
      // Find matching task group by name (case-insensitive)
      const matchingGroup = taskGroups.find(
        (group) => group.name.toLowerCase() === statusName.toLowerCase()
      );

      if (matchingGroup) {
        // Get icon from database or fallback
        const icon =
          ICON_MAP[matchingGroup.icon] ||
          STATUS_FALLBACK_ICONS[statusName.toLowerCase()] ||
          CircleDashedIcon;

        return {
          icon,
          color: matchingGroup.color || STATUS_FALLBACK_COLORS[statusName.toLowerCase()] || "gray",
          name: matchingGroup.name,
        };
      }
    }

    // Fallback to hardcoded values if no task group found
    const normalizedStatus = statusName.toLowerCase();
    return {
      icon: STATUS_FALLBACK_ICONS[normalizedStatus] || CircleDashedIcon,
      color: STATUS_FALLBACK_COLORS[normalizedStatus] || "gray",
      name: statusName,
    };
  };

  const getAllStatusOptions = (): TaskStatusInfo[] => {
    if (!projectId) {
      // Return default statuses if no project
      return Object.entries(STATUS_FALLBACK_ICONS).map(([status, icon]) => ({
        icon,
        color: STATUS_FALLBACK_COLORS[status] || "gray",
        name: status.charAt(0).toUpperCase() + status.slice(1),
      }));
    }

    // Get task groups from cache
    const taskGroups = queryClient.getQueryData(["tasks", "groups", projectId]) as
      | TaskGroupModel[]
      | undefined;

    if (taskGroups && taskGroups.length > 0) {
      return taskGroups.map((group) => ({
        icon:
          ICON_MAP[group.icon] ||
          STATUS_FALLBACK_ICONS[group.name.toLowerCase()] ||
          CircleDashedIcon,
        color: group.color || STATUS_FALLBACK_COLORS[group.name.toLowerCase()] || "gray",
        name: group.name,
      }));
    }

    // Fallback to default statuses
    return Object.entries(STATUS_FALLBACK_ICONS).map(([status, icon]) => ({
      icon,
      color: STATUS_FALLBACK_COLORS[status] || "gray",
      name: status.charAt(0).toUpperCase() + status.slice(1),
    }));
  };

  return {
    getStatusInfo,
    getAllStatusOptions,
  };
}

/**
 * Non-hook version for use in components that can't use hooks
 * Requires task groups to be passed in
 */
export function getTaskStatusInfo(
  statusName: string,
  taskGroups?: TaskGroupModel[]
): TaskStatusInfo {
  if (!statusName) {
    return {
      icon: CircleDashedIcon,
      color: "gray",
      name: "Unknown",
    };
  }

  if (taskGroups && taskGroups.length > 0) {
    // Find matching task group by name (case-insensitive)
    const matchingGroup = taskGroups.find(
      (group) => group.name.toLowerCase() === statusName.toLowerCase()
    );

    if (matchingGroup) {
      // Get icon from database or fallback
      const icon =
        ICON_MAP[matchingGroup.icon] ||
        STATUS_FALLBACK_ICONS[statusName.toLowerCase()] ||
        CircleDashedIcon;

      return {
        icon,
        color: matchingGroup.color || STATUS_FALLBACK_COLORS[statusName.toLowerCase()] || "gray",
        name: matchingGroup.name,
      };
    }
  }

  // Fallback to hardcoded values if no task group found
  const normalizedStatus = statusName.toLowerCase();
  return {
    icon: STATUS_FALLBACK_ICONS[normalizedStatus] || CircleDashedIcon,
    color: STATUS_FALLBACK_COLORS[normalizedStatus] || "gray",
    name: statusName,
  };
}
