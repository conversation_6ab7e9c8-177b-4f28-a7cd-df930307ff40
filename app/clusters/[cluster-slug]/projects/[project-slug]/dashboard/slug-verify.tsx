import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ProtectedLink } from "@/components/ui/protected-link";
import { getCurrentProject } from "@/db/actions/project.action";
import { PageParams } from "@/types/next.types";
import { ArrowLeftIcon } from "lucide-react";

export default async function DashboardPage(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
) {
  const params = await props.params;
  const project = await getCurrentProject(params["project-slug"]);

  if (!project) {
    return <div>Project not found</div>;
  }

  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Project Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Badge variant="outline">Cluster: {params["cluster-slug"]}</Badge>
              <Badge variant="outline">Project: {params["project-slug"]}</Badge>
              <Badge variant="outline" className="text-xs">
                tenant: {project.tenant_id?.slice(0, 8)}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">Project ID: {project.id}</div>
          </div>
        </CardContent>
      </Card>
      <div>Dashboard Content</div>
      <ProtectedLink href="/home">
        <Button variant="outline" size="sm">
          <ArrowLeftIcon className="w-4 h-4" />
          Back to homepage
        </Button>
      </ProtectedLink>
    </div>
  );
}
