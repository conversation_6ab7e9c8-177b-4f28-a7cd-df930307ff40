import type { DashboardSummary, Objective, TeamMember } from "../_types/okr-types";

export const teamMembers: TeamMember[] = [
  {
    id: "sarah.johnson",
    name: "<PERSON>",
    avatar: "https://github.com/shadcn.png",
    department: "Executive",
  },
  {
    id: "micha<PERSON>.chen",
    name: "<PERSON>",
    avatar: "https://github.com/shadcn.png",
    department: "Product",
  },
  {
    id: "alex.rodriguez",
    name: "<PERSON>",
    avatar: "https://github.com/shadcn.png",
    department: "Operations",
  },
  {
    id: "emily.wong",
    name: "<PERSON>",
    avatar: "https://github.com/shadcn.png",
    department: "Sales",
  },
  {
    id: "david.park",
    name: "David Park",
    avatar: "https://github.com/shadcn.png",
    department: "Engineering",
  },
  {
    id: "lisa.wang",
    name: "<PERSON>",
    avatar: "https://github.com/shadcn.png",
    department: "Product",
  },
];

export const departments = [
  "Executive",
  "Product",
  "Engineering",
  "Sales",
  "Marketing",
  "Operations",
  "HR",
  "Finance",
  "Customer Success",
];

export const okrData: Objective[] = [
  {
    id: "okr-1",
    title: "Accelerate Customer Growth & Market Expansion",
    description:
      "Drive significant customer acquisition and expand into new market segments to achieve 40% revenue growth",
    progress: 68,
    cycle: "Q1 2025",
    owner: "Sarah Johnson",
    ownerAvatar: "https://github.com/shadcn.png",
    department: "Executive",
    status: "active",
    priority: "high",
    createdAt: "2024-12-01",
    updatedAt: "2025-01-15",
    linkedProjects: ["project-alpha", "market-expansion"],
    collaborators: ["mike.chen", "alex.rodriguez"],
    keyResults: [
      {
        id: "kr-1-1",
        title: "Achieve 40% revenue growth year-over-year",
        description: "Focus on enterprise accounts and international expansion",
        progress: 75,
        target: 40,
        current: 30,
        unit: "%",
        status: "on-track",
        confidence: 4,
        lastUpdated: "2025-01-15",
        owner: "sarah.johnson",
        linkedTasks: [
          {
            id: "task-1",
            title: "Enterprise sales pipeline review",
            status: "in-progress",
            url: "/tasks/task-1",
          },
          {
            id: "task-2",
            title: "Q1 revenue forecasting",
            status: "completed",
            url: "/tasks/task-2",
          },
        ],
        notes:
          "Strong pipeline in enterprise segment, international deals closing faster than expected",
      },
      {
        id: "kr-1-2",
        title: "Launch operations in 3 new international markets",
        progress: 100,
        target: 3,
        current: 3,
        unit: "markets",
        status: "completed",
        confidence: 5,
        lastUpdated: "2025-01-10",
        owner: "alex.rodriguez",
        linkedTasks: [
          {
            id: "task-3",
            title: "Germany market launch",
            status: "completed",
            url: "/tasks/task-3",
          },
          {
            id: "task-4",
            title: "Japan partnership setup",
            status: "completed",
            url: "/tasks/task-4",
          },
        ],
      },
      {
        id: "kr-1-3",
        title: "Increase customer retention rate to 92%",
        progress: 60,
        target: 92,
        current: 87,
        unit: "%",
        status: "at-risk",
        confidence: 3,
        lastUpdated: "2025-01-12",
        owner: "emily.wong",
        linkedTasks: [
          {
            id: "task-5",
            title: "Customer success program implementation",
            status: "in-progress",
            url: "/tasks/task-5",
          },
        ],
        notes: "Need to accelerate customer success initiatives to meet target",
      },
    ],
  },
  {
    id: "okr-2",
    title: "Transform Product Excellence & User Experience",
    description:
      "Deliver world-class product quality and user experience that drives customer satisfaction and reduces churn",
    progress: 82,
    cycle: "Q1 2025",
    owner: "Michael Chen",
    ownerAvatar: "https://github.com/shadcn.png",
    department: "Product",
    status: "active",
    priority: "high",
    createdAt: "2024-12-01",
    updatedAt: "2025-01-14",
    linkedProjects: ["product-v2", "ux-redesign"],
    collaborators: ["david.park", "lisa.wang"],
    keyResults: [
      {
        id: "kr-2-1",
        title: "Reduce critical bugs by 70%",
        progress: 85,
        target: 70,
        current: 65,
        unit: "%",
        status: "on-track",
        confidence: 4,
        lastUpdated: "2025-01-14",
        owner: "michael.chen",
        linkedTasks: [
          {
            id: "task-6",
            title: "Automated testing implementation",
            status: "completed",
            url: "/tasks/task-6",
          },
          {
            id: "task-7",
            title: "Code quality review process",
            status: "in-progress",
            url: "/tasks/task-7",
          },
        ],
      },
      {
        id: "kr-2-2",
        title: "Achieve Net Promoter Score (NPS) of 65+",
        progress: 80,
        target: 65,
        current: 58,
        unit: "points",
        status: "on-track",
        confidence: 4,
        lastUpdated: "2025-01-13",
        owner: "lisa.wang",
        linkedTasks: [
          {
            id: "task-8",
            title: "User feedback collection system",
            status: "completed",
            url: "/tasks/task-8",
          },
        ],
      },
      {
        id: "kr-2-3",
        title: "Decrease average page load time to under 2 seconds",
        progress: 90,
        target: 2,
        current: 2.1,
        unit: "seconds",
        status: "on-track",
        confidence: 5,
        lastUpdated: "2025-01-15",
        owner: "david.park",
        linkedTasks: [
          {
            id: "task-9",
            title: "Performance optimization sprint",
            status: "completed",
            url: "/tasks/task-9",
          },
        ],
      },
    ],
  },
  {
    id: "okr-3",
    title: "Optimize Operational Excellence & Efficiency",
    description:
      "Streamline operations, reduce costs, and implement automation to improve overall organizational efficiency",
    progress: 45,
    cycle: "Q1 2025",
    owner: "Alex Rodriguez",
    ownerAvatar: "https://github.com/shadcn.png",
    department: "Operations",
    status: "active",
    priority: "medium",
    createdAt: "2024-12-01",
    updatedAt: "2025-01-10",
    linkedProjects: ["automation-initiative"],
    collaborators: ["sarah.johnson"],
    keyResults: [
      {
        id: "kr-3-1",
        title: "Reduce operational costs by 25%",
        progress: 40,
        target: 25,
        current: 12,
        unit: "%",
        status: "at-risk",
        confidence: 2,
        lastUpdated: "2025-01-10",
        owner: "alex.rodriguez",
        linkedTasks: [
          {
            id: "task-10",
            title: "Cost analysis and optimization",
            status: "in-progress",
            url: "/tasks/task-10",
          },
        ],
        notes: "Need to accelerate automation initiatives to meet cost reduction targets",
      },
      {
        id: "kr-3-2",
        title: "Implement automation for 8 key business processes",
        progress: 62.5,
        target: 8,
        current: 5,
        unit: "processes",
        status: "on-track",
        confidence: 3,
        lastUpdated: "2025-01-08",
        owner: "david.park",
        linkedTasks: [
          {
            id: "task-11",
            title: "Process automation roadmap",
            status: "completed",
            url: "/tasks/task-11",
          },
        ],
      },
      {
        id: "kr-3-3",
        title: "Improve project delivery time by 30%",
        progress: 35,
        target: 30,
        current: 8,
        unit: "%",
        status: "behind",
        confidence: 2,
        lastUpdated: "2025-01-05",
        owner: "emily.wong",
        linkedTasks: [
          {
            id: "task-12",
            title: "Agile process improvements",
            status: "in-progress",
            url: "/tasks/task-12",
          },
        ],
      },
    ],
  },
];

export const dashboardSummary: DashboardSummary = {
  totalObjectives: okrData.length,
  onTrackObjectives: okrData.filter((okr) => okr.status === "active" && okr.progress >= 70).length,
  atRiskObjectives: okrData.filter((okr) => okr.progress < 50 && okr.status === "active").length,
  completedObjectives: okrData.filter((okr) => okr.status === "completed").length,
  overallProgress: Math.round(okrData.reduce((sum, okr) => sum + okr.progress, 0) / okrData.length),
  avgConfidence: 3.8,
  lastUpdated: "2025-01-15",
};
