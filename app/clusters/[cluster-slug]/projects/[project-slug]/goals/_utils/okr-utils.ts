import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle2, Clock } from "lucide-react";
import type {
  ConfidenceLevel,
  KeyResultStatus,
  ObjectiveStatus,
  Priority,
} from "../_types/okr-types";

export const getStatusColor = (status: KeyResultStatus | ObjectiveStatus) => {
  switch (status) {
    case "completed":
      return "bg-green-500/20 text-green-700 dark:bg-green-500/30 dark:text-green-400";
    case "at-risk":
      return "bg-yellow-500/20 text-yellow-700 dark:bg-yellow-500/30 dark:text-yellow-400";
    case "behind":
      return "bg-red-500/20 text-red-700 dark:bg-red-500/30 dark:text-red-400";
    case "on-track":
    case "active":
      return "bg-blue-500/20 text-blue-700 dark:bg-blue-500/30 dark:text-blue-400";
    case "draft":
      return "bg-gray-500/20 text-gray-700 dark:bg-gray-500/30 dark:text-gray-400";
    case "archived":
      return "bg-slate-500/20 text-slate-700 dark:bg-slate-500/30 dark:text-slate-400";
    default:
      return "bg-gray-500/20 text-gray-700 dark:bg-gray-500/30 dark:text-gray-400";
  }
};

export const getProgressColor = (progress: number, status?: KeyResultStatus) => {
  if (status === "completed") return "bg-green-600";
  if (status === "behind") return "bg-red-500";
  if (status === "at-risk") return "bg-yellow-500";
  if (progress >= 75) return "bg-green-600";
  if (progress >= 50) return "bg-blue-500";
  return "bg-gray-400";
};

export const getPriorityColor = (priority: Priority) => {
  switch (priority) {
    case "high":
      return "bg-red-500/20 text-red-700 dark:bg-red-500/30 dark:text-red-400";
    case "medium":
      return "bg-yellow-500/20 text-yellow-700 dark:bg-yellow-500/30 dark:text-yellow-400";
    case "low":
      return "bg-green-500/20 text-green-700 dark:bg-green-500/30 dark:text-green-400";
  }
};

export const getConfidenceIcon = (confidence: ConfidenceLevel) => {
  if (confidence >= 4) return { icon: CheckCircle2, className: "h-4 w-4 text-green-600" };
  if (confidence >= 3) return { icon: Clock, className: "h-4 w-4 text-yellow-600" };
  return { icon: AlertTriangle, className: "h-4 w-4 text-red-600" };
};

export const getConfidenceLabel = (confidence: ConfidenceLevel): string => {
  switch (confidence) {
    case 5:
      return "Very High";
    case 4:
      return "High";
    case 3:
      return "Medium";
    case 2:
      return "Low";
    case 1:
      return "Very Low";
  }
};
