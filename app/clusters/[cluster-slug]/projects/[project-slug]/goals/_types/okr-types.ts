export type KeyResultStatus = "on-track" | "at-risk" | "behind" | "completed";
export type ObjectiveStatus = "draft" | "active" | "completed" | "archived";
export type OKRCycle = "Q1 2025" | "Q2 2025" | "Q3 2025" | "Q4 2025";
export type ConfidenceLevel = 1 | 2 | 3 | 4 | 5;
export type Priority = "high" | "medium" | "low";

export interface LinkedTask {
  id: string;
  title: string;
  status: string;
  url: string;
}

export interface KeyResult {
  id: string;
  title: string;
  description?: string;
  progress: number;
  target: number;
  current: number;
  unit: string;
  status: KeyResultStatus;
  confidence: ConfidenceLevel;
  lastUpdated: string;
  linkedTasks: LinkedTask[];
  owner: string;
  notes?: string;
}

export interface Objective {
  id: string;
  title: string;
  description: string;
  progress: number;
  cycle: OKRCycle;
  owner: string;
  ownerAvatar?: string;
  department: string;
  status: ObjectiveStatus;
  priority: Priority;
  keyResults: KeyResult[];
  createdAt: string;
  updatedAt: string;
  linkedProjects: string[];
  collaborators: string[];
}

export interface DashboardSummary {
  totalObjectives: number;
  onTrackObjectives: number;
  atRiskObjectives: number;
  completedObjectives: number;
  overallProgress: number;
  avgConfidence: number;
  lastUpdated: string;
}

export interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  department: string;
}
