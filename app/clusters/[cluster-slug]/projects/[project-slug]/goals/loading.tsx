import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function GoalsLoading() {
  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-80" />
        </div>
        <Skeleton className="h-9 w-32" /> {/* New Objective button */}
      </div>

      {/* Summary tabs */}
      <div className="flex items-center gap-6 text-sm">
        <Skeleton className="h-4 w-16" /> {/* Summary */}
        <Skeleton className="h-4 w-12" /> {/* 3 Total */}
        <Skeleton className="h-4 w-16" /> {/* 1 On Track */}
        <Skeleton className="h-4 w-12" /> {/* 1 At Risk */}
        <Skeleton className="h-4 w-24" /> {/* 65% Avg Progress */}
      </div>

      {/* OKR Cards */}
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="shadow-xs hover:shadow-sm transition-shadow py-0">
            <CardContent className="p-6">
              {/* Main objective header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-3 w-3 rounded-full" /> {/* Status dot */}
                  <Skeleton className="h-6 w-64" /> {/* Title */}
                  <Skeleton className="h-5 w-16 rounded-full" /> {/* Cycle badge */}
                  <Skeleton className="h-5 w-24 rounded-full" /> {/* Priority badge */}
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <Skeleton className="h-4 w-12 mb-1" /> {/* Progress percentage */}
                    <Skeleton className="h-2 w-20 rounded-full" /> {/* Progress bar */}
                  </div>
                  <Skeleton className="h-8 w-8 rounded" /> {/* More options button */}
                  <Skeleton className="h-8 w-8 rounded" /> {/* Expand button */}
                </div>
              </div>

              {/* Owner and metadata */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1.5">
                    <Skeleton className="h-5 w-5 rounded-full" /> {/* Avatar */}
                    <Skeleton className="h-4 w-24" /> {/* Owner name */}
                  </div>
                  <Skeleton className="h-4 w-20" /> {/* Department */}
                  <Skeleton className="h-4 w-24" /> {/* Collaborators */}
                </div>
                <Skeleton className="h-4 w-20" /> {/* Key results count */}
              </div>

              {/* Key Results preview */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" /> {/* "Key Results" label */}
                <div className="space-y-1">
                  {Array.from({ length: 3 }).map((_, krIndex) => (
                    <div key={krIndex} className="flex items-center gap-2">
                      <Skeleton className="h-2 w-2 rounded-full" /> {/* Status dot */}
                      <Skeleton className="h-3 w-48" /> {/* Key result title */}
                      <Skeleton className="h-3 w-8 ml-auto" /> {/* Progress percentage */}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
