import { useMemo, useState } from "react";
import type { Objective } from "../_types/okr-types";

// Simple version that just returns filtered data
export function useOKRFilters(okrData: Objective[], filter: string, searchQuery: string) {
  return useMemo(() => {
    if (!okrData || !Array.isArray(okrData)) {
      return [];
    }

    let filtered = okrData;

    // Apply status/department/cycle/priority filter
    if (filter !== "all") {
      filtered = filtered.filter(
        (okr) =>
          okr.department === filter ||
          okr.status === filter ||
          okr.cycle === filter ||
          okr.priority === filter
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (okr) =>
          okr.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          okr.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          okr.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
          okr.keyResults.some((kr) => kr.title.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filtered;
  }, [okrData, filter, searchQuery]);
}

// Enhanced version with state management for the new goals page client
export function useOKRFiltersWithState(okrData: Objective[]) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({ status: "all", department: "all", cycle: "all" });

  const filteredOKRs = useMemo(() => {
    if (!okrData || !Array.isArray(okrData)) {
      return [];
    }

    let filtered = okrData;

    // Apply filters
    if (filters.status !== "all") {
      filtered = filtered.filter((okr) => okr.status === filters.status);
    }
    if (filters.department !== "all") {
      filtered = filtered.filter((okr) => okr.department === filters.department);
    }
    if (filters.cycle !== "all") {
      filtered = filtered.filter((okr) => okr.cycle === filters.cycle);
    }

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(
        (okr) =>
          okr.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          okr.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          okr.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
          okr.keyResults.some((kr) => kr.title.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [okrData, filters, searchTerm]);

  return {
    filteredOKRs,
    searchTerm,
    setSearchTerm,
    filters,
    setFilters,
  };
}
