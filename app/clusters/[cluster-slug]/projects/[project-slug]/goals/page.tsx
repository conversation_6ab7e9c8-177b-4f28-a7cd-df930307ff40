import { getTenantId } from "@/db/tenant.db";
import { PageParams } from "@/types/next.types";
import { Suspense } from "react";
import { GoalsPageClient } from "./_components/goals-page-client";
import GoalsLoading from "./loading";

export default async function GoalsPage(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
) {
  // Only fetch essential data for routing - everything else handled by client
  const tenantId = await getTenantId();
  if (!tenantId) {
    throw new Error("Tenant ID not found");
  }

  const params = await props.params;

  return (
    <div className="p-6">
      <Suspense fallback={<GoalsLoading />}>
        <GoalsPageClient
          tenantId={tenantId}
          clusterSlug={params["cluster-slug"]}
          projectSlug={params["project-slug"]}
        />
      </Suspense>
    </div>
  );
}
