"use client";

import { Card } from "@/components/ui";
import type { DashboardSummary } from "../_types/okr-types";

interface DashboardSummaryProps {
  summary: DashboardSummary;
}

export function DashboardSummaryCards({ summary }: DashboardSummaryProps) {
  return (
    <Card className="flex flex-row items-center gap-6 text-sm text-muted-foreground px-6">
      <div className="text-sm font-bold">Summary</div>
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-blue-500" />
        <span>{summary.totalObjectives} Total</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-green-500" />
        <span>{summary.onTrackObjectives} On Track</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-yellow-500" />
        <span>{summary.atRiskObjectives} At Risk</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="font-medium">{summary.overallProgress}% Avg Progress</span>
      </div>
    </Card>
  );
}
