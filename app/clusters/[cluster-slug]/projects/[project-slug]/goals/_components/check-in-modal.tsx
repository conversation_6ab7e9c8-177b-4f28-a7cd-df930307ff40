"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import type { Objective } from "../_types/okr-types";
import { getConfidenceLabel } from "../_utils/okr-utils";

interface CheckInModalProps {
  objective: Objective | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CheckInModal({ objective, isOpen, onClose }: CheckInModalProps) {
  if (!objective) return null;

  const handleSubmit = () => {
    // TODO: Implement check-in submission logic
    console.log("Submitting check-in for:", objective.id);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="min-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="mb-6">
          <DialogTitle className="text-xl">Weekly Check-in: {objective.title}</DialogTitle>
          <DialogDescription className="text-base">
            Update progress and confidence for key results
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-8">
          {objective.keyResults.map((kr) => (
            <div key={kr.id} className="space-y-6 p-6 border rounded-lg bg-muted/20">
              <div>
                <h4 className="text-lg font-semibold mb-2">{kr.title}</h4>
                {kr.description && (
                  <p className="text-sm text-muted-foreground">{kr.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Current Value</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      defaultValue={kr.current}
                      className="flex-1"
                      placeholder="Enter current value"
                    />
                    <div className="px-3 py-2 bg-muted rounded-md border text-sm text-muted-foreground min-w-fit">
                      {kr.unit}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Target: {kr.target} {kr.unit}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Confidence (1-5)</Label>
                  <Select defaultValue={kr.confidence.toString()}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map((num) => (
                        <SelectItem key={num} value={num.toString()}>
                          <div className="flex items-center justify-between w-full">
                            <span>{num}</span>
                            <span className="text-muted-foreground ml-2">
                              {getConfidenceLabel(num as 1 | 2 | 3 | 4 | 5)}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Status Update</Label>
                <Textarea
                  placeholder="Share progress, blockers, or insights..."
                  className="min-h-20"
                  defaultValue={kr.notes}
                />
                <p className="text-xs text-muted-foreground">
                  Describe any challenges, wins, or changes in approach
                </p>
              </div>
            </div>
          ))}
        </div>

        <DialogFooter className="mt-8 gap-3">
          <Button variant="outline" onClick={onClose} className="px-6">
            Cancel
          </Button>
          <Button onClick={handleSubmit} className="px-6">
            Save Check-in
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
