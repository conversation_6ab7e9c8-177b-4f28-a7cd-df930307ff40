"use client";

import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import type { KeyResult } from "../_types/okr-types";
import { getConfidenceIcon, getProgressColor, getStatusColor } from "../_utils/okr-utils";

interface KeyResultDetailsProps {
  keyResult: KeyResult | null;
  isOpen: boolean;
  onClose: () => void;
}

export function KeyResultDetails({ keyResult, isOpen, onClose }: KeyResultDetailsProps) {
  if (!keyResult) return null;

  const confidenceIconData = getConfidenceIcon(keyResult.confidence);
  const ConfidenceIcon = confidenceIconData.icon;

  return (
    <Sheet open={isOpen} onOpenChange={() => onClose()}>
      <SheetContent className="min-w-2xl max-h-[100vh] overflow-y-auto p-6">
        <SheetHeader className="mb-6">
          <SheetTitle className="text-xl">Key Result Details</SheetTitle>
          <SheetDescription>View and manage key result progress and linked tasks</SheetDescription>
        </SheetHeader>

        <div className="space-y-8">
          {/* Header Section */}
          <div className="space-y-4">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <h4 className="text-lg font-semibold leading-tight">{keyResult.title}</h4>
                {keyResult.description && (
                  <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                    {keyResult.description}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-3 flex-shrink-0">
                <ConfidenceIcon className={confidenceIconData.className} />
                <Badge className={getStatusColor(keyResult.status)}>
                  {keyResult.status.replace("-", " ")}
                </Badge>
              </div>
            </div>
          </div>

          {/* Metrics Section */}
          <div className="grid grid-cols-2 gap-6 p-6 bg-muted/30 rounded-lg">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Current / Target</Label>
              <p className="text-2xl font-bold">
                {keyResult.current} / {keyResult.target} {keyResult.unit}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Progress</Label>
              <p className="text-2xl font-bold">{keyResult.progress}%</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Progress Visualization</Label>
            <Progress
              value={keyResult.progress}
              className={`h-4 ${getProgressColor(keyResult.progress, keyResult.status)}`}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0%</span>
              <span className="font-medium">{keyResult.progress}%</span>
              <span>100%</span>
            </div>
          </div>

          {/* Confidence Level */}
          <div className="space-y-3 p-4 border rounded-lg">
            <Label className="text-sm font-medium flex items-center gap-2">
              <ConfidenceIcon className={confidenceIconData.className} />
              Confidence Level
            </Label>
            <div className="flex items-center gap-3">
              <div className="text-lg font-semibold">{keyResult.confidence}/5</div>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((level) => (
                  <div
                    key={level}
                    className={`h-3 w-6 rounded-sm ${
                      level <= keyResult.confidence ? "bg-blue-500" : "bg-muted"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Linked Tasks */}
          {keyResult.linkedTasks.length > 0 && (
            <div className="space-y-4">
              <Label className="text-sm font-semibold">
                Linked Tasks ({keyResult.linkedTasks.length})
              </Label>
              <div className="space-y-3">
                {keyResult.linkedTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors"
                  >
                    <span className="text-sm font-medium">{task.title}</span>
                    <Badge variant="outline" className="text-xs">
                      {task.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Notes */}
          {keyResult.notes && (
            <div className="space-y-3">
              <Label className="text-sm font-semibold">Notes & Updates</Label>
              <div className="p-4 bg-muted/30 rounded-lg">
                <p className="text-sm leading-relaxed">{keyResult.notes}</p>
              </div>
            </div>
          )}

          {/* Footer Info */}
          <div className="flex items-center justify-between pt-6 border-t text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>
                Owner: <span className="font-medium">{keyResult.owner}</span>
              </span>
            </div>
            <span>Updated: {new Date(keyResult.lastUpdated).toLocaleDateString()}</span>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
