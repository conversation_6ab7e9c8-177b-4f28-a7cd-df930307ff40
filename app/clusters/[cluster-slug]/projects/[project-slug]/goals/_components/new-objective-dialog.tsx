"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Building2, Flag, Plus, Target, Trash2, Users } from "lucide-react";
import { useState } from "react";
import { departments, teamMembers } from "../_data/okr-data";
import type { OKRCycle, Priority } from "../_types/okr-types";
import { getPriorityColor } from "../_utils/okr-utils";

interface KeyResultForm {
  id: string;
  title: string;
  description: string;
  target: string;
  unit: string;
  owner: string;
}

interface ObjectiveForm {
  title: string;
  description: string;
  cycle: OKRCycle | "";
  owner: string;
  department: string;
  priority: Priority | "";
  keyResults: KeyResultForm[];
}

export function NewObjectiveDialog({ children }: { children?: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const [objective, setObjective] = useState<ObjectiveForm>({
    title: "",
    description: "",
    cycle: "",
    owner: "",
    department: "",
    priority: "",
    keyResults: [],
  });

  const addKeyResult = () => {
    const newKR: KeyResultForm = {
      id: `kr-${Date.now()}`,
      title: "",
      description: "",
      target: "",
      unit: "",
      owner: "",
    };
    setObjective((prev) => ({
      ...prev,
      keyResults: [...prev.keyResults, newKR],
    }));
  };

  const updateKeyResult = (id: string, field: keyof KeyResultForm, value: string) => {
    setObjective((prev) => ({
      ...prev,
      keyResults: prev.keyResults.map((kr) => (kr.id === id ? { ...kr, [field]: value } : kr)),
    }));
  };

  const removeKeyResult = (id: string) => {
    setObjective((prev) => ({
      ...prev,
      keyResults: prev.keyResults.filter((kr) => kr.id !== id),
    }));
  };

  const handleSubmit = () => {
    // TODO: Implement objective creation logic
    console.log("Creating objective:", objective);
    setOpen(false);
    // Reset form
    setObjective({
      title: "",
      description: "",
      cycle: "",
      owner: "",
      department: "",
      priority: "",
      keyResults: [],
    });
  };

  // Import utility function instead
  // const getPriorityColor = ... (removed)

  const isFormValid = () => {
    return (
      objective.title.trim() !== "" &&
      objective.description.trim() !== "" &&
      objective.cycle !== "" &&
      objective.owner !== "" &&
      objective.department !== "" &&
      objective.priority !== "" &&
      objective.keyResults.length >= 1 &&
      objective.keyResults.every(
        (kr) =>
          kr.title.trim() !== "" &&
          kr.target.trim() !== "" &&
          kr.unit.trim() !== "" &&
          kr.owner !== ""
      )
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button size="sm">
            <Plus className="h-4 w-4 mr-1" />
            New OKR
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="min-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Create New Objective
          </DialogTitle>
          <DialogDescription>
            Define a strategic objective with measurable key results to track progress
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Objective Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Target className="h-4 w-4" />
                Objective Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Objective Title *</Label>
                <Input
                  id="title"
                  placeholder="Enter a clear, inspiring objective..."
                  value={objective.title}
                  onChange={(e) => setObjective((prev) => ({ ...prev, title: e.target.value }))}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what success looks like and why this objective matters..."
                  value={objective.description}
                  onChange={(e) =>
                    setObjective((prev) => ({ ...prev, description: e.target.value }))
                  }
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cycle">Cycle *</Label>
                  <Select
                    value={objective.cycle}
                    onValueChange={(value: OKRCycle) =>
                      setObjective((prev) => ({ ...prev, cycle: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select quarter" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Q1 2025">Q1 2025</SelectItem>
                      <SelectItem value="Q2 2025">Q2 2025</SelectItem>
                      <SelectItem value="Q3 2025">Q3 2025</SelectItem>
                      <SelectItem value="Q4 2025">Q4 2025</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="priority">Priority *</Label>
                  <Select
                    value={objective.priority}
                    onValueChange={(value: Priority) =>
                      setObjective((prev) => ({ ...prev, priority: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">
                        <div className="flex items-center gap-2">
                          <Flag className="h-4 w-4 text-red-500" />
                          High Priority
                        </div>
                      </SelectItem>
                      <SelectItem value="medium">
                        <div className="flex items-center gap-2">
                          <Flag className="h-4 w-4 text-yellow-500" />
                          Medium Priority
                        </div>
                      </SelectItem>
                      <SelectItem value="low">
                        <div className="flex items-center gap-2">
                          <Flag className="h-4 w-4 text-green-500" />
                          Low Priority
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="owner">Owner *</Label>
                  <Select
                    value={objective.owner}
                    onValueChange={(value) => setObjective((prev) => ({ ...prev, owner: value }))}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select owner" />
                    </SelectTrigger>
                    <SelectContent>
                      {teamMembers.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={member.avatar} />
                              <AvatarFallback className="text-xs">
                                {member.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <span>{member.name}</span>
                            <Badge variant="secondary" className="text-xs ml-auto">
                              {member.department}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="department">Department *</Label>
                  <Select
                    value={objective.department}
                    onValueChange={(value) =>
                      setObjective((prev) => ({ ...prev, department: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4" />
                            {dept}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Preview */}
              {objective.title && (
                <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Preview</h4>
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className="font-medium">{objective.title}</span>
                    {objective.cycle && <Badge variant="outline">{objective.cycle}</Badge>}
                    {objective.priority && (
                      <Badge className={getPriorityColor(objective.priority)}>
                        {objective.priority} priority
                      </Badge>
                    )}
                  </div>
                  {objective.description && (
                    <p className="text-sm text-muted-foreground mt-2">{objective.description}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Key Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Key Results ({objective.keyResults.length})
                </CardTitle>
                <Button onClick={addKeyResult} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Key Result
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {objective.keyResults.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Target className="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
                  <p>No key results added yet</p>
                  <p className="text-xs">Add 3-5 measurable key results to track progress</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {objective.keyResults.map((kr, index) => (
                    <div key={kr.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <h5 className="font-medium">Key Result #{index + 1}</h5>
                        <Button
                          onClick={() => removeKeyResult(kr.id)}
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <Label>Title *</Label>
                          <Input
                            placeholder="Enter a specific, measurable key result..."
                            value={kr.title}
                            onChange={(e) => updateKeyResult(kr.id, "title", e.target.value)}
                            className="mt-1"
                          />
                        </div>

                        <div>
                          <Label>Description</Label>
                          <Textarea
                            placeholder="Additional context or details (optional)..."
                            value={kr.description}
                            onChange={(e) => updateKeyResult(kr.id, "description", e.target.value)}
                            className="mt-1"
                            rows={2}
                          />
                        </div>

                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <Label>Target Value *</Label>
                            <Input
                              type="number"
                              placeholder="100"
                              value={kr.target}
                              onChange={(e) => updateKeyResult(kr.id, "target", e.target.value)}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label>Unit *</Label>
                            <Select
                              value={kr.unit}
                              onValueChange={(value) => updateKeyResult(kr.id, "unit", value)}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue placeholder="Unit" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="%">Percentage (%)</SelectItem>
                                <SelectItem value="$">Dollars ($)</SelectItem>
                                <SelectItem value="users">Users</SelectItem>
                                <SelectItem value="customers">Customers</SelectItem>
                                <SelectItem value="points">Points</SelectItem>
                                <SelectItem value="days">Days</SelectItem>
                                <SelectItem value="hours">Hours</SelectItem>
                                <SelectItem value="count">Count</SelectItem>
                                <SelectItem value="score">Score</SelectItem>
                                <SelectItem value="rate">Rate</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label>Owner *</Label>
                            <Select
                              value={kr.owner}
                              onValueChange={(value) => updateKeyResult(kr.id, "owner", value)}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue placeholder="Select" />
                              </SelectTrigger>
                              <SelectContent>
                                {teamMembers.map((member) => (
                                  <SelectItem key={member.id} value={member.id}>
                                    <div className="flex items-center gap-2">
                                      <Avatar className="h-4 w-4">
                                        <AvatarImage src={member.avatar} />
                                        <AvatarFallback className="text-xs">
                                          {member.name
                                            .split(" ")
                                            .map((n) => n[0])
                                            .join("")}
                                        </AvatarFallback>
                                      </Avatar>
                                      <span className="text-xs">{member.name}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!isFormValid()}>
            Create Objective
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
