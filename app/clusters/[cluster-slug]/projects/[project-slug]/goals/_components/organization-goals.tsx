"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";

import { dashboardSummary, okrData } from "../_data/okr-data";
import { useOKRFilters } from "../_hooks/use-okr-filters";
import type { KeyResult, Objective } from "../_types/okr-types";

import { CheckInModal } from "./check-in-modal";
import { DashboardSummaryCards } from "./dashboard-summary";
import { KeyResultDetails } from "./key-result-details";
import { NewObjectiveDialog } from "./new-objective-dialog";
import { ObjectiveCard } from "./objective-card";
import { SearchAndFilters } from "./search-and-filters";

export function OrganizationGoals() {
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedKeyResult, setSelectedKeyResult] = useState<KeyResult | null>(null);
  const [checkInObjective, setCheckInObjective] = useState<Objective | null>(null);

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const filteredOKRs = useOKRFilters(okrData, filter, searchQuery);

  const handleCheckIn = (objective: Objective) => {
    setCheckInObjective(objective);
  };

  const handleKeyResultView = (keyResult: KeyResult) => {
    setSelectedKeyResult(keyResult);
  };

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-semibold">Objectives & Key Results</h1>
          <p className="text-sm text-muted-foreground">
            Track strategic goals and measure progress toward execution
          </p>
        </div>

        <div className="flex items-center gap-3">
          <SearchAndFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filter={filter}
            onFilterChange={setFilter}
          />
          <NewObjectiveDialog>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-1" />
              New OKR
            </Button>
          </NewObjectiveDialog>
        </div>
      </div>

      {/* Dashboard Summary */}
      <DashboardSummaryCards summary={dashboardSummary} />
      {/* OKR List */}
      <div className="space-y-4">
        {filteredOKRs.map((okr: Objective) => (
          <ObjectiveCard
            key={okr.id}
            objective={okr}
            isExpanded={expandedItems.includes(okr.id)}
            onToggleExpand={() => toggleExpand(okr.id)}
            onCheckIn={() => handleCheckIn(okr)}
            onKeyResultView={handleKeyResultView}
          />
        ))}

        {filteredOKRs.length === 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">No objectives found</h3>
            <p className="text-sm">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>

      {/* Modals and Side Panels */}
      <KeyResultDetails
        keyResult={selectedKeyResult}
        isOpen={!!selectedKeyResult}
        onClose={() => setSelectedKeyResult(null)}
      />

      <CheckInModal
        objective={checkInObjective}
        isOpen={!!checkInObjective}
        onClose={() => setCheckInObjective(null)}
      />
    </div>
  );
}
