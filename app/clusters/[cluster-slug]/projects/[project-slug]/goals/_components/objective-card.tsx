"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import {
  ChevronDown,
  ChevronRight,
  Edit,
  Eye,
  MessageSquare,
  MoreHorizontal,
  Plus,
  Users,
} from "lucide-react";
import type { KeyResult, Objective } from "../_types/okr-types";

interface ObjectiveCardProps {
  objective: Objective;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onCheckIn: () => void;
  onKeyResultView: (keyResult: KeyResult) => void;
}

export function ObjectiveCard({
  objective,
  isExpanded,
  onToggleExpand,
  onCheckIn,
  onKeyResultView,
}: ObjectiveCardProps) {
  const statusIcon =
    objective.status === "completed" ? (
      <div className="h-3 w-3 rounded-full bg-green-500" />
    ) : objective.status === "active" ? (
      <div className="h-3 w-3 rounded-full bg-blue-500" />
    ) : (
      <div className="h-3 w-3 rounded-full bg-gray-400" />
    );

  return (
    <Card
      className="shadow-xs hover:shadow-sm transition-shadow py-0 hover:cursor-pointer"
      onClick={onToggleExpand}
    >
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {statusIcon}
            <h3 className="font-semibold text-base">{objective.title}</h3>
            <Badge variant="outline" className="text-xs">
              {objective.cycle}
            </Badge>
            <Badge
              variant="outline"
              className={`text-xs ${
                objective.priority === "high"
                  ? "border-red-200 text-red-600"
                  : objective.priority === "medium"
                    ? "border-yellow-200 text-yellow-600"
                    : "border-green-200 text-green-600"
              }`}
            >
              {objective.priority} priority
            </Badge>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-sm font-medium mb-1">{objective.progress}%</div>
              <Progress value={objective.progress} className="h-2 w-20" />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onCheckIn}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Weekly Check-in
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Objective
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1.5">
              <Avatar className="h-5 w-5">
                <AvatarImage src={objective.ownerAvatar} />
                <AvatarFallback className="text-xs">
                  {objective.owner
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <span>{objective.owner}</span>
            </div>

            <span>{objective.department}</span>

            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{objective.collaborators.length} collaborators</span>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            {objective.keyResults.length}/5 Completed
          </div>
        </div>

        <div className="space-y-2">
          <div className="text-xs font-medium text-muted-foreground">Key Results</div>
          <div className="space-y-1">
            {objective.keyResults.slice(0, 3).map((kr) => (
              <div key={kr.id} className="flex items-center gap-2 text-xs">
                <div
                  className={`h-2 w-2 rounded-full ${
                    kr.status === "completed"
                      ? "bg-green-500"
                      : kr.status === "on-track"
                        ? "bg-blue-500"
                        : kr.status === "at-risk"
                          ? "bg-yellow-500"
                          : "bg-red-500"
                  }`}
                />
                <span className="flex-1 truncate">{kr.title}</span>
                <span className="text-muted-foreground">{kr.progress}%</span>
              </div>
            ))}
            {objective.keyResults.length > 3 && (
              <div className="text-xs text-muted-foreground pl-4">
                +{objective.keyResults.length - 3} more
              </div>
            )}
          </div>
        </div>

        {isExpanded && (
          <div className="mt-6 pt-4 border-t space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">All Key Results</h4>
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                Add Key Result
              </Button>
            </div>

            <div className="space-y-3">
              {objective.keyResults.map((kr) => (
                <div
                  key={kr.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 transition-colors cursor-pointer"
                  onClick={() => onKeyResultView(kr)}
                >
                  <div className="flex items-center gap-3 flex-1">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        kr.status === "completed"
                          ? "bg-green-500"
                          : kr.status === "on-track"
                            ? "bg-blue-500"
                            : kr.status === "at-risk"
                              ? "bg-yellow-500"
                              : "bg-red-500"
                      }`}
                    />

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{kr.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {kr.current} / {kr.target} {kr.unit}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="text-xs font-medium">{kr.progress}%</div>
                      <Progress value={kr.progress} className="h-1.5 w-16" />
                    </div>
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
