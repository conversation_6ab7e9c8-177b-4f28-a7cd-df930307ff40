"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";

import { dashboardSummary, okrData } from "../_data/okr-data";
import { useOKRFilters } from "../_hooks/use-okr-filters";
import type { Objective } from "../_types/okr-types";

import { DashboardSummaryCards } from "./dashboard-summary";
import { ObjectiveCard } from "./objective-card";

interface GoalsPageClientProps {
  tenantId: string;
  clusterSlug: string;
  projectSlug: string;
}

export function GoalsPageClient({}: GoalsPageClientProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [searchTerm] = useState("");
  const [filter] = useState("all");

  // Use mock data for now - will be replaced with React Query when backend is ready
  const objectives = okrData || [];
  const dashboard = dashboardSummary;

  // Use the OKR filters hook with safe fallback
  const filteredOKRs = useOKRFilters(objectives, filter, searchTerm);

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleCheckIn = () => {
    // TODO: Implement check-in functionality
  };

  const handleKeyResultView = () => {
    // TODO: Implement key result view functionality
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Goals & Objectives</h1>
          <p className="text-sm text-gray-500">Track and manage your OKRs</p>
        </div>
        <Button onClick={() => {}}>
          <Plus className="h-4 w-4 mr-2" />
          New Objective
        </Button>
      </div>

      {/* Dashboard Summary */}
      <DashboardSummaryCards summary={dashboard} />

      {/* OKR List */}
      <div className="space-y-4">
        {filteredOKRs.map((okr: Objective) => (
          <ObjectiveCard
            key={okr.id}
            objective={okr}
            isExpanded={expandedItems.includes(okr.id)}
            onToggleExpand={() => toggleExpand(okr.id)}
            onCheckIn={() => handleCheckIn()}
            onKeyResultView={() => handleKeyResultView()}
          />
        ))}

        {filteredOKRs.length === 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">No objectives found</h3>
            <p className="text-sm">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}
