import { LogoutButton } from "@/components/auth/logout-button";
import { AppSidebar } from "@/components/navbar/app-sidebar";
import { InsetBreadcrumb } from "@/components/navbar/nav-inset-breadcrumb";
import GlobalNotificationBell from "@/components/origin-ui/global-notification-bell";
import UserAvatar from "@/components/origin-ui/user-avatar";
import { AppSidebarSkeleton } from "@/components/skeletons/app-sidebar-skeleton";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { goToMainDomain } from "@/lib/utils";
import { Computer, CreditCard, HelpCircle, TvMinimal, User } from "lucide-react";
import Link from "next/link";
import React, { Suspense } from "react";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <Suspense fallback={<AppSidebarSkeleton />}>
        <AppSidebar />
      </Suspense>

      <main className="flex flex-col w-full h-screen">
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-16 pl-4 pr-6">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <SidebarTrigger />
                <Separator
                  orientation="vertical"
                  className="data-[orientation=vertical]:h-4 mr-2"
                />
                <div className="flex items-center line-clamp-1 gap-0">
                  <Suspense fallback={<Skeleton className="w-20 h-8" />}>
                    <InsetBreadcrumb />
                  </Suspense>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 md:hidden lg:flex">
                  <ThemeToggle />
                  <GlobalNotificationBell side="bottom" align="end" />
                </div>
                <Suspense fallback={<Skeleton className="w-8 h-8" />}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="relative w-8 h-8 rounded-full">
                        <UserAvatar />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" side="bottom" sideOffset={7}>
                      <DropdownMenuLabel>My Account</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <div className="flex items-center gap-2">
                          <TvMinimal className="w-4 h-4" />
                          <Link href="/demo/home/">Demo Application</Link>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4" />
                          <Link href="/home/<USER>">Account</Link>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <div className="flex items-center gap-2">
                          <Computer className="w-4 h-4" />
                          <Link href={goToMainDomain("auth/organizations")}>My Organizations</Link>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <div className="flex items-center gap-2">
                          <CreditCard className="w-4 h-4" />
                          <Link href="/home/<USER>">Manage Plan</Link>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <div className="flex items-center gap-2">
                          <HelpCircle className="w-4 h-4" />
                          <Link href="/home/<USER>">Help</Link>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />

                      <DropdownMenuItem className="p-0 pl-0 m-0">
                        <LogoutButton
                          variant="ghost"
                          className="flex justify-start w-full"
                          isMenuButton
                        />
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </Suspense>
              </div>
            </div>
          </header>
        </SidebarInset>
        {children}
      </main>
    </SidebarProvider>
  );
}
