import { Card, CardDescription, CardHeader, TimelineDate, TimelineTitle } from "@/components/ui";

import { AssigneeDisplay } from "@/components/tasks/tasks-assignee-display";
import { Skeleton } from "@/components/ui/skeleton";
import { type TaskModel } from "@/db/schemas/tasks.schema";
import { Suspense } from "react";

export function CompletedTaskCard({ task }: { task: TaskModel }) {
  return (
    <Card className="max-w-xs p-4 min-w-72 h-24">
      <CardHeader className="flex items-center justify-between p-0 -mb-6">
        <TimelineTitle>{task.title}</TimelineTitle>
        <div className="flex items-center gap-1">
          <Suspense fallback={<Skeleton className="w-4 h-4 rounded-full" />}>
            {task.assignees.map((assignee) => (
              <AssigneeDisplay key={assignee.id} assignees={[assignee]} />
            ))}
          </Suspense>
        </div>
      </CardHeader>
      <TimelineDate>{task.key}</TimelineDate>
      <CardDescription className="-mt-5 hidden">
        <div className="text-sm text-gray-500 line-clamp-3">{task.description}</div>
      </CardDescription>
    </Card>
  );
}
