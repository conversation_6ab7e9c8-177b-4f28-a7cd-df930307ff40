"use client";

import {
  <PERSON><PERSON>,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, SearchIcon } from "lucide-react";
import { useState } from "react";

type FiltersProps = {
  onSearch: (searchTerm: string) => void;
  onMonthChange: (month: number | null) => void;
  onYearChange: (year: number | null) => void;
};

export function TaskFilters({ onSearch, onMonthChange, onYearChange }: FiltersProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [date, setDate] = useState<Date | undefined>(undefined);

  // Generate years for the dropdown (current year - 5 years)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 6 }, (_, i) => currentYear - i);

  // Generate all months
  const months = [
    { label: "January", value: 0 },
    { label: "February", value: 1 },
    { label: "March", value: 2 },
    { label: "April", value: 3 },
    { label: "May", value: 4 },
    { label: "June", value: 5 },
    { label: "July", value: 6 },
    { label: "August", value: 7 },
    { label: "September", value: 8 },
    { label: "October", value: 9 },
    { label: "November", value: 10 },
    { label: "December", value: 11 },
  ];

  // Handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  // Handle month selection
  const handleMonthChange = (value: string) => {
    const monthValue = value === "all" ? null : parseInt(value);
    onMonthChange(monthValue);
  };

  // Handle year selection
  const handleYearChange = (value: string) => {
    const yearValue = value === "all" ? null : parseInt(value);
    onYearChange(yearValue);
  };

  // Handle date selection from calendar
  const handleDateChange = (date: Date | undefined) => {
    setDate(date);
    if (date) {
      onMonthChange(date.getMonth());
      onYearChange(date.getFullYear());
    } else {
      onMonthChange(null);
      onYearChange(null);
    }
  };

  return (
    <div className="flex flex-col w-full gap-2 sm:flex-row sm:items-center sm:w-auto">
      {/* Search input */}
      <div className="relative w-full sm:w-64">
        <SearchIcon className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search by key, title or assignee"
          value={searchTerm}
          onChange={handleSearch}
          className="pl-8"
        />
      </div>

      <div className="flex items-center gap-2">
        {/* Month dropdown */}
        <Select onValueChange={handleMonthChange}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="All months" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Months</SelectItem>
            {months.map((month) => (
              <SelectItem key={month.value} value={month.value.toString()}>
                {month.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Year dropdown */}
        <Select onValueChange={handleYearChange}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue placeholder="All years" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {years.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Calendar popup for date selection */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className={cn("w-9 p-0", !date && "text-muted-foreground")}>
              <CalendarIcon className="w-4 h-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar mode="single" selected={date} onSelect={handleDateChange} initialFocus />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
