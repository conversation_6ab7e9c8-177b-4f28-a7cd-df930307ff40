"use client";

import {
  Timeline,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
} from "@/components/ui";
import { Button } from "@/components/ui/button";
import { type TaskModel } from "@/db/schemas/tasks.schema";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";
import { CompletedTaskCard } from "./completed-tasks-task-card";

// Component for stacked task cards with expand/collapse functionality
function StackedTaskCards({ tasks }: { tasks: TaskModel[] }) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (tasks.length <= 1) {
    // If only one task, show it normally without stacking
    return (
      <div className="flex flex-wrap gap-2">
        {tasks.map((task: TaskModel) => (
          <CompletedTaskCard key={task.id} task={task} />
        ))}
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Toggle button */}
      <motion.div
        className="flex justify-center -mt-7"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 rounded-full pl-4 py-0 text-xs"
        >
          <div>
            <div className="flex items-center gap-2">
              {isExpanded ? "Hide" : "Show All"} ({tasks.length})
              {isExpanded ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
            </div>
          </div>
        </Button>
      </motion.div>
      <AnimatePresence mode="wait">
        {isExpanded ? (
          // Expanded view: show all cards in a flex wrap layout
          <motion.div
            key="expanded"
            initial={{ opacity: 1, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1, ease: "easeInOut" }}
            className="flex flex-wrap gap-2"
          >
            {tasks.map((task: TaskModel, index: number) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 1, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.05,
                  ease: "easeOut",
                }}
              >
                <CompletedTaskCard task={task} />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          // Stacked view: show cards stacked on top of each other
          <motion.div
            key="stacked"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="relative"
            style={{ minHeight: `${120 + (tasks.length - 1) * 8}px` }}
          >
            {tasks.map((task: TaskModel, index: number) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1 - index * 0.02,
                }}
                transition={{
                  duration: 0.4,
                  delay: index * 0.1,
                  ease: "easeOut",
                }}
                className="absolute"
                style={{
                  top: `${index * 8}px`,
                  left: 0,
                  right: 0,
                  zIndex: tasks.length - index,
                  transformOrigin: "top center",
                }}
              >
                <CompletedTaskCard task={task} />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

type TimelineViewProps = {
  tasksByDate: Record<string, TaskModel[]>;
  searchTerm: string;
  selectedMonth: number | null;
  selectedYear: number | null;
};

export function TimelineView({
  tasksByDate,
  searchTerm,
  selectedMonth,
  selectedYear,
}: TimelineViewProps) {
  // Filter tasks based on search term, month, and year
  const filteredDates = Object.entries(tasksByDate)
    .map(([date, tasks]) => {
      // Filter tasks by search term
      const filteredTasks = tasks.filter((task) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          searchTerm === "" ||
          task.key.toLowerCase().includes(searchLower) ||
          task.title.toLowerCase().includes(searchLower) ||
          task.description.toLowerCase().includes(searchLower) ||
          task.assignees.some((assignee) => assignee.name.toLowerCase().includes(searchLower))
        );
      });

      // If we have selected month or year, filter dates
      const dateObj = new Date(date);
      const monthMatch = selectedMonth === null || dateObj.getMonth() === selectedMonth;
      const yearMatch = selectedYear === null || dateObj.getFullYear() === selectedYear;

      // Return the date and filtered tasks if they match all criteria
      return {
        date,
        tasks: filteredTasks,
        monthMatch,
        yearMatch,
      };
    })
    // Filter out any dates where there are no tasks or date doesn't match month/year criteria
    .filter(({ tasks, monthMatch, yearMatch }) => tasks.length > 0 && monthMatch && yearMatch)
    // Convert back to [date, tasks] format
    .map(({ date, tasks }) => [date, tasks] as [string, TaskModel[]]);

  // Sort dates chronologically
  const sortedDates = filteredDates.sort(
    ([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime()
  );

  return (
    <Timeline value={sortedDates.length} className="overflow-y-auto max-h-[calc(100vh-12rem)]">
      {sortedDates.length > 0 ? (
        sortedDates.map(([date, tasks], index) => (
          <TimelineItem key={date} step={index} className="max-w-full space-y-2">
            <TimelineHeader>
              <TimelineSeparator />
              <TimelineDate>{new Date(date).toDateString()}</TimelineDate>
              <TimelineIndicator />
            </TimelineHeader>

            <StackedTaskCards tasks={tasks} />
          </TimelineItem>
        ))
      ) : (
        <div className="flex items-center justify-center py-10 text-gray-500">
          No tasks match the current filters
        </div>
      )}
      <TimelineSeparator />
    </Timeline>
  );
}
