import { Skeleton } from "@/components/ui/skeleton";

export function CompletedTasksSkeleton() {
  return (
    <>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex flex-col">
          <Skeleton className="h-7 w-64 mb-1" />
          <Skeleton className="h-4 w-80" />
        </div>
        <div className="flex flex-col w-full gap-2 sm:flex-row sm:items-center sm:w-auto">
          {/* Search input skeleton */}
          <Skeleton className="h-9 w-full sm:w-64" />
          
          {/* Filters skeleton */}
          <div className="flex gap-2 w-full sm:w-auto">
            <Skeleton className="h-9 w-full sm:w-32" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
      </div>

      {/* Timeline Skeleton */}
      <div className="flex flex-col gap-10">
        {[1, 2, 3].map((day) => (
          <div key={day} className="flex flex-col gap-4">
            {/* Date marker with timeline styling */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                <div className="h-px bg-gray-200 w-4" />
                <Skeleton className="h-4 w-32" />
                <div className="h-px bg-gray-200 flex-1" />
                <Skeleton className="h-4 w-4 rounded-full" />
              </div>
            </div>
            
            {/* Stacked task cards skeleton */}
            <div className="pl-7">
              <div className="relative" style={{ minHeight: "152px" }}>
                {/* Simulate stacked cards */}
                {[0, 1, 2].map((index) => (
                  <div
                    key={index}
                    className="absolute border rounded-lg bg-background p-4 min-w-72 max-w-xs h-24"
                    style={{
                      top: `${index * 8}px`,
                      left: 0,
                      right: 0,
                      transform: `scale(${1 - index * 0.02})`,
                      zIndex: 3 - index,
                      transformOrigin: "top center",
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <Skeleton className="h-4 w-28" />
                      <div className="flex gap-1">
                        <Skeleton className="h-4 w-4 rounded-full" />
                        <Skeleton className="h-4 w-4 rounded-full" />
                      </div>
                    </div>
                    <Skeleton className="h-3 w-16" />
                  </div>
                ))}
              </div>
              
              {/* Show/Hide button skeleton */}
              <div className="flex justify-center mt-4">
                <Skeleton className="h-8 w-24 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
