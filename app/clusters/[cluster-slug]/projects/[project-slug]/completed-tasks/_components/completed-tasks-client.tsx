"use client";

import { TaskModel } from "@/db/schemas/tasks.schema";
import { cacheConfigs, commonQueryOptions, taskQueryKeys } from "@/lib/query-keys";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { TaskFilters } from "./completed-tasks-filters";
import { CompletedTasksSkeleton } from "./completed-tasks-skeleton";
import { TimelineView } from "./completed-tasks-timeline-view";

type CompletedTasksClientProps = {
  params: { "cluster-slug": string; "project-slug": string };
};

export function CompletedTasksClient({ params }: CompletedTasksClientProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const queryClient = useQueryClient();

  // Fetch completed tasks using optimized React Query with server-side filtering
  const {
    data: tasks,
    isLoading,
    error,
  } = useQuery({
    queryKey: taskQueryKeys.completed(params["project-slug"]),
    queryFn: async () => {
      // Use the existing working function and filter client-side for now
      const { getTasksByProjectFromDb } = await import("@/db/tasks.db");

      // Create props object that the function expects
      const props = {
        params: Promise.resolve({
          "project-slug": params["project-slug"],
          "cluster-slug": params["cluster-slug"],
        }),
      };

      const allTasks = await getTasksByProjectFromDb(props);

      // Filter for completed tasks
      const completedTasks = allTasks.filter(
        (task) => task.lifeCycleStatus === "dev done" || task.status === "Done"
      );

      return completedTasks;
    },
    ...cacheConfigs.completedTasks, // Use longer stale time for completed tasks
    ...commonQueryOptions,
    // Use cached data immediately if available
    placeholderData: (previousData) => {
      const cachedData = queryClient.getQueryData(taskQueryKeys.completed(params["project-slug"]));
      return (cachedData as TaskModel[]) || previousData;
    },
  });

  // Show skeleton while loading only if no cached data
  if (isLoading && !tasks) {
    return <CompletedTasksSkeleton />;
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <h2 className="text-lg font-semibold text-red-600 mb-2">Error loading completed tasks</h2>
        <p className="text-sm text-gray-500">
          {error instanceof Error ? error.message : "Unknown error occurred"}
        </p>
      </div>
    );
  }

  // Group tasks by done date (date only, not datetime)
  const tasksByDoneDate = (tasks || [])
    .filter((task: TaskModel) => task.doneDate) // Tasks are already filtered for completed status
    .reduce<Record<string, TaskModel[]>>((acc, task: TaskModel) => {
      if (!task.doneDate) return acc;

      // Extract just the date part (YYYY-MM-DD) to group tasks by day, not by exact timestamp
      const dateKey = new Date(task.doneDate).toISOString().split("T")[0];

      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }

      acc[dateKey].push(task);
      return acc;
    }, {});

  return (
    <>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <header className="flex flex-col">
          <h1 className="text-2xl font-semibold">Completed tasks</h1>
          <p className="text-sm text-gray-500">Here are the tasks that have been completed.</p>
        </header>
        <TaskFilters
          onSearch={setSearchTerm}
          onMonthChange={setSelectedMonth}
          onYearChange={setSelectedYear}
        />
      </div>
      <div className="mt-8">
        <TimelineView
          tasksByDate={tasksByDoneDate}
          searchTerm={searchTerm}
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
        />
      </div>
    </>
  );
}
