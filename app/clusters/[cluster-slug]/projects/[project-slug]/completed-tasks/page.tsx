import { PageParams } from "@/types/next.types";
import { Suspense } from "react";
import { CompletedTasksClient } from "./_components";
import { CompletedTasksSkeleton } from "./_components/completed-tasks-skeleton";

export default async function CompletedTasksPage(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
) {
  const params = await props.params;

  return (
    <div className="p-6 pt-2">
      <Suspense fallback={<CompletedTasksSkeleton />}>
        <CompletedTasksClient params={params} />
      </Suspense>
    </div>
  );
}
