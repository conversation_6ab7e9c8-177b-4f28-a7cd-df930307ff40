import { Skeleton } from "@/components/ui/skeleton";

export default function CompletedTasksLoading() {
  return (
    <div className="px-6 pt-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Skeleton className="h-7 w-64 mb-1" />
          <Skeleton className="h-4 w-80" />
        </div>
        <div className="flex items-center gap-3">
          <Skeleton className="h-9 w-48" />
          <Skeleton className="h-9 w-32" />
        </div>
      </div>

      {/* Timeline Skeleton */}
      <div className="flex flex-col">
        {[1, 2, 3].map((day) => (
          <div key={day} className="flex flex-col">
            {/* Date marker */}
            <div className="flex items-center gap-3">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
            {/* Tasks for the day */}
            <div className="flex flex-wrap gap-4 pl-7">
              {[1, 2, 3, 4].map((task) => (
                <div
                  key={task}
                  className="flex flex-col gap-2 border rounded-lg bg-background p-4 min-w-[260px] max-w-xs"
                >
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-28" />
                    <Skeleton className="h-4 w-8 rounded-full" />
                  </div>
                  <Skeleton className="h-3 w-40" />
                  <div className="flex gap-1">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
