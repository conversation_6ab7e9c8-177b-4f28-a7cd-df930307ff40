import { Skeleton } from "@/components/ui/skeleton";

export default function SettingsLoading() {
  return (
    <div className="flex flex-col gap-8 px-6 pt-4">
      {/* Tabs */}
      <div className="flex gap-2 mb-2">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-8 w-40" />
      </div>

      {/* Project Card */}
      <div className="border rounded-lg bg-background p-6 flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <Skeleton className="h-16 w-16 rounded-full" />
          <div className="flex flex-col gap-2 flex-1">
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-5 w-14 rounded" />
            </div>
            <div className="flex items-center gap-4">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <Skeleton className="h-9 w-16 ml-auto" />
        </div>
        <div className="flex justify-between mt-2">
          <div className="flex flex-col items-center gap-1">
            <Skeleton className="h-5 w-8" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex flex-col items-center gap-1">
            <Skeleton className="h-5 w-8" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex flex-col items-center gap-1">
            <Skeleton className="h-5 w-8" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </div>

      {/* Project Details */}
      <div className="border rounded-lg bg-background p-6 flex flex-col gap-6">
        <div className="flex items-center justify-between mb-2">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-14" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex flex-col gap-4">
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
          <div className="flex flex-col gap-4">
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-20 w-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Archive & Delete */}
      <div className="border rounded-lg bg-background p-6 flex flex-col gap-4">
        <Skeleton className="h-6 w-40 mb-2" />
        <div className="flex gap-4">
          <Skeleton className="h-9 w-40" />
          <Skeleton className="h-9 w-40" />
        </div>
      </div>
    </div>
  );
}
