export type Member = {
  id: string;
  name: string;
  email: string;
  role: "admin" | "member" | "guest";
  position: string;
  avatarUrl: string;
};

export type Project = {
  id: string;
  name: string;
  color: string;
};

export type Team = {
  id: string;
  name: string;
  members: Member[];
  projects: Project[];
};

export type OrganizationTeam = {
  id: string;
  name: string;
  memberCount: number;
  projects: Project[];
};
