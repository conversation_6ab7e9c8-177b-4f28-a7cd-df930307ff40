"use client";

import { <PERSON><PERSON>, Ava<PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Activity,
  ArchiveIcon,
  Blocks,
  Building2,
  CalendarClock,
  EyeIcon,
  Link2,
  Pen,
  PencilIcon,
  Save,
  Star,
  Text,
  Trash,
  UserRound,
  UsersIcon,
} from "lucide-react";
import { useState } from "react";

const projectInfo = {
  id: "prj_12345789",
  status: "Active",
  name: "Apollo",
  description:
    "Apollo is a project management tool that helps you manage your projects.",
  department: "Engineering",
  slug: "APO",
  projectLead: "Anthony ABRAMO",
  createdAt: "March 2024",
  stats: [
    {
      label: "Team•s Assigned",
      value: "2",
      icon: <UsersIcon className="w-4 h-4" />,
    },
    {
      label: "Guest•s",
      value: "1",
      icon: <EyeIcon className="w-4 h-4" />,
    },
    {
      label: "Admin•s",
      value: "2",
      icon: <Star className="w-4 h-4" />,
    },
  ],
};

export function ProjectInformation() {
  const [isEditing, setIsEditing] = useState(false);
  return (
    <div className="flex flex-col gap-4">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-6">
            <Avatar className="h-24 w-24">
              <AvatarImage
                src="https://placehold.co/100x100"
                alt="Project Avatar"
              />
              <AvatarFallback>PN</AvatarFallback>
            </Avatar>

            <div className="flex flex-col gap-1 w-full">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CardTitle>
                    <span>{projectInfo.name}</span>{" "}
                    <span className="text-xs text-muted-foreground">
                      Project
                    </span>
                  </CardTitle>
                  <Badge
                    variant="outline"
                    className={`px-2 py-1 ${
                      projectInfo.status === "Active"
                        ? "bg-green-50 text-green-700 border-green-200"
                        : "bg-amber-50 text-amber-700 border-amber-200"
                    }`}
                  >
                    {projectInfo.status || "Active"}
                  </Badge>{" "}
                </div>
                <Button variant="outline" size="sm">
                  <PencilIcon className="w-3 h-3" />
                  Edit
                </Button>
              </div>
              <div className="flex items-center gap-10">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <Link2 className="w-4 h-4" />
                    <span className="text-sm text-muted-foreground">
                      Slug: <b>{projectInfo.slug}</b>
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CalendarClock className="w-4 h-4" />
                    <span className="text-sm text-muted-foreground">
                      Created: <b>{projectInfo.createdAt}</b>
                    </span>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <Blocks className="w-4 h-4" />
                    <span className="text-sm text-muted-foreground">
                      Department: <b>{projectInfo.department}</b>
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <UserRound className="w-4 h-4" />
                    <span className="text-sm text-muted-foreground">
                      Leader: <b>{projectInfo.projectLead}</b>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-8 border rounded-lg p-4">
            {projectInfo.stats.map((stat, index) => (
              <div
                key={stat.label}
                className={`flex flex-col items-center justify-center ${
                  index !== projectInfo.stats.length - 1 ? "border-r" : ""
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {stat.icon}
                  <span className="text-xl font-semibold">{stat.value}</span>
                </div>
                <span className="text-xs text-muted-foreground">
                  {stat.label}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm hover:shadow-md transition-shadow">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-xl">Project Details</CardTitle>
              <CardDescription className="text-muted-foreground">
                Update your project information and settings
              </CardDescription>
            </div>
            <div className="flex justify-end gap-3 pt-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    type="button"
                    className="px-4"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" className="px-4 gap-2">
                    <Save className="h-4 w-4" />
                    Save Changes
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  type="button"
                  className="px-4"
                  onClick={() => setIsEditing(true)}
                >
                  <Pen className="h-4 w-4" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Project Name
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    className="w-full"
                    defaultValue={projectInfo.name}
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="department"
                    className="text-sm font-medium flex items-center gap-2"
                  >
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    Department
                  </Label>
                  <Select defaultValue={projectInfo.department}>
                    <SelectTrigger id="department" className="w-full">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Engineering">Engineering</SelectItem>
                      <SelectItem value="Design">Design</SelectItem>
                      <SelectItem value="Marketing">Marketing</SelectItem>
                      <SelectItem value="Product">Product</SelectItem>
                      <SelectItem value="Sales">Sales</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="status"
                    className="text-sm font-medium flex items-center gap-2"
                  >
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    Status
                  </Label>
                  <Select defaultValue={projectInfo.status || "Active"}>
                    <SelectTrigger id="status" className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="On Hold">On Hold</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="projectLead"
                    className="text-sm font-medium flex items-center gap-2"
                  >
                    <UserRound className="h-4 w-4 text-muted-foreground" />
                    Project Leader
                  </Label>
                  <Select defaultValue={projectInfo.projectLead}>
                    <SelectTrigger id="projectLead" className="w-full">
                      <SelectValue placeholder="Select project leader" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Anthony ABRAMO">
                        Anthony ABRAMO
                      </SelectItem>
                      <SelectItem value="John Doe">John Doe</SelectItem>
                      <SelectItem value="Jane Smith">Jane Smith</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="slug"
                    className="text-sm font-medium flex items-center gap-2"
                  >
                    <Link2 className="h-4 w-4 text-muted-foreground" />
                    Slug
                  </Label>
                  <Input
                    id="slug"
                    type="text"
                    className="w-full"
                    defaultValue={projectInfo.slug}
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="description"
                    className="text-sm font-medium flex items-center gap-2"
                  >
                    <Text className="h-4 w-4 text-muted-foreground" />
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    rows={2}
                    className="w-full"
                    defaultValue={projectInfo.description}
                  />
                </div>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Archive & Delete</CardTitle>
          <CardDescription>Archive or dele te your project</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button
              variant="outline"
              className="w-1/2 text-amber-500 hover:bg-amber-50 hover:text-amber-500 hover:border-amber-300"
            >
              <ArchiveIcon className="h-4 w-4" /> Archive Project
            </Button>
            <Button
              variant="outline"
              className="w-1/2 text-red-500 hover:bg-red-50 hover:text-red-500 hover:border-red-300"
            >
              <Trash className="h-4 w-4" /> Delete Project
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
