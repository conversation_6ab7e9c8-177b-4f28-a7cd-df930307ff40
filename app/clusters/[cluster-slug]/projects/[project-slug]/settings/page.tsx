import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { ProjectInformation } from "./_components/project-settings-information";
import { ProjectSettingsKanban } from "./_components/project-settings-kanban";
import { ProjectSettingsTeams } from "./_components/project-settings-teams";

export default function ProjectSettingsPage() {
  return (
    <div className="h-[calc(100vh-4rem)]">
      <Tabs defaultValue="general" className="flex flex-col h-full">
        <div className="sticky top-0 z-10 px-6 py-4 bg-background">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="team">
                Teams <span className="text-xs">&nbsp;&&nbsp;</span> Members
              </TabsTrigger>
              <TabsTrigger value="kanban">
                Kanban <span className="text-xs">&nbsp;&&nbsp;</span> Tasks
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <div className="flex-1 px-6 overflow-y-auto">
          <TabsContent value="general" className="h-full mt-0">
            <ProjectInformation />
          </TabsContent>

          <TabsContent value="team" className="h-full mt-0">
            <ProjectSettingsTeams />
          </TabsContent>

          <TabsContent value="kanban" className="h-full mt-0">
            <ProjectSettingsKanban />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
