"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { CheckCircle2, Clock, LoaderCircle, XCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

type Deployment = {
  uid: string;
  name: string;
  url: string;
  state: string;
  createdAt: number;
  meta: { githubCommitMessage?: string };
  creator: { username?: string };
};

// Pipeline stages for each deployment
type PipelineStage = {
  name: string;
  status: "success" | "error" | "in-progress" | "pending" | "canceled";
  info?: string;
};

export function VercelDeployments() {
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDeployments() {
      try {
        const res = await fetch("/api/vercel-deployments");

        if (!res.ok) {
          throw new Error(`Failed to fetch deployments: ${res.status}`);
        }

        const data = await res.json();
        setDeployments(data);
        setError(null);
      } catch (e) {
        console.info("Failed to load deployments", e);
        setError("Failed to load deployments. Please try again later.");
      }
    }

    fetchDeployments();
  }, []);

  // Function to determine pipeline stages based on deployment state
  const getPipelineStages = (deployment: Deployment): PipelineStage[] => {
    // Default pipeline stages
    const stages: PipelineStage[] = [
      { name: "Build on Env Test", status: "pending", info: "Github Actions" },
      { name: "Unit Tests", status: "pending", info: "Github Actions" },
      { name: "Integration Tests", status: "pending", info: "Github Actions" },
      { name: "Build Production - Vercel", status: "pending", info: "Vercel" },
      { name: "Deployment", status: "pending", info: "Vercel" },
    ];

    // Based on the deployment state, update the pipeline stages
    switch (deployment.state) {
      case "BUILDING":
        stages[0].status = "in-progress";
        break;
      case "ERROR":
        // Find the last in-progress stage and mark it as error
        const lastInProgressIndex = stages.findIndex((stage) => stage.status === "in-progress");
        if (lastInProgressIndex >= 0) {
          stages[lastInProgressIndex].status = "error";
        } else {
          stages[0].status = "error";
        }
        break;
      case "READY":
        // All stages complete successfully
        stages.forEach((_stage, index) => {
          stages[index].status = "success";
        });
        break;
      case "CANCELED":
        // Mark all stages as canceled or mark the first few as completed and the rest as canceled
        // Here we'll assume the cancellation happened in the middle of the process
        const completedSteps = 3; // Simulate that 0-2 steps completed before cancellation

        stages.forEach((_stage, index) => {
          if (index < completedSteps) {
            stages[index].status = "success";
          } else if (index === completedSteps) {
            stages[index].status = "canceled";
          } else {
            stages[index].status = "pending";
          }
        });
        break;
      default:
        // For other states, assume at least build is running
        stages[0].status = "in-progress";
    }

    return stages;
  };

  // Function to calculate the progress percentage
  const calculateProgress = (stages: PipelineStage[]): number => {
    const totalStages = stages.length;
    const completedStages = stages.filter((stage) => stage.status === "success").length;
    return Math.round((completedStages / totalStages) * 100);
  };

  // Status indicator component
  const StatusIndicator = ({ status }: { status: PipelineStage["status"] }) => {
    switch (status) {
      case "success":
        return <CheckCircle2 className="text-green-500 h-4 w-4" />;
      case "error":
        return <XCircle className="text-red-500 h-4 w-4" />;
      case "in-progress":
        return <LoaderCircle className="text-blue-500 h-4 w-4 animate-spin" />;
      case "canceled":
        return <XCircle className="text-orange-500 h-4 w-4" />;
      default:
        return <Clock className="text-gray-300 h-4 w-4" />;
    }
  };

  const formattedDate = (date: number) => {
    return new Date(date).toLocaleDateString("en-US", {
      weekday: "short",
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  // Function to get deployment badge variant
  const getDeploymentStateVariant = (state: string) => {
    switch (state) {
      case "READY":
        return "default";
      case "ERROR":
        return "destructive";
      case "CANCELED":
        return "outline";
      default:
        return "secondary";
    }
  };

  if (error) {
    return toast.error("Failed to load deployments. Please try again later.", {
      description: error,
    });
  }

  if (deployments.length === 0) {
    return (
      <div className="w-full mx-auto">
        <div>
          <h1 className="text-xl font-semibold">Deployment Pipelines</h1>
          <p className="text-sm text-muted-foreground">
            These are the deployment pipelines for the project.
          </p>
        </div>
        <Card className="mt-4 flex items-center justify-center p-8 border-dashed">
          <CardContent className="text-center text-muted-foreground">
            No deployments found. When you deploy your project, the pipeline information will appear
            here.
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      <div>
        <h1 className="text-2xl font-semibold">Deployment Pipelines</h1>
        <p className="text-sm text-muted-foreground">
          These are the deployment pipelines for the project.
        </p>
      </div>
      <div className="flex flex-col gap-4 w-full mx-auto">
        {deployments.map((dep, index) => {
          const pipelineStages = getPipelineStages(dep);
          const progress = calculateProgress(pipelineStages);

          return (
            <Card key={dep.uid} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={getDeploymentStateVariant(dep.state)}
                      className={cn(
                        "text-xs",
                        dep.state === "CANCELED" &&
                          "border-orange-200 text-orange-700 dark:text-orange-400 dark:border-orange-400"
                      )}
                    >
                      {dep.state}
                    </Badge>
                    <a
                      href={`https://${dep.url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                    >
                      {dep.name}.vercel.app
                    </a>
                  </div>
                  <Badge variant="outline" className={`${index === 0 ? "block" : "hidden"}`}>
                    latest
                  </Badge>
                </div>
                <span className="text-xs font-mono tracking-tighter text-gray-500 dark:text-gray-400">
                  {formattedDate(dep.createdAt)}
                </span>
              </CardHeader>

              <div className="px-8">
                {pipelineStages.map((stage, index) => (
                  <div key={index} className="flex items-center justify-between py-1">
                    <div className="flex items-center justify-between gap-2 w-full">
                      {/* Stage name and status */}
                      <div className="flex items-center gap-2">
                        <StatusIndicator status={stage.status} />
                        <span className="font-medium text-xs md:text-sm">{stage.name}</span>
                      </div>
                      {/* Info badge */}
                      <Badge variant="outline" className="text-xs">
                        {stage.info}
                      </Badge>
                    </div>
                    {/* Status badge */}
                    <span
                      className={`text-xs px-2 py-0.5 rounded-full min-w-fit ml-1 ${
                        stage.status === "success"
                          ? "text-green-700 bg-green-50 dark:bg-green-950 dark:text-green-400 border border-green-200 dark:border-green-800"
                          : stage.status === "error"
                          ? "text-red-700 bg-red-50 dark:bg-red-950 dark:text-red-400 border border-red-200 dark:border-red-800 "
                          : stage.status === "in-progress"
                          ? "text-blue-700 bg-blue-50 dark:bg-blue-950 dark:text-blue-400 border border-blue-200 dark:border-blue-800"
                          : stage.status === "canceled"
                          ? "text-orange-700 bg-orange-50 dark:bg-orange-950 dark:text-orange-400 border border-orange-200 dark:border-orange-400"
                          : "text-gray-700 bg-gray-50 dark:bg-gray-800 dark:text-gray-400 border border-gray-200 dark:border-gray-800"
                      }`}
                    >
                      {stage.status === "success" && "Success"}
                      {stage.status === "error" && "Failed"}
                      {stage.status === "in-progress" && "In Progress"}
                      {stage.status === "canceled" && "Canceled"}
                      {stage.status === "pending" && "Pending"}
                    </span>
                  </div>
                ))}
              </div>

              <CardFooter>
                <div className="w-full">
                  <Progress value={progress} className="h-1" />
                  <div className="flex justify-between mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                      {progress}% Complete
                    </span>
                  </div>
                </div>
              </CardFooter>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
