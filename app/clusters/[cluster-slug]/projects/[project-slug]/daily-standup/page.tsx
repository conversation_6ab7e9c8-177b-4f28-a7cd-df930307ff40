"use client";

import { useSidebar } from "@/components/ui/sidebar";
import { useState } from "react";
import { PageHeader } from "./_components/page-header";
import { RightSidebar } from "./_components/right-sidebar";
import { TaskView } from "./_components/task-view";
import { teamMembers } from "./_data/mock-data";

export default function DailyStandupPage() {
  const [isRightSidebarVisible, setIsRightSidebarVisible] = useState(true);

  const { toggleSidebar } = useSidebar();
  const handleRightSidebarVisibility = () => {
    setIsRightSidebarVisible(!isRightSidebarVisible);
    toggleSidebar();
  };

  return (
    <div className="w-full pl-6 border-t h-[calc(100vh-5rem)] overflow-y-auto">
      <div className="flex flex-col lg:flex-row">
        {/* Main content area */}
        <div className="flex-1 w-full pr-6">
          <PageHeader isRightSidebarVisible={handleRightSidebarVisibility} />
          <TaskView />
        </div>

        {/* Right sidebar */}
        {isRightSidebarVisible && <RightSidebar teamMembers={teamMembers} />}
      </div>
    </div>
  );
}
