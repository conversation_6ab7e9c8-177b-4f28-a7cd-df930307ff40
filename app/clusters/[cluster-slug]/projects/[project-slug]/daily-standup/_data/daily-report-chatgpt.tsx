import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  CartesianGrid,
  Line,
  LineChart,
  ReferenceLine,
  Responsive<PERSON>ontainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

export default function DailyStandupReport() {
  return (
    <div className="p-4 space-y-6 text-sm text-gray-800">
      <Card>
        {/* <CardHeader className="w-1/2"></CardHeader> */}
        <CardContent className="space-y-2">
          <div className="flex items-start justify-between">
            <div className="flex flex-col justify-start gap-8">
              <div>
                <CardTitle>📝 Renwu Daily Standup Report</CardTitle>
                <CardDescription>Example with ChatGPT: input members view</CardDescription>
              </div>
              <div>
                <p>
                  <strong>Date:</strong> Wednesday, May 14, 2025
                </p>
                <p>
                  <strong>Time:</strong> 17:24
                </p>
                <p>
                  <strong>Project:</strong> AI-app-v2 Website Redesign
                </p>
              </div>
            </div>
            <div className="w-64 h-32 pt-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={[
                    { day: "1 May", time: 12 },
                    { day: "2 May", time: 15 },
                    { day: "3 May", time: 14 },
                    { day: "6 May", time: 18 },
                    { day: "7 May", time: 16 },
                    { day: "8 May", time: 15 },
                    { day: "9 May", time: 13 },
                    { day: "10 May", time: 12 },
                    { day: "13 May", time: 10 },
                    { day: "14 May", time: 11 },
                  ]}
                  margin={{ top: 5, right: 5, left: 0, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                  <XAxis dataKey="day" tick={{ fontSize: 10 }} />
                  <YAxis tick={{ fontSize: 10 }} unit="min" />
                  <Tooltip
                    contentStyle={{ fontSize: 12 }}
                    formatter={(value) => [`${value} min`, "Duration"]}
                  />
                  <ReferenceLine
                    y={14}
                    stroke="#888"
                    strokeDasharray="3 3"
                    label={{ value: "Avg: 14m", position: "insideBottomRight", fontSize: 10 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="time"
                    stroke="#8884d8"
                    dot={{ r: 2 }}
                    activeDot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Daniel Parker</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Task</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>Optimize DB queries (#-001)</TableCell>
                <TableCell>3d</TableCell>
                <TableCell>In Progress</TableCell>
                <TableCell>Waiting for access to production DB metrics</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Implement caching strategy (#-007)</TableCell>
                <TableCell>2d</TableCell>
                <TableCell>In Progress</TableCell>
                <TableCell>No impediments</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Database indexing plan (#-008)</TableCell>
                <TableCell>1d</TableCell>
                <TableCell>Done</TableCell>
                <TableCell>Completed on time</TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <p className="mt-2">
            ✅ <strong>Status:</strong> Healthy
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>✅ Completion Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Task ID</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>#-008</TableCell>
                <TableCell>Create database indexing plan</TableCell>
                <TableCell>✅ Done</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>#-010</TableCell>
                <TableCell>Optimize frontend bundle size</TableCell>
                <TableCell>✅ Done</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>⚠️ Issues & Risks</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-5 space-y-1">
            <li>
              <strong>RB:</strong> Needs code review support
            </li>
            <li>
              <strong>SB:</strong> Blocked by dependencies and approval delays
            </li>
            <li>
              <strong>OW:</strong> Coordination delays with infra team
            </li>
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>🔗 Links</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-5 space-y-1 text-blue-600 underline">
            <li>
              <a href="/clusters/ai-app-v2/projects/website-redesign/dashboard">Dashboard</a>
            </li>
            <li>
              <a href="/clusters/ai-app-v2/projects/website-redesign/daily-standup">
                Daily Standup
              </a>
            </li>
            <li>
              <a href="/clusters/ai-app-v2/projects/website-redesign/tasks">Tasks</a>
            </li>
            <li>
              <a href="/clusters/ai-app-v2/projects/website-redesign/completed-tasks">
                Completed Tasks
              </a>
            </li>
            <li>
              <a href="/clusters/ai-app-v2/projects/website-redesign/sprint-review">
                Sprint Review
              </a>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
