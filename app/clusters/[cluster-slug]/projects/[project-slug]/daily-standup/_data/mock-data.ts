export type TeamMember = {
  id: string;
  name: string;
  initials: string;
  responded: boolean;
  responseStatus?: "accepted" | "declined" | "pending";
};

export type FeatureTask = {
  id: string;
  title: string;
  yesterday: string;
  today: string;
  impediments?: string;
  status: "healthy" | "at-risk" | "late";
};

export type MemberTask = {
  memberId: string;
  yesterday: string;
  today: string;
  impediments?: string;
  status: "healthy" | "at-risk" | "late";
  dueDate: string;
};

export const teamMembers: TeamMember[] = [
  { id: "1", name: "<PERSON>", initials: "D<PERSON>", responded: true, responseStatus: "accepted" },
  { id: "2", name: "<PERSON>", initials: "MR", responded: true, responseStatus: "accepted" },
  { id: "3", name: "<PERSON>", initials: "RB", responded: true, responseStatus: "declined" },
  { id: "4", name: "<PERSON>", initials: "J<PERSON>", responded: true, responseStatus: "accepted" },
  { id: "5", name: "<PERSON>", initials: "<PERSON><PERSON>", responded: true, responseStatus: "declined" },
  { id: "6", name: "<PERSON>", initials: "OW", responded: false, responseStatus: "pending" },
];

export const featureTasks: FeatureTask[] = [
  {
    id: "f1",
    title: "User Authentication",
    yesterday: "Implemented login form validation and error handling",
    today: "Working on password reset functionality and email notifications",
    impediments: "Waiting for API access from the security team",
    status: "at-risk",
  },
  {
    id: "f2",
    title: "Dashboard Analytics",
    yesterday: "Created chart components and integrated with data sources",
    today: "Implementing filter options and date range selectors",
    status: "healthy",
  },
  {
    id: "f3",
    title: "Payment Processing",
    yesterday: "Fixed bug with transaction history not showing recent payments",
    today: "Adding support for new payment provider and testing integration",
    impediments: "API documentation is outdated, need clarification from vendor",
    status: "late",
  },
];

export const memberTasks: MemberTask[] = [
  {
    memberId: "1",
    yesterday: "Completed user profile page design and implemented responsive layout",
    today: "Starting work on settings page and notification preferences",
    status: "healthy",
    dueDate: "2025-05-15",
  },
  {
    memberId: "2",
    yesterday: "Fixed bugs in the search functionality and improved query performance",
    today: "Implementing advanced filters and sorting options",
    status: "healthy",
    dueDate: "2025-05-15",
  },
  {
    memberId: "3",
    yesterday: "Worked on API integration for the analytics dashboard",
    today: "Continuing with data visualization components and real-time updates",
    impediments: "Need access to production metrics API",
    status: "at-risk",
    dueDate: "2025-05-15",
  },
  {
    memberId: "4",
    yesterday: "Implemented email notification system and templates",
    today: "Working on in-app notification center and user preferences",
    status: "healthy",
    dueDate: "2025-05-15",
  },
  {
    memberId: "5",
    yesterday: "Designed new onboarding flow and created prototype",
    today: "Implementing first-time user experience improvements",
    impediments: "Waiting for final approval on designs from product team",
    status: "late",
    dueDate: "2025-05-15",
  },
  {
    memberId: "6",
    yesterday: "Set up CI/CD pipeline for automated testing",
    today: "Configuring deployment workflows and environment variables",
    status: "healthy",
    dueDate: "2025-05-15",
  },
];
