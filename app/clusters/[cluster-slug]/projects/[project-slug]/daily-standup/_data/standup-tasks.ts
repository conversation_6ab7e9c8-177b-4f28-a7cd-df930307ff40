export type StandupTask = {
  id: string;
  key: string;
  title: string;
  tags: string[];
  status: "todo" | "in-progress" | "in-review" | "done";
  duration: string;
  assignee: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  impediments?: string;
  deadlineStatus: "healthy" | "at-risk" | "late";
  dueDate?: string;
};

// Tasks for yesterday (previous state)
export const yesterdayTasks: StandupTask[] = [
  // <PERSON> (DP) - 3 tasks
  {
    id: "001",
    key: "#-001",
    title: "Optimize database queries for dashboard",
    tags: ["performance"],
    status: "todo",
    duration: "3d",
    assignee: {
      id: "1",
      name: "<PERSON>",
      initials: "DP",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "007",
    key: "#-007",
    title: "Implement dashboard caching strategy",
    tags: ["performance", "architecture"],
    status: "todo",
    duration: "2d",
    assignee: {
      id: "1",
      name: "<PERSON>",
      initials: "D<PERSON>",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-17",
  },
  {
    id: "008",
    key: "#-008",
    title: "Create database indexing plan",
    tags: ["performance", "architecture"],
    status: "in-progress",
    duration: "1d",
    assignee: {
      id: "1",
      name: "Daniel Parker",
      initials: "DP",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-14",
  },

  // <PERSON> (MR) - 2 tasks
  {
    id: "002",
    key: "#-002",
    title: "Fix user authentication edge cases",
    tags: ["bug", "testing"],
    status: "todo",
    duration: "2d",
    assignee: {
      id: "2",
      name: "Matthew Reed",
      initials: "MR",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "009",
    key: "#-009",
    title: "Update security protocols for API endpoints",
    tags: ["security"],
    status: "todo",
    duration: "3d",
    assignee: {
      id: "2",
      name: "Matthew Reed",
      initials: "MR",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-16",
  },

  // Ryan Brooks (RB) - 2 tasks
  {
    id: "003",
    key: "#-003",
    title: "Resolve infinite scroll memory leak",
    tags: ["bug"],
    status: "todo",
    duration: "4d",
    assignee: {
      id: "3",
      name: "Ryan Brooks",
      initials: "RB",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "010",
    key: "#-010",
    title: "Optimize frontend bundle size",
    tags: ["performance"],
    status: "in-progress",
    duration: "2d",
    assignee: {
      id: "3",
      name: "Ryan Brooks",
      initials: "RB",
    },
    deadlineStatus: "at-risk",
    dueDate: "2025-05-14",
  },

  // Jacob Dawson (JD) - 1 task
  {
    id: "004",
    key: "#-004",
    title: "Implement Redis caching layer",
    tags: ["scalability"],
    status: "todo",
    duration: "5d",
    assignee: {
      id: "4",
      name: "Jacob Dawson",
      initials: "JD",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },

  // Sophie Bennett (SB) - 3 tasks
  {
    id: "005",
    key: "#-005",
    title: "Restructure API response handling",
    tags: ["refactor"],
    status: "todo",
    duration: "3d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "011",
    key: "#-011",
    title: "Create API documentation generator",
    tags: ["feature"],
    status: "todo",
    duration: "2d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-18",
  },
  {
    id: "012",
    key: "#-012",
    title: "Implement API rate limiting",
    tags: ["security", "performance"],
    status: "todo",
    duration: "1d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    deadlineStatus: "at-risk",
    dueDate: "2025-05-13",
  },

  // Olivia Wilson (OW) - 2 tasks
  {
    id: "006",
    key: "#-006",
    title: "Add real-time notifications system",
    tags: ["feature"],
    status: "todo",
    duration: "6d",
    assignee: {
      id: "6",
      name: "Olivia Wilson",
      initials: "OW",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "013",
    key: "#-013",
    title: "Create WebSocket connection manager",
    tags: ["feature", "architecture"],
    status: "todo",
    duration: "3d",
    assignee: {
      id: "6",
      name: "Olivia Wilson",
      initials: "OW",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-16",
  },
];

// Tasks for today (current state)
export const todayTasks: StandupTask[] = [
  // Daniel Parker (DP) - 3 tasks
  {
    id: "001",
    key: "#-001",
    title: "Optimize database queries for dashboard",
    tags: ["performance"],
    status: "in-progress",
    duration: "3d",
    assignee: {
      id: "1",
      name: "Daniel Parker",
      initials: "DP",
    },
    impediments: "Waiting for access to production database metrics",
    deadlineStatus: "at-risk",
    dueDate: "2025-05-15",
  },
  {
    id: "007",
    key: "#-007",
    title: "Implement dashboard caching strategy",
    tags: ["performance", "architecture"],
    status: "in-progress",
    duration: "2d",
    assignee: {
      id: "1",
      name: "Daniel Parker",
      initials: "DP",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-17",
  },
  {
    id: "008",
    key: "#-008",
    title: "Create database indexing plan",
    tags: ["performance", "architecture"],
    status: "done",
    duration: "1d",
    assignee: {
      id: "1",
      name: "Daniel Parker",
      initials: "DP",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-14",
  },

  // Matthew Reed (MR) - 2 tasks
  {
    id: "002",
    key: "#-002",
    title: "Fix user authentication edge cases",
    tags: ["bug", "testing"],
    status: "in-progress",
    duration: "2d",
    assignee: {
      id: "2",
      name: "Matthew Reed",
      initials: "MR",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },
  {
    id: "009",
    key: "#-009",
    title: "Update security protocols for API endpoints",
    tags: ["security"],
    status: "in-progress",
    duration: "3d",
    assignee: {
      id: "2",
      name: "Matthew Reed",
      initials: "MR",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-16",
  },

  // Ryan Brooks (RB) - 2 tasks
  {
    id: "003",
    key: "#-003",
    title: "Resolve infinite scroll memory leak",
    tags: ["bug"],
    status: "in-review",
    duration: "4d",
    assignee: {
      id: "3",
      name: "Ryan Brooks",
      initials: "RB",
    },
    impediments: "Need help from senior developer to review approach",
    deadlineStatus: "at-risk",
    dueDate: "2025-05-15",
  },
  {
    id: "010",
    key: "#-010",
    title: "Optimize frontend bundle size",
    tags: ["performance"],
    status: "done",
    duration: "2d",
    assignee: {
      id: "3",
      name: "Ryan Brooks",
      initials: "RB",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-14",
  },

  // Jacob Dawson (JD) - 1 task
  {
    id: "004",
    key: "#-004",
    title: "Fix existing Redis caching layer",
    tags: ["bug"],
    status: "in-progress",
    duration: "5d",
    assignee: {
      id: "4",
      name: "Jacob Dawson",
      initials: "JD",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-15",
  },

  // Sophie Bennett (SB) - 3 tasks
  {
    id: "005",
    key: "#-005",
    title: "Restructure API response handling",
    tags: ["refactor"],
    status: "in-review",
    duration: "3d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    impediments: "Blocked by dependency on auth service update",
    deadlineStatus: "late",
    dueDate: "2025-05-15",
  },
  {
    id: "011",
    key: "#-011",
    title: "Create API documentation generator",
    tags: ["feature"],
    status: "in-progress",
    duration: "2d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    deadlineStatus: "healthy",
    dueDate: "2025-05-18",
  },
  {
    id: "012",
    key: "#-012",
    title: "Implement API rate limiting",
    tags: ["security", "performance"],
    status: "in-review",
    duration: "1d",
    assignee: {
      id: "5",
      name: "Sophie Bennett",
      initials: "SB",
    },
    impediments: "Waiting for security team approval on approach",
    deadlineStatus: "late",
    dueDate: "2025-05-13",
  },

  // Olivia Wilson (OW) - 2 tasks
  {
    id: "006",
    key: "#-006",
    title: "Add real-time notifications system",
    tags: ["feature"],
    status: "in-progress",
    duration: "6d",
    assignee: {
      id: "6",
      name: "Olivia Wilson",
      initials: "OW",
    },
    deadlineStatus: "at-risk",
    dueDate: "2025-05-15",
  },
  {
    id: "013",
    key: "#-013",
    title: "Create WebSocket connection manager",
    tags: ["feature", "architecture"],
    status: "in-progress",
    duration: "3d",
    assignee: {
      id: "6",
      name: "Olivia Wilson",
      initials: "OW",
    },
    impediments: "Need to coordinate with infrastructure team on WebSocket support",
    deadlineStatus: "at-risk",
    dueDate: "2025-05-16",
  },
];

// Map of member IDs to their tasks
export const memberTasksMap = {
  "1": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "1"),
    today: todayTasks.filter((task) => task.assignee.id === "1"),
  },
  "2": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "2"),
    today: todayTasks.filter((task) => task.assignee.id === "2"),
  },
  "3": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "3"),
    today: todayTasks.filter((task) => task.assignee.id === "3"),
  },
  "4": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "4"),
    today: todayTasks.filter((task) => task.assignee.id === "4"),
  },
  "5": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "5"),
    today: todayTasks.filter((task) => task.assignee.id === "5"),
  },
  "6": {
    yesterday: yesterdayTasks.filter((task) => task.assignee.id === "6"),
    today: todayTasks.filter((task) => task.assignee.id === "6"),
  },
};
