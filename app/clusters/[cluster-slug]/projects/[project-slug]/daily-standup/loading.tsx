import { Skeleton } from "@/components/ui/skeleton";

export default function DailyStandupLoading() {
  return (
    <div className="w-full pl-6 border-t h-[calc(100vh-5rem)] overflow-y-auto">
      <div className="flex flex-col lg:flex-row">
        {/* Main content area */}
        <div className="flex-1 w-full pr-6">
          {/* Header skeleton */}
          <div className="flex items-center justify-between py-4">
            <div className="flex flex-col gap-2">
              <Skeleton className="h-8 w-48" />
            </div>
            <Skeleton className="h-4 w-36" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>

          {/* Participants status skeleton */}
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-4">
              <div className="flex -space-x-1">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="relative">
                    <Skeleton className="h-8 w-8 rounded-full border-2 border-background" />
                    <Skeleton className="absolute -bottom-0.5 -right-0.5 h-4 w-4 rounded-full border-2 border-background" />
                  </div>
                ))}
              </div>
              <Skeleton className="h-6 w-12" />
            </div>
            <Skeleton className="h-8 w-32" />
          </div>

          {/* Task cards skeleton */}
          <div className="space-y-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="rounded-lg p-4 border-b pb-10 dark:border-gray-800">
                {/* Card header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-6 w-32" />
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>

                {/* Card content */}
                <div className="flex flex-col gap-4 mt-4">
                  {/* Task rows */}
                  <div className="grid grid-cols-12 gap-4">
                    {/* Progress Column */}
                    <div className="col-span-12 flex items-center gap-4">
                      <div className="flex-1">
                        <Skeleton className="h-4 w-16 mb-2" />
                        <Skeleton className="h-24 w-full rounded-lg" />
                      </div>
                      <Skeleton className="h-6 w-6 flex-shrink-0" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-16 mb-2" />
                        <Skeleton className="h-24 w-full rounded-lg" />
                      </div>
                      <div className="flex-1">
                        <Skeleton className="h-24 w-full rounded-lg" />
                      </div>
                      <div className="flex-1">
                        <Skeleton className="h-24 w-full rounded-lg" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right sidebar skeleton */}
        <div className="flex flex-col items-center w-72 border-l pt-6">
          {/* Calendar skeleton */}
          <div className="w-full px-4">
            <div className="grid grid-cols-7 gap-1">
              {[...Array(42)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-full rounded-sm" />
              ))}
            </div>
          </div>

          <div className="h-[1px] border-t w-full my-6" />

          {/* Members list skeleton */}
          <div className="w-full px-4">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-6" />
            </div>
            <Skeleton className="h-10 w-full mb-4" />
            <div className="space-y-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
