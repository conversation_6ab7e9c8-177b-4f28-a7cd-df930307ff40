"use client";

import { AlertCircle } from "lucide-react";

type FeatureCardProps = {
  title: string;
  yesterday: string;
  today: string;
  impediments?: string;
  status: "healthy" | "at-risk" | "late";
};

export function FeatureCard({ title, yesterday, today, impediments, status }: FeatureCardProps) {
  const statusColors = {
    healthy: "bg-green-500",
    "at-risk": "bg-yellow-500",
    late: "bg-red-500",
  };

  const statusLabels = {
    healthy: "On Track",
    "at-risk": "At Risk",
    late: "Late",
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-xs border">
      <h3 className="text-center text-lg font-medium mb-4">{title}</h3>

      {/* Yesterday → Today Progress */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-500 mb-2">Progress</h4>
        <div className="flex gap-4">
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Yesterday</p>
            <p className="p-3 bg-gray-50 rounded-md text-sm min-h-16">{yesterday}</p>
          </div>
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              />
            </svg>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Today</p>
            <p className="p-3 bg-gray-50 rounded-md text-sm min-h-16">{today}</p>
          </div>
        </div>
      </div>

      {/* Impediments */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <h4 className="text-sm font-medium text-gray-500">Impediments</h4>
          {impediments && <AlertCircle className="h-5 w-5 text-red-500" />}
        </div>
        <div className="p-3 bg-gray-50 rounded-md text-sm min-h-12">
          {impediments || "No impediments reported"}
        </div>
      </div>

      {/* Deadline Status */}
      <div>
        <h4 className="text-sm font-medium text-gray-500 mb-2">Status</h4>
        <div className="flex items-center gap-2">
          <div className={`h-6 w-6 rounded-full ${statusColors[status]}`}></div>
          <span className="text-sm">{statusLabels[status]}</span>
        </div>
      </div>
    </div>
  );
}
