"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ExpandIcon, Play, Square } from "lucide-react";
import { useEffect, useState } from "react";

type PageHeaderProps = {
  isRightSidebarVisible: () => void;
};

export function PageHeader({ isRightSidebarVisible }: PageHeaderProps) {
  const [seconds, setSeconds] = useState(0);
  const [isActive, setIsActive] = useState(false);

  // Format time as MM:SS
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Stopwatch logic
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds + 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive]);

  // Start/stop timer
  const toggleTimer = () => {
    setIsActive(!isActive);
  };
  return (
    <div className="flex items-center justify-between py-4">
      <div className="flex flex-col">
        <h1 className="text-2xl font-semibold">Daily Standup</h1>
        <p className="text-sm text-muted-foreground">
          {new Date().toLocaleDateString("en-US", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </p>
      </div>

      <div className="flex items-end gap-4">
        <div className="w-16 text-2xl font-stretch font-light">{formatTime(seconds)}</div>
        <Button
          size="xs"
          variant={isActive ? "destructive" : "default"}
          onClick={toggleTimer}
          className="flex items-center h-8 w-8"
        >
          {isActive ? (
            <>
              <Square className="h-4 w-4" />
            </>
          ) : (
            <>
              <Play className="h-3 w-3" />
              {/* <span className="hidden group-hover:block">Start daily</span> */}
            </>
          )}
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" onClick={isRightSidebarVisible}>
          <ExpandIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
