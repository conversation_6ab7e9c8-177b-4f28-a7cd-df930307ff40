"use client";

import { Calendar } from "@/components/ui/calendar";
import { useState } from "react";
import { TeamMember } from "../_data/mock-data";
import { MembersList } from "./members-list";
import { Notes } from "./notes";

type SidebarProps = {
  teamMembers: TeamMember[];
};

export function RightSidebar({ teamMembers }: SidebarProps) {
  const [date, setDate] = useState<Date>(new Date());

  return (
    <div className="flex flex-col items-center w-72 border-l pt-6">
      {/* Calendar section */}
      <Calendar
        mode="single"
        className="border-none"
        selected={date}
        onSelect={(newDate) => newDate && setDate(newDate)}
      />

      <div className="h-[1px] border-t w-full my-6" />

      <Notes />

      <div className="h-[1px] border-t w-full my-6" />

      {/* Team members list */}
      <MembersList teamMembers={teamMembers} />
    </div>
  );
}
