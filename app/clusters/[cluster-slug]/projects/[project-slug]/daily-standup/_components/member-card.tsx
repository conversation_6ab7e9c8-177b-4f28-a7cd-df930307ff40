"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import {
  AlertCircle,
  ArrowLeftRight,
  ArrowRight,
  CheckCircle,
  ChevronsDownUp,
  ChevronsUpDown,
  Clock,
  Flag,
  MessageCircle,
} from "lucide-react";
import { useId, useState } from "react";
import { memberTasksMap } from "../_data/standup-tasks";
import { TaskCard } from "./task-card";

type MemberCardProps = {
  memberId: string;
  memberName: string;
  initials: string;
  memberStatus: "accepted" | "declined" | "pending" | undefined;
};

export function MemberCard({ memberId, memberName, initials, memberStatus }: MemberCardProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [checkedTasks, setCheckedTasks] = useState<Record<string, boolean>>({});

  const memberTasks = memberTasksMap[memberId as keyof typeof memberTasksMap];
  const yesterdayTasks = memberTasks?.yesterday || [];
  const todayTasks = memberTasks?.today || [];

  const handleCheckTask = (taskId: string) => {
    setCheckedTasks((prev) => ({
      ...prev,
      [taskId]: !prev[taskId],
    }));
  };

  const statusIcons = {
    healthy: <CheckCircle className="h-4 w-4" />,
    "at-risk": <Clock className="h-4 w-4" />,
    late: <AlertCircle className="h-4 w-4" />,
  };

  const statusColors = {
    healthy:
      "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800/60",
    "at-risk":
      "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800/60",
    late: "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800/60",
  };

  // Map of background colors for each member
  const bgColors: Record<string, string> = {
    DP: "bg-gray-300",
    MR: "bg-gray-400",
    RB: "bg-gray-500",
    JD: "bg-gray-600",
    SB: "bg-gray-700",
    OW: "bg-gray-800",
  };

  const id = useId();
  return (
    <div className="rounded-lg p-4 pl-0 border-b pb-12 dark:border-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar className="h-7 w-7">
              <AvatarFallback className={`text-xs text-white ${bgColors[initials]}`}>
                {initials}
              </AvatarFallback>
            </Avatar>
            <div
              className={`absolute -bottom-0.5 -right-1 h-4 w-4 rounded-full 
                  ${
                    memberStatus === "accepted"
                      ? "bg-green-500"
                      : memberStatus === "declined"
                      ? "bg-red-500"
                      : "bg-gray-500"
                  } 
                  border-2 border-white flex items-center justify-center`}
            >
              <span className="text-white text-[8px]">
                {memberStatus === "accepted" ? "✓" : memberStatus === "declined" ? "✕" : "⃝"}
              </span>
            </div>
          </div>
          <h3 className="text-lg font-medium dark:text-gray-200">{memberName}</h3>
        </div>
        <Button variant="ghost" size="icon" onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? (
            <ChevronsDownUp className="h-4 w-4" />
          ) : (
            <ChevronsUpDown className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Content */}
      <div className={cn("flex flex-col gap-4 mt-4", isExpanded ? "block" : "hidden")}>
        {/* Labels */}
        <div className="grid grid-cols-12 md:grid-cols-12 gap-2">
          <h4 className="col-span-12 md:col-span-7 text-sm font-medium text-gray-500 dark:text-gray-400">
            Progress
          </h4>
          <h4 className="hidden md:block md:col-span-3 text-sm font-medium text-gray-500 dark:text-gray-400 px-4">
            Impediments
          </h4>
          <h4 className="hidden md:block md:col-span-1 text-sm font-medium text-gray-500 dark:text-gray-400 px-4">
            Status
          </h4>
          <h4 className="hidden md:block md:col-span-1 text-sm font-medium text-gray-500 dark:text-gray-400 px-4">
            Actions
          </h4>
        </div>

        {/* Task rows */}
        <div className="flex flex-col">
          {todayTasks.map((todayTask, index) => {
            const yesterdayTask = yesterdayTasks[index];
            const isChecked = checkedTasks[todayTask.id];

            return (
              <div
                key={todayTask.id}
                className={cn(
                  "grid grid-cols-12 md:grid-cols-12 gap-4 py-2 rounded-lg",
                  isChecked && "opacity-50 grayscale"
                )}
              >
                {/* Progress Column */}
                <div className="col-span-12 md:col-span-7 flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex-1 w-full">
                    {index === 0 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Yesterday</p>
                    )}
                    {yesterdayTask ? (
                      <TaskCard task={yesterdayTask} />
                    ) : (
                      <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md text-sm min-h-16 text-gray-500 dark:text-gray-400">
                        No task assigned
                      </div>
                    )}
                  </div>

                  <ArrowRight
                    className={cn(
                      "hidden md:block h-5 w-5 text-gray-400 dark:text-gray-600 flex-shrink-0 rotate-90 md:rotate-0",
                      `${index === 0 && "md:mt-6"}`
                    )}
                  />

                  <div className="flex-1 w-full">
                    {index === 0 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Today</p>
                    )}
                    <TaskCard task={todayTask} />
                  </div>
                </div>

                {/* Mobile Impediments */}
                <div className="col-span-12 md:hidden px-2">
                  <div className="p-3 bg-gray-50 dark:bg-background dark:border dark:border-[#30363d] rounded-md text-sm min-h-12 text-gray-700 dark:text-gray-300">
                    <div className="flex items-center gap-2 mb-2">
                      {todayTask.impediments && <Flag className="h-4 w-4 text-red-500" />}
                    </div>
                    <div className="flex-1">
                      {todayTask.impediments || "No impediments reported"}
                    </div>
                  </div>
                </div>

                {/* Mobile Status and Actions */}
                <div className="col-span-12 md:hidden flex items-center justify-between mt-2 px-2 border-b mb-6 pb-6">
                  <Badge
                    variant="outline"
                    className={cn(
                      "flex items-center gap-1.5 px-2 py-1 font-medium",
                      statusColors[todayTask.deadlineStatus]
                    )}
                  >
                    {statusIcons[todayTask.deadlineStatus]}
                    <span>
                      {todayTask.deadlineStatus === "at-risk"
                        ? "At Risk"
                        : todayTask.deadlineStatus.charAt(0).toUpperCase() +
                          todayTask.deadlineStatus.slice(1)}
                    </span>
                  </Badge>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id={id + index + "mobile"}
                        checked={isChecked}
                        onCheckedChange={() => handleCheckTask(todayTask.id)}
                        className="h-3.5 w-3.5 border-gray-700"
                      />
                      <Label htmlFor={id + index + "mobile"} className="text-xs font-medium">
                        Complete
                      </Label>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Desktop Impediments Column */}
                <div
                  className={cn(
                    "hidden md:block md:col-span-3 px-4",
                    index === 0 ? "pt-6" : "pt-0"
                  )}
                >
                  <div className="p-3 bg-gray-50 dark:bg-background dark:border dark:border-[#30363d] rounded-md text-sm min-h-12 text-gray-700 dark:text-gray-300 h-full flex flex-col">
                    <div className="flex items-center gap-2 mb-2">
                      {todayTask.impediments && <Flag className="h-4 w-4 text-red-500" />}
                    </div>
                    <div className="flex-1 flex items-center">
                      {todayTask.impediments || "No impediments reported"}
                    </div>
                  </div>
                </div>

                {/* Desktop Deadline Status Column */}
                <div
                  className={cn(
                    "hidden md:flex md:flex-col md:col-span-1 gap-4 px-2",
                    index === 0 ? "pt-6" : "pt-0"
                  )}
                >
                  <Badge
                    variant="outline"
                    className={cn(
                      "flex items-center gap-1.5 px-2 py-1 font-medium",
                      statusColors[todayTask.deadlineStatus]
                    )}
                  >
                    {statusIcons[todayTask.deadlineStatus]}
                    <span>
                      {todayTask.deadlineStatus === "at-risk"
                        ? "At Risk"
                        : todayTask.deadlineStatus.charAt(0).toUpperCase() +
                          todayTask.deadlineStatus.slice(1)}
                    </span>
                  </Badge>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Due to:
                    <br />
                    <span className="text-sm font-medium">
                      {todayTask.dueDate
                        ? new Date(todayTask.dueDate).toLocaleDateString("en-US", {
                            month: "long",
                            day: "numeric",
                            year: "numeric",
                          })
                        : "No due date"}
                    </span>
                  </div>
                </div>

                {/* Desktop Action Buttons Column */}
                <div className={cn("hidden md:block md:col-span-1", index === 0 ? "pt-6" : "pt-0")}>
                  <div className="flex flex-col items-start justify-center">
                    <div className="flex items-center gap-2 pb-2 hover:bg-accent rounded-md py-2 pr-2 hover:cursor-pointer">
                      <Checkbox
                        id={id + index}
                        checked={isChecked}
                        onCheckedChange={() => handleCheckTask(todayTask.id)}
                        className="h-3.5 w-3.5 ml-3 border-gray-700"
                      />
                      <Label htmlFor={id + index} className="text-xs font-medium cursor-pointer">
                        Complete
                      </Label>
                    </div>
                    <Button variant="ghost">
                      <MessageCircle className="h-4 w-4" />
                      <span className="text-xs">Comment</span>
                    </Button>
                    <Button variant="ghost">
                      <ArrowLeftRight className="h-4 w-4" />
                      <span className="text-xs">Move to</span>
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
