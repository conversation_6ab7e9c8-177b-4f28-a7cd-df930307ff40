"use client";

import { TeamMember } from "../_data/mock-data";

type ParticipantsStatusProps = {
  teamMembers: TeamMember[];
};

export function ParticipantsStatus({ teamMembers }: ParticipantsStatusProps) {
  // Calculate response percentage
  const acceptedCount = teamMembers.filter((member) => member.responseStatus === "accepted").length;
  const acceptedPercentage = Math.round((acceptedCount / teamMembers.length) * 100);

  // Map of background colors for each member
  const bgColors: Record<string, string> = {
    "1": "bg-gray-300",
    "2": "bg-gray-400",
    "3": "bg-gray-500",
    "4": "bg-gray-600",
    "5": "bg-gray-700",
    "6": "bg-gray-800",
  };

  return (
    <div className="flex items-center gap-4">
      <div className="flex -space-x-1">
        {teamMembers.map((member, index) => (
          <div key={index} className="relative">
            <div
              className={`h-8 w-8 rounded-full ${
                bgColors[member.id]
              } border-2 border-white flex items-center justify-center`}
            >
              <span className="text-xs text-white font-bold">{member.initials}</span>
            </div>
            <div
              className={`absolute -bottom-0.5 -right-0.5 h-4 w-4 rounded-full 
                  ${
                    member.responseStatus === "accepted"
                      ? "bg-green-500"
                      : member.responseStatus === "declined"
                      ? "bg-red-500"
                      : "bg-gray-500"
                  } 
                  border-2 border-white flex items-center justify-center`}
            >
              <span className="text-white text-[8px]">
                {member.responseStatus === "accepted"
                  ? "✓"
                  : member.responseStatus === "declined"
                  ? "✕"
                  : "⃝"}
              </span>
            </div>
          </div>
        ))}
      </div>
      <p className="text-md font-medium text-gray-700">{acceptedPercentage}%</p>
    </div>
  );
}
