"use client";

import { useState } from "react";
import DailyStandupReport from "../_data/daily-report-chatgpt";
import { teamMembers } from "../_data/mock-data";
import { MemberCard } from "./member-card";
import { ParticipantsStatus } from "./participants-status";
import { ViewSwitcher } from "./view-switcher";

export function TaskView() {
  const [activeView, setActiveView] = useState<"feature" | "member" | "example-report">("member");

  return (
    <div>
      {/* View toggle buttons */}
      <div className="flex items-center justify-between py-4">
        <ParticipantsStatus teamMembers={teamMembers} />
        <ViewSwitcher activeView={activeView} onViewChange={(view) => setActiveView(view)} />
      </div>

      {/* Standup cards container */}
      <div className="space-y-6">
        {activeView === "feature" && (
          // Feature view (not implemented yet)
          <div className="flex flex-col items-center justify-center h-[calc(100vh-15rem)] gap-4 bg-accent rounded-lg mx-6">
            <div className="flex flex-col items-center text-center">
              <h1 className="text-2xl font-semibold">View per feature</h1>
              <p className="text-sm text-gray-500">Coming soon</p>
            </div>
          </div>
        )}

        {activeView === "member" &&
          // Member cards
          teamMembers.map((member) => (
            <MemberCard
              key={member.id}
              memberId={member.id}
              memberName={member.name}
              initials={member.initials}
              memberStatus={member.responseStatus}
            />
          ))}

        {activeView === "example-report" && <DailyStandupReport />}
      </div>
    </div>
  );
}
