"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { FileText, LayoutGrid, Users } from "lucide-react";

type ViewSwitcherProps = {
  activeView: "feature" | "member" | "example-report";
  onViewChange: (view: "feature" | "member" | "example-report") => void;
};

export function ViewSwitcher({ activeView, onViewChange }: ViewSwitcherProps) {
  return (
    <div className="flex items-center overflow-hidden border rounded-md">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("member")}
        className={cn(
          "h-8 px-3 rounded-none border-r",
          activeView === "member"
            ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"
            : "text-gray-500 hover:text-gray-700"
        )}
      >
        <span className="flex items-center gap-1.5 text-sm">
          <LayoutGrid className="h-4 w-4" />
          <span>Members</span>
        </span>
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("feature")}
        className={cn(
          "h-8 px-3 rounded-none",
          activeView === "feature"
            ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"
            : "text-gray-500 hover:text-gray-700"
        )}
      >
        <span className="flex items-center gap-1.5 text-sm">
          <Users className="h-4 w-4" />
          <span>Features</span>
        </span>
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("example-report")}
        className={cn(
          "h-8 px-3 rounded-none",
          activeView === "example-report"
            ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"
            : "text-gray-500 hover:text-gray-700"
        )}
      >
        <span className="flex items-center gap-1.5 text-sm">
          <FileText className="h-4 w-4" />
          <span>Example report</span>
        </span>
      </Button>
    </div>
  );
}
