"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronDown, ChevronUp, Search } from "lucide-react";
import { useState } from "react";
import { TeamMember } from "../_data/mock-data";

type MembersListProps = {
  teamMembers: TeamMember[];
};

export function MembersList({ teamMembers }: MembersListProps) {
  const [isOpen, setIsOpen] = useState(true);

  // Map of background colors for each member
  const bgColors: Record<string, string> = {
    "1": "bg-gray-300",
    "2": "bg-gray-400",
    "3": "bg-gray-500",
    "4": "bg-gray-600",
    "5": "bg-gray-700",
    "6": "bg-gray-800",
  };

  return (
    <div className="w-full rounded-lg px-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Members</h3>
        <Button variant="ghost" size="icon" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
        </Button>
      </div>

      {/* Search input */}
      {isOpen && (
        <>
          <div className="relative mb-4">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Add members to daily"
              className="pl-8 w-full text-sm bg-gray-50"
            />
          </div>

          <div className="space-y-3">
            {teamMembers.map((member) => (
              <div key={member.id} className="flex items-center gap-3">
                <div
                  className={`h-8 w-8 rounded-full ${
                    bgColors[member.id]
                  } flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-medium">{member.initials}</span>
                </div>
                <span className="text-sm">{member.name}</span>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
