"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Ellipsis } from "lucide-react";
import { type StandupTask } from "../_data/standup-tasks";

interface TaskCardProps {
  task: StandupTask;
}

// Helper to get tag style based on tag name
const getTagStyle = (tag: string) => {
  const styles: Record<string, string> = {
    performance:
      "text-pink-700 bg-pink-50 border-pink-200 dark:text-pink-400 dark:bg-pink-900/20 dark:border-pink-800/60",
    bug: "text-red-700 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800/60",
    testing:
      "text-purple-700 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800/60",
    scalability:
      "text-blue-700 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800/60",
    refactor:
      "text-yellow-700 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800/60",
    feature:
      "text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800/60",
    security:
      "text-red-700 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800/60",
    architecture:
      "text-blue-700 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800/60",
  };

  return (
    styles[tag] ||
    "text-gray-700 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800/60"
  );
};

// Helper to get status style based on status
const getStatusStyle = (status: string) => {
  const styles: Record<string, string> = {
    todo: "text-gray-700 border-gray-200 dark:text-gray-400",
    "in-progress": "text-amber-700 border-amber-200 dark:text-amber-400",
    "in-review": "text-purple-700 border-purple-200 dark:text-purple-400",
    done: "text-green-700 border-green-200 dark:text-green-400",
  };

  return styles[status] || "text-gray-700 border-gray-200 dark:text-gray-400";
};

// Helper to format status display
const formatStatus = (status: string): string => {
  switch (status) {
    case "todo":
      return "todo";
    case "in-progress":
      return "in-progress";
    case "in-review":
      return "in-review";
    case "done":
      return "done";
    default:
      return status;
  }
};

export function TaskCard({ task }: TaskCardProps) {
  return (
    <div className="flex flex-col justify-between bg-white dark:bg-background border border-[#e5e7eb] dark:border-[#30363d] rounded-lg p-2 shadow-xs hover:shadow-md transition-all duration-200 cursor-pointer group">
      <header className="flex flex-row items-center justify-between">
        <div className="flex flex-row flex-wrap gap-1">
          {task.tags.map((tag) => (
            <Badge
              key={tag}
              variant="outline"
              className={cn(
                "w-fit px-1.5 py-0.2 text-[10px] font-medium border-[.08rem]",
                getTagStyle(tag)
              )}
            >
              {tag}
            </Badge>
          ))}
        </div>
        <div className="flex flex-row items-center gap-1">
          <span className="text-xs text-gray-400">{task.duration}</span>
          <Ellipsis className="size-3 text-gray-400" />
        </div>
      </header>

      <div className="flex-1 cursor-pointer pt-2 pb-3">
        <span className="text-[#374151] dark:text-[#c9d1d9] text-sm font-medium line-clamp-2">
          {task.title}
        </span>
      </div>

      <footer className="flex flex-row items-center justify-between gap-2 text-xs">
        <div className="flex flex-row items-center gap-2">
          <span>{task.key}</span>
          <Badge variant="outline" className={cn("text-[10px]", getStatusStyle(task.status))}>
            {formatStatus(task.status)}
          </Badge>
        </div>
        <Avatar className="size-6 bg-gray-500">
          <AvatarFallback>{task.assignee.initials}</AvatarFallback>
        </Avatar>
      </footer>
    </div>
  );
}
