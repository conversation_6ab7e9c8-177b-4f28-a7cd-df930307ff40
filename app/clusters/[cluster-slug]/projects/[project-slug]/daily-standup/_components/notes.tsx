"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

export function Notes() {
  const [notes, setNotes] = useState<string>("");
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className="w-full rounded-lg px-4">
      <div className="flex items-start justify-between">
        <div className="flex flex-col gap-1">
          <h3 className="text-lg font-medium">Notes</h3>
          {isOpen && (
            <p className="text-muted-foreground pb-2 text-xs" role="region" aria-live="polite">
              Added to the report when the daily is completed.
            </p>
          )}
        </div>

        <Button variant="ghost" size="icon" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
        </Button>
      </div>

      {/* Notes textarea */}
      {isOpen && (
        <div>
          <Textarea
            placeholder="Take notes for today's standup..."
            className="w-full text-sm bg-gray-50 min-h-[120px] relative"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            minLength={10}
            maxLength={1000}
          />
          <span className="text-[8px] text-gray-500 flex justify-end pt-1 pr-1">
            {notes.length} / 1000
          </span>
        </div>
      )}
    </div>
  );
}
