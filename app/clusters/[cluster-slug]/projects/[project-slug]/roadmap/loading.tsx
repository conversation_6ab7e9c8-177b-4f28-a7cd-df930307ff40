import { Skeleton } from "@/components/ui/skeleton";

export default function RoadmapLoading() {
  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between px-4">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex items-center gap-3">
          <Skeleton className="h-9 w-[100px]" />
          <Skeleton className="h-9 w-[120px]" />
          <div className="flex gap-1">
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
      </div>

      {/* Timeline Headers */}
      <div className="grid grid-cols-5 gap-4 px-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-12 flex items-center justify-center border-r last:border-r-0">
            <Skeleton className="h-5 w-24" />
          </div>
        ))}
      </div>

      {/* Timeline Items */}
      <div className="flex flex-col gap-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex flex-col gap-2">
            {/* Item Header */}
            <div className="flex items-center gap-3 px-4">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-5 w-24 ml-2" />
            </div>

            {/* Progress Bar */}
            <div className="px-4">
              <div className="relative h-12 rounded-lg border">
                <Skeleton className="absolute h-8 top-2 left-2 w-[60%]" />
                <div className="absolute right-3 top-3">
                  <Skeleton className="h-5 w-12" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
