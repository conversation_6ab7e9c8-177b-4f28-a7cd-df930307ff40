"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ChevronDown, ChevronRight, Download, Filter, ZoomIn, ZoomOut } from "lucide-react";
import { useState } from "react";

// Sample OKR data with timeline information
const okrData = [
  {
    id: "okr-1",
    title: "Increase Market Share",
    description: "Grow our market share in key segments",
    progress: 65,
    startQuarter: "Q1 2023",
    endQuarter: "Q3 2023",
    owner: "<PERSON>",
    department: "Executive",
    status: "On Track",
    keyResults: [
      {
        id: "kr-1-1",
        title: "Achieve 20% growth in enterprise segment",
        progress: 75,
        status: "On Track",
        milestone: "Q2 2023",
      },
      {
        id: "kr-1-2",
        title: "Launch 3 new products in SMB market",
        progress: 100,
        status: "Completed",
        milestone: "Q1 2023",
      },
      {
        id: "kr-1-3",
        title: "Increase customer retention rate to 85%",
        progress: 60,
        status: "At Risk",
        milestone: "Q3 2023",
      },
    ],
  },
  {
    id: "okr-2",
    title: "Improve Product Quality",
    description: "Enhance product reliability and user satisfaction",
    progress: 80,
    startQuarter: "Q1 2023",
    endQuarter: "Q2 2023",
    owner: "Michael Chen",
    department: "Product",
    status: "On Track",
    keyResults: [
      {
        id: "kr-2-1",
        title: "Reduce critical bugs by 50%",
        progress: 90,
        status: "On Track",
        milestone: "Q1 2023",
      },
      {
        id: "kr-2-2",
        title: "Achieve NPS score of 60+",
        progress: 75,
        status: "On Track",
        milestone: "Q2 2023",
      },
      {
        id: "kr-2-3",
        title: "Decrease customer support tickets by 30%",
        progress: 85,
        status: "On Track",
        milestone: "Q2 2023",
      },
    ],
  },
  {
    id: "okr-3",
    title: "Optimize Operational Efficiency",
    description: "Streamline processes and reduce operational costs",
    progress: 45,
    startQuarter: "Q2 2023",
    endQuarter: "Q4 2023",
    owner: "Alex Rodriguez",
    department: "Operations",
    status: "At Risk",
    keyResults: [
      {
        id: "kr-3-1",
        title: "Reduce operational costs by 15%",
        progress: 40,
        status: "At Risk",
        milestone: "Q3 2023",
      },
      {
        id: "kr-3-2",
        title: "Implement automated workflows for 5 key processes",
        progress: 60,
        status: "On Track",
        milestone: "Q2 2023",
      },
      {
        id: "kr-3-3",
        title: "Decrease project delivery time by 20%",
        progress: 35,
        status: "Behind",
        milestone: "Q4 2023",
      },
    ],
  },
  {
    id: "okr-4",
    title: "Expand Global Presence",
    description: "Enter new markets and strengthen international operations",
    progress: 30,
    startQuarter: "Q3 2023",
    endQuarter: "Q1 2024",
    owner: "Emily Wong",
    department: "Sales",
    status: "Behind",
    keyResults: [
      {
        id: "kr-4-1",
        title: "Launch operations in 3 new countries",
        progress: 33,
        status: "On Track",
        milestone: "Q4 2023",
      },
      {
        id: "kr-4-2",
        title: "Achieve 25% revenue from international markets",
        progress: 20,
        status: "Behind",
        milestone: "Q1 2024",
      },
      {
        id: "kr-4-3",
        title: "Establish 5 strategic partnerships in key regions",
        progress: 40,
        status: "At Risk",
        milestone: "Q4 2023",
      },
    ],
  },
  {
    id: "okr-5",
    title: "Enhance Employee Experience",
    description: "Improve employee satisfaction and reduce turnover",
    progress: 85,
    startQuarter: "Q1 2023",
    endQuarter: "Q2 2023",
    owner: "David Park",
    department: "HR",
    status: "On Track",
    keyResults: [
      {
        id: "kr-5-1",
        title: "Achieve employee satisfaction score of 4.5/5",
        progress: 90,
        status: "On Track",
        milestone: "Q2 2023",
      },
      {
        id: "kr-5-2",
        title: "Reduce employee turnover by 15%",
        progress: 80,
        status: "On Track",
        milestone: "Q1 2023",
      },
      {
        id: "kr-5-3",
        title: "Implement 3 new employee wellness programs",
        progress: 100,
        status: "Completed",
        milestone: "Q1 2023",
      },
    ],
  },
];

// Define quarters for the roadmap
const quarters = ["Q1 2023", "Q2 2023", "Q3 2023", "Q4 2023", "Q1 2024"];

export function ProjectRoadmap() {
  const [filter, setFilter] = useState("all");
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [year, setYear] = useState("2023");
  const [zoomLevel, setZoomLevel] = useState(1);

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "On Track":
        return "bg-green-500/20 text-green-700 dark:bg-green-500/30 dark:text-green-400";
      case "At Risk":
        return "bg-yellow-500/20 text-yellow-700 dark:bg-yellow-500/30 dark:text-yellow-400";
      case "Behind":
        return "bg-red-500/20 text-red-700 dark:bg-red-500/30 dark:text-red-400";
      case "Completed":
        return "bg-blue-500/20 text-blue-700 dark:bg-blue-500/30 dark:text-blue-400";
      default:
        return "bg-gray-500/20 text-gray-700 dark:bg-gray-500/30 dark:text-gray-400";
    }
  };

  const getStatusBgColor = (status: string) => {
    switch (status) {
      case "On Track":
        return "bg-green-500/10 dark:bg-green-500/5";
      case "At Risk":
        return "bg-yellow-500/10 dark:bg-yellow-500/5";
      case "Behind":
        return "bg-red-500/10 dark:bg-red-500/5";
      case "Completed":
        return "bg-blue-500/10 dark:bg-blue-500/5";
      default:
        return "bg-gray-500/10 dark:bg-gray-500/5";
    }
  };

  const getStatusSolidColor = (status: string) => {
    switch (status) {
      case "On Track":
        return "bg-green-500/60 dark:bg-green-600/60";
      case "At Risk":
        return "bg-yellow-500/60 dark:bg-yellow-600/60";
      case "Behind":
        return "bg-red-500/60 dark:bg-red-600/60";
      case "Completed":
        return "bg-blue-500/60 dark:bg-blue-600/60";
      default:
        return "bg-gray-500/60 dark:bg-gray-600/60";
    }
  };

  const getBorderColor = (status: string) => {
    switch (status) {
      case "On Track":
        return "border-green-500";
      case "At Risk":
        return "border-yellow-500";
      case "Behind":
        return "border-red-500";
      case "Completed":
        return "border-blue-500";
      default:
        return "border-gray-500";
    }
  };

  const filteredOKRs =
    filter === "all"
      ? okrData
      : okrData.filter((okr) => okr.department === filter || okr.status === filter);

  // Function to determine the position and width of an OKR on the timeline
  const getTimelinePosition = (startQuarter: string, endQuarter: string) => {
    const startIndex = quarters.indexOf(startQuarter);
    const endIndex = quarters.indexOf(endQuarter);

    if (startIndex === -1 || endIndex === -1) return { left: 0, width: "20%" };

    const left = (startIndex / quarters.length) * 100;
    const width = ((endIndex - startIndex + 1) / quarters.length) * 100;

    return { left: `${left}%`, width: `${width}%` };
  };

  const zoomIn = () => {
    if (zoomLevel < 2) setZoomLevel(zoomLevel + 0.25);
  };

  const zoomOut = () => {
    if (zoomLevel > 0.5) setZoomLevel(zoomLevel - 0.25);
  };

  return (
    <Card className="border-none shadow-none">
      <CardContent className="p-6 pt-0 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-2xl font-semibold">Strategic Roadmap</h3>
            <p className="text-sm text-muted-foreground">
              Timeline view of organizational goals and objectives
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Select value={year} onValueChange={setYear}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2023">2023</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All OKRs</SelectItem>
                <SelectItem value="Executive">Executive</SelectItem>
                <SelectItem value="Product">Product</SelectItem>
                <SelectItem value="Operations">Operations</SelectItem>
                <SelectItem value="Sales">Sales</SelectItem>
                <SelectItem value="HR">HR</SelectItem>
                <SelectItem value="On Track">On Track</SelectItem>
                <SelectItem value="At Risk">At Risk</SelectItem>
                <SelectItem value="Behind">Behind</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <TooltipProvider>
              <div className="flex items-center gap-1 border rounded-md">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button size="icon" variant="ghost" onClick={zoomOut} className="h-8 w-8">
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button size="icon" variant="ghost" onClick={zoomIn} className="h-8 w-8">
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In</TooltipContent>
                </Tooltip>
              </div>
            </TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="icon" variant="outline" className="h-8 w-8">
                  <Filter className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Advanced Filters</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="icon" variant="outline" className="h-8 w-8">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Export Roadmap</TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Timeline header */}
        <div className="relative mt-4">
          <div className="flex border-b mb-4">
            {quarters.map((quarter) => (
              <div key={quarter} className="flex-1 text-center pb-2 font-medium">
                {quarter}
              </div>
            ))}
          </div>

          {/* Timeline grid lines */}
          <div className="absolute top-0 left-0 w-full h-[calc(100vh-12rem)] pointer-events-none">
            {quarters.map((_, index) => (
              <div
                key={index}
                className="absolute top-0 bottom-0 border-l border-dashed border-gray-200 dark:border-gray-700"
                style={{ left: `${(index / quarters.length) * 100}%` }}
              />
            ))}
          </div>

          {/* OKRs on timeline */}
          <div
            className="space-y-4 relative"
            style={{
              transform: `scale(${zoomLevel})`,
              transformOrigin: "top left",
              transition: "transform 0.2s ease-in-out",
              height: `${filteredOKRs.length * 80 + 100}px`,
            }}
          >
            {filteredOKRs.map((okr) => {
              const { left, width } = getTimelinePosition(okr.startQuarter, okr.endQuarter);
              const isExpanded = expandedItems.includes(okr.id);
              const borderColor = getBorderColor(okr.status);
              const bgColor = getStatusBgColor(okr.status);
              const solidColor = getStatusSolidColor(okr.status);

              return (
                <div key={okr.id} className="relative mb-12 px-4">
                  <div className="flex items-center mb-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-0 h-5 w-5 rounded-full mr-1"
                      onClick={() => toggleExpand(okr.id)}
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                    <div className="text-sm font-medium text-muted-foreground flex items-center">
                      {okr.title}
                      <Badge className={`ml-2 text-xs ${getStatusColor(okr.status)}`}>
                        {okr.status}
                      </Badge>
                      <span className="text-xs text-muted-foreground ml-2">({okr.department})</span>
                    </div>
                  </div>

                  <div className="relative h-12 mb-1">
                    <div
                      className={`absolute h-full rounded-md flex items-center px-3 shadow-sm border ${borderColor} ${bgColor}`}
                      style={{
                        left,
                        width,
                        borderLeftWidth: "4px",
                      }}
                    >
                      <div className="flex justify-between items-center w-full">
                        <div className="text-sm font-medium truncate mr-2">{okr.title}</div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-medium">{okr.progress}%</span>
                          <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${solidColor}`}
                              style={{ width: `${okr.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="pl-6 space-y-2 mb-3">
                      {okr.keyResults.map((kr) => {
                        const milestoneIndex = quarters.indexOf(kr.milestone);
                        const milestonePosition = `${(milestoneIndex / quarters.length) * 100}%`;
                        const krBorderColor = getBorderColor(kr.status);
                        const krSolidColor = getStatusSolidColor(kr.status);

                        return (
                          <div key={kr.id} className="relative">
                            <div
                              className={`ml-4 pl-3 py-2 rounded-md bg-gray-50 dark:bg-neutral-900 border shadow-sm ${krBorderColor}/20`}
                              style={{
                                marginLeft: milestonePosition,
                                borderLeftWidth: "4px",
                              }}
                            >
                              <div className="flex items-center">
                                <div
                                  className="absolute -left-3 top-1/2 h-2 w-2 rounded-full transform -translate-y-1/2 -translate-x-1.5"
                                  style={{
                                    backgroundColor:
                                      kr.status === "Completed"
                                        ? "#3b82f6"
                                        : kr.status === "On Track"
                                        ? "#10b981"
                                        : kr.status === "At Risk"
                                        ? "#f59e0b"
                                        : "#ef4444",
                                  }}
                                />
                                <div className="flex-1 pr-4">
                                  <div className="flex items-center justify-between">
                                    <span className="text-xs font-medium">{kr.title}</span>
                                    <div className="flex items-center gap-2">
                                      <Badge className={`text-xs ${getStatusColor(kr.status)}`}>
                                        {kr.status}
                                      </Badge>
                                      <span className="text-xs">{kr.progress}%</span>
                                    </div>
                                  </div>
                                  <div className="mt-1 w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                    <div
                                      className={krSolidColor}
                                      style={{ width: `${kr.progress}%`, height: "100%" }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
