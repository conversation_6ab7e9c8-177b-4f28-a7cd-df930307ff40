"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ertTriangle, CheckCircle, Clock, HelpCircle } from "lucide-react";
import { useEffect, useState } from "react";

export function ProjectHealthMatrix() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const projects = [
    {
      name: "Website Redesign",
      schedule: "At Risk",
      budget: "On Track",
      scope: "At Risk",
      quality: "On Track",
      overall: "At Risk",
    },
    {
      name: "Mobile App Development",
      schedule: "On Track",
      budget: "At Risk",
      scope: "On Track",
      quality: "On Track",
      overall: "On Track",
    },
    {
      name: "Marketing Campaign",
      schedule: "On Track",
      budget: "On Track",
      scope: "On Track",
      quality: "On Track",
      overall: "On Track",
    },
    {
      name: "Product Launch",
      schedule: "At Risk",
      budget: "On Track",
      scope: "On Track",
      quality: "On Track",
      overall: "At Risk",
    },
    {
      name: "CRM Implementation",
      schedule: "On Track",
      budget: "On Track",
      scope: "On Track",
      quality: "On Track",
      overall: "On Track",
    },
    {
      name: "Office Renovation",
      schedule: "Complete",
      budget: "Complete",
      scope: "Complete",
      quality: "Complete",
      overall: "Complete",
    },
    {
      name: "Data Migration",
      schedule: "On Track",
      budget: "On Track",
      scope: "At Risk",
      quality: "On Track",
      overall: "At Risk",
    },
    {
      name: "Security Audit",
      schedule: "On Track",
      budget: "On Track",
      scope: "On Track",
      quality: "On Track",
      overall: "On Track",
    },
    {
      name: "Employee Training Program",
      schedule: "Not Started",
      budget: "Not Started",
      scope: "Not Started",
      quality: "Not Started",
      overall: "Not Started",
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "On Track":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "At Risk":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "Off Track":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "Complete":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "Not Started":
        return <Clock className="h-4 w-4 text-gray-500" />;
      default:
        return <HelpCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "On Track":
        return (
          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
            On Track
          </Badge>
        );
      case "At Risk":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
          >
            At Risk
          </Badge>
        );
      case "Off Track":
        return (
          <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">
            Off Track
          </Badge>
        );
      case "Complete":
        return (
          <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            Complete
          </Badge>
        );
      case "Not Started":
        return (
          <Badge variant="outline" className="bg-gray-500/10 text-gray-500 border-gray-500/20">
            Not Started
          </Badge>
        );
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle className="text-base">Project Health Matrix</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col">
        {mounted ? (
          <div className="overflow-x-auto flex-1">
            <table className="w-full text-sm h-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2 text-xs">Project</th>
                  <th className="text-center p-2 text-xs">Schedule</th>
                  <th className="text-center p-2 text-xs">Budget</th>
                  <th className="text-center p-2 text-xs">Scope</th>
                  <th className="text-center p-2 text-xs">Quality</th>
                  <th className="text-center p-2 text-xs">Overall</th>
                </tr>
              </thead>
              <tbody>
                {projects.map((project) => (
                  <tr key={project.name} className="border-b last:border-0">
                    <td className="p-2 font-medium text-sm">{project.name}</td>
                    <td className="text-center p-2">{getStatusIcon(project.schedule)}</td>
                    <td className="text-center p-2">{getStatusIcon(project.budget)}</td>
                    <td className="text-center p-2">{getStatusIcon(project.scope)}</td>
                    <td className="text-center p-2">{getStatusIcon(project.quality)}</td>
                    <td className="text-center p-2">{getStatusBadge(project.overall)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-muted-foreground">Loading health matrix...</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
