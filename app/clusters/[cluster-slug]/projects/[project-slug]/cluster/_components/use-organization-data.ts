"use client";

import {
  getOrganizationClustersAction,
  getUserOrganizationRoleAction,
} from "@/db/actions/organization-data.action";
import { cacheConfigs, commonQueryOptions, organizationQueryKeys } from "@/lib/query-keys";
import { useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Hook to fetch all clusters with projects for organization overview
 */
export function useOrganizationClusters() {
  return useQuery({
    queryKey: organizationQueryKeys.clusters(),
    queryFn: async () => {
      const result = await getOrganizationClustersAction();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch organization clusters");
      }
      return result;
    },
    ...cacheConfigs.organization, // Use longer stale time for organization data
    ...commonQueryOptions,
  });
}

/**
 * Hook to get current user's organization role (admin/member)
 */
export function useUserOrganizationRole() {
  return useQuery({
    queryKey: organizationQueryKeys.userRole(),
    queryFn: async () => {
      const result = await getUserOrganizationRoleAction();
      return result;
    },
    ...cacheConfigs.organization, // Use longer stale time for organization data
    ...commonQueryOptions,
  });
}

/**
 * Helper hook to invalidate organization data cache
 */
export function useInvalidateOrganizationData() {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: organizationQueryKeys.all }),
    invalidateClusters: () =>
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.clusters() }),
    invalidateUserRole: () =>
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.userRole() }),
  };
}

/**
 * Helper hook to prefetch organization data
 */
export function usePrefetchOrganizationData() {
  const queryClient = useQueryClient();

  return {
    prefetchClusters: () => {
      queryClient.prefetchQuery({
        queryKey: organizationQueryKeys.clusters(),
        queryFn: async () => {
          const result = await getOrganizationClustersAction();
          if (!result.success) {
            throw new Error(result.error || "Failed to fetch organization clusters");
          }
          return result;
        },
        ...cacheConfigs.organization,
      });
    },
    prefetchUserRole: () => {
      queryClient.prefetchQuery({
        queryKey: organizationQueryKeys.userRole(),
        queryFn: async () => {
          const result = await getUserOrganizationRoleAction();
          return result;
        },
        ...cacheConfigs.organization,
      });
    },
  };
}

// Types for convenience
export type { OrganizationCluster } from "@/db/actions/cluster.action";
