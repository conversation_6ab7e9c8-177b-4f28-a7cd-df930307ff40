"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useTheme } from "next-themes";
import { useMemo } from "react";
import {
  Bar,
  BarChart,
  Cell,
  LabelList,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts";
import { NameType, ValueType } from "recharts/types/component/DefaultTooltipContent";

interface ProjectProgressChartProps {
  detailed?: boolean;
}

const PROJECTS = [
  { name: "Website Redesign", progress: 75 },
  { name: "Mobile App Development", progress: 40 },
  { name: "Marketing Campaign", progress: 60 },
  { name: "Product Launch", progress: 25 },
  { name: "CRM Implementation", progress: 90 },
  { name: "Office Renovation", progress: 100 },
  { name: "Data Migration", progress: 50 },
  { name: "Security Audit", progress: 80 },
  { name: "Employee Training Program", progress: 30 },
];

const getBarColor = (progress: number) => {
  if (progress < 30) return "#ef4444"; // red-500
  if (progress < 70) return "#f59e0b"; // amber-500
  if (progress < 100) return "#3b82f6"; // blue-500
  return "#22c55e"; // green-500
};

const CustomTooltip = ({ active, payload }: TooltipProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    const { name, progress } = payload[0].payload;
    return (
      <div className="bg-background border rounded-md p-2 shadow-sm">
        <div className="font-medium">{name}</div>
        <div className="text-sm text-muted-foreground">Progress: {progress}%</div>
      </div>
    );
  }
  return null;
};

export function ProjectProgressChart({ detailed = false }: ProjectProgressChartProps) {
  const { resolvedTheme } = useTheme();

  // Memoize color mapping for performance
  const chartData = useMemo(
    () =>
      PROJECTS.map((p) => ({
        ...p,
        fill: getBarColor(p.progress),
      })).sort((a, b) => b.progress - a.progress),
    []
  );

  // Axis color for dark/light mode
  const axisColor = resolvedTheme === "dark" ? "#e2e8f0" : "#334155";

  return (
    <Card className="gap-2">
      <CardHeader>
        <CardTitle>Project Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className={detailed ? "h-[400px]" : "h-[280px]"}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              layout="vertical"
              margin={{ top: 20, right: 40, left: 60, bottom: 20 }}
              barCategoryGap={detailed ? 16 : 8}
              barGap={4}
            >
              <XAxis
                type="number"
                domain={[0, 100]}
                tick={{ fill: axisColor, fontSize: 13 }}
                axisLine={{ stroke: axisColor }}
                tickLine={{ stroke: axisColor }}
                tickCount={6}
                allowDecimals={false}
                label={{
                  value: "Progress (%)",
                  position: "insideBottomRight",
                  offset: -10,
                  fill: axisColor,
                  fontSize: 13,
                }}
              />
              <YAxis
                type="category"
                dataKey="name"
                width={180}
                tick={{ fill: axisColor, fontSize: 13 }}
                axisLine={{ stroke: axisColor }}
                tickLine={false}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{
                  fill: resolvedTheme === "dark" ? "rgba(255,255,255,0.04)" : "rgba(0,0,0,0.04)",
                }}
              />
              <ReferenceLine x={100} stroke="#22c55e" strokeDasharray="3 3" />
              <Bar
                dataKey="progress"
                isAnimationActive={true}
                radius={[6, 6, 6, 6]}
                minPointSize={4}
              >
                <LabelList
                  dataKey="progress"
                  position="right"
                  formatter={(v: number) => `${v}%`}
                  style={{ fill: axisColor, fontWeight: 500, fontSize: 13 }}
                />
                {chartData.map((entry, idx) => (
                  <Cell key={`cell-${idx}`} fill={entry.fill} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
