"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON>, Legend, <PERSON>, <PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TooltipProps } from "recharts";
import { NameType, ValueType } from "recharts/types/component/DefaultTooltipContent";

interface ProjectStatusDistributionProps {
  detailed?: boolean;
}

export function ProjectStatusDistribution({ detailed = false }: ProjectStatusDistributionProps) {
  const data = [
    { name: "Planning", value: 2, color: "#3b82f6" },
    { name: "In Progress", value: 4, color: "#eab308" },
    { name: "Final Review", value: 2, color: "#8b5cf6" },
    { name: "Completed", value: 1, color: "#22c55e" },
  ];

  const totalProjects = data.reduce((sum, item) => sum + item.value, 0);

  const detailedData = [
    { status: "Planning", count: 2, projects: ["Product Launch", "Employee Training Program"] },
    {
      status: "In Progress",
      count: 4,
      projects: [
        "Website Redesign",
        "Mobile App Development",
        "Marketing Campaign",
        "Data Migration",
      ],
    },
    { status: "Final Review", count: 2, projects: ["CRM Implementation", "Security Audit"] },
    { status: "Completed", count: 1, projects: ["Office Renovation"] },
  ];

  const CustomTooltip = ({ active, payload }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const percentage = Math.round((data.value / totalProjects) * 100);
      return (
        <div className="bg-background border rounded-md p-2 shadow-sm">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            {data.value} ({percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full flex flex-col gap-0">
      <CardHeader className="pb-1 flex-shrink-0 py-0">
        <CardTitle className="text-base">Project Status</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 p-3 pt-0">
        <div className="h-[140px] w-full relative flex-shrink-0 mb-6">
          <ResponsiveContainer className="py-0" width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={55}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign="bottom"
                height={8}
                formatter={(value) => <span className="text-xs">{value}</span>}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center flex-col pointer-events-none mb-8">
            <div className="text-lg font-bold">{totalProjects}</div>
            <div className="text-xs text-muted-foreground">Total Projects</div>
          </div>
        </div>

        {detailed && (
          <div className="flex-1 mt-6 border rounded-md overflow-hidden min-h-0">
            <div className="overflow-y-auto h-full">
              <table className="w-full text-sm">
                <thead className="sticky top-0 bg-background border-b">
                  <tr>
                    <th className="text-left p-1.5 text-xs font-medium">Status</th>
                    <th className="text-center p-1.5 text-xs font-medium">Count</th>
                    <th className="text-left p-1.5 text-xs font-medium">Projects</th>
                  </tr>
                </thead>
                <tbody>
                  {detailedData.map((item) => (
                    <tr key={item.status} className="border-b last:border-0">
                      <td className="p-1.5 font-medium text-sm">{item.status}</td>
                      <td className="text-center p-1.5 font-medium text-sm">{item.count}</td>
                      <td className="p-1.5 text-xs text-muted-foreground leading-relaxed">
                        {item.projects.join(", ")}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
