"use client";

import { ClusterPortfolioHealth } from "./cluster-portfolio-health";
import { ProjectHealthMatrix } from "./cluster-projects-health-matrix";
import { ProjectProgressChart } from "./cluster-projects-progress-chart";
import { ProjectStatusDistribution } from "./cluster-projects-status-distribution";

export function ClusterStatusOverview() {
  return (
    <div className="space-y-6">
      {/* Portfolio Health Score */}
      <div className="flex flex-col gap-6">
        <ClusterPortfolioHealth />

        {/* Main Status Grid with Matched Heights */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Project Health Matrix - Takes 2 columns */}
          <div className="lg:col-span-2 h-[480px]">
            <ProjectHealthMatrix />
          </div>

          {/* Status Distribution - Takes 1 column with matching height */}
          <div className="lg:col-span-1 h-[480px]">
            <ProjectStatusDistribution detailed={true} />
          </div>
        </div>
      </div>

      {/* Progress Chart - Full width */}
      <div>
        <ProjectProgressChart detailed={true} />
      </div>
    </div>
  );
}
