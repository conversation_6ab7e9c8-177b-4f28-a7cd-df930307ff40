"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Input } from "@/components/ui";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { OrganizationProject } from "@/db/actions/cluster.action";
import {
  ArrowRight,
  ArrowUpRight,
  Calendar,
  CheckCircle2,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Filter,
  FolderOpen,
  Search,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
  useOrganizationClusters,
  useUserOrganizationRole,
  type OrganizationCluster,
} from "./use-organization-data";

interface OrganizationOverviewTableProps {
  currentClusterId?: string;
}

type SortField =
  | "title"
  | "status"
  | "businessUnit"
  | "progress"
  | "dueDate"
  | "value"
  | "budget"
  | "teams"
  | "tasks";
type SortDirection = "asc" | "desc";

// Flattened data structure for sorting and filtering
interface TableRow {
  id: string;
  type: "cluster" | "project" | "spacer";
  clusterId?: string;
  title: string;
  description: string;
  status: string;
  statusColor: string;
  businessUnit: string;
  progress: number;
  dueDate: string;
  value: number;
  budget: {
    spent: number;
    allocated: number;
  };
  tasks: {
    completed: number;
    total: number;
  };
  teams: number;
  members: Array<{ name: string; avatar?: string; initials: string }>;
  isCurrentCluster?: boolean;
  cluster?: OrganizationCluster;
  project?: OrganizationProject;
}

export function OrganizationTableContent({ currentClusterId }: OrganizationOverviewTableProps) {
  const { data: clustersData, error } = useOrganizationClusters();
  const { data: userRole } = useUserOrganizationRole();

  const [sortField, setSortField] = useState<SortField>("title");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedClusters, setExpandedClusters] = useState<Set<string>>(new Set());
  const hasSetInitialExpanded = useRef(false);

  // Set default expanded cluster when data loads (only once)
  useEffect(() => {
    if (currentClusterId && clustersData?.data && !hasSetInitialExpanded.current) {
      const matchingCluster = clustersData.data.find((c) => c.slug === currentClusterId);
      if (matchingCluster) {
        setExpandedClusters(new Set([matchingCluster.id]));
        hasSetInitialExpanded.current = true;
      }
    }
  }, [currentClusterId, clustersData?.data]);

  if (error) {
    return (
      <div className="space-y-4">
        <FolderOpen className="h-6 w-6 mx-auto text-muted-foreground mb-4" />
        <p className="text-muted-foreground">No clusters found</p>
      </div>
    );
  }

  const isAdmin = userRole?.data?.isAdmin || false;

  const toggleClusterExpansion = (clusterId: string) => {
    const newExpanded = new Set(expandedClusters);
    if (newExpanded.has(clusterId)) {
      newExpanded.delete(clusterId);
    } else {
      newExpanded.add(clusterId);
    }
    setExpandedClusters(newExpanded);
  };

  const expandAllClusters = () => {
    if (!clustersData?.data) return;
    const allClusterIds = clustersData.data.map((cluster) => cluster.id);
    setExpandedClusters(new Set(allClusterIds));
  };

  const collapseAllClusters = () => {
    setExpandedClusters(new Set());
  };

  const isAllExpanded = clustersData?.data
    ? clustersData.data.length > 0 &&
      clustersData.data.every((cluster) => expandedClusters.has(cluster.id))
    : false;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
      case "completed":
        return "bg-green-500";
      case "at risk":
        return "bg-orange-500";
      case "cancelled":
        return "bg-red-500";
      case "planning":
        return "bg-purple-500";
      case "in progress":
        return "bg-blue-500";
      case "final review":
        return "bg-purple-500"; // Keeping final review as purple like planning
      default:
        return "bg-gray-500";
    }
  };

  const getClusterStatus = (cluster: OrganizationCluster) => {
    const { aggregatedMetrics } = cluster;
    const completionRate =
      aggregatedMetrics.totalProjects > 0
        ? (aggregatedMetrics.completedProjects / aggregatedMetrics.totalProjects) * 100
        : 0;

    if (completionRate === 100) return "Completed";
    if (aggregatedMetrics.atRiskProjects > 0) return "At Risk";
    if (aggregatedMetrics.inProgressProjects > 0) return "In Progress";
    return "Planning";
  };

  const getProjectValue = (project: OrganizationProject) => {
    const roi = (project.progress / 100) * (project.budget.allocated / 1000);
    return Math.round(roi);
  };

  const getClusterValue = (cluster: OrganizationCluster) => {
    const totalAllocated = cluster.aggregatedMetrics.totalBudget.allocated;
    const avgProgress = cluster.aggregatedMetrics.progress;
    const value = (avgProgress / 100) * (totalAllocated / 1000);
    return Math.round(value);
  };

  const getValueBadgeVariant = (value: number) => {
    if (value >= 600) return "default"; // Huge value
    if (value >= 150) return "secondary"; // High value
    if (value >= 100) return "outline"; // Normal value
    return "destructive"; // Low value
  };

  const formatValueDisplay = (value: number) => {
    if (value >= 700) return "Huge";
    if (value >= 150) return "High";
    if (value >= 100) return "Normal";
    return "Low";
  };

  const getProjectTeams = () => {
    return Math.floor(Math.random() * 2) + 1; // Random number between 1 and 2
  };

  const getClusterUniqueTeams = (cluster: OrganizationCluster) => {
    // Calculate unique teams across all projects in the cluster
    // For now, return the number of projects as an approximation
    return cluster.projects.length;
  };

  const getClusterLatestDueDate = (cluster: OrganizationCluster) => {
    // Get the latest due date from all projects in the cluster
    const dueDates = cluster.projects
      .map((project) => project.dueDate)
      .filter((date) => date && date !== "-")
      .map((date) => new Date(date).getTime())
      .filter((time) => !isNaN(time));

    if (dueDates.length === 0) {
      return "-";
    }

    const latestTime = Math.max(...dueDates);
    return new Date(latestTime).toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  const mapToBusinessUnit = (department: string) => {
    // Map project departments to major business units in a large company
    const departmentMap: Record<string, string> = {
      Marketing: "Sales & Marketing",
      Engineering: "Technology",
      Product: "Technology",
      IT: "Technology",
      Sales: "Sales & Marketing",
      Operations: "Operations",
      HR: "Human Resources",
      Finance: "Finance & Accounting",
      Legal: "Legal & Compliance",
      Research: "Research & Development",
    };

    return departmentMap[department] || department;
  };

  const getClusterBusinessDivision = (cluster: OrganizationCluster) => {
    // Primary logic: bubble up from project business units to determine cluster business division
    const projectBusinessUnits = cluster.projects.map((p) => mapToBusinessUnit(p.department));
    const businessUnitCounts = projectBusinessUnits.reduce(
      (acc, bu) => {
        acc[bu] = (acc[bu] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Determine business division based on the predominant business units in the cluster
    const dominantBusinessUnits = Object.keys(businessUnitCounts);

    // E-Commerce Division - when we have Sales & Marketing + Technology combination (typical e-commerce)
    if (
      dominantBusinessUnits.includes("Sales & Marketing") &&
      dominantBusinessUnits.includes("Technology")
    ) {
      return "E-Commerce";
    }

    // Enterprise Solutions - when we have Technology + Finance/Operations (typical B2B/enterprise)
    if (
      dominantBusinessUnits.includes("Technology") &&
      (dominantBusinessUnits.includes("Finance & Accounting") ||
        dominantBusinessUnits.includes("Operations"))
    ) {
      return "Enterprise Solutions";
    }

    // Financial Services - finance-heavy clusters
    if (dominantBusinessUnits.includes("Finance & Accounting")) {
      return "Financial Services";
    }

    // Supply Chain - operations-focused clusters
    if (dominantBusinessUnits.includes("Operations")) {
      return "Supply Chain";
    }

    // Digital Products - primarily technology-focused
    if (dominantBusinessUnits.includes("Technology") && dominantBusinessUnits.length <= 2) {
      return "Digital Products";
    }

    // Customer Experience - HR + Marketing/Sales combination
    if (
      dominantBusinessUnits.includes("Human Resources") &&
      dominantBusinessUnits.includes("Sales & Marketing")
    ) {
      return "Customer Experience";
    }

    // Data & Analytics - technology + research combination
    if (
      dominantBusinessUnits.includes("Technology") &&
      dominantBusinessUnits.includes("Research & Development")
    ) {
      return "Data & Analytics";
    }

    // Growth & Revenue - primarily sales & marketing focused
    if (dominantBusinessUnits.includes("Sales & Marketing") && dominantBusinessUnits.length <= 2) {
      return "Growth & Revenue";
    }

    // Corporate Operations - mixed operational departments
    if (
      dominantBusinessUnits.some((bu) =>
        ["Human Resources", "Legal & Compliance", "Finance & Accounting"].includes(bu)
      )
    ) {
      return "Corporate Operations";
    }

    // Technology Platform - pure technology clusters
    if (dominantBusinessUnits.includes("Technology")) {
      return "Technology Platform";
    }

    // Fallback to cluster name patterns if project-based logic doesn't match
    const clusterName = cluster.name.toLowerCase();
    if (
      clusterName.includes("web") ||
      clusterName.includes("ecommerce") ||
      clusterName.includes("commerce")
    ) {
      return "E-Commerce";
    }
    if (clusterName.includes("enterprise") || clusterName.includes("b2b")) {
      return "Enterprise Solutions";
    }

    // Default fallback
    return "Strategic Initiatives";
  };

  const createTableRows = (): TableRow[] => {
    if (!clustersData?.data) return [];

    const rows: TableRow[] = [];

    // Sort clusters with current cluster at top, then apply field-based sorting
    const sortedClusters = [...clustersData.data].sort((a, b) => {
      const aIsCurrent = a.slug === currentClusterId;
      const bIsCurrent = b.slug === currentClusterId;

      // Always put current cluster first
      if (aIsCurrent && !bIsCurrent) return -1;
      if (!aIsCurrent && bIsCurrent) return 1;

      // Then apply field-based sorting for non-current clusters
      let comparison = 0;
      switch (sortField) {
        case "title":
          comparison = a.name.localeCompare(b.name);
          break;
        case "status":
          comparison = getClusterStatus(a).localeCompare(getClusterStatus(b));
          break;
        case "businessUnit":
          comparison = 0; // All clusters are "Organization"
          break;
        case "progress":
          comparison = a.aggregatedMetrics.progress - b.aggregatedMetrics.progress;
          break;
        case "dueDate":
          const aDate = getClusterLatestDueDate(a);
          const bDate = getClusterLatestDueDate(b);
          if (aDate === "-" && bDate === "-") comparison = 0;
          else if (aDate === "-") comparison = 1;
          else if (bDate === "-") comparison = -1;
          else comparison = new Date(aDate).getTime() - new Date(bDate).getTime();
          break;
        case "value":
          const aValue = getClusterValue(a);
          const bValue = getClusterValue(b);
          comparison = aValue - bValue;
          break;
        case "budget":
          comparison =
            a.aggregatedMetrics.totalBudget.spent - b.aggregatedMetrics.totalBudget.spent;
          break;
        case "tasks":
          const aTaskRate =
            a.aggregatedMetrics.totalTasks.total > 0
              ? a.aggregatedMetrics.totalTasks.completed / a.aggregatedMetrics.totalTasks.total
              : 0;
          const bTaskRate =
            b.aggregatedMetrics.totalTasks.total > 0
              ? b.aggregatedMetrics.totalTasks.completed / b.aggregatedMetrics.totalTasks.total
              : 0;
          comparison = aTaskRate - bTaskRate;
          break;
        case "teams":
          comparison = getClusterUniqueTeams(a) - getClusterUniqueTeams(b);
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortDirection === "asc" ? comparison : -comparison;
    });

    sortedClusters.forEach((cluster) => {
      // Add cluster row
      rows.push({
        id: cluster.id,
        type: "cluster",
        title: cluster.name,
        description: cluster.description || "",
        status: getClusterStatus(cluster),
        statusColor: getStatusColor(getClusterStatus(cluster)),
        businessUnit: getClusterBusinessDivision(cluster),
        progress: cluster.aggregatedMetrics.progress,
        dueDate: getClusterLatestDueDate(cluster),
        value: getClusterValue(cluster),
        budget: cluster.aggregatedMetrics.totalBudget,
        tasks: cluster.aggregatedMetrics.totalTasks,
        teams: getClusterUniqueTeams(cluster),
        members: [],
        isCurrentCluster: cluster.slug === currentClusterId,
        cluster,
      });

      // Add project rows immediately after their parent cluster if expanded
      if (expandedClusters.has(cluster.id)) {
        const projectRows = cluster.projects.map((project) => ({
          id: project.id,
          type: "project" as const,
          clusterId: cluster.id,
          title: project.name,
          description: project.description || "",
          status: project.status,
          statusColor: getStatusColor(project.status),
          businessUnit: mapToBusinessUnit(project.department),
          progress: project.progress,
          dueDate: project.dueDate,
          value: getProjectValue(project),
          budget: project.budget,
          tasks: project.tasks,
          teams: getProjectTeams(),
          members: project.members,
          project,
        }));

        // Sort projects within the cluster
        const sortedProjects = projectRows.sort((a, b) => {
          let comparison = 0;
          switch (sortField) {
            case "title":
              comparison = a.title.localeCompare(b.title);
              break;
            case "status":
              comparison = a.status.localeCompare(b.status);
              break;
            case "businessUnit":
              comparison = a.businessUnit.localeCompare(b.businessUnit);
              break;
            case "progress":
              comparison = a.progress - b.progress;
              break;
            case "dueDate":
              if (a.dueDate === "-" && b.dueDate === "-") return 0;
              if (a.dueDate === "-") return 1;
              if (b.dueDate === "-") return -1;
              comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
              break;
            case "value":
              comparison = a.value - b.value;
              break;
            case "budget":
              comparison = a.budget.spent - b.budget.spent;
              break;
            case "tasks":
              const aTaskRate = a.tasks.total > 0 ? a.tasks.completed / a.tasks.total : 0;
              const bTaskRate = b.tasks.total > 0 ? b.tasks.completed / b.tasks.total : 0;
              comparison = aTaskRate - bTaskRate;
              break;
            case "teams":
              comparison = a.teams - b.teams;
              break;
          }
          return sortDirection === "asc" ? comparison : -comparison;
        });

        rows.push(...sortedProjects);

        // Add spacing row after expanded cluster's projects
        if (sortedProjects.length > 0) {
          rows.push({
            id: `${cluster.id}-spacer`,
            type: "spacer" as const,
            title: "",
            description: "",
            status: "",
            statusColor: "",
            businessUnit: "",
            progress: 0,
            dueDate: "",
            value: 0,
            budget: { spent: 0, allocated: 0 },
            tasks: { completed: 0, total: 0 },
            teams: 0,
            members: [],
          });
        }
      }
    });

    return rows;
  };

  // Handle sort
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return <ChevronDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    );
  };

  const tableRows = createTableRows();

  const filteredRows = tableRows.filter(
    (row) =>
      row.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.businessUnit.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProjectClick = (project: OrganizationProject) => {
    toast.success(`Project: ${project.name}`, {
      description: `Business Unit: ${project.department} | Status: ${project.status}`,
      duration: 3000,
    });
  };

  if (!clustersData?.data?.length) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Organization Overview</h2>
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search projects and clusters..."
                className="w-full pl-8 h-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              className="h-8 gap-1"
              onClick={isAllExpanded ? collapseAllClusters : expandAllClusters}
            >
              {isAllExpanded ? (
                <>
                  <ChevronUp className="h-4 w-4" />
                  Collapse All
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4" />
                  Expand All
                </>
              )}
            </Button>
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button size="sm" className="h-8 gap-1">
              <ArrowRight className="h-4 w-4" />
              Manage Project
            </Button>
          </div>
        </div>

        <div className="rounded-md border">
          <Table aria-readonly={!isAdmin}>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[450px] max-w-[450px] pl-6">Cluster</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Business Unit</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Tasks</TableHead>
                <TableHead>Teams</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell colSpan={9} className="h-14 text-center">
                    <p className="h-8 bg-accent" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Organization Overview</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1"
            onClick={isAllExpanded ? collapseAllClusters : expandAllClusters}
          >
            {isAllExpanded ? (
              <>
                <ChevronUp className="h-4 w-4" />
                Collapse All
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4" />
                Expand All
              </>
            )}
          </Button>
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <div className="relative w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search clusters and projects..."
              className="w-full pl-8 h-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Link href="/home/<USER>">
            <Button size="sm" className="h-8 gap-1">
              <ArrowUpRight className="h-4 w-4" />
              Manage Project
            </Button>
          </Link>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[450px] max-w-[450px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort("title")}
                  className="flex items-center font-medium"
                >
                  Cluster {renderSortIcon("title")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("status")}
                  className="flex items-center font-medium"
                >
                  Status {renderSortIcon("status")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("businessUnit")}
                  className="flex items-center font-medium"
                >
                  Business Unit {renderSortIcon("businessUnit")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("progress")}
                  className="flex items-center font-medium"
                >
                  Progress {renderSortIcon("progress")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("dueDate")}
                  className="flex items-center font-medium"
                >
                  Due Date {renderSortIcon("dueDate")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("value")}
                  className="flex items-center font-medium"
                >
                  Value {renderSortIcon("value")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("budget")}
                  className="flex items-center font-medium"
                >
                  Budget {renderSortIcon("budget")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("teams")}
                  className="flex items-center font-medium"
                >
                  Teams {renderSortIcon("teams")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("tasks")}
                  className="flex items-center font-medium"
                >
                  Tasks {renderSortIcon("tasks")}
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRows.map((row) => {
              // Handle spacer rows
              if (row.type === "spacer") {
                return (
                  <TableRow key={row.id}>
                    <TableCell colSpan={9} className="h-8 p-0" />
                  </TableRow>
                );
              }

              return (
                <TableRow
                  key={row.id}
                  className={`${
                    row.type === "cluster"
                      ? `hover:bg-muted/50 cursor-pointer group ${
                          expandedClusters.has(row.id) ? "bg-gray-50 dark:bg-gray-900/50" : ""
                        }`
                      : "hover:bg-muted/30 cursor-pointer"
                  }`}
                  onClick={
                    row.type === "cluster"
                      ? () => toggleClusterExpansion(row.id)
                      : row.type === "project" && row.project
                        ? () => handleProjectClick(row.project!)
                        : undefined
                  }
                >
                  <TableCell className="font-medium w-[450px] max-w-[450px]">
                    <div className={`flex items-start ${row.type === "project" ? "pl-6" : ""}`}>
                      {row.type === "cluster" && (
                        <div className="h-6 w-6 pr-1 flex items-center justify-center">
                          {expandedClusters.has(row.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </div>
                      )}
                      {row.type === "project" && <div className="px-2">↳</div>}
                      <div>
                        <div className="flex items-center gap-2">
                          {row.title}
                          {row.isCurrentCluster && (
                            <Badge variant="outline" className="text-xs font-normal">
                              Current
                            </Badge>
                          )}
                        </div>
                        {row.description && (
                          <p className="text-xs text-muted-foreground truncate text-ellipsis text-wrap line-clamp-1">
                            {row.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${row.statusColor}`} />
                      <span>{row.status}</span>
                    </div>
                  </TableCell>
                  <TableCell>{row.businessUnit}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={row.progress} className="h-2 w-16" />
                      <span className="text-sm">{row.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {row.dueDate !== "-" && (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                        <span>
                          {new Date(row.dueDate).toLocaleDateString("en-US", {
                            day: "numeric",
                            month: "short",
                            year: "numeric",
                          })}
                        </span>
                      </div>
                    )}
                    {row.dueDate === "-" && <span className="text-muted-foreground">-</span>}
                  </TableCell>
                  <TableCell>
                    <Badge variant={getValueBadgeVariant(row.value)} className="font-medium">
                      {formatValueDisplay(row.value)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="font-medium">{formatCurrency(row.budget.spent)}</div>
                      <div className="text-xs text-muted-foreground">
                        of {formatCurrency(row.budget.allocated)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="flex justify-center pr-6 font-medium">{row.teams}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <CheckCircle2 className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>
                        {row.tasks.completed}/{row.tasks.total}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
