import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Activity,
  AlertTriangle,
  Calendar,
  CheckCircle2,
  Clock,
  FolderKanban,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";

export function ClusterMetricsOverview() {
  const metrics = [
    {
      title: "Total Projects",
      value: "9",
      icon: FolderKanban,
      trend: "+2 this quarter",
      trendUp: true,
      color: "text-blue-500",
    },
    {
      title: "In Progress",
      value: "4",
      icon: Activity,
      trend: "44% of total",
      trendUp: null,
      color: "text-yellow-500",
    },
    {
      title: "Completed",
      value: "1",
      icon: CheckCircle2,
      trend: "+1 this month",
      trendUp: true,
      color: "text-green-500",
    },
    {
      title: "At Risk",
      value: "2",
      icon: AlertTriangle,
      trend: "+1 from last week",
      trendUp: false,
      color: "text-red-500",
    },
    {
      title: "Team Members",
      value: "24",
      icon: Users,
      trend: "Across all projects",
      trendUp: null,
      color: "text-purple-500",
    },
    {
      title: "Avg. Completion",
      value: "62%",
      icon: Clock,
      trend: "+5% from last month",
      trendUp: true,
      color: "text-cyan-500",
    },
    {
      title: "Upcoming Deadlines",
      value: "5",
      icon: Calendar,
      trend: "Next 30 days",
      trendUp: null,
      color: "text-orange-500",
    },
  ];

  return (
    <div className="grid gap-3 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 py-2">
      {metrics.map((metric) => (
        <Card key={metric.title} className="overflow-hidden">
          <CardContent className="p-3">
            <div className="flex flex-col gap-1.5">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-muted-foreground truncate pr-1">
                  {metric.title}
                </span>
                <metric.icon className={`h-3.5 w-3.5 flex-shrink-0 ${metric.color}`} />
              </div>
              <div className="text-xl font-bold">{metric.value}</div>
              <div className="flex items-center text-xs">
                {metric.trendUp !== null && (
                  <Badge
                    variant="outline"
                    className={`mr-1 h-4 text-xs ${metric.trendUp ? "text-green-500" : "text-red-500"}`}
                  >
                    {metric.trendUp ? (
                      <TrendingUp className="h-2.5 w-2.5 mr-0.5" />
                    ) : (
                      <TrendingDown className="h-2.5 w-2.5 mr-0.5" />
                    )}
                  </Badge>
                )}
                <span className="text-muted-foreground text-xs truncate">{metric.trend}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
