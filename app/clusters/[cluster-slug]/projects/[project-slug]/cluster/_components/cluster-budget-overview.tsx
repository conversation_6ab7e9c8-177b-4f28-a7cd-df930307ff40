"use client";

import type React from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts";
import { NameType, Payload, ValueType } from "recharts/types/component/DefaultTooltipContent";

// Sample data for department budgets with consistent colors
const departmentBudgets = [
  {
    name: "Engineering",
    allocated: 1200000,
    spent: 780000,
    remaining: 420000,
    projects: 12,
    color: "#10b981", // green-500
  },
  {
    name: "Marketing",
    allocated: 800000,
    spent: 650000,
    remaining: 150000,
    projects: 8,
    color: "#f59e0b", // amber-500
  },
  {
    name: "Sales",
    allocated: 600000,
    spent: 450000,
    remaining: 150000,
    projects: 5,
    color: "#3b82f6", // blue-500
  },
  {
    name: "Product",
    allocated: 900000,
    spent: 500000,
    remaining: 400000,
    projects: 7,
    color: "#8b5cf6", // violet-500
  },
  {
    name: "HR",
    allocated: 300000,
    spent: 200000,
    remaining: 100000,
    projects: 3,
    color: "#ec4899", // pink-500
  },
  {
    name: "Finance",
    allocated: 250000,
    spent: 180000,
    remaining: 70000,
    projects: 2,
    color: "#6366f1", // indigo-500
  },
];

// Chart data
const chartData = departmentBudgets.map((dept) => ({
  name: dept.name,
  Allocated: dept.allocated,
  Spent: dept.spent,
  Remaining: dept.remaining,
  color: dept.color,
}));

export function ClusterBudgetOverview() {
  const [timeframe, setTimeframe] = useState("yearly");

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const calculatePercentage = (spent: number, allocated: number) => {
    return Math.round((spent / allocated) * 100);
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium mb-1">{label}</p>
          {payload.map((entry: Payload<ValueType, NameType>, index: number) => (
            <div key={`item-${index}`} className="flex items-center justify-between gap-4">
              <span style={{ color: entry.color }}>{entry.name}:</span>
              <span className="font-medium">{formatCurrency(entry.value as number)}</span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  // Define colors for the chart that match the department colors
  const allocatedColor = "#10b981"; // green-500
  const spentColor = "#f59e0b"; // amber-500
  const remainingColor = "#3b82f6"; // blue-500

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Budget Allocation & Utilization</h3>
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Select timeframe" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="quarterly">Quarterly</SelectItem>
            <SelectItem value="yearly">Yearly</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="w-full" style={{ height: "280px", maxWidth: "100%" }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 10, right: 20, left: 30, bottom: 40 }}
            barGap={0}
            barCategoryGap="12%"
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" tick={{ fontSize: 11 }} height={40} tickMargin={8} />
            <YAxis
              tickFormatter={(value) => `$${value / 1000}k`}
              width={70}
              tick={{ fontSize: 11 }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend
              verticalAlign="top"
              height={28}
              wrapperStyle={{ paddingTop: 0, bottom: 0 }}
              iconSize={12}
            />
            <Bar dataKey="Allocated" name="Allocated Budget" fill={allocatedColor} />
            <Bar dataKey="Spent" name="Spent Budget" fill={spentColor} />
            <Bar dataKey="Remaining" name="Remaining Budget" fill={remainingColor} />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="grid gap-3 grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
        {departmentBudgets.map((dept) => (
          <Card key={dept.name} className="overflow-hidden">
            <CardContent className="p-3">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-1.5">
                  <div className="w-2 h-2 rounded-full" style={{ backgroundColor: dept.color }} />
                  <h4 className="font-medium text-sm">{dept.name}</h4>
                </div>
                <span className="text-xs text-muted-foreground">{dept.projects} Projects</span>
              </div>
              <div className="flex justify-between items-center text-xs mb-1">
                <span>Utilization</span>
                <span className="font-medium">
                  {calculatePercentage(dept.spent, dept.allocated)}%
                </span>
              </div>
              <Progress
                value={calculatePercentage(dept.spent, dept.allocated)}
                className="h-1.5 mb-3"
                style={
                  {
                    "--progress-background": dept.color,
                  } as React.CSSProperties
                }
              />
              <div className="grid grid-cols-2 gap-1.5 text-xs">
                <div>
                  <p className="text-muted-foreground">Allocated</p>
                  <p className="font-medium text-xs">{formatCurrency(dept.allocated)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Spent</p>
                  <p className="font-medium text-xs">{formatCurrency(dept.spent)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Remaining</p>
                  <p className="font-medium text-xs">{formatCurrency(dept.remaining)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Burn Rate</p>
                  <p className="font-medium text-xs">
                    {formatCurrency(Math.round(dept.spent / 12))}/mo
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
