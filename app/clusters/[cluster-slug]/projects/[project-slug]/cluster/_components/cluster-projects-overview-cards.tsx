"use client";

import { <PERSON><PERSON>, Ava<PERSON>Fallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Calendar, CheckCircle2, Clock, MoreHorizontal, Star, Target, Users } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export function ProjectOverviewCards() {
  const [sortBy, setSortBy] = useState<"name" | "progress" | "deadline">("deadline");

  const projects = [
    {
      id: "website-redesign",
      title: "Website Redesign",
      description: "Redesign the company website with a modern look and improved user experience.",
      progress: 75,
      status: "In Progress",
      statusColor: "bg-yellow-500",
      dueDate: "May 15, 2023",
      startDate: "March 1, 2023",
      department: "Marketing",
      priority: "High",
      members: [
        { name: "<PERSON>", avatar: "/placeholder-user.jpg", initials: "J<PERSON>" },
        { name: "<PERSON>", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
        { name: "<PERSON> Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
      ],
      tasks: { completed: 15, total: 20 },
      milestones: [
        { title: "Design Approval", completed: true, date: "April 1, 2023" },
        { title: "Content Migration", completed: false, date: "May 1, 2023" },
        { title: "Launch", completed: false, date: "May 15, 2023" },
      ],
      isStarred: true,
    },
    {
      id: "mobile-app",
      title: "Mobile App Development",
      description: "Develop a cross-platform mobile application for iOS and Android.",
      progress: 40,
      status: "In Progress",
      statusColor: "bg-yellow-500",
      dueDate: "July 30, 2023",
      startDate: "April 1, 2023",
      department: "Engineering",
      priority: "High",
      members: [
        { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
        { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
        { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
        { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
      ],
      tasks: { completed: 8, total: 20 },
      milestones: [
        { title: "Requirements Gathering", completed: true, date: "April 15, 2023" },
        { title: "UI/UX Design", completed: false, date: "May 30, 2023" },
        { title: "Beta Release", completed: false, date: "June 30, 2023" },
        { title: "Launch", completed: false, date: "July 30, 2023" },
      ],
      isStarred: true,
    },
    {
      id: "marketing-campaign",
      title: "Marketing Campaign",
      description: "Plan and execute a comprehensive marketing campaign for Q2.",
      progress: 60,
      status: "In Progress",
      statusColor: "bg-yellow-500",
      dueDate: "June 1, 2023",
      startDate: "April 15, 2023",
      department: "Marketing",
      priority: "Medium",
      members: [
        { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
        { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
        { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
      ],
      tasks: { completed: 12, total: 20 },
      milestones: [
        { title: "Strategy Development", completed: true, date: "April 20, 2023" },
        { title: "Content Creation", completed: true, date: "May 10, 2023" },
        { title: "Campaign Launch", completed: false, date: "June 1, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "product-launch",
      title: "Product Launch",
      description: "Coordinate the launch of our new flagship product.",
      progress: 25,
      status: "Planning",
      statusColor: "bg-blue-500",
      dueDate: "August 15, 2023",
      startDate: "May 1, 2023",
      department: "Product",
      priority: "High",
      members: [
        { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
        { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
        { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
      ],
      tasks: { completed: 5, total: 20 },
      milestones: [
        { title: "Market Research", completed: true, date: "May 15, 2023" },
        { title: "Product Finalization", completed: false, date: "June 30, 2023" },
        { title: "Marketing Materials", completed: false, date: "July 15, 2023" },
        { title: "Launch Event", completed: false, date: "August 15, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "crm-implementation",
      title: "CRM Implementation",
      description: "Implement and configure a new CRM system for the sales team.",
      progress: 90,
      status: "Final Review",
      statusColor: "bg-purple-500",
      dueDate: "May 5, 2023",
      startDate: "February 1, 2023",
      department: "Sales",
      priority: "Medium",
      members: [
        { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
        { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
      ],
      tasks: { completed: 18, total: 20 },
      milestones: [
        { title: "Requirements Analysis", completed: true, date: "February 15, 2023" },
        { title: "System Configuration", completed: true, date: "March 30, 2023" },
        { title: "Data Migration", completed: true, date: "April 15, 2023" },
        { title: "User Training", completed: false, date: "May 5, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "office-renovation",
      title: "Office Renovation",
      description: "Coordinate the renovation of the main office space.",
      progress: 100,
      status: "Completed",
      statusColor: "bg-green-500",
      dueDate: "April 15, 2023",
      startDate: "January 15, 2023",
      department: "Operations",
      priority: "Low",
      members: [
        { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
        { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
        { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
      ],
      tasks: { completed: 20, total: 20 },
      milestones: [
        { title: "Design Approval", completed: true, date: "January 30, 2023" },
        { title: "Construction", completed: true, date: "March 15, 2023" },
        { title: "Furnishing", completed: true, date: "April 1, 2023" },
        { title: "Move-in", completed: true, date: "April 15, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "data-migration",
      title: "Data Migration",
      description: "Migrate data from legacy systems to the new cloud platform.",
      progress: 50,
      status: "In Progress",
      statusColor: "bg-yellow-500",
      dueDate: "June 30, 2023",
      startDate: "April 1, 2023",
      department: "Engineering",
      priority: "Medium",
      members: [
        { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
        { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
      ],
      tasks: { completed: 10, total: 20 },
      milestones: [
        { title: "Data Mapping", completed: true, date: "April 15, 2023" },
        { title: "Test Migration", completed: true, date: "May 15, 2023" },
        { title: "Full Migration", completed: false, date: "June 15, 2023" },
        { title: "Validation", completed: false, date: "June 30, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "security-audit",
      title: "Security Audit",
      description: "Conduct a comprehensive security audit of all systems.",
      progress: 80,
      status: "Final Review",
      statusColor: "bg-purple-500",
      dueDate: "May 20, 2023",
      startDate: "March 15, 2023",
      department: "IT",
      priority: "High",
      members: [
        { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
        { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
      ],
      tasks: { completed: 16, total: 20 },
      milestones: [
        { title: "Scope Definition", completed: true, date: "March 20, 2023" },
        { title: "Vulnerability Assessment", completed: true, date: "April 15, 2023" },
        { title: "Penetration Testing", completed: true, date: "May 5, 2023" },
        { title: "Final Report", completed: false, date: "May 20, 2023" },
      ],
      isStarred: false,
    },
    {
      id: "employee-training",
      title: "Employee Training Program",
      description: "Develop and implement a new employee training program.",
      progress: 30,
      status: "Planning",
      statusColor: "bg-blue-500",
      dueDate: "July 15, 2023",
      startDate: "May 1, 2023",
      department: "HR",
      priority: "Low",
      members: [
        { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
        { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
      ],
      tasks: { completed: 6, total: 20 },
      milestones: [
        { title: "Needs Assessment", completed: true, date: "May 15, 2023" },
        { title: "Curriculum Development", completed: false, date: "June 15, 2023" },
        { title: "Pilot Program", completed: false, date: "July 1, 2023" },
        { title: "Full Rollout", completed: false, date: "July 15, 2023" },
      ],
      isStarred: false,
    },
  ];

  // Sort projects based on the selected sort option
  const sortedProjects = [...projects].sort((a, b) => {
    if (sortBy === "name") {
      return a.title.localeCompare(b.title);
    } else if (sortBy === "progress") {
      return b.progress - a.progress;
    } else {
      // Sort by deadline (dueDate)
      return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
    }
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">All Projects</h2>
        <div className="flex gap-2">
          <Button
            variant={sortBy === "name" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("name")}
          >
            Sort by Name
          </Button>
          <Button
            variant={sortBy === "progress" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("progress")}
          >
            Sort by Progress
          </Button>
          <Button
            variant={sortBy === "deadline" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("deadline")}
          >
            Sort by Deadline
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sortedProjects.map((project) => (
          <Card key={project.id} className="overflow-hidden">
            <CardHeader className="p-3 pb-0">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-base">
                    <Link href={`/projects/${project.id}`} className="hover:underline">
                      {project.title}
                    </Link>
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-7 w-7">
                    {project.isStarred ? (
                      <Star className="h-3.5 w-3.5 fill-yellow-400 text-yellow-400" />
                    ) : (
                      <Star className="h-3.5 w-3.5" />
                    )}
                    <span className="sr-only">{project.isStarred ? "Unstar" : "Star"} project</span>
                  </Button>
                  <Button variant="ghost" size="icon" className="h-7 w-7">
                    <MoreHorizontal className="h-3.5 w-3.5" />
                    <span className="sr-only">More options</span>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-3">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`h-2 w-2 rounded-full ${project.statusColor}`} />
                    <span className="text-sm font-medium">{project.status}</span>
                  </div>
                  <Badge variant="outline" className="font-normal text-xs">
                    {project.department}
                  </Badge>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span className="font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">Due: {project.dueDate}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">
                      {project.members.length} Members
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">
                      {project.priority} Priority
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle2 className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">
                      {project.tasks.completed}/{project.tasks.total} Tasks
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Milestones</span>
                    <span className="text-xs text-muted-foreground">
                      {project.milestones.filter((m) => m.completed).length}/
                      {project.milestones.length} Completed
                    </span>
                  </div>
                  <div className="space-y-1">
                    {project.milestones.slice(0, 3).map((milestone, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <div
                            className={`h-1.5 w-1.5 rounded-full ${
                              milestone.completed ? "bg-green-500" : "bg-muted-foreground"
                            }`}
                          />
                          <span
                            className={
                              milestone.completed
                                ? "line-through text-muted-foreground text-xs"
                                : "text-xs"
                            }
                          >
                            {milestone.title}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">{milestone.date}</span>
                      </div>
                    ))}
                    {project.milestones.length > 3 && (
                      <div className="text-xs text-muted-foreground text-right">
                        +{project.milestones.length - 3} more
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex -space-x-2">
                    {project.members.slice(0, 3).map((member, index) => (
                      <Avatar key={index} className="h-7 w-7 border-2 border-background">
                        <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                        <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                      </Avatar>
                    ))}
                    {project.members.length > 3 && (
                      <Avatar className="h-7 w-7 border-2 border-background bg-muted">
                        <AvatarFallback className="text-xs">
                          +{project.members.length - 3}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span className="text-xs">
                      {project.startDate} - {project.dueDate}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
