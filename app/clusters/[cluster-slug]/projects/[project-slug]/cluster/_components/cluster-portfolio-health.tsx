"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp } from "lucide-react";

interface PortfolioHealthData {
  score: number;
  trend: number;
  status: "Excellent" | "Good" | "Fair" | "Poor";
}

interface PortfolioHealthProps {
  data?: PortfolioHealthData;
}

export function ClusterPortfolioHealth({
  data = { score: 78, trend: 5, status: "Good" },
}: PortfolioHealthProps) {
  const getHealthColor = (score: number) => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-yellow-500";
    return "text-red-500";
  };

  const getHealthBadgeVariant = (status: string) => {
    switch (status) {
      case "Excellent":
        return "default";
      case "Good":
        return "secondary";
      case "Fair":
        return "outline";
      case "Poor":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Project Portfolio Health</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={getHealthBadgeVariant(data.status)}>{data.status}</Badge>
            <Button variant="outline" size="sm" className="h-7 gap-1">
              <BarChart3 className="h-3.5 w-3.5" />
              View Details
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-8">
          {/* Health Score */}
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className={`text-3xl font-bold ${getHealthColor(data.score)}`}>
                {data.score}%
              </div>
              <div className="text-xs text-muted-foreground">Health Score</div>
            </div>
            <div className="h-12 w-px bg-border" />
          </div>

          {/* All Metrics in One Row */}
          <div className="flex items-center justify-between w-full flex-1">
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Projects on schedule</div>
              <div className="font-semibold">7 of 9</div>
              <div className="text-xs text-green-600">78%</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Budget utilization</div>
              <div className="font-semibold">$2.1M</div>
              <div className="text-xs text-muted-foreground">of $3.2M</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Quality score</div>
              <div className="font-semibold">4.2/5.0</div>
              <div className="text-xs text-green-600">84%</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Team satisfaction</div>
              <div className="font-semibold">85%</div>
              <div className="text-xs text-green-600">+3%</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Sprint velocity</div>
              <div className="font-semibold">92%</div>
              <div className="text-xs text-green-600">+8%</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Risk mitigation</div>
              <div className="font-semibold">73%</div>
              <div className="text-xs text-yellow-600">-2%</div>
            </div>

            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Next milestone</div>
              <div className="font-semibold">12 days</div>
              <div className="text-xs text-blue-600">Mobile App Beta</div>
            </div>
          </div>

          {/* Trend Indicator */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <div className="text-center">
              <div className="text-sm font-semibold text-green-500">+{data.trend}%</div>
              <div className="text-xs text-muted-foreground">vs last month</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
