"use client";

import { <PERSON><PERSON>, Avatar<PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Calendar,
  CheckCircle2,
  ChevronDown,
  ChevronUp,
  Filter,
  PlusCircle,
  Search,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

// Define project type
interface Member {
  name: string;
  avatar: string;
  initials: string;
}

interface Project {
  id: string;
  title: string;
  description: string;
  progress: number;
  status: string;
  statusColor: string;
  dueDate: string;
  department: string;
  priority: string;
  members: Member[];
  tasks: {
    completed: number;
    total: number;
  };
  isStarred: boolean;
}

// Sample project data (same as used in other components)
const projects: Project[] = [
  {
    id: "website-redesign",
    title: "Website Redesign",
    description: "Redesign the company website with a modern look and improved user experience.",
    progress: 75,
    status: "In Progress",
    statusColor: "bg-yellow-500",
    dueDate: "May 15, 2023",
    department: "Marketing",
    priority: "High",
    members: [
      { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
      { name: "Sarah <PERSON>", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
      { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
    ],
    tasks: { completed: 15, total: 20 },
    isStarred: true,
  },
  {
    id: "mobile-app",
    title: "Mobile App Development",
    description: "Develop a cross-platform mobile application for iOS and Android.",
    progress: 40,
    status: "In Progress",
    statusColor: "bg-yellow-500",
    dueDate: "July 30, 2023",
    department: "Engineering",
    priority: "High",
    members: [
      { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
      { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
      { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
      { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
    ],
    tasks: { completed: 8, total: 20 },
    isStarred: true,
  },
  {
    id: "marketing-campaign",
    title: "Marketing Campaign",
    description: "Plan and execute a comprehensive marketing campaign for Q2.",
    progress: 60,
    status: "In Progress",
    statusColor: "bg-yellow-500",
    dueDate: "June 1, 2023",
    department: "Marketing",
    priority: "Medium",
    members: [
      { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
      { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
      { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
    ],
    tasks: { completed: 12, total: 20 },
    isStarred: false,
  },
  {
    id: "product-launch",
    title: "Product Launch",
    description: "Coordinate the launch of our new flagship product.",
    progress: 25,
    status: "Planning",
    statusColor: "bg-blue-500",
    dueDate: "August 15, 2023",
    department: "Product",
    priority: "High",
    members: [
      { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
      { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
      { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
    ],
    tasks: { completed: 5, total: 20 },
    isStarred: false,
  },
  {
    id: "crm-implementation",
    title: "CRM Implementation",
    description: "Implement and configure a new CRM system for the sales team.",
    progress: 90,
    status: "Final Review",
    statusColor: "bg-purple-500",
    dueDate: "May 5, 2023",
    department: "Sales",
    priority: "Medium",
    members: [
      { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
      { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
    ],
    tasks: { completed: 18, total: 20 },
    isStarred: false,
  },
  {
    id: "office-renovation",
    title: "Office Renovation",
    description: "Coordinate the renovation of the main office space.",
    progress: 100,
    status: "Completed",
    statusColor: "bg-green-500",
    dueDate: "April 15, 2023",
    department: "Operations",
    priority: "Low",
    members: [
      { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
      { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
      { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
    ],
    tasks: { completed: 20, total: 20 },
    isStarred: false,
  },
  {
    id: "data-migration",
    title: "Data Migration",
    description: "Migrate data from legacy systems to the new cloud platform.",
    progress: 50,
    status: "In Progress",
    statusColor: "bg-yellow-500",
    dueDate: "June 30, 2023",
    department: "Engineering",
    priority: "Medium",
    members: [
      { name: "Emily Wilson", avatar: "/placeholder.svg?height=32&width=32", initials: "EW" },
      { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
    ],
    tasks: { completed: 10, total: 20 },
    isStarred: false,
  },
  {
    id: "security-audit",
    title: "Security Audit",
    description: "Conduct a comprehensive security audit of all systems.",
    progress: 80,
    status: "Final Review",
    statusColor: "bg-purple-500",
    dueDate: "May 20, 2023",
    department: "IT",
    priority: "High",
    members: [
      { name: "David Kim", avatar: "/placeholder.svg?height=32&width=32", initials: "DK" },
      { name: "Michael Chen", avatar: "/placeholder.svg?height=32&width=32", initials: "MC" },
    ],
    tasks: { completed: 16, total: 20 },
    isStarred: false,
  },
  {
    id: "employee-training",
    title: "Employee Training Program",
    description: "Develop and implement a new employee training program.",
    progress: 30,
    status: "Planning",
    statusColor: "bg-blue-500",
    dueDate: "July 15, 2023",
    department: "HR",
    priority: "Low",
    members: [
      { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", initials: "SJ" },
      { name: "Lisa Wang", avatar: "/placeholder.svg?height=32&width=32", initials: "LW" },
    ],
    tasks: { completed: 6, total: 20 },
    isStarred: false,
  },
];

type SortField = "title" | "status" | "department" | "priority" | "progress" | "dueDate" | "tasks";
type SortDirection = "asc" | "desc";

export function ProjectsTable() {
  const [sortField, setSortField] = useState<SortField>("title");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [searchTerm, setSearchTerm] = useState("");

  // Handle sort
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Sort projects
  const sortedProjects = [...projects].sort((a, b) => {
    let comparison = 0;

    switch (sortField) {
      case "title":
        comparison = a.title.localeCompare(b.title);
        break;
      case "status":
        comparison = a.status.localeCompare(b.status);
        break;
      case "department":
        comparison = a.department.localeCompare(b.department);
        break;
      case "priority":
        const priorityOrder = { High: 1, Medium: 2, Low: 3 };
        comparison =
          priorityOrder[a.priority as keyof typeof priorityOrder] -
          priorityOrder[b.priority as keyof typeof priorityOrder];
        break;
      case "progress":
        comparison = a.progress - b.progress;
        break;
      case "dueDate":
        comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        break;
      case "tasks":
        comparison = a.tasks.completed / a.tasks.total - b.tasks.completed / b.tasks.total;
        break;
    }

    return sortDirection === "asc" ? comparison : -comparison;
  });

  // Filter projects by search term
  const filteredProjects = sortedProjects.filter(
    (project) =>
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return <ChevronDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Projects Table</h2>
        <div className="flex items-center gap-2">
          <div className="relative w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search projects..."
              className="w-full pl-8 h-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" className="h-8 gap-1">
            <PlusCircle className="h-4 w-4" />
            New Project
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort("title")}
                  className="flex items-center font-medium"
                >
                  Project {renderSortIcon("title")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("status")}
                  className="flex items-center font-medium"
                >
                  Status {renderSortIcon("status")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("department")}
                  className="flex items-center font-medium"
                >
                  Department {renderSortIcon("department")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("progress")}
                  className="flex items-center font-medium"
                >
                  Progress {renderSortIcon("progress")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("dueDate")}
                  className="flex items-center font-medium"
                >
                  Due Date {renderSortIcon("dueDate")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("priority")}
                  className="flex items-center font-medium"
                >
                  Priority {renderSortIcon("priority")}
                </Button>
              </TableHead>
              <TableHead>Team</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("tasks")}
                  className="flex items-center font-medium"
                >
                  Tasks {renderSortIcon("tasks")}
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProjects.map((project) => (
              <TableRow key={project.id}>
                <TableCell className="font-medium">
                  <Link href={`/projects/${project.id}`} className="pl-3 hover:underline">
                    {project.title}
                  </Link>
                  <p className="text-xs text-muted-foreground line-clamp-1 mt-1 pl-3">
                    {project.description}
                  </p>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className={`h-2 w-2 rounded-full ${project.statusColor}`} />
                    <span>{project.status}</span>
                  </div>
                </TableCell>
                <TableCell>{project.department}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Progress value={project.progress} className="h-2 w-16" />
                    <span className="text-sm">{project.progress}%</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                    <span>{project.dueDate}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      project.priority === "High"
                        ? "destructive"
                        : project.priority === "Medium"
                        ? "default"
                        : "outline"
                    }
                  >
                    {project.priority}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex -space-x-2">
                    {project.members.slice(0, 3).map((member, index) => (
                      <Avatar key={index} className="h-6 w-6 border-2 border-background">
                        <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                        <AvatarFallback>{member.initials}</AvatarFallback>
                      </Avatar>
                    ))}
                    {project.members.length > 3 && (
                      <Avatar className="h-6 w-6 border-2 border-background bg-muted">
                        <AvatarFallback>+{project.members.length - 3}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <CheckCircle2 className="h-3.5 w-3.5 text-muted-foreground" />
                    <span>
                      {project.tasks.completed}/{project.tasks.total}
                    </span>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
