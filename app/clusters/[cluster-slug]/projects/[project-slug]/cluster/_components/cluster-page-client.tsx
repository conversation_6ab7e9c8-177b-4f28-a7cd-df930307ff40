"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { useState } from "react";
import { ClusterBudgetOverview } from "./cluster-budget-overview";
import { ClusterMetricsOverview } from "./cluster-metrics-overview";
import { ProjectOverviewCards } from "./cluster-projects-overview-cards";
import { ProjectsTable } from "./cluster-projects-table";
import { ClusterStatusOverview } from "./cluster-status-overview";
import { OrganizationTableContent } from "./organization-overview-table";

interface ClusterPageClientProps {
  currentClusterSlug: string;
}

export function ClusterPageClient({ currentClusterSlug }: ClusterPageClientProps) {
  const [showAnalytics, setShowAnalytics] = useState(false);

  return (
    <div className="flex flex-col h-[calc(100vh-10rem)]">
      <header className="flex flex-col items-center justify-between flex-shrink-0 px-6">
        <div className="flex items-center justify-between w-full">
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold">Cluster</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              High-level overview of the clusters and their projects
            </p>
          </div>
          <Button variant="outline" size="sm" onClick={() => setShowAnalytics(!showAnalytics)}>
            {showAnalytics ? (
              <>
                <ChevronUpIcon className="w-4 h-4" /> Hide tasks analytics
              </>
            ) : (
              <>
                <ChevronDownIcon className="w-4 h-4" /> See tasks analytics
              </>
            )}
          </Button>
        </div>
        <div className="w-full">
          <AnimatePresence>
            {showAnalytics && (
              <motion.div
                layout={false} // Disable layout animation which can be expensive
                initial={{
                  height: 0,
                  opacity: 0,
                }}
                animate={{
                  height: "auto",
                  opacity: 1,
                  transition: {
                    height: { type: "spring", stiffness: 500, damping: 40 },
                    opacity: { duration: 0.15, delay: 0.05 },
                  },
                }}
                exit={{
                  height: 0,
                  opacity: 0,
                  transition: {
                    height: { type: "spring", stiffness: 500, damping: 40 },
                    opacity: { duration: 0.15 },
                  },
                }}
                style={{
                  willChange: "transform, opacity, height",
                  transform: "translateZ(0)", // Hardware acceleration
                  backfaceVisibility: "hidden", // Additional hardware acceleration
                  perspective: 1000, // Improve performance for 3D transforms
                  contain: "paint layout", // Optimize repaints
                }}
                className="overflow-hidden transform-gpu"
              >
                <ClusterMetricsOverview />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </header>
      <div className="flex-1 p-6 pt-4 space-y-6">
        <Tabs defaultValue="organization">
          <TabsList>
            <TabsTrigger value="organization">Organization</TabsTrigger>
            <TabsTrigger value="Projects">Projects</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="organization" className="mt-2 overflow-y-scroll">
            <div className="grid gap-6 overflow-y-scroll h-[calc(100vh-13rem)]">
              <OrganizationTableContent currentClusterId={currentClusterSlug} />
            </div>
          </TabsContent>

          <TabsContent value="Projects" className="mt-2  overflow-y-scroll">
            <div className="grid gap-6 overflow-y-scroll h-[calc(100vh-13rem)]">
              <ProjectsTable />
            </div>
          </TabsContent>

          <TabsContent value="projects" className="mt-2">
            <div className="grid gap-6">
              <ProjectOverviewCards />
            </div>
          </TabsContent>

          <TabsContent value="status" className="mt-2 overflow-y-scroll">
            <div className="overflow-y-scroll h-[calc(100vh-13rem)]">
              <ClusterStatusOverview />
            </div>
          </TabsContent>

          <TabsContent value="budget" className="mt-2">
            <div className="grid gap-6">
              <ClusterBudgetOverview />
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="mt-2">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <h1 className="col-span-3 text-center text-2xl font-semibold mt-32">Timeline</h1>
              <p className="col-span-3 text-center text-sm text-gray-500">Coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
