import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ChevronDownIcon } from "lucide-react";

export function ClusterPageSkeleton() {
  return (
    <div className="flex flex-col h-[calc(100vh-10rem)]">
      <header className="flex flex-col items-center justify-between flex-shrink-0 px-6">
        <div className="flex items-center justify-between w-full">
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold">Cluster</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              High-level overview of the clusters and their projects
            </p>
          </div>
          <Button variant="outline" size="sm" disabled>
            <ChevronDownIcon className="w-4 h-4" /> See tasks analytics
          </Button>
        </div>
      </header>
      <div className="flex-1 p-6 pt-4 space-y-6">
        <Tabs defaultValue="organization">
          <TabsList>
            <TabsTrigger value="organization">Organization</TabsTrigger>
            <TabsTrigger value="Projects">Projects</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="organization" className="mt-2 overflow-y-scroll">
            <div className="grid gap-6 overflow-y-scroll h-[calc(100vh-13rem)]">
              {/* Organization table skeleton */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-6 w-48" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-8 w-32" />
                  </div>
                </div>
                
                <div className="rounded-md border">
                  <div className="p-4">
                    {/* Table header skeleton */}
                    <div className="grid grid-cols-9 gap-4 mb-4">
                      {Array.from({ length: 9 }).map((_, i) => (
                        <Skeleton key={i} className="h-4 w-full" />
                      ))}
                    </div>
                    
                    {/* Table rows skeleton */}
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div key={i} className="grid grid-cols-9 gap-4 mb-3">
                        {Array.from({ length: 9 }).map((_, j) => (
                          <Skeleton key={j} className="h-8 w-full" />
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="Projects" className="mt-2 overflow-y-scroll">
            <div className="grid gap-6 overflow-y-scroll h-[calc(100vh-13rem)]">
              <Skeleton className="h-64 w-full" />
            </div>
          </TabsContent>

          <TabsContent value="projects" className="mt-2">
            <div className="grid gap-6">
              <Skeleton className="h-48 w-full" />
            </div>
          </TabsContent>

          <TabsContent value="status" className="mt-2 overflow-y-scroll">
            <div className="overflow-y-scroll h-[calc(100vh-13rem)]">
              <Skeleton className="h-64 w-full" />
            </div>
          </TabsContent>

          <TabsContent value="budget" className="mt-2">
            <div className="grid gap-6">
              <Skeleton className="h-48 w-full" />
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="mt-2">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <h1 className="col-span-3 text-center text-2xl font-semibold mt-32">Timeline</h1>
              <p className="col-span-3 text-center text-sm text-gray-500">Coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
