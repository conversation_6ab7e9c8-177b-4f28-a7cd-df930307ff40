import { getTenantId } from "@/db/tenant.db";
import { PageParams } from "@/types/next.types";
import { Suspense } from "react";
import { ClusterPageClient } from "./_components/cluster-page-client";
import { ClusterPageSkeleton } from "./_components/cluster-page-skeleton";

type ClusterPageProps = PageParams<{
  "cluster-slug": string;
  "project-slug": string;
}>;

// Server component for instant navigation with minimal server-side data
export default async function ClusterPage(props: ClusterPageProps) {
  // Only fetch essential data for routing - everything else handled by client
  const tenantId = await getTenantId();
  if (!tenantId) {
    throw new Error("Tenant ID not found");
  }

  const params = await props.params;

  return (
    <Suspense fallback={<ClusterPageSkeleton />}>
      <ClusterPageClient currentClusterSlug={params["cluster-slug"]} />
    </Suspense>
  );
}
