import BannerCountdown from "@/components/origin-ui/banner-countdown";
import { ProjectListSkeleton, UserProfileSkeleton } from "@/components/skeletons";
import { Suspense } from "react";
import { UserAgenda, UserProfile, UserTasks } from "./_components";
import { HumanRessources } from "./_components/home-human-ressources";
import { ProjectListClient } from "./_components/home-project-list-client";

export default function HomePage() {
  return (
    <div className="flex flex-col w-full h-full overflow-auto p-6">
      <BannerCountdown />
      <div className="flex flex-col xl:flex-row justify-between xl:h-[350px] space-y-4 xl:space-y-4 gap-4">
        {/* User Profile Card - Human Resources - Agenda */}
        <Suspense fallback={<UserProfileSkeleton />}>
          <UserProfile />
        </Suspense>
        <HumanRessources />
        <UserAgenda />
      </div>
      {/* User Tasks & Pull Requests - Projects List */}
      <div className="flex flex-col justify-between xl:flex-row gap-4 w-full flex-1 min-h-0">
        <UserTasks />
        <Suspense fallback={<ProjectListSkeleton />}>
          <ProjectListClient />
        </Suspense>
      </div>
    </div>
  );
}
