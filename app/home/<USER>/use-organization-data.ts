"use client";

import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery } from "@tanstack/react-query";
import {
  Cluster,
  ClusterProject,
} from "../organization/_components/_clusters_components/clusters-types";

/**
 * Hook to fetch organization clusters with React Query caching
 * Uses organization cache config since clusters data changes very rarely
 */
export function useOrganizationClusters() {
  return useQuery({
    queryKey: ["home", "organization", "clusters"],
    queryFn: async () => {
      const response = await fetch("/api/organization/clusters");
      if (!response.ok) {
        throw new Error("Failed to fetch organization clusters");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch organization clusters");
      }
      return result.data;
    },
    ...cacheConfigs.organization, // 2 hours stale time - clusters data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Hook to fetch organization projects with React Query caching
 * Uses organization cache config since projects data changes very rarely
 */
export function useOrganizationProjects() {
  return useQuery({
    queryKey: ["home", "organization", "projects"],
    queryFn: async () => {
      const response = await fetch("/api/organization/projects");
      if (!response.ok) {
        throw new Error("Failed to fetch organization projects");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch organization projects");
      }
      return result.data;
    },
    ...cacheConfigs.organization, // 2 hours stale time - projects data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Combined hook for organization clusters page data
 */
export function useOrganizationClustersData() {
  const clustersQuery = useOrganizationClusters();

  const formattedData = clustersQuery.data
    ? {
        // Format clusters for the UI
        clusters: clustersQuery.data.map((cluster: Cluster) => ({
          id: cluster.id,
          name: cluster.name,
          description: cluster.description,
          slug: cluster.slug || "",
          projects: cluster.projects.map((project: ClusterProject) => ({
            id: project.id,
            name: project.name,
            description: project.description,
            slug: project.slug || "",
            status: project.status || "active",
          })),
        })) satisfies Cluster[],

        // Create a flat list of all projects for the add project dialog
        availableProjects: Array.from(
          new Map(
            clustersQuery.data.flatMap((cluster: Cluster) =>
              cluster.projects.map((project: ClusterProject) => [
                project.id,
                {
                  id: project.id,
                  name: project.name,
                  description: project.description,
                  slug: project.slug || "",
                  status: project.status || "active",
                },
              ])
            )
          ).values()
        ) as ClusterProject[],
      }
    : { clusters: [], availableProjects: [] };

  return {
    ...formattedData,
    isLoading: clustersQuery.isLoading,
    isError: clustersQuery.isError,
    error: clustersQuery.error,
    clustersQuery,
  };
}

/**
 * Combined hook for organization projects page data
 */
export function useOrganizationProjectsData() {
  const projectsQuery = useOrganizationProjects();

  return {
    projects: projectsQuery.data?.projects || [],
    availableTeams: projectsQuery.data?.availableTeams || [],
    isLoading: projectsQuery.isLoading,
    isError: projectsQuery.isError,
    error: projectsQuery.error,
    projectsQuery,
  };
}
