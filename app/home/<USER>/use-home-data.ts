"use client";

import { getUserTeamProjectsAction } from "@/db/actions/get-user-team-projects.action";
import { getCurrentOrganizationAction } from "@/db/actions/organization.action";
import { useQuery } from "@tanstack/react-query";

// Query keys for cache management (scoped to prevent conflicts)
export const homeQueryKeys = {
  all: ["home"] as const,
  userTeamProjects: () => [...homeQueryKeys.all, "user-team-projects"] as const,
  // Use home-scoped organization key to prevent conflicts with project org data
  currentOrganization: () => [...homeQueryKeys.all, "organization"] as const,
};

/**
 * Hook to fetch user team projects with React Query
 */
export function useUserTeamProjects() {
  return useQuery({
    queryKey: homeQueryKeys.userTeamProjects(),
    queryFn: async () => {
      const clusters = await getUserTeamProjectsAction();

      // Transform the data to include cluster information in projects
      const allProjects =
        clusters?.flatMap((cluster) =>
          cluster.projects.map((project) => ({
            ...project,
            clusterName: cluster.name,
            clusterSlug: cluster.slug,
            clusterId: cluster.id,
            clusterTenantId: cluster.tenant_id,
            clusterDescription: cluster.description,
          }))
        ) || [];

      return { clusters, allProjects };
    },
    staleTime: 60 * 60 * 1000, // 1 hour - project list changes rarely, increased for better navigation UX
    gcTime: 4 * 60 * 60 * 1000, // 4 hours - longer retention for navigation
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

/**
 * Hook to fetch current organization with React Query
 */
export function useCurrentOrganization() {
  return useQuery({
    queryKey: homeQueryKeys.currentOrganization(),
    queryFn: async () => {
      return await getCurrentOrganizationAction();
    },
    staleTime: 4 * 60 * 60 * 1000, // 4 hours - organization data very rarely changes, increased for navigation UX
    gcTime: 8 * 60 * 60 * 1000, // 8 hours - very long retention for stable data
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

/**
 * Combined hook for home page data
 */
export function useHomeData() {
  const userTeamProjectsQuery = useUserTeamProjects();
  const currentOrganizationQuery = useCurrentOrganization();

  return {
    // User team projects
    clusters: userTeamProjectsQuery.data?.clusters,
    allProjects: userTeamProjectsQuery.data?.allProjects,
    isProjectsLoading: userTeamProjectsQuery.isLoading,
    projectsError: userTeamProjectsQuery.error,

    // Current organization
    organization: currentOrganizationQuery.data,
    isOrganizationLoading: currentOrganizationQuery.isLoading,
    organizationError: currentOrganizationQuery.error,

    // Combined loading state
    isLoading: userTeamProjectsQuery.isLoading || currentOrganizationQuery.isLoading,

    // Refetch functions
    refetchProjects: userTeamProjectsQuery.refetch,
    refetchOrganization: currentOrganizationQuery.refetch,
  };
}
