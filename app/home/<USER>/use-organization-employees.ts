"use client";

import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to fetch organization employees with React Query caching
 * Uses organization cache config since employees data changes rarely
 */
export function useOrganizationEmployees() {
  return useQuery({
    queryKey: ["home", "organization", "employees"],
    queryFn: async () => {
      const response = await fetch("/api/organization/employees");
      if (!response.ok) {
        throw new Error("Failed to fetch organization employees");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch organization employees");
      }
      return result.data;
    },
    ...cacheConfigs.organization, // 2 hours stale time - employees data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Combined hook for organization employees page data
 */
export function useOrganizationEmployeesData() {
  const employeesQuery = useOrganizationEmployees();

  return {
    employees: employeesQuery.data?.employees || [],
    organization: employeesQuery.data?.organization || null,
    isAdmin: employeesQuery.data?.isAdmin || false,
    isLoading: employeesQuery.isLoading,
    isError: employeesQuery.isError,
    error: employeesQuery.error,
    employeesQuery,
  };
}
