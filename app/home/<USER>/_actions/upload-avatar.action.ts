"use server";

import { getUserId } from "@/db/user.db";
import { createClient } from "@/lib/supabase/server";
import { randomUUID } from "crypto";

/**
 * Upload an avatar image to Supabase Storage
 */
export async function uploadAvatar(formData: FormData): Promise<{ url?: string; error?: string }> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return { error: "User not authenticated" };
    }

    const file = formData.get("avatar") as File;
    if (!file) {
      return { error: "No file provided" };
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      return { error: "File must be an image" };
    }

    // Limit file size (2MB)
    if (file.size > 2 * 1024 * 1024) {
      return { error: "File size must be less than 2MB" };
    }

    const supabase = await createClient();

    // Create a unique file name - Notice the format is userId-randomId.extension
    // This is important for the RLS policy that checks if auth.uid() = SPLIT_PART(name, '-', 1)
    const fileExt = file.name.split(".").pop();
    const fileName = `${userId}-${randomUUID()}.${fileExt}`;

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage.from("profiles").upload(fileName, file, {
      cacheControl: "3600",
      upsert: true,
    });

    if (uploadError) {
      console.info("Error uploading avatar:", uploadError);
      return { error: uploadError.message };
    }

    // Get the public URL
    const { data: urlData } = supabase.storage.from("profiles").getPublicUrl(fileName);

    return { url: urlData.publicUrl };
  } catch (error) {
    console.info("Error in uploadAvatar action:", error);
    return {
      error: error instanceof Error ? error.message : "Failed to upload avatar",
    };
  }
}
