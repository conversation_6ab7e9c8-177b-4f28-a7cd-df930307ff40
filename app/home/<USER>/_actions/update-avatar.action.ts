"use server";

import { getUserId } from "@/db/user.db";
import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";

/**
 * Update the user's avatar URL
 */
export async function updateAvatar(
  avatarUrl: string | null
): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return { success: false, error: "User not authenticated" };
    }

    const supabase = await createClient();

    const { error } = await supabase
      .from("profiles")
      .update({
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", userId);

    if (error) {
      console.info("Error updating avatar:", error);
      return { success: false, error: error.message };
    }

    // Revalidate the profile page to show updated data
    revalidatePath("/home/<USER>");

    return { success: true };
  } catch (error) {
    console.info("Error in updateAvatar action:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update avatar",
    };
  }
}
