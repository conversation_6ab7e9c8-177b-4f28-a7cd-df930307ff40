"use server";

import { getUserId, getUserIdSafe } from "@/db/user.db";
import { createClient } from "@/lib/supabase/server";
import { UserProfileDB } from "@/types/user.types";
import { revalidatePath } from "next/cache";
import { connection } from "next/server";
import { z } from "zod";

const ProfileSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").optional(),
  position: z.string().optional(),
  department: z.string().optional(),
  location: z.string().optional(),
  avatar_url: z.string().url().optional().nullable(),
});

export type ProfileFormValues = z.infer<typeof ProfileSchema>;

/**
 * Update a user's profile
 */
export async function updateProfile(
  formData: ProfileFormValues
): Promise<{ success: boolean; error?: string }> {
  try {
    const validatedData = ProfileSchema.parse(formData);

    const userId = await getUserId();
    if (!userId) {
      return { success: false, error: "User not authenticated" };
    }

    const supabase = await createClient();

    // Calculate the full name once
    const fullName = `${validatedData.first_name} ${validatedData.last_name}`;

    // Update the profiles table
    const { error: profileError } = await supabase
      .from("profiles")
      .update({
        ...validatedData,
        full_name: fullName,
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", userId);

    if (profileError) {
      console.info("Error updating profile:", profileError);
      return { success: false, error: profileError.message };
    }

    // Also update the display_name in auth.users table
    const { error: authError } = await supabase.auth.updateUser({
      data: {
        display_name: fullName,
        first_name: validatedData.first_name,
        last_name: validatedData.last_name,
      },
    });

    if (authError) {
      console.info("Error updating auth user:", authError);
      return { success: false, error: authError.message };
    }

    // Revalidate the profile page to show updated data
    revalidatePath("/home/<USER>");

    return { success: true };
  } catch (error) {
    console.info("Error in updateProfile action:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => `${e.path}: ${e.message}`).join(", "),
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update profile",
    };
  }
}

/**
 * Get the current user's profile
 * Safe version that doesn't redirect - for use in both server and client components
 */
export async function getUserProfile(): Promise<UserProfileDB | null> {
  await connection();
  try {
    // Use the safe version that doesn't redirect
    const { userId, authenticated } = await getUserIdSafe();
    if (!authenticated || !userId) return null;

    const supabase = await createClient();

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("user_id", userId)
      .single();

    if (error) {
      console.info("Error fetching profile:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.info("Error getting user profile:", error);
    return null;
  }
}
