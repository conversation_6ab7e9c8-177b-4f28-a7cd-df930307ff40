"use client";

import { ProjectCardSkeleton } from "@/components/skeletons";
import { ProjectTableSkeleton } from "@/components/skeletons/home-project-table-skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Project } from "@/types/cluster-project.types";
import { CircleIcon, LayoutGrid, LayoutList } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { SingleProjectCard } from "./home-project-card";

// View toggle button component
export function ProjectViewToggle({
  isCardView,
  onToggle,
}: {
  isCardView: boolean;
  onToggle: () => void;
}) {
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={onToggle}
      title={isCardView ? "Switch to list view" : "Switch to card view"}
    >
      {isCardView ? <LayoutList className="h-4 w-4" /> : <LayoutGrid className="h-4 w-4" />}
    </Button>
  );
}

// Projects view container with toggle state
export function ProjectsView({
  projects,
  isCardView = true,
}: {
  projects: (Project & {
    clusterName: string;
    clusterSlug: string;
    clusterId: string;
    clusterTenantId: string;
    clusterDescription: string | null;
  })[];
  isCardView?: boolean;
}) {
  return (
    <div>
      {isCardView ? (
        <AllProjectsCardView projects={projects} />
      ) : (
        <AllProjectsCompactView projects={projects} />
      )}
    </div>
  );
}

// Card view for projects
function AllProjectsCardView({
  projects,
}: {
  projects: (Project & {
    clusterName: string;
    clusterSlug: string;
    clusterId: string;
    clusterTenantId: string;
    clusterDescription: string | null;
  })[];
}) {
  return (
    <div className="relative w-full overflow-hidden">
      <ScrollArea className="w-full" type="always">
        <div className="flex flex-row xl:flex-row gap-4 pb-2 min-w-full w-max">
          {projects.map((project) => {
            // Create a cluster object that matches the ClusterWithProjects interface
            const cluster = {
              id: project.clusterId,
              name: project.clusterName,
              slug: project.clusterSlug,
              tenant_id: project.clusterTenantId,
              description: project.clusterDescription,
              projects: [], // Empty array as we don't need this for SingleProjectCard
            };

            return (
              <Suspense key={project.id} fallback={<ProjectCardSkeleton />}>
                <SingleProjectCard project={project} cluster={cluster} />
              </Suspense>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" className="hidden" />
      </ScrollArea>
    </div>
  );
}

// Compact view for projects with table rows that load as they're ready
function AllProjectsCompactView({
  projects,
}: {
  projects: (Project & {
    clusterName: string;
    clusterSlug: string;
    clusterId: string;
    clusterTenantId: string;
    clusterDescription: string | null;
  })[];
}) {
  return (
    <Suspense fallback={<ProjectTableSkeleton />}>
      <div className="w-full">
        <div className="rounded-md border">
          <table className="w-full divide-y divide-border">
            <thead>
              <tr className="bg-muted/50">
                <th className="px-4 py-3 text-left text-sm font-medium">Project</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Cluster</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Your Teams</th>
                <th className="px-6 py-3 text-left text-sm font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {projects.map((project) => (
                <ProjectTableRow key={project.id} project={project} />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </Suspense>
  );
}

// Extracted ProjectTableRow component to use with Suspense
function ProjectTableRow({
  project,
}: {
  project: Project & {
    clusterName?: string;
    clusterSlug?: string;
    clusterId?: string;
    clusterTenantId?: string;
    clusterDescription?: string | null;
  };
}) {
  // Create a cluster object for consistency
  const cluster = {
    id: project.clusterId || "",
    name: project.clusterName || "",
    slug: project.clusterSlug || "",
    tenant_id: project.clusterTenantId || "",
    description: project.clusterDescription || null,
    projects: [],
  };

  // Get status colors
  const statusColors = getStatusColor(project.status);

  return (
    <tr className="hover:bg-muted/30">
      <td className="px-4 py-3 text-sm font-medium">{project.name}</td>
      <td className="px-4 py-3 text-sm">
        <Badge variant="outline" className="bg-secondary/30">
          {cluster.name}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm">
        <span className="flex items-center">
          <CircleIcon className={`h-2 w-2 mr-2 ${statusColors.icon}`} />
          <span className="text-sm">{project.status}</span>
        </span>
      </td>
      <td className="px-4 py-3 text-sm">
        <div className="flex flex-wrap gap-1 max-w-xs">
          {project.teams && project.teams.length > 0 ? (
            project.teams.slice(0, 2).map((team) => (
              <Badge key={team.id} variant="outline" className="bg-primary/10">
                {team.name}
              </Badge>
            ))
          ) : (
            <span className="text-muted-foreground">None</span>
          )}

          {project.teams && project.teams.length > 2 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="bg-primary/10">
                  +{project.teams.length - 2}
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="p-2">
                <div className="flex flex-col gap-1 text-xs">
                  {project.teams.slice(2).map((team) => (
                    <div key={team.id}>{team.name}</div>
                  ))}
                </div>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </td>
      <td className="px-4 py-3 text-sm text-right">
        <Button asChild variant="default" size="sm">
          <Link href={`/clusters/${cluster.slug}/projects/${project.slug}/dashboard`}>
            View Project
          </Link>
        </Button>
      </td>
    </tr>
  );
}

// Status color utility function
function getStatusColor(status: string) {
  switch (status?.toLowerCase()) {
    case "active":
      return {
        icon: "text-emerald-500 fill-emerald-500 dark:text-emerald-400 dark:fill-emerald-400",
        bg: "bg-emerald-50 dark:bg-emerald-900",
        text: "text-emerald-700 dark:text-emerald-300",
        ring: "ring-emerald-600/20 dark:ring-emerald-400/20",
      };
    case "planning":
      return {
        icon: "text-blue-500 fill-blue-500 dark:text-blue-400 dark:fill-blue-400",
        bg: "bg-blue-50 dark:bg-blue-900",
        text: "text-blue-700 dark:text-blue-300",
        ring: "ring-blue-600/20 dark:ring-blue-400/20",
      };
    default:
      return {
        icon: "text-slate-500 fill-slate-500 dark:text-slate-400 dark:fill-slate-400",
        bg: "bg-slate-50 dark:bg-slate-900",
        text: "text-slate-700 dark:text-slate-300",
        ring: "ring-slate-600/20 dark:ring-slate-400/20",
      };
  }
}
