"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { CalendarClockIcon, CircleDotIcon } from "lucide-react";

type Priority = "High" | "Medium" | "Low";

type DeadlineItem = {
  title: string;
  category: string;
  priority: Priority;
  dueDate: string;
  completionPercentage: number;
};

const priorityBadgeColors: Record<Priority, string> = {
  High: "border-red-200 bg-red-50 text-red-600 dark:border-red-900 dark:bg-red-950/30 dark:text-red-400",
  Medium:
    "border-amber-200 bg-amber-50 text-amber-600 dark:border-amber-900 dark:bg-amber-950/30 dark:text-amber-400",
  Low: "border-green-200 bg-green-50 text-green-600 dark:border-green-900 dark:bg-green-950/30 dark:text-green-400",
};

export function UpcomingDeadlines() {
  const deadlines: DeadlineItem[] = [
    {
      title: "Finalize homepage design",
      category: "Website Redesign",
      priority: "High",
      dueDate: "Tomorrow, 5:00 PM",
      completionPercentage: 85,
    },
    {
      title: "Complete user authentication flow",
      category: "Mobile App Development",
      priority: "Medium",
      dueDate: "May 2, 2023",
      completionPercentage: 60,
    },
    {
      title: "Create social media content calendar",
      category: "Marketing Campaign",
      priority: "Medium",
      dueDate: "May 5, 2023",
      completionPercentage: 30,
    },
    {
      title: "Prepare Q2 budget report",
      category: "Finance",
      priority: "Low",
      dueDate: "May 10, 2023",
      completionPercentage: 10,
    },
    {
      title: "Draft press release",
      category: "Product Launch",
      priority: "Low",
      dueDate: "May 15, 2023",
      completionPercentage: 0,
    },
  ];

  return (
    <Card className="w-full p-4">
      <h2 className="text-xl font-semibold mb-3">Upcoming Deadlines</h2>
      <div className="flex flex-col gap-2">
        {deadlines.map((deadline, index) => (
          <DeadlineCard key={index} deadline={deadline} />
        ))}
      </div>
    </Card>
  );
}

function DeadlineCard({ deadline }: { deadline: DeadlineItem }) {
  return (
    <div className="border border-border rounded-lg p-3 hover:shadow-sm">
      <div className="flex items-start justify-between gap-2 mb-2">
        <h3 className="font-medium">{deadline.title}</h3>
        <div className="flex items-center">
          <CalendarClockIcon className="h-3 w-3 text-muted-foreground mr-1" />
          <span className="text-xs text-muted-foreground">{deadline.dueDate}</span>
        </div>
      </div>

      <div className="flex gap-2 mb-2">
        <Badge variant="outline" className="text-xs py-0 h-5 border-muted bg-muted/50">
          {deadline.category}
        </Badge>
        <Badge
          variant="outline"
          className={cn("text-xs py-0 h-5", priorityBadgeColors[deadline.priority])}
        >
          <CircleDotIcon className="h-2 w-2 mr-1" />
          {deadline.priority}
        </Badge>
      </div>

      <div className="flex items-center gap-2">
        <div className="h-1.5 bg-muted rounded-full overflow-hidden flex-grow">
          <div
            className={cn(
              "h-full rounded-full",
              deadline.completionPercentage >= 80
                ? "bg-green-500"
                : deadline.completionPercentage >= 40
                ? "bg-amber-500"
                : "bg-primary"
            )}
            style={{ width: `${deadline.completionPercentage}%` }}
          />
        </div>
        <span className="text-xs font-medium">{deadline.completionPercentage}%</span>
      </div>
    </div>
  );
}
