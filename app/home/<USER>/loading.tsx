import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header skeleton */}
      <div className="flex flex-col gap-2">
        <Skeleton className="h-6 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
      </div>

      {/* Stats cards skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="flex flex-col gap-2 py-4">
              <Skeleton className="h-4 w-1/2 mb-2" />
              <Skeleton className="h-6 w-1/3" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs skeleton */}
      <div className="flex gap-4 mt-2">
        {["Overview", "Projects", "Finance", "People", "Budget", "Timeline"].map((tab, i) => (
          <Skeleton key={tab + i} className="h-8 w-24 rounded-md" />
        ))}
      </div>

      {/* Table skeleton */}
      <div className="mt-4">
        <Card>
          <CardContent className="p-0">
            {/* Table header */}
            <div className="flex items-center px-6 py-3 border-b">
              <Skeleton className="h-5 w-40 mr-4" />
              <Skeleton className="h-8 w-64 ml-auto" />
            </div>
            {/* Table rows */}
            <div>
              {Array.from({ length: 9 }).map((_, i) => (
                <div key={i} className="flex items-center px-6 py-4 border-b last:border-b-0 gap-4">
                  <Skeleton className="h-4 w-8" /> {/* Index */}
                  <Skeleton className="h-4 w-56" /> {/* Project Title */}
                  <Skeleton className="h-4 w-24" /> {/* Status */}
                  <Skeleton className="h-4 w-40" /> {/* Description */}
                  <Skeleton className="h-4 w-16" /> {/* Progress */}
                  <Skeleton className="h-4 w-16" /> {/* Budget */}
                  <Skeleton className="h-4 w-16" /> {/* Spent */}
                  <Skeleton className="h-4 w-16" /> {/* Remaining */}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
