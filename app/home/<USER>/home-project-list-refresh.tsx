"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import { RefreshCcw } from "lucide-react";
import { useState } from "react";

export function ProjectListRefresh() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // FIXED: Use targeted invalidation to prevent clearing project caches
      await Promise.all([
        // Only invalidate home-specific data
        queryClient.invalidateQueries({
          queryKey: ["home"], // Invalidates all home-scoped data
          exact: false, // Include all home sub-keys like ["home", "user-team-projects"]
        }),
        // Optionally refresh user projects for project switcher without forcing refetch
        queryClient.invalidateQueries({
          queryKey: ["user", "projects"],
          refetchType: "none", // Mark stale but don't force immediate refetch
        }),
        // Keep organization data fresh but don't clear project-specific org cache
        queryClient.invalidateQueries({
          queryKey: ["home", "organization"],
          refetchType: "none", // Mark stale but preserve for project navigation
        }),
      ]);
    } catch (error) {
      console.info("Failed to refresh home data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Button
      variant="ghost"
      onClick={handleRefresh}
      className="text-muted-foreground"
      disabled={isRefreshing}
    >
      <RefreshCcw className={`w-4 h-4 lg:mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
      <span className="hidden md:block">{isRefreshing ? "Refreshing..." : "Refresh"}</span>
    </Button>
  );
}
