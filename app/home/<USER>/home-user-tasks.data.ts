export type TaskItemType = {
  tag: string;
  description: string;
  status: string;
  bgColor: string;
};

export const TASK_ITEMS: TaskItemType[] = [
  {
    tag: "Docs",
    description: "File structure documentation for the project",
    status: "Not started",
    bgColor: "bg-gray-50 dark:bg-gray-900",
  },
  {
    tag: "Feature",
    description: "Implement authentication flow",
    status: "In progress",
    bgColor: "bg-blue-50 dark:bg-blue-900",
  },
  {
    tag: "Bug",
    description: "Fix API integration issues",
    status: "Blocked",
    bgColor: "bg-red-50 dark:bg-red-900",
  },
  {
    tag: "Task",
    description: "Implement authentication flow",
    status: "In progress",
    bgColor: "bg-purple-50 dark:bg-purple-900",
  },
  {
    tag: "Docs",
    description: "File structure documentation for the project",
    status: "Not started",
    bgColor: "bg-gray-50 dark:bg-gray-900",
  },
];

export const PR_ITEMS: TaskItemType[] = [
  {
    tag: "Pending",
    description: "Feature: Add user dashboard",
    status: "Pending",
    bgColor: "bg-yellow-50 dark:bg-yellow-900",
  },
  {
    tag: "Pending",
    description: "Fix: Resolve navigation issues",
    status: "Pending",
    bgColor: "bg-yellow-50 dark:bg-yellow-900",
  },
  {
    tag: "To Merge",
    description: "Feature: Add user dashboard",
    status: "Merge Ready",
    bgColor: "bg-green-50 dark:bg-green-900",
  },
];
