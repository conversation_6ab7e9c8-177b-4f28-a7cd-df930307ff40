import { Card } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  CalendarClock,
  CalendarDays,
  Clock,
  ExternalLink,
  Laptop,
  Receipt,
  Stethoscope,
} from "lucide-react";

export function HumanRessources() {
  return (
    <Card className="flex flex-col xl:flex-row justify-between p-6 w-full shadow-none">
      <div className="flex items-center w-full h-full">
        <div className="flex flex-col justify-between h-full w-full">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">My HR Profile</h3>
            <Button variant="outline" size="sm">
              <span className="text-xs">Open HR Platform</span>
              <ExternalLink className="w-3 h-3 ml-1" />
            </Button>
          </div>
          <ViewOptions />

          <div className="flex flex-row items-center justify-evenly w-full gap-8 p-4 border rounded-lg text-center">
            <div className="flex flex-col items-center justify-center">
              <span className="text-xl font-semibold">12</span>
              <span className="mt-1 text-tiny md:text-xs text-muted-foreground">Annual Leave</span>
            </div>
            <Separator orientation="vertical" className="data-[orientation=vertical]:h-8 my-auto" />
            <div className="flex flex-col items-center justify-center">
              <span className="text-xl font-semibold">3</span>
              <span className="mt-1 text-tiny md:text-xs text-muted-foreground">Sick Days</span>
            </div>
            <Separator orientation="vertical" className="data-[orientation=vertical]:h-8 my-auto" />
            <div className="flex flex-col items-center justify-center">
              <span className="text-xl font-semibold">7</span>
              <span className="mt-1 text-tiny md:text-xs text-muted-foreground">Overtime</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

function ViewOptions() {
  return (
    <div className="grid w-full grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-1 my-3">
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <CalendarDays className="w-4 h-4" />
        <span className="text-xs hidden md:block">Leave Request</span>
      </Button>
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <Stethoscope className="w-4 h-4" />
        <span className="text-xs hidden md:block">Sick Day</span>
      </Button>
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <Receipt className="w-4 h-4" />
        <span className="text-xs hidden md:block">Reimbursement</span>
      </Button>
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <Clock className="w-4 h-4" />
        <span className="text-xs hidden md:block">Overtime</span>
      </Button>
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <Laptop className="w-4 h-4" />
        <span className="text-xs hidden md:block">Equipment Request</span>
      </Button>
      <Button
        variant="ghost"
        className="flex flex-col items-center h-14 py-1.5 bg-muted/70 dark:bg-muted/30"
      >
        <CalendarClock className="w-4 h-4" />
        <span className="text-xs hidden md:block">Schedule Meeting</span>
      </Button>
    </div>
  );
}
