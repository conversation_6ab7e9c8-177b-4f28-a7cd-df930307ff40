"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Building2, Copy } from "lucide-react";
import { toast } from "sonner";

type OrganizationInfoProps = {
  id: string;
  name: string;
  subdomain_id: string;
};

export function OrganizationInfo({ id, name, subdomain_id }: OrganizationInfoProps) {
  const copyId = () => {
    navigator.clipboard.writeText(id);
    toast.success("Organization ID copied to clipboard");
  };

  return (
    <div className="flex items-center justify-between gap-4 p-4 bg-card rounded-lg border mb-4">
      <div className="flex items-center gap-3">
        <div className="p-2 rounded-md bg-primary/10">
          <Building2 className="h-4 w-4 text-primary" />
        </div>
        <div>
          <h3 className="font-medium text-sm">{name}</h3>
          <p className="text-xs text-muted-foreground">{subdomain_id}</p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="h-6">
          <span className="text-xs font-medium">{id}</span>
        </Badge>
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={copyId}>
          <Copy className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
