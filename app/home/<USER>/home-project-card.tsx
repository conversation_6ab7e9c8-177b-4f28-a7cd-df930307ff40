"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { ClusterWithProjects, Project } from "@/types/cluster-project.types";
import { Team } from "@/types/team.types";
import { CalendarIcon, CircleIcon, ListTodoIcon, UserIcon, Users2Icon } from "lucide-react";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

// Function to determine the color for a given status
function getStatusColor(status: string) {
  switch (status?.toLowerCase()) {
    case "active":
      return {
        icon: "text-emerald-500 fill-emerald-500 dark:text-emerald-400 dark:fill-emerald-400",
        bg: "bg-emerald-50 dark:bg-emerald-900",
        text: "text-emerald-700 dark:text-emerald-300",
        ring: "ring-emerald-600/20 dark:ring-emerald-400/20",
      };
    case "planning":
      return {
        icon: "text-blue-500 fill-blue-500 dark:text-blue-400 dark:fill-blue-400",
        bg: "bg-blue-50 dark:bg-blue-900",
        text: "text-blue-700 dark:text-blue-300",
        ring: "ring-blue-600/20 dark:ring-blue-400/20",
      };
    case "completed":
      return {
        icon: "text-green-500 fill-green-500 dark:text-green-400 dark:fill-green-400",
        bg: "bg-green-50 dark:bg-green-900",
        text: "text-green-700 dark:text-green-300",
        ring: "ring-green-600/20 dark:ring-green-400/20",
      };
    case "archived":
      return {
        icon: "text-gray-500 fill-gray-500 dark:text-gray-400 dark:fill-gray-400",
        bg: "bg-gray-50 dark:bg-gray-900",
        text: "text-gray-700 dark:text-gray-300",
        ring: "ring-gray-600/20 dark:ring-gray-400/20",
      };
    case "cancelled":
      return {
        icon: "text-red-500 fill-red-500 dark:text-red-400 dark:fill-red-400",
        bg: "bg-red-50 dark:bg-red-900",
        text: "text-red-700 dark:text-red-300",
        ring: "ring-red-600/20 dark:ring-red-400/20",
      };
    default:
      return {
        icon: "text-slate-500 fill-slate-500 dark:text-slate-400 dark:fill-slate-400",
        bg: "bg-slate-50 dark:bg-slate-900",
        text: "text-slate-700 dark:text-slate-300",
        ring: "ring-slate-600/20 dark:ring-slate-400/20",
      };
  }
}

// Progress bar component
function MultiSegmentProgress({
  todoPercentage,
  inProgressPercentage,
  donePercentage,
}: {
  todoPercentage: number;
  inProgressPercentage: number;
  donePercentage: number;
}) {
  return (
    <div className="relative h-0.75 mt-1 w-full overflow-hidden rounded-full bg-gray-100">
      {/* Todo segment */}
      <div
        className="absolute h-full bg-gray-300 left-0 top-0 rounded-l-full"
        style={{ width: `${todoPercentage}%` }}
      />
      {/* In Progress segment */}
      <div
        className="absolute h-full bg-blue-400"
        style={{
          left: `${todoPercentage}%`,
          width: `${inProgressPercentage}%`,
        }}
      />
      {/* Done segment */}
      <div
        className="absolute h-full bg-green-500 left-0 top-0 rounded-r-full"
        style={{
          left: `${todoPercentage + inProgressPercentage}%`,
          width: `${donePercentage}%`,
        }}
      />
    </div>
  );
}

// The SingleProjectCard component
export function SingleProjectCard({
  project,
  cluster,
}: {
  project: Project;
  cluster: ClusterWithProjects;
}) {
  // These would normally come from your project data
  const todoPercentage = 55;
  const inProgressPercentage = 25;
  const donePercentage = 20;
  const statusColors = getStatusColor(project.status);

  // Reference for the teams container to measure its height
  const teamsContainerRef = useRef<HTMLDivElement>(null);
  const [visibleTeams, setVisibleTeams] = useState<Team[]>([]);
  const [hiddenTeams, setHiddenTeams] = useState<Team[]>([]);

  // Effect to handle team overflow
  useEffect(() => {
    if (!teamsContainerRef.current || !project.teams || project.teams.length === 0) return;

    // Clone the original teams
    const allTeams = [...project.teams];
    let visibleCount = 0;
    let containerHeight = 0;
    const lineHeight = 28; // Approximate height of a badge + margin

    // Reset the container to ensure proper measurement
    teamsContainerRef.current.innerHTML = "";

    // Create temporary elements to measure
    for (let i = 0; i < allTeams.length; i++) {
      const tempBadge = document.createElement("span");
      tempBadge.className = "inline-block m-0.5 px-2 py-0.5 bg-primary/10 text-xs rounded-lg";
      tempBadge.textContent = allTeams[i].name;
      teamsContainerRef.current.appendChild(tempBadge);

      // Check if we've exceeded one line
      containerHeight = teamsContainerRef.current.offsetHeight;
      if (containerHeight > lineHeight) {
        // We've exceeded one line, revert and stop adding
        teamsContainerRef.current.removeChild(tempBadge);
        visibleCount = i;
        break;
      }
    }

    // If all teams fit, show them all
    if (containerHeight <= lineHeight || visibleCount === 0) {
      setVisibleTeams(allTeams);
      setHiddenTeams([]);
    } else {
      // Otherwise, split into visible and hidden teams
      setVisibleTeams(allTeams.slice(0, visibleCount));
      setHiddenTeams(allTeams.slice(visibleCount));
    }

    // Clear the container, as React will handle the actual rendering
    teamsContainerRef.current.innerHTML = "";
  }, [project.teams]);

  return (
    <Card className="flex flex-col justify-between w-[350px] h-full flex-shrink-0">
      <CardHeader>
        <div className="flex items-center justify-between pb-2">
          <CardTitle className="text-lg">{project.name}</CardTitle>
          <div className="flex -mr-2">
            {/* Cluster and Project Slugs information */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="mt-1 text-xs bg-secondary/30">
                  {cluster.name}
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="p-2">
                <div className="grid gap-1 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Cluster Slug:</span>
                    <span className="ml-2">{cluster.slug}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Project Slug:</span>
                    <span className="ml-2">{project.slug}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Tenant ID:</span>
                    <span className="ml-2">{project.tenant_id}</span>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        <CardDescription className="line-clamp-2">
          {project.description ||
            "Redesign the company website with a modern look and improved user experience."}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col gap-4">
        {/* Status */}
        <div className="flex items-center">
          <span className="flex items-center px-2 py-0.5 border rounded-full">
            <CircleIcon className={`h-2 w-2 mr-2 ${statusColors.icon}`} />
            <span className="text-xs font-medium">{project.status}</span>
          </span>
          <div className="ml-auto flex items-center">
            <CalendarIcon className="h-3 w-3 mr-1 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">May 15, 2023</span>
          </div>
        </div>

        {/* Progress */}
        <div>
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <div className="flex gap-2 items-center">
              <span className="inline-flex items-center">
                <span className="h-2 w-2 rounded-full bg-gray-300 mr-1"></span>
                <span className="text-xs">{todoPercentage}%</span>
              </span>
              <span className="inline-flex items-center">
                <span className="h-2 w-2 rounded-full bg-blue-300 mr-1"></span>
                <span className="text-xs">{inProgressPercentage}%</span>
              </span>
              <span className="inline-flex items-center">
                <span className="h-2 w-2 rounded-full bg-green-300 mr-1"></span>
                <span className="text-xs">{donePercentage}%</span>
              </span>
            </div>
          </div>
          <MultiSegmentProgress
            todoPercentage={todoPercentage}
            inProgressPercentage={inProgressPercentage}
            donePercentage={donePercentage}
          />
        </div>

        {/* Team Count */}
        <div className="flex items-center justify-between">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-muted/30 hover:bg-muted/50 px-2">
                  <Users2Icon className="h-3.5 w-3.5 mr-1.5" />
                  <span className="text-xs font-medium">
                    {project.teams?.length || 0} {project.teams?.length === 1 ? "team" : "teams"}
                  </span>
                </Badge>
                <Badge variant="outline" className="bg-muted/30 hover:bg-muted/50 px-2">
                  <UserIcon className="h-3.5 w-3.5 mr-1.5" />
                  <span className="text-xs font-medium">
                    {project.teams?.length
                      ? `${project.teams.length * 3}-${project.teams.length * 5}`
                      : 0}{" "}
                    members
                  </span>
                </Badge>
              </div>
            </TooltipTrigger>
            <TooltipContent className="p-2">
              <div className="flex flex-col gap-1 text-xs">
                {project.teams?.length > 0 ? (
                  project.teams.map((team) => <div key={team.id}>Team: {team.name}</div>)
                ) : (
                  <div>No teams assigned</div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>

          {/* Tasks and PRs count */}
          <div className="flex items-center text-sm text-muted-foreground">
            <ListTodoIcon className="h-3 w-3 mr-1" />
            <span>15/20</span>
          </div>
        </div>

        {/* View Project Button */}
        <div>
          <Link
            href={`/clusters/${cluster.slug}/projects/${project.slug}/dashboard`}
            className="w-full"
          >
            <Button variant="default" className="w-full">
              View Project
            </Button>
          </Link>
        </div>

        {/* User's teams for this project */}
        {project.teams && project.teams.length > 0 && (
          <div className="pt-2 border-t border-border">
            <h4 className="text-xs font-semibold my-1.5">Your Teams for This Project:</h4>
            <div ref={teamsContainerRef} className="flex flex-wrap gap-1.5">
              {visibleTeams.map((team) => (
                <Badge key={team.id} variant="outline" className="bg-primary/10 text-xs">
                  {team.name}
                </Badge>
              ))}

              {hiddenTeams.length > 0 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="outline" className="bg-primary/10 text-xs font-medium">
                      +{hiddenTeams.length}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent className="p-2">
                    <div className="flex flex-col gap-1 text-xs">
                      <h5 className="font-semibold">Additional Teams:</h5>
                      {hiddenTeams.map((team) => (
                        <div key={team.id}>{team.name}</div>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
