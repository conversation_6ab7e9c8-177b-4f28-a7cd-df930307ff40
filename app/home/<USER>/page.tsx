import { Card } from "@/components/ui/card";
import { Suspense } from "react";
import { ProfileContainer } from "./_components/profile-container";
import { ProfileSkeleton } from "./_components/profile-skeleton";

export default async function AccountPage() {
  return (
    <div className="flex flex-col w-full max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Profile Settings</h1>

      <Card className="p-6">
        <Suspense fallback={<ProfileSkeleton />}>
          <ProfileContainer />
        </Suspense>
      </Card>
    </div>
  );
}
