import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { Suspense } from "react";
import TeamContent from "./_components/teams-content";

export const dynamic = "force-dynamic";

export default function TeamsPage() {
  return (
    <div className="flex flex-col h-[calc(100vh-80px)] p-8 pt-2">
      <Suspense fallback={<TeamSkeleton />}>
        <TeamContent />
      </Suspense>
    </div>
  );
}
