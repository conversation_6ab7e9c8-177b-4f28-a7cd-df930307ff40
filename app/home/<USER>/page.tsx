import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ClusterBudgetOverview } from "./_components/cluster-budget-overview";
import { ClusterMetricsOverview } from "./_components/cluster-metrics-overview";
import { ProjectHealthMatrix } from "./_components/cluster-projects-health-matrix";
import { ProjectOverviewCards } from "./_components/cluster-projects-overview-cards";
import { ProjectProgressChart } from "./_components/cluster-projects-progress-chart";
import { ProjectStatusDistribution } from "./_components/cluster-projects-status-distribution";
import { ProjectsTable } from "./_components/cluster-projects-table";

export default function ClusterPage() {
  return (
    <div className="flex flex-col h-[calc(100vh-10rem)] overflow-y-scroll">
      <div className="flex-1 p-6 space-y-6">
        <ClusterMetricsOverview />

        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-2">
            <div className="grid gap-6 overflow-y-scroll h-[calc(100vh-25rem)]">
              <ProjectsTable />
            </div>
          </TabsContent>

          <TabsContent value="projects" className="mt-2">
            <div className="grid gap-6">
              <ProjectOverviewCards />
            </div>
          </TabsContent>

          <TabsContent value="status" className="mt-2">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="lg:col-span-2">
                <ProjectHealthMatrix />
              </div>
              <div className="lg:col-span-1">
                <ProjectStatusDistribution detailed={true} />
              </div>
              <div className="lg:col-span-3">
                <ProjectProgressChart detailed={true} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="budget" className="mt-2">
            <div className="grid gap-6">
              <ClusterBudgetOverview />
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="mt-2">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <h1 className="col-span-3 text-center text-2xl font-semibold mt-32">Timeline</h1>
              <p className="col-span-3 text-center text-sm text-gray-500">Coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
