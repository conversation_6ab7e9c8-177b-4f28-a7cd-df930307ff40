import OrganizationTabsClient from "@/app/home/<USER>/_components/organization-tabs-client";
import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";
import { Suspense } from "react";

export default async function OrganizationsPage() {
  const response = await getCurrentUserOrganizationRoleAction();

  const organizationData = {
    isAdmin: response.data?.isAdmin || false,
    role: response.data?.role || "unknown",
    success: response.success,
    message: response.message,
  };

  return (
    <Suspense fallback={<TeamSkeleton />}>
      <OrganizationTabsClient {...organizationData} />
    </Suspense>
  );
}
