"use client";

import { Project } from "@/types/cluster-project.types";
import { useEffect, useState } from "react";
import { ProjectViewToggle, ProjectsView } from "./home-project-views";

// Type for projects with cluster information
export type ProjectWithCluster = Project & {
  clusterName: string;
  clusterSlug: string;
  clusterId: string;
  clusterTenantId: string;
  clusterDescription: string | null;
};

// Client component to handle the view toggle button
export function ProjectListViewToggle() {
  const [isCardView, setIsCardView] = useState(() => {
    // Initialize from localStorage if available (client-side only)
    if (typeof window !== "undefined") {
      const savedPreference = localStorage.getItem("projectViewPreference");
      return savedPreference !== "list"; // Default to card view if not specified
    }
    return true;
  });

  return (
    <ProjectViewToggle
      isCardView={isCardView}
      onToggle={() => {
        const newValue = !isCardView;
        setIsCardView(newValue);
        // Using localStorage to persist the view preference
        localStorage.setItem("projectViewPreference", newValue ? "card" : "list");
        // Trigger a custom event to notify other components about the view change
        window.dispatchEvent(
          new CustomEvent("projectViewChange", {
            detail: { isCardView: newValue },
          })
        );
      }}
    />
  );
}

// Client component that listens for view changes
export function ProjectsWithViewState({ projects }: { projects: ProjectWithCluster[] }) {
  const [isCardView, setIsCardView] = useState(() => {
    // Initialize from localStorage if available (client-side only)
    if (typeof window !== "undefined") {
      const savedPreference = localStorage.getItem("projectViewPreference");
      return savedPreference !== "list"; // Default to card view if not specified
    }
    return true;
  });

  // Effect to listen for view change events
  useEffect(() => {
    const handleViewChange = (event: CustomEvent<{ isCardView: boolean }>) => {
      setIsCardView(event.detail.isCardView);
    };

    if (typeof window !== "undefined") {
      window.addEventListener("projectViewChange", handleViewChange as EventListener);
      return () => {
        window.removeEventListener("projectViewChange", handleViewChange as EventListener);
      };
    }
  }, []);

  return <ProjectsView projects={projects} isCardView={isCardView} />;
}
