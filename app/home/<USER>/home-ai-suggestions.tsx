"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  CheckCircle,
  Circle,
  MessageSquareText,
  RefreshCw,
  ThumbsDown,
  ThumbsUp,
  XCircle,
} from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { mockSuggestions, type Suggestion } from "../_mock-data";

export function AISuggestions() {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  const [isCompact, setIsCompact] = useState<boolean>(true);

  // Memoize event handlers with useCallback
  const handleDismiss = useCallback((id: string) => {
    setSuggestions((prev) => prev.filter((suggestion) => suggestion.id !== id));
  }, []);

  const handleImplement = useCallback(
    (id: string) => {
      handleDismiss(id);
    },
    [handleDismiss]
  );

  const handleFeedback = useCallback(
    (id: string) => {
      handleDismiss(id);
    },
    [handleDismiss]
  );

  const loadSuggestions = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setSuggestions(mockSuggestions);
    } catch (error) {
      console.info("Failed to fetch suggestions:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadSuggestions();
    setIsRefreshing(false);
  }, [loadSuggestions]);

  const handleCompactMode = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsCompact((prev) => !prev);
  }, []);

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  // Set up the initial load and refresh interval
  useEffect(() => {
    loadSuggestions();

    if (typeof window !== "undefined") {
      const refreshInterval = setInterval(() => {
        handleRefresh();
      }, 3 * 60 * 60 * 1000); // 3 hours

      return () => clearInterval(refreshInterval);
    }
  }, [loadSuggestions, handleRefresh]);

  // Memoize the UI for empty and loading states
  const loadingUI = useMemo(
    () => (
      <div className="p-8 flex flex-col items-center justify-center">
        <div className="animate-pulse flex space-x-4 w-full max-w-md">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 rounded"></div>
              <div className="h-4 rounded w-5/6"></div>
            </div>
          </div>
        </div>
        <p className="mt-4 text-sm text-gray-500">Loading your personalized suggestions...</p>
      </div>
    ),
    []
  );

  const emptyUI = useMemo(
    () => (
      <div className="p-8 flex flex-col items-center justify-center text-center">
        <div className="w-12 h-12 rounded-full bg-gray-50 dark:bg-gray-900 flex items-center justify-center mb-4">
          <MessageSquareText size={24} className="" />
        </div>
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2">All caught up!</h3>
        <p className="text-sm text-gray-500 max-w-md">
          Probably is the time to address some technical debt, write some documentation or just take
          a break.
        </p>
        <Button
          variant="default"
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 text-sm font-medium"
        >
          Refresh Suggestions
        </Button>
      </div>
    ),
    [handleRefresh]
  );

  return (
    <Card className="max-h-[320px] overflow-y-auto bg-white dark:bg-black rounded-lg shadow-xs overflow-hidden transition-all duration-300 ease-in-out py-4 gap-2">
      <div
        className="px-4 py-0 flex items-center justify-between cursor-pointer"
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-2">
          <Circle className="w-4 h-4 text-green-500 rounded-full" />
          <div className="flex items-center gap-6">
            <h2 className="text-md font-semibold text-gray-900 dark:text-white">AI Assistant</h2>
            <p className="text-xs text-gray-600 dark:text-gray-300 hidden md:block">
              Personalized task recommendations
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Switch id="compact-mode" checked={isCompact} onClick={handleCompactMode} />
          <Label htmlFor="compact-mode" className="text-xs">
            Compact
          </Label>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleRefresh();
            }}
            variant="ghost"
            className="rounded-full"
            disabled={isRefreshing}
            aria-label="Refresh suggestions"
          >
            <RefreshCw size={18} className={`${isRefreshing ? "animate-spin" : ""}`} />
          </Button>
          <span className="text-sm hidden md:block">
            {suggestions.length} suggestion{suggestions.length !== 1 ? "s" : ""}
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="min-h-[320px] overflow-y-auto transition-all duration-300 ease-in-out border-t">
          {isLoading ? (
            loadingUI
          ) : suggestions.length > 0 ? (
            <div className="grid grid-cols-1 xl:grid-cols-2 p-2 gap-2">
              {suggestions.map((suggestion, index) => (
                <SuggestionCard
                  key={suggestion.id}
                  suggestion={suggestion}
                  onDismiss={handleDismiss}
                  onImplement={handleImplement}
                  onFeedback={handleFeedback}
                  animationDelay={index * 150}
                  isCompact={isCompact}
                />
              ))}
            </div>
          ) : (
            emptyUI
          )}
        </div>
      )}
    </Card>
  );
}

interface SuggestionCardProps {
  suggestion: Suggestion;
  onDismiss: (id: string) => void;
  onImplement: (id: string) => void;
  onFeedback: (id: string, helpful: boolean) => void;
  animationDelay?: number;
  isCompact?: boolean;
}

// Extract SuggestionCard to a separate memoized component
const SuggestionCard = React.memo(
  ({
    suggestion,
    onDismiss,
    onImplement,
    onFeedback,
    animationDelay = 0,
    isCompact = false,
  }: SuggestionCardProps) => {
    const [isShowingFeedback, setIsShowingFeedback] = useState(false);
    const [isFading, setIsFading] = useState(false);

    const handleDismiss = useCallback(() => {
      setIsFading(true);
      setTimeout(() => onDismiss(suggestion.id), 300);
    }, [onDismiss, suggestion.id]);

    const handleImplement = useCallback(() => {
      setIsFading(true);
      setTimeout(() => onImplement(suggestion.id), 300);
    }, [onImplement, suggestion.id]);

    const handleFeedback = useCallback(
      (helpful: boolean) => {
        setIsFading(true);
        setTimeout(() => onFeedback(suggestion.id, helpful), 300);
      },
      [onFeedback, suggestion.id]
    );

    const priorityColor = useMemo(() => {
      switch (suggestion.priority) {
        case "high":
          return "text-red-400 bg-red-50 dark:bg-red-900/20 dark:text-red-400";
        case "medium":
          return "text-amber-600 bg-amber-50 dark:bg-amber-900/20 dark:text-amber-300";
        case "low":
          return "text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400";
        default:
          return "text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400";
      }
    }, [suggestion.priority]);

    const animationStyle = useMemo(
      () => ({
        animationDelay: `${animationDelay}ms`,
      }),
      [animationDelay]
    );

    return (
      <div
        className={`rounded-lg shadow-xs p-4 transform transition-all duration-300 ease-in-out 
          ${isFading ? "opacity-0 scale-95" : "opacity-100 scale-100"}
          animate-fadeIn`}
        style={animationStyle}
      >
        <div className="mb-2 flex justify-between items-center">
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${priorityColor}`}
            >
              <suggestion.categoryIcon size={16} className="mr-1" />
              {suggestion.category}
            </span>
            {suggestion.project && (
              <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 truncate">
                {suggestion.project}
              </span>
            )}
          </div>
        </div>

        <h3 className="font-medium text-gray-900 dark:text-white text-sm line-clamp-1">
          {suggestion.title}
        </h3>
        {!isCompact && (
          <p className="mt-1 text-xs text-muted-foreground dark:text-gray-400 line-clamp-2">
            {suggestion.description}
          </p>
        )}

        {!isShowingFeedback ? (
          <div className="mt-3 flex flex-wrap gap-2">
            <Button onClick={handleImplement} size="sm" className="h-7 text-xs" variant="default">
              <CheckCircle size={14} className="mr-1" />
              {suggestion.actionText || "Implement"}
            </Button>

            <Button onClick={handleDismiss} size="sm" className="h-7 text-xs" variant="outline">
              <XCircle size={14} className="mr-1" />
              Dismiss
            </Button>

            <Button
              onClick={() => setIsShowingFeedback(true)}
              size="sm"
              className="h-7 text-xs"
              variant="ghost"
            >
              Improve
            </Button>
          </div>
        ) : (
          <div className="mt-3">
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
              Was this suggestion helpful?
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleFeedback(true)}
                className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-50 hover:bg-green-100 dark:text-green-400 dark:bg-green-900/20 dark:hover:bg-green-800/30 rounded-md transition-colors"
              >
                <ThumbsUp size={14} className="mr-1" />
                Yes
              </button>

              <button
                onClick={() => handleFeedback(false)}
                className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-800/30 rounded-md transition-colors"
              >
                <ThumbsDown size={14} className="mr-1" />
                No
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }
);

SuggestionCard.displayName = "SuggestionCard";
