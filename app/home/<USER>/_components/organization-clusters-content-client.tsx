"use client";

import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { useOrganizationClustersData } from "@/app/home/<USER>/use-organization-data";
import { ClustersList } from "./organization-clusters-list";

interface OrganizationClustersContentClientProps {
  isAdmin: boolean;
}

export default function OrganizationClustersContentClient({ 
  isAdmin 
}: OrganizationClustersContentClientProps) {
  const { clusters, availableProjects, isLoading, isError, error } = useOrganizationClustersData();

  if (isLoading) {
    return <TeamSkeleton />;
  }

  if (isError) {
    console.info("Error fetching organization clusters:", error);
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading clusters. Please try again later.</p>
      </div>
    );
  }

  if (!clusters || clusters.length === 0) {
    return (
      <div className="py-4">
        <p className="text-muted-foreground">No clusters found. Create one to get started.</p>
      </div>
    );
  }

  return (
    <ClustersList 
      clusters={clusters} 
      availableProjects={availableProjects} 
      isAdmin={isAdmin} 
    />
  );
}
