"use client";

import EmployeesList from "@/app/home/<USER>/_components/_employees_components/employees-list";
import InvitationsList from "@/app/home/<USER>/_components/_employees_components/invitations-list";
import MemberInvitation from "@/components/auth/member-invitation";
import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui";
import { Input } from "@/components/ui/input";
import { SearchIcon } from "lucide-react";
import { useOrganizationEmployeesData } from "../../_hooks/use-organization-employees";

export default function OrganizationEmployeesContentClient() {
  const { employees, organization, isAdmin, isLoading, isError, error } =
    useOrganizationEmployeesData();

  if (isLoading) {
    return <TeamSkeleton />;
  }

  if (isError) {
    console.info("Error fetching organization employees:", error);
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading employees. Please try again later.</p>
      </div>
    );
  }

  if (!employees || !organization) {
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading employees. Please try again later.</p>
      </div>
    );
  }

  // Client-side header component
  const OrganizationHeader = () => (
    <CardHeader className="py-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-2xl font-semibold">Organization Employees</CardTitle>
          <CardDescription>Manage organization members and their roles.</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Input
              id="employees-search"
              placeholder="Search by name, email, role or position"
              className="w-[350px] ps-9"
              type="search"
              readOnly
            />
            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3">
              <SearchIcon size={16} aria-hidden="true" />
            </div>
          </div>
          {isAdmin && <MemberInvitation organization={organization} />}
        </div>
      </div>
    </CardHeader>
  );

  return (
    <div className="flex flex-col w-full">
      {/* Page header at the top */}
      <OrganizationHeader />

      {/* Invitations list below the header */}
      {isAdmin && (
        <div className="w-full pb-12">
          <InvitationsList isAdmin={isAdmin} organization={organization} />
        </div>
      )}

      {/* EmployeesList remains a client component */}
      <EmployeesList
        employees={employees}
        isAdmin={isAdmin}
        organization={organization}
        hideHeader={true}
      />
    </div>
  );
}
