"use client";

import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { useUserTeamsData } from "@/app/home/<USER>/use-organization-teams";
import TeamsList from "../../organization/_components/organization-teams-list";

export default function TeamsContentClient() {
  const { teams, currentUserName, isAdmin, isLoading, isError, error } = useUserTeamsData();

  if (isLoading) {
    return <TeamSkeleton />;
  }

  if (isError) {
    console.info("Error fetching teams:", error);
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading teams. Please try again later.</p>
      </div>
    );
  }

  return <TeamsList teams={teams} currentUserName={currentUserName} isAdmin={isAdmin} />;
}
