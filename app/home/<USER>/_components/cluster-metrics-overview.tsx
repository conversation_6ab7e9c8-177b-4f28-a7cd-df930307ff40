import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Activity,
  AlertTriangle,
  Calendar,
  CheckCircle2,
  Clock,
  FolderKanban,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";

export function ClusterMetricsOverview() {
  const metrics = [
    {
      title: "Total Projects",
      value: "9",
      icon: FolderKanban,
      trend: "+2 this quarter",
      trendUp: true,
      color: "text-blue-500",
    },
    {
      title: "In Progress",
      value: "4",
      icon: Activity,
      trend: "44% of total",
      trendUp: null,
      color: "text-yellow-500",
    },
    {
      title: "Completed",
      value: "1",
      icon: CheckCircle2,
      trend: "+1 this month",
      trendUp: true,
      color: "text-green-500",
    },
    {
      title: "At Risk",
      value: "2",
      icon: AlertTriangle,
      trend: "+1 from last week",
      trendUp: false,
      color: "text-red-500",
    },
    {
      title: "Team Members",
      value: "24",
      icon: Users,
      trend: "Across all projects",
      trendUp: null,
      color: "text-purple-500",
    },
    {
      title: "Avg. Completion",
      value: "62%",
      icon: Clock,
      trend: "+5% from last month",
      trendUp: true,
      color: "text-cyan-500",
    },
    {
      title: "Upcoming Deadlines",
      value: "5",
      icon: Calendar,
      trend: "Next 30 days",
      trendUp: null,
      color: "text-orange-500",
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7">
      {metrics.map((metric) => (
        <Card key={metric.title} className="overflow-hidden py-4">
          <CardContent className="px-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{metric.title}</span>
                <metric.icon className={`h-4 w-4 ${metric.color}`} />
              </div>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="flex items-center text-xs">
                {metric.trendUp !== null && (
                  <Badge
                    variant="outline"
                    className={`mr-1 ${metric.trendUp ? "text-green-500" : "text-red-500"}`}
                  >
                    {metric.trendUp ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    )}
                  </Badge>
                )}
                <span className="text-muted-foreground">{metric.trend}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
