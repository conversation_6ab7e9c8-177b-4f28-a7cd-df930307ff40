"use client";

import { Card<PERSON>ontent, Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui";
import {
  createTeamMemberAction,
  moveTeamMemberToNewTeamAction,
  removeTeamMemberFromTeamAction,
  updateTeamMemberDetailsAction,
} from "@/db/actions/team-members.action";
import { deleteTeamAction } from "@/db/actions/team.actions";
import { Position, Role, TeamMemberUI, TeamUI } from "@/types/team.types";
import React, { useState } from "react";
import { toast } from "sonner";

// Import our extracted components
import {
  AddTeamMemberDialog,
  DeleteTeamDialog,
  TeamHeaderRow,
  TeamMembersSection,
  TeamsListHeader,
} from "./_teams-components/index";
import TeamsEmptyState from "./_teams-components/teams-empty-state";

// Types
export type LoadingState = Record<string, { type: string; loading: boolean; success: boolean }>;
export type TeamVisibilityState = Record<string, boolean>;

type TeamsListProps = {
  teams: TeamUI[];
  currentUserName: string;
  isAdmin: boolean;
};

export default function TeamsList({
  teams: initialTeams,
  currentUserName,
  isAdmin,
}: TeamsListProps) {
  const [teams, setTeams] = useState<TeamUI[]>(initialTeams);
  const [teamVisibility, setTeamVisibility] = useState<TeamVisibilityState>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [loadingState, setLoadingState] = useState<LoadingState>({});

  // Delete team
  const [isDeleting, setIsDeleting] = useState(false);
  const [teamToDelete, setTeamToDelete] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Add member to team
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);
  const [isAddingMember, setIsAddingMember] = useState(false);

  const filterMembers = (members: TeamMemberUI[]) => {
    if (!searchQuery) return members;

    const query = searchQuery.toLowerCase();
    return members.filter(
      (member) =>
        member.name.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query) ||
        member.role.toLowerCase().includes(query) ||
        (member.position?.toLowerCase().includes(query) ?? false)
    );
  };

  // Filter teams and their members
  const filteredTeams = teams
    .map((team) => ({
      ...team,
      members: filterMembers(team.members),
    }))
    .filter((team) => (searchQuery ? team.members.length > 0 : true));
  // Filter members based on search query

  const handleTeamMembersVisible = (teamId: string) => {
    setTeamVisibility((prev) => ({
      ...prev,
      [teamId]: !prev[teamId],
    }));
  };

  const handleMoveTeamMemberToNewTeam = async (teamMemberId: string, newTeamId: string) => {
    if (!teamMemberId) {
      console.info("Team member ID is missing or invalid:", teamMemberId);
      toast.error("Invalid team member ID");
      return;
    }

    let memberToMove: TeamMemberUI | undefined;
    let currentTeamId: string | undefined;

    // Find member and current team in a single pass
    for (const team of teams) {
      const foundMember = team.members.find((member) => member.id === teamMemberId);
      if (foundMember) {
        memberToMove = foundMember;
        currentTeamId = team.id;
        break;
      }
    }

    // Validate all required data exists
    if (!memberToMove || !currentTeamId) {
      toast.error("Team member not found");
      return;
    }

    if (currentTeamId === newTeamId) {
      toast.error("Member is already in this team");
      return;
    }

    // Get the destination team
    const destinationTeam = teams.find((team) => team.id === newTeamId);
    if (!destinationTeam) {
      toast.error("Target team not found");
      return;
    }

    // Check if the user already exists in the destination team
    // Log the comparison for debugging

    const userAlreadyExists = destinationTeam.members.some(
      (member) => member.userId === memberToMove.userId
    );
    if (userAlreadyExists) {
      toast.error("User is already a member of the destination team");
      return;
    }

    // Add loading state
    const toastId = toast.loading("Moving team member...");

    try {
      const result = await moveTeamMemberToNewTeamAction(teamMemberId, newTeamId);

      if (!result.success || result.error) {
        toast.error(result.message, { id: toastId });
        return;
      }

      // Update local state to reflect the change
      setTeams((prevTeams) =>
        prevTeams.map((team) => {
          // Remove member from original team
          if (team.id === currentTeamId) {
            return {
              ...team,
              members: team.members.filter((member) => member.id !== teamMemberId),
            };
          }

          // Add member to new team with the NEW ID
          if (team.id === newTeamId && memberToMove) {
            // Make sure we're using the server-returned new ID
            const newTeamMemberId = result.data?.newTeamMemberId;

            if (!newTeamMemberId) {
              console.info("No new team member ID returned from server");
              return team;
            }

            const updatedMember = {
              ...memberToMove,
              id: newTeamMemberId, // Use the new ID from the server
            };

            return {
              ...team,
              members: [...team.members, updatedMember],
            };
          }

          return team;
        })
      );

      toast.success(result.message, { id: toastId });
    } catch (error) {
      console.info("Error moving team member:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while moving the team member",
        { id: toastId }
      );
    }
  };

  const handleDeleteTeam = async (teamId: string) => {
    setIsDeleting(true);

    try {
      const result = await deleteTeamAction(teamId);

      if (!result.success) {
        toast.error(result.message);
        return;
      }

      // Update local state to remove the team
      setTeams((prevTeams) => prevTeams.filter((team) => team.id !== teamId));
      toast.success(result.message);
      setDeleteDialogOpen(false);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while deleting the team"
      );
    } finally {
      setIsDeleting(false);
      setTeamToDelete(null);
    }
  };

  const handleRoleChange = async (memberId: string, newRole: string) => {
    // Skip if already loading
    if (loadingState[memberId]?.type === "role" && loadingState[memberId]?.loading) return;

    // Set loading state
    setLoadingState((prev) => ({
      ...prev,
      [memberId]: { type: "role", loading: true, success: false },
    }));

    // Add loading toast
    const toastId = toast.loading("Updating role...");

    try {
      const result = await updateTeamMemberDetailsAction(memberId, { role: newRole });

      if (result.success) {
        // Update local state with new role
        setTeams((prevTeams) =>
          prevTeams.map((team) => ({
            ...team,
            members: team.members.map((member) =>
              member.id === memberId ? { ...member, role: newRole as Role } : member
            ),
          }))
        );

        // Show success toast
        toast.success(result.message || "Role updated successfully", { id: toastId });
      } else {
        toast.error(result.message || "Failed to update role", { id: toastId });
      }
    } catch (error) {
      toast.error("An unexpected error occurred while updating role", {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating role",
      });
    } finally {
      // Reset loading state
      setLoadingState((prev) => ({
        ...prev,
        [memberId]: { type: "role", loading: false, success: false },
      }));
    }
  };

  const handlePositionChange = async (memberId: string, newPosition: string) => {
    // Skip if already loading
    if (loadingState[memberId]?.type === "position" && loadingState[memberId]?.loading) return;

    // Set loading state
    setLoadingState((prev) => ({
      ...prev,
      [memberId]: { type: "position", loading: true, success: false },
    }));

    // Add loading toast
    const toastId = toast.loading("Updating position...");

    try {
      const result = await updateTeamMemberDetailsAction(memberId, { position: newPosition });

      if (result.success) {
        // Update local state with new position
        setTeams((prevTeams) =>
          prevTeams.map((team) => ({
            ...team,
            members: team.members.map((member) =>
              member.id === memberId ? { ...member, position: newPosition as Position } : member
            ),
          }))
        );

        // Show success toast
        toast.success(result.message || "Position updated successfully", { id: toastId });
      } else {
        toast.error(result.message || "Failed to update position", { id: toastId });
      }
    } catch (error) {
      toast.error("An unexpected error occurred while updating position", {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating position",
      });
    } finally {
      // Reset loading state
      setLoadingState((prev) => ({
        ...prev,
        [memberId]: { type: "position", loading: false, success: false },
      }));
    }
  };

  const handleMemberRemoved = async (memberId: string, teamId: string) => {
    // Validate all required data exists
    if (!memberId) {
      console.info("Team member ID is missing or invalid:", memberId);
      toast.error("Team member not found");
      return;
    }

    if (!teamId) {
      toast.error("Team not found");
      return;
    }

    // Verify the member exists in our local state
    const team = teams.find((t) => t.id === teamId);
    if (!team) {
      toast.error("Team not found");
      return;
    }

    const memberExists = team.members.some((m) => m.id === memberId);
    if (!memberExists) {
      console.info("Member not found in local state. Member ID:", memberId, "Team:", team);
      toast.error("Team member not found in this team");
      return;
    }

    // Add loading state
    const toastId = toast.loading("Removing member from team...");

    try {
      const result = await removeTeamMemberFromTeamAction(memberId, teamId);

      if (!result.success) {
        toast.error(result.message, { id: toastId });
        return;
      }

      // Update the local state to remove the member
      setTeams((prevTeams) =>
        prevTeams.map((team) => {
          if (team.id === teamId) {
            return {
              ...team,
              members: team.members.filter((member) => member.id !== memberId),
            };
          }
          return team;
        })
      );

      toast.success("Member removed successfully", { id: toastId });
    } catch (error) {
      console.info("Error removing team member:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while removing the team member",
        { id: toastId }
      );
    }
  };

  const handleAddMemberClick = (teamId: string) => {
    setSelectedTeamId(teamId);
    setAddMemberDialogOpen(true);
  };

  const handleAddMember = async (data: {
    userId: string;
    role: Role;
    position: Position;
    name: string;
    email: string;
    avatarUrl?: string;
    tenantId: string;
  }) => {
    if (!selectedTeamId) {
      toast.error("Team not found");
      return;
    }

    setIsAddingMember(true);
    const toastId = toast.loading("Adding team member...");

    try {
      // Get team from the selected team ID
      const team = teams.find((t) => t.id === selectedTeamId);
      if (!team) {
        toast.error("Team not found", { id: toastId });
        setIsAddingMember(false);
        return;
      }

      // Check if user is already in this team
      const isMemberAlreadyInTeam = team.members.some((member) => member.userId === data.userId);
      if (isMemberAlreadyInTeam) {
        toast.error("User is already a member of this team", { id: toastId });
        setIsAddingMember(false);
        return;
      }

      // Create new team member - use the tenant ID from the member data
      // This ensures the tenant_id in team_members matches the user's tenant_id
      const result = await createTeamMemberAction({
        userId: data.userId,
        teamId: selectedTeamId,
        role: data.role,
        position: data.position,
        tenantId: data.tenantId,
      });

      if (!result.success || !result.data) {
        toast.error(result.message || "Failed to add team member", { id: toastId });
        setIsAddingMember(false);
        return;
      }

      // Extract the ID - handle different response formats that might be returned
      // The server may return either teamMemberId or id directly
      let teamMemberId: string;

      if ("teamMemberId" in result.data && result.data.teamMemberId) {
        teamMemberId = result.data.teamMemberId;
      } else if ("id" in result.data && result.data.id) {
        teamMemberId = (result.data as { id: string }).id;
      } else if (typeof result.data === "string") {
        // Handle case where the data itself might be the ID
        teamMemberId = result.data;
      } else {
        // For other response formats, try to extract any ID field
        const possibleId = Object.entries(result.data).find(([key]) =>
          key.toLowerCase().includes("id")
        )?.[1];

        if (typeof possibleId === "string") {
          teamMemberId = possibleId;
        } else {
          // Log the full response for debugging
          console.info(
            "No recognizable team member ID in response:",
            JSON.stringify(result, null, 2)
          );

          // Show error but continue - the user was added on server side
          toast.warning("User was added but UI might need a refresh", { id: toastId });
          setAddMemberDialogOpen(false);
          setIsAddingMember(false);
          return;
        }
      }

      toast.success("Team member added successfully", { id: toastId });

      // Update local state with the complete user data
      setTeams((prevTeams) =>
        prevTeams.map((team) => {
          if (team.id === selectedTeamId) {
            // Create a new member object with the correct ID from the server response
            // Store the avatar URL properly
            // Store exactly what was provided from the user selection dialog, not any other source
            const avatarUrl = data.avatarUrl || undefined;

            const newMember: TeamMemberUI = {
              id: teamMemberId, // Use the server-generated ID that we already validated
              userId: data.userId,
              name: data.name,
              email: data.email,
              role: (result.data && "role" in result.data ? result.data.role : data.role) as Role,
              position: (result.data && "position" in result.data
                ? result.data.position
                : "No one knows") as Position,
              createdAt: new Date().toISOString(),
              dateAdded: new Date().toISOString(),
              avatarUrl: avatarUrl, // Use our properly handled avatar URL
            };

            return {
              ...team,
              members: [...team.members, newMember],
            };
          }
          return team;
        })
      );

      // Close the dialog
      setAddMemberDialogOpen(false);
    } catch (error) {
      console.info("Error adding team member:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while adding the team member",
        { id: toastId }
      );
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleEditTeamName = (teamId: string) => {
    toast.info(`Feature coming soon!`, {
      description: `Edit team: ${teamId}`,
    });
  };

  return (
    <div className="flex flex-col w-full h-[calc(100vh-8rem)] border-none">
      <TeamsListHeader isAdmin={isAdmin} onSearchChange={setSearchQuery} />
      <CardContent className="min-h-0">
        {filteredTeams.length > 0 ? (
          <div className="h-full overflow-auto">
            <Table className="border-b rounded-lg">
              <TableHeader>
                <TableRow>
                  <TableHead className="pl-4">Member</TableHead>
                  <TableHead>Date Added</TableHead>
                  <TableHead>
                    Role <span className="text-xs text-muted-foreground">(Team level)</span>
                  </TableHead>
                  <TableHead>
                    Position <span className="text-xs text-muted-foreground">(Team level)</span>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="[&_tr:last-child]:border-x">
                {filteredTeams.map((team, index) => (
                  <React.Fragment key={team.id}>
                    {index > 0 && <TableRow className="h-8" />}

                    <TeamHeaderRow
                      team={team}
                      isAdmin={isAdmin}
                      onVisibilityToggle={() => handleTeamMembersVisible(team.id)}
                      onDeleteClick={() => setTeamToDelete(team.id)}
                      deleteDialogOpen={teamToDelete === team.id && deleteDialogOpen}
                      onDeleteDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setTeamToDelete(null);
                        }
                        setDeleteDialogOpen(open);
                      }}
                      deleteDialogComponent={
                        teamToDelete === team.id ? (
                          <DeleteTeamDialog
                            teamName={team.name}
                            onOpenChange={setDeleteDialogOpen}
                            onConfirm={() => handleDeleteTeam(team.id)}
                            isDeleting={isDeleting}
                          />
                        ) : null
                      }
                      onAddMemberClick={() => handleAddMemberClick(team.id)}
                      addMemberDialogOpen={selectedTeamId === team.id && addMemberDialogOpen}
                      onAddMemberDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setSelectedTeamId(null);
                        }
                        setAddMemberDialogOpen(open);
                      }}
                      addMemberDialogComponent={
                        selectedTeamId === team.id ? (
                          <AddTeamMemberDialog
                            open={addMemberDialogOpen}
                            onOpenChange={setAddMemberDialogOpen}
                            onConfirm={handleAddMember}
                            isAddingMember={isAddingMember}
                          />
                        ) : null
                      }
                      onEditTeamName={() => handleEditTeamName(team.id)}
                    />

                    <TeamMembersSection
                      team={team}
                      teams={teams}
                      isVisible={teamVisibility[team.id]}
                      isAdmin={isAdmin}
                      currentUserName={currentUserName}
                      loadingState={loadingState}
                      onRoleChange={handleRoleChange}
                      onPositionChange={handlePositionChange}
                      onMoveTeamMember={handleMoveTeamMemberToNewTeam}
                      onMemberRemoved={handleMemberRemoved}
                    />
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <TeamsEmptyState isAdmin={isAdmin} />
        )}
      </CardContent>
    </div>
  );
}
