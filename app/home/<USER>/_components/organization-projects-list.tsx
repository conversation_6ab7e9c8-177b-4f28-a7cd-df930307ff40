"use client";

import { <PERSON><PERSON>ontent, Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui";
import { deleteProjectAction } from "@/db/actions/project.action";
import {
  assignTeamToProjectAction,
  removeTeamFromProjectAction,
} from "@/db/actions/team-projects.action";
import React, { useState } from "react";
import { toast } from "sonner";
import {
  AddTeamDialog,
  DeleteProjectDialog,
  ProjectHeaderRow,
  ProjectsEmptyState,
  ProjectsListHeader,
  ProjectTeamsSection,
} from "./_projects_components";
import {
  Project,
  ProjectTeam,
  ProjectVisibilityState,
} from "./_projects_components/projects-types";

type ProjectsListProps = {
  projects: Project[];
  availableTeams: ProjectTeam[];
  isAdmin: boolean;
};

export function ProjectsList({
  projects: initialProjects,
  availableTeams: initialAvailableTeams,
  isAdmin,
}: ProjectsListProps) {
  const [projects, setProjects] = useState<Project[]>(initialProjects);
  const [projectVisibility, setProjectVisibility] = useState<ProjectVisibilityState>(() => {
    const initialVisibility: ProjectVisibilityState = {};
    initialProjects.forEach((project) => {
      initialVisibility[project.id] = false; // false = collapsed, true = expanded
    });
    return initialVisibility;
  });
  const [searchQuery, setSearchQuery] = useState("");

  // Delete project
  const [isDeleting, setIsDeleting] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Add team to project
  const [addTeamDialogOpen, setAddTeamDialogOpen] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [isAddingTeam, setIsAddingTeam] = useState(false);

  // Filter teams based on search query
  const filterTeams = (teams: ProjectTeam[] = []) => {
    if (!searchQuery) return teams;

    const query = searchQuery.toLowerCase();
    return teams.filter(
      (team) =>
        team.name.toLowerCase().includes(query) ||
        (team.description?.toLowerCase().includes(query) ?? false)
    );
  };

  // Filter projects and their teams
  const filteredProjects = projects
    .map((project) => ({
      ...project,
      teams: filterTeams(project.teams),
    }))
    .filter((project) => (searchQuery ? project.teams.length > 0 : true));

  const handleProjectTeamsVisible = (projectId: string) => {
    setProjectVisibility((prev) => ({
      ...prev,
      [projectId]: !prev[projectId],
    }));
  };

  const handleDeleteProject = async (projectId: string) => {
    setIsDeleting(true);

    try {
      const result = await deleteProjectAction(projectId);

      if (!result.success) {
        toast.error(result.message);
        return;
      }

      setProjects((prevProjects) => prevProjects.filter((project) => project.id !== projectId));
      toast.success(result.message);
      setDeleteDialogOpen(false);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while deleting the project"
      );
    } finally {
      setIsDeleting(false);
      setProjectToDelete(null);
    }
  };

  const handleAddTeamClick = (projectId: string) => {
    setSelectedProjectId(projectId);
    setAddTeamDialogOpen(true);
  };

  const handleAddTeam = async (data: { teamId: string }) => {
    if (!selectedProjectId) {
      toast.error("Project not found");
      return;
    }

    // Check if team is already assigned to the project
    const project = projects.find((p) => p.id === selectedProjectId);
    if (project?.teams?.some((team) => team.id === data.teamId)) {
      toast.error("This team is already assigned to the project");
      return;
    }

    setIsAddingTeam(true);
    const toastId = toast.loading("Adding team to project...");

    try {
      const result = await assignTeamToProjectAction(data.teamId, selectedProjectId);

      if (!result.success) {
        toast.error(result.message || "Failed to add team to project", { id: toastId });
        return;
      }

      // Find the team from available teams
      const teamToAdd = initialAvailableTeams.find((team) => team.id === data.teamId);
      if (!teamToAdd) {
        toast.error("Team not found", { id: toastId });
        return;
      }

      // Update local state with the added team
      setProjects((prevProjects) =>
        prevProjects.map((project) => {
          if (project.id === selectedProjectId) {
            return {
              ...project,
              teams: [...(project.teams || []), teamToAdd],
            };
          }
          return project;
        })
      );

      toast.success("Team added to project successfully", { id: toastId });
      setAddTeamDialogOpen(false);
    } catch (error) {
      console.info("Error adding team to project:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while adding the team to the project",
        { id: toastId }
      );
    } finally {
      setIsAddingTeam(false);
    }
  };

  const handleRemoveTeam = async (teamId: string, projectId: string) => {
    const toastId = toast.loading("Removing team from project...");

    try {
      const result = await removeTeamFromProjectAction(teamId, projectId);

      if (!result.success) {
        toast.error(result.message || "Failed to remove team from project", { id: toastId });
        return;
      }

      // Update local state to remove the team
      setProjects((prevProjects) =>
        prevProjects.map((project) => {
          if (project.id === projectId) {
            return {
              ...project,
              teams: (project.teams || []).filter((team) => team.id !== teamId),
            };
          }
          return project;
        })
      );

      toast.success("Team removed from project successfully", { id: toastId });
    } catch (error) {
      console.info("Error removing team from project:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while removing the team from the project",
        { id: toastId }
      );
    }
  };

  const handleEditProjectName = (projectId: string) => {
    toast.info(`Feature coming soon!`, {
      description: `Edit project: ${projectId}`,
    });
  };

  return (
    <div className="flex flex-col w-full border-none">
      <ProjectsListHeader isAdmin={isAdmin} onSearchChange={setSearchQuery} />

      <CardContent className="min-h-0">
        {filteredProjects.length > 0 ? (
          <div className="h-full overflow-auto">
            <Table className="border-b rounded-lg">
              <TableHeader className="w-full">
                <TableRow>
                  <TableHead className="pl-4">Project</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Members</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="[&_tr:last-child]:border-x">
                {filteredProjects.map((project, index) => (
                  <React.Fragment key={project.id}>
                    {index > 0 && <TableRow className="h-8" />}

                    <ProjectHeaderRow
                      project={project}
                      isAdmin={isAdmin}
                      onVisibilityToggle={() => handleProjectTeamsVisible(project.id)}
                      onDeleteClick={() => setProjectToDelete(project.id)}
                      deleteDialogOpen={projectToDelete === project.id && deleteDialogOpen}
                      onDeleteDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setProjectToDelete(null);
                        }
                        setDeleteDialogOpen(open);
                      }}
                      deleteDialogComponent={
                        projectToDelete === project.id ? (
                          <DeleteProjectDialog
                            projectName={project.name}
                            onOpenChange={setDeleteDialogOpen}
                            onConfirm={() => handleDeleteProject(project.id)}
                            isDeleting={isDeleting}
                          />
                        ) : null
                      }
                      onAddTeamClick={() => handleAddTeamClick(project.id)}
                      addTeamDialogOpen={selectedProjectId === project.id && addTeamDialogOpen}
                      onAddTeamDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setSelectedProjectId(null);
                        }
                        setAddTeamDialogOpen(open);
                      }}
                      addTeamDialogComponent={
                        selectedProjectId === project.id ? (
                          <AddTeamDialog
                            open={addTeamDialogOpen}
                            onOpenChange={setAddTeamDialogOpen}
                            onConfirm={handleAddTeam}
                            isAddingTeam={isAddingTeam}
                            availableTeams={initialAvailableTeams.filter(
                              (team) => !project.teams?.some((pt) => pt.id === team.id)
                            )}
                          />
                        ) : null
                      }
                      onEditProjectName={() => handleEditProjectName(project.id)}
                    />

                    <ProjectTeamsSection
                      projectId={project.id}
                      isVisible={projectVisibility[project.id]}
                      onRemoveTeam={handleRemoveTeam}
                      teams={project.teams || []}
                      isLoading={false}
                      error={null}
                    />
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <ProjectsEmptyState isAdmin={isAdmin} />
        )}
      </CardContent>
    </div>
  );
}
