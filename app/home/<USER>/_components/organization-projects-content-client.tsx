"use client";

import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { useOrganizationProjectsData } from "@/app/home/<USER>/use-organization-data";
import { ProjectsList } from "./organization-projects-list";

interface OrganizationProjectsContentClientProps {
  isAdmin: boolean;
}

export default function OrganizationProjectsContentClient({ 
  isAdmin 
}: OrganizationProjectsContentClientProps) {
  const { projects, availableTeams, isLoading, isError, error } = useOrganizationProjectsData();

  if (isLoading) {
    return <TeamSkeleton />;
  }

  if (isError) {
    console.info("Error fetching organization projects:", error);
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading projects. Please try again later.</p>
      </div>
    );
  }

  return (
    <ProjectsList 
      projects={projects} 
      availableTeams={availableTeams} 
      isAdmin={isAdmin} 
    />
  );
}
