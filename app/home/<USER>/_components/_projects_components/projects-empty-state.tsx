import { Button } from "@/components/ui";
import Link from "next/link";

type ProjectsEmptyStateProps = {
  isAdmin: boolean;
};

export default function ProjectsEmptyState({ isAdmin }: ProjectsEmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-6">
      <h3 className="mb-2 text-lg font-medium">No projects available</h3>
      <p className="mb-4 text-muted-foreground">
        This organization doesn&apos;t have any projects yet. Create a new project to add teams.
      </p>
      {isAdmin && (
        <Link href="/home/<USER>/projects/new">
          <Button size="sm">Create Project</Button>
        </Link>
      )}
    </div>
  );
}
