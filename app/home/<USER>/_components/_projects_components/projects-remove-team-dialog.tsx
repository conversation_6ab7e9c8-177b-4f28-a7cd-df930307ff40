import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui";
import { AlertTriangleIcon } from "lucide-react";

type DeleteTeamDialogProps = {
  teamName: string;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
};

export function RemoveTeamDialog({
  teamName,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteTeamDialogProps) {
  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader className="gap-4">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
          <AlertTriangleIcon className="h-6 w-6 text-red-500" />
        </div>
        <DialogTitle className="text-center text-xl">Remove Team</DialogTitle>
        <DialogDescription className="text-center">
          Are you sure you want to remove team{" "}
          <span className="font-semibold text-foreground">{teamName}</span> from this project?
          <span className="mt-2 text-xs text-muted-foreground">
            Note: This action cannot be undone and may affect team members&apos; access.
          </span>
        </DialogDescription>
      </DialogHeader>
      <DialogFooter className="sm:justify-center gap-2 mt-2">
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={isDeleting}
          className="min-w-[100px]"
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={onConfirm}
          disabled={isDeleting}
          className="min-w-[100px]"
        >
          {isDeleting ? "Removing..." : "Remove Team"}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
