// Type definitions for projects and teams
export type ProjectTeam = {
  id: string;
  name: string;
  description: string | null;
};

export type Project = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  status: string;
  clusterId: string;
  clusterName: string;
  clusterSlug: string;
  teams: ProjectTeam[];
};

// Type for tracking which projects are expanded
export type ProjectVisibilityState = Record<string, boolean>;

// Type for tracking loading states
export type LoadingState = Record<string, { type: string; loading: boolean; success: boolean }>;
