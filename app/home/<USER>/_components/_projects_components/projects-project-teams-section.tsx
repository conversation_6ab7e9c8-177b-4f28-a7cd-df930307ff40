"use client";

import { TableCell, TableRow } from "@/components/ui";
import { ProjectTeamRow } from "./index";
import { ProjectTeam } from "./projects-types";

type ProjectTeamsSectionProps = {
  projectId: string;
  isVisible: boolean; // When true, component renders nothing (teams are hidden). When false, teams are displayed.
  teams: ProjectTeam[];
  isLoading?: boolean;
  error?: string | null;
  onRemoveTeam: (teamId: string, projectId: string) => Promise<void>;
};

export function ProjectTeamsSection({
  projectId,
  isVisible,
  teams,
  isLoading = false,
  error = null,
  onRemoveTeam,
}: ProjectTeamsSectionProps) {
  // If not visible, don't render anything - this allows controlling visibility from parent
  if (isVisible) return null;

  if (isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="text-muted-foreground pl-8 h-12 border-x">
          Loading teams...
        </TableCell>
      </TableRow>
    );
  }

  if (error) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="pl-8 text-destructive border-x h-12">
          {error}
        </TableCell>
      </TableRow>
    );
  }

  if (teams.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="text-muted-foreground pl-8 border-x h-12">
          No teams assigned to this project yet.
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {teams.map((team) => (
        <ProjectTeamRow
          key={team.id}
          team={team}
          projectId={projectId}
          onRemoveTeam={onRemoveTeam}
        />
      ))}
    </>
  );
}
