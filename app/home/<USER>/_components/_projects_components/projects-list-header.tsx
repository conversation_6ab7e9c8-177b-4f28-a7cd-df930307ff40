import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { Button } from "@/components/ui/button";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusIcon } from "lucide-react";
import Link from "next/link";

type ProjectsListHeaderProps = {
  isAdmin: boolean;
  onSearchChange: (query: string) => void;
};

// Header component for projects list
export function ProjectsListHeader({ isAdmin, onSearchChange }: ProjectsListHeaderProps) {
  return (
    <CardHeader className="py-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-2xl font-semibold">Projects</CardTitle>
          <CardDescription>Manage projects and their assigned teams.</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <SearchInputWithVoice
            id="team-members-search"
            placeholder="Search teams by name"
            className="w-[250px]"
            onChange={onSearchChange}
          />

          {isAdmin && (
            <>
              <Link href="/home/<USER>/projects/new">
                <Button variant="outline">
                  <PlusIcon className="w-4 h-4" />
                  New Project
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </CardHeader>
  );
}
