// Export all project-related components for easier imports
import { DeleteTeamDialog } from "../_teams-components";
import { AddTeamDialog } from "./projects-add-team-dialog";
import { DeleteProjectDialog } from "./projects-delete-project-dialog";
import { default as ProjectsEmptyState } from "./projects-empty-state";
import { ProjectsListHeader } from "./projects-list-header";
import { default as ProjectHeaderRow } from "./projects-project-header-row";
import { default as ProjectTeamRow } from "./projects-project-team-row";
import { ProjectTeamsSection } from "./projects-project-teams-section";
import { RemoveTeamDialog } from "./projects-remove-team-dialog";

export {
  AddTeamDialog,
  DeleteProjectDialog,
  DeleteTeamDialog,
  ProjectHeaderRow,
  ProjectsEmptyState,
  ProjectsListHeader,
  ProjectTeamRow,
  ProjectTeamsSection,
  RemoveTeamDialog,
};
