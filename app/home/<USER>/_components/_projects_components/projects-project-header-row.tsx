"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableCell, TableRow } from "@/components/ui";
import { PenIcon, TrashIcon, UserPlusIcon } from "lucide-react";
import { ReactNode } from "react";
import { Project } from "./projects-types";

type ProjectHeaderRowProps = {
  project: Project;
  isAdmin: boolean;
  onVisibilityToggle: () => void;
  onDeleteClick: () => void;
  deleteDialogOpen: boolean;
  onDeleteDialogOpenChange: (open: boolean) => void;
  deleteDialogComponent: ReactNode;
  onAddTeamClick: () => void;
  addTeamDialogOpen: boolean;
  onAddTeamDialogOpenChange: (open: boolean) => void;
  addTeamDialogComponent: ReactNode;
  onEditProjectName: () => void;
};

export default function ProjectHeaderRow({
  project,
  isAdmin,
  onVisibilityToggle,
  onDeleteClick,
  deleteDialogO<PERSON>,
  onDeleteDialog<PERSON><PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON>og<PERSON><PERSON>ponent,
  onAddTeamClick,
  addTeamDialogOpen,
  onAddTeamDialogOpenChange,
  addTeamDialogComponent,
  onEditProjectName,
}: ProjectHeaderRowProps) {
  return (
    <TableRow className="w-full h-12 bg-muted/50 border-x group">
      <TableCell colSpan={6} className="py-2 pl-4">
        <div className="flex items-center justify-between">
          <div
            className="flex items-center flex-1 gap-3 cursor-pointer"
            onClick={onVisibilityToggle}
          >
            <span className="font-medium">{project.name}</span>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className={`text-xs ${
                  project.status === "active"
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                    : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100"
                }`}
              >
                {project.status}
              </Badge>
              <Badge
                variant="outline"
                className="text-xs bg-white dark:bg-background text-muted-foreground"
              >
                {project.teams.length} teams
              </Badge>
              <Badge variant="outline" className="text-xs">
                <span className="text-muted-foreground">Cluster:</span> {project.clusterName}
              </Badge>
            </div>
          </div>

          {isAdmin && (
            <div className="flex items-center gap-2 transition-opacity duration-300 opacity-0 group-hover:opacity-100">
              <Button
                variant="outline"
                size="sm"
                className="transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                onClick={onEditProjectName}
              >
                <PenIcon className="w-3.5 h-3.5" /> Edit name
              </Button>
              <Dialog open={addTeamDialogOpen} onOpenChange={onAddTeamDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={onAddTeamClick}>
                    <UserPlusIcon className="w-4 h-4" /> Add team
                  </Button>
                </DialogTrigger>
                {addTeamDialogComponent}
              </Dialog>
              <Dialog open={deleteDialogOpen} onOpenChange={onDeleteDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={onDeleteClick} disabled={!isAdmin}>
                    <TrashIcon className="w-4 h-4" />
                    Delete Project
                  </Button>
                </DialogTrigger>
                {deleteDialogComponent}
              </Dialog>
            </div>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}
