"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui";
import { Input } from "@/components/ui/input";
import { AlertTriangleIcon } from "lucide-react";
import { ChangeEvent, useState } from "react";

type DeleteProjectDialogProps = {
  projectName: string;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
};

export function DeleteProjectDialog({
  projectName,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteProjectDialogProps) {
  const [confirmText, setConfirmText] = useState("");

  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader className="gap-4">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
          <AlertTriangleIcon className="h-6 w-6 text-red-500" />
        </div>
        <DialogTitle className="text-center text-xl">Delete Project</DialogTitle>
        <DialogDescription className="text-center">
          Are you sure you want to delete project{" "}
          <span className="font-semibold text-foreground">{projectName}</span>?
          <span className="mt-2 text-xs text-muted-foreground">
            Note: This action cannot be undone and will permanently remove all project data.
          </span>
        </DialogDescription>
      </DialogHeader>
      <div className="mt-2">
        <label htmlFor="confirm-project" className="text-sm font-base mb-1.5 block">
          Type &quot;<span className="font-semibold">{projectName}</span>&quot; to confirm:
        </label>
        <Input
          id="confirm-project"
          type="text"
          placeholder={`${projectName}`}
          onChange={(e: ChangeEvent<HTMLInputElement>) => setConfirmText(e.target.value)}
          className="text-base font-medium"
          autoComplete="off"
        />
      </div>
      <DialogFooter className="sm:justify-center gap-2 mt-4">
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={isDeleting}
          className="min-w-[100px]"
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={onConfirm}
          disabled={isDeleting || confirmText !== projectName}
          className="min-w-[100px]"
        >
          {isDeleting ? "Deleting..." : "Delete Project"}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
