"use client";

import AvatarsGroupMock from "@/components/origin-ui/avatar-group-mock";
import AvatarMock from "@/components/origin-ui/avatar-mock";
import { Button, Dialog, TableCell, TableRow } from "@/components/ui";
import { TrashIcon, UserCogIcon } from "lucide-react";
import { useState } from "react";
import { RemoveTeamDialog } from "./projects-remove-team-dialog";
import { ProjectTeam } from "./projects-types";

type ProjectTeamRowProps = {
  team: ProjectTeam;
  projectId: string;
  onRemoveTeam: (teamId: string, projectId: string) => Promise<void>;
};

export default function ProjectTeamRow({ team, projectId, onRemoveTeam }: ProjectTeamRowProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeletingTeam, setIsDeletingTeam] = useState(false);

  const handleRemoveTeam = async () => {
    try {
      setIsDeletingTeam(true);
      await onRemoveTeam(team.id, projectId);
      setIsDeletingTeam(false);
    } catch (error) {
      console.info("Error removing team:", error);
    }
  };

  return (
    <TableRow className="bg-muted/20 border-x h-12">
      <TableCell className="pl-4">
        <div className="flex items-center gap-2">
          <AvatarMock /> {<span className="font-medium px-2">{team.name}</span>}
        </div>
      </TableCell>
      <TableCell className="text-muted-foreground">{team.description || "-"}</TableCell>
      <TableCell>
        <AvatarsGroupMock />
      </TableCell>
      <TableCell>
        <Button variant="ghost" size="sm" onClick={handleRemoveTeam}>
          <UserCogIcon className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => setShowDeleteDialog(true)}>
          <TrashIcon className="w-4 h-4" />
        </Button>

        {/* Delete Member Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <RemoveTeamDialog
            teamName={team.name}
            onOpenChange={setShowDeleteDialog}
            onConfirm={handleRemoveTeam}
            isDeleting={isDeletingTeam}
          />
        </Dialog>
      </TableCell>
    </TableRow>
  );
}
