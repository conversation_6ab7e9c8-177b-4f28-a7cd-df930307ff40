"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>onte<PERSON>,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui";
import { zodResolver } from "@hookform/resolvers/zod";
import { UsersIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type Team = {
  id: string;
  name: string;
  description: string | null;
};

// Form schema
const formSchema = z.object({
  teamId: z.string({
    required_error: "Please select a team",
  }),
});

type FormValues = z.infer<typeof formSchema>;

type AddTeamDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (data: { teamId: string }) => Promise<void>;
  isAddingTeam: boolean;
  availableTeams?: Team[];
};

export function AddTeamDialog({
  onOpenChange,
  onConfirm,
  isAddingTeam,
  availableTeams = [],
}: AddTeamDialogProps) {
  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      teamId: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    await onConfirm(data);
    form.reset();
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    onOpenChange(open);
  };

  return (
    <DialogContent
      className="sm:max-w-[425px]"
      onCloseAutoFocus={() => {
        form.reset();
      }}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4">
          <div
            className="flex size-12 shrink-0 items-center justify-center rounded-full border bg-background"
            aria-hidden="true"
          >
            <UsersIcon className="size-6 text-foreground/60" />
          </div>
          <DialogHeader>
            <DialogTitle className="text-center">Assign Team to Project</DialogTitle>
            <DialogDescription className="text-center">
              Select a team to assign to this project. Team members will have access to project
              resources.
            </DialogDescription>
          </DialogHeader>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="teamId"
              render={({
                field,
              }: {
                field: { onChange: (value: string) => void; value: string };
              }) => (
                <FormItem>
                  <FormLabel>Team</FormLabel>
                  <FormControl>
                    <Select
                      disabled={isAddingTeam}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a team">
                          {field.value &&
                            availableTeams.find((team) => team.id === field.value)?.name}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {availableTeams.length > 0 ? (
                          availableTeams.map((team) => (
                            <SelectItem key={team.id} value={team.id}>
                              <div className="flex flex-col gap-1">
                                <div>{team.name}</div>
                                {team.description && (
                                  <div className="text-xs text-muted-foreground">
                                    {team.description}
                                  </div>
                                )}
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <div className="relative flex items-center justify-center py-4 text-sm text-muted-foreground">
                            No teams available
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isAddingTeam}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isAddingTeam}>
                {isAddingTeam ? "Adding..." : "Add Team"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </div>
    </DialogContent>
  );
}
