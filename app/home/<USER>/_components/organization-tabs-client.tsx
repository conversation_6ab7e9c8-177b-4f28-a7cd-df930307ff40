"use client";

import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { LockIcon } from "lucide-react";
import { parseAsString, useQueryState } from "nuqs";
import { Suspense, useEffect } from "react";
import { toast } from "sonner";
import OrganizationInformation from "./_general_components/organization-settings";
import OrganizationClustersContent from "./organization-clusters-content";
import OrganizationEmployeesContent from "./organization-employees-content";
import OrganizationProjectsContent from "./organization-projects-content";
import OrganizationTeamsContent from "./organization-teams-content";

// Valid tab values for URL validation
const VALID_TABS = ["general", "employees", "clusters", "projects", "teams"] as const;
type ValidTab = (typeof VALID_TABS)[number];

interface OrganizationTabsClientProps {
  isAdmin: boolean;
  role: string;
  success: boolean;
  message?: string;
}

export default function OrganizationTabsClient({
  isAdmin,
  role,
  success,
  message,
}: OrganizationTabsClientProps) {
  // Use nuqs for URL state management with proper typing
  const [activeTab, setActiveTab] = useQueryState(
    "tab",
    parseAsString.withDefault("general").withOptions({
      clearOnDefault: true,
      shallow: false, // Enable history entries for better back button behavior
    })
  );

  // Validate URL parameters and redirect to clean URL if invalid
  useEffect(() => {
    if (!activeTab) return; // Skip if no tab is set (will use default)

    const isValidTab = VALID_TABS.includes(activeTab as ValidTab);

    if (!isValidTab) {
      console.warn("Invalid tab parameter detected:", activeTab);

      // Reset to default (this will clear the URL due to clearOnDefault: true)
      setActiveTab("general");

      toast.info("Invalid tab parameter detected. Redirected to General tab.");
    }
  }, [activeTab, setActiveTab]);

  // Helper component for restricted access
  const AdminOnlyMessage = ({ item }: { item: string }) => (
    <Card className="flex flex-col items-center justify-center h-[80%] gap-2 text-center">
      <LockIcon className="w-6 h-6" />
      <h1 className="text-2xl font-bold">Admin access required</h1>
      <p className="text-muted-foreground">
        Sorry for the inconvenience, only admins can view {item}.
        <br />
        <br />
        Your role: {role}
        <br />
        {!success && <>Error: {message}</>}
      </p>
    </Card>
  );

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="flex w-full h-full p-6 pt-0">
      <div className="sticky top-0 z-10 px-6 py-4 bg-background">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="clusters">Clusters</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="teams">
              Teams <span className="text-xs">&nbsp;&&nbsp;</span> Members
            </TabsTrigger>
          </TabsList>
        </div>
      </div>

      <div className="flex-1 px-6 overflow-y-auto">
        <TabsContent value="general" className="h-full mt-0">
          <OrganizationInformation />
        </TabsContent>

        <TabsContent value="employees" className="h-full mt-0">
          {isAdmin ? (
            <Suspense fallback={<TeamSkeleton />}>
              <OrganizationEmployeesContent />
            </Suspense>
          ) : (
            <AdminOnlyMessage item="employee management" />
          )}
        </TabsContent>

        <TabsContent value="clusters" className="h-full mt-0">
          {isAdmin ? (
            <Suspense fallback={<TeamSkeleton />}>
              <OrganizationClustersContent isAdmin={isAdmin} />
            </Suspense>
          ) : (
            <AdminOnlyMessage item="cluster management" />
          )}
        </TabsContent>

        <TabsContent value="projects" className="h-full mt-0">
          {isAdmin ? (
            <Suspense fallback={<TeamSkeleton />}>
              <OrganizationProjectsContent isAdmin={isAdmin} />
            </Suspense>
          ) : (
            <AdminOnlyMessage item="all the organization's projects" />
          )}
        </TabsContent>

        <TabsContent value="teams" className="h-full mt-0">
          {isAdmin ? (
            <Suspense fallback={<TeamSkeleton />}>
              <OrganizationTeamsContent />
            </Suspense>
          ) : (
            <AdminOnlyMessage item="all the organization's teams" />
          )}
        </TabsContent>
      </div>
    </Tabs>
  );
}
