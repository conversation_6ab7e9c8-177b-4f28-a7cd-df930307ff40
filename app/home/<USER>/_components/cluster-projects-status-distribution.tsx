"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON>, Legend, <PERSON>, <PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TooltipProps } from "recharts";
import { NameType, ValueType } from "recharts/types/component/DefaultTooltipContent";

interface ProjectStatusDistributionProps {
  detailed?: boolean;
}

export function ProjectStatusDistribution({ detailed = false }: ProjectStatusDistributionProps) {
  const data = [
    { name: "Planning", value: 2, color: "#3b82f6" },
    { name: "In Progress", value: 4, color: "#eab308" },
    { name: "Final Review", value: 2, color: "#8b5cf6" },
    { name: "Completed", value: 1, color: "#22c55e" },
  ];

  const totalProjects = data.reduce((sum, item) => sum + item.value, 0);

  const detailedData = [
    { status: "Planning", count: 2, projects: ["Product Launch", "Employee Training Program"] },
    {
      status: "In Progress",
      count: 4,
      projects: [
        "Website Redesign",
        "Mobile App Development",
        "Marketing Campaign",
        "Data Migration",
      ],
    },
    { status: "Final Review", count: 2, projects: ["CRM Implementation", "Security Audit"] },
    { status: "Completed", count: 1, projects: ["Office Renovation"] },
  ];

  const CustomTooltip = ({ active, payload }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const percentage = Math.round((data.value / totalProjects) * 100);
      return (
        <div className="bg-background border rounded-md p-2 shadow-sm">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            {data.value} ({percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Project Status</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center">
        <div className="h-[200px] w-full relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(value) => <span className="text-sm">{value}</span>}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center flex-col pointer-events-none">
            <div className="text-2xl font-bold">{totalProjects}</div>
            <div className="text-xs text-muted-foreground">Total Projects</div>
          </div>
        </div>

        {detailed && (
          <div className="w-full mt-4 border rounded-md">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Status</th>
                  <th className="text-center p-2">Count</th>
                  <th className="text-left p-2">Projects</th>
                </tr>
              </thead>
              <tbody>
                {detailedData.map((item) => (
                  <tr key={item.status} className="border-b last:border-0">
                    <td className="p-2">{item.status}</td>
                    <td className="text-center p-2">{item.count}</td>
                    <td className="p-2 text-xs text-muted-foreground">
                      {item.projects.join(", ")}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
