"use client";

import { <PERSON><PERSON>ontent, Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui";
import React, { useState } from "react";
import { toast } from "sonner";
import {
  AddProjectDialog,
  ClusterHeaderRow,
  ClusterProjectsSection,
  ClustersEmptyState,
  ClustersListHeader,
  DeleteClusterDialog,
} from "./_clusters_components";
import {
  Cluster,
  ClusterProject,
  ClusterVisibilityState,
} from "./_clusters_components/clusters-types";

type ClustersListProps = {
  clusters: Cluster[];
  availableProjects: ClusterProject[];
  isAdmin: boolean;
};

export function ClustersList({
  clusters: initialClusters,
  availableProjects: initialAvailableProjects,
  isAdmin,
}: ClustersListProps) {
  const [clusters, setClusters] = useState<Cluster[]>(initialClusters);
  const [clusterVisibility, setClusterVisibility] = useState<ClusterVisibilityState>(() => {
    const initialVisibility: ClusterVisibilityState = {};
    initialClusters.forEach((cluster) => {
      initialVisibility[cluster.id] = false; // false = collapsed, true = expanded
    });
    return initialVisibility;
  });
  const [searchQuery, setSearchQuery] = useState("");

  // Delete cluster
  const [isDeleting, setIsDeleting] = useState(false);
  const [clusterToDelete, setClusterToDelete] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Add project to cluster
  const [addProjectDialogOpen, setAddProjectDialogOpen] = useState(false);
  const [selectedClusterId, setSelectedClusterId] = useState<string | null>(null);
  const [isAddingProject, setIsAddingProject] = useState(false);

  // Filter clusters based on search query
  const filteredClusters = clusters
    .map((cluster) => ({
      ...cluster,
      projects: cluster.projects.filter((project) => {
        if (!searchQuery) return true;
        const query = searchQuery.toLowerCase();
        return (
          project.name.toLowerCase().includes(query) ||
          (project.description?.toLowerCase().includes(query) ?? false)
        );
      }),
    }))
    .filter((cluster) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        cluster.name.toLowerCase().includes(query) ||
        (cluster.description?.toLowerCase().includes(query) ?? false) ||
        cluster.projects.length > 0
      );
    });

  const handleClusterProjectsVisible = (clusterId: string) => {
    setClusterVisibility((prev) => ({
      ...prev,
      [clusterId]: !prev[clusterId],
    }));
  };

  const handleDeleteCluster = async (clusterId: string) => {
    setIsDeleting(true);

    try {
      const { deleteClusterAction } = await import("@/db/actions/cluster.action");
      const result = await deleteClusterAction(clusterId);

      if (!result.success) {
        toast.error(result.message);
        return;
      }

      setClusters((prevClusters) => prevClusters.filter((cluster) => cluster.id !== clusterId));
      toast.success("Cluster deleted successfully");
      setDeleteDialogOpen(false);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while deleting the cluster"
      );
    } finally {
      setIsDeleting(false);
      setClusterToDelete(null);
    }
  };

  const handleAddProjectClick = (clusterId: string) => {
    setSelectedClusterId(clusterId);
    setAddProjectDialogOpen(true);
  };

  const handleAddProject = async (data: { projectId: string }) => {
    if (!selectedClusterId) {
      toast.error("Cluster not found");
      return;
    }

    // Check if project is already assigned to the cluster
    const cluster = clusters.find((c) => c.id === selectedClusterId);
    if (cluster?.projects?.some((project) => project.id === data.projectId)) {
      toast.error("This project is already assigned to the cluster");
      return;
    }

    setIsAddingProject(true);
    const toastId = toast.loading("Adding project to cluster...");

    try {
      const { assignProjectToClusterAction } = await import("@/db/actions/cluster.action");
      const result = await assignProjectToClusterAction(data.projectId, selectedClusterId);

      if (!result.success) {
        toast.error(result.message || "Failed to add project to cluster", { id: toastId });
        return;
      }

      // Find the project from available projects
      const projectToAdd = initialAvailableProjects.find(
        (project) => project.id === data.projectId
      );
      if (!projectToAdd) {
        toast.error("Project not found", { id: toastId });
        return;
      }

      // Update local state with the added project
      setClusters((prevClusters) =>
        prevClusters.map((cluster) => {
          if (cluster.id === selectedClusterId) {
            return {
              ...cluster,
              projects: [...(cluster.projects || []), projectToAdd],
            };
          }
          return cluster;
        })
      );

      toast.success("Project added to cluster successfully", { id: toastId });
      setAddProjectDialogOpen(false);
    } catch (error) {
      console.info("Error adding project to cluster:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while adding the project to the cluster",
        { id: toastId }
      );
    } finally {
      setIsAddingProject(false);
    }
  };

  const handleRemoveProject = async (projectId: string, clusterId: string) => {
    const toastId = toast.loading("Removing project from cluster...");

    try {
      const { removeProjectFromClusterAction } = await import("@/db/actions/cluster.action");
      const result = await removeProjectFromClusterAction(projectId, clusterId);

      if (!result.success) {
        toast.error(result.message || "Failed to remove project from cluster", { id: toastId });
        return;
      }

      // Update local state to remove the project
      setClusters((prevClusters) =>
        prevClusters.map((cluster) => {
          if (cluster.id === clusterId) {
            return {
              ...cluster,
              projects: (cluster.projects || []).filter((project) => project.id !== projectId),
            };
          }
          return cluster;
        })
      );

      toast.success("Project removed from cluster successfully", { id: toastId });
    } catch (error) {
      console.info("Error removing project from cluster:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while removing the project from the cluster",
        { id: toastId }
      );
    }
  };

  const handleEditClusterName = (clusterId: string) => {
    toast.info(`Feature coming soon!`, {
      description: `Edit cluster: ${clusterId}`,
    });
  };

  return (
    <div className="flex flex-col w-full border-none">
      <ClustersListHeader isAdmin={isAdmin} onSearchChange={setSearchQuery} />

      <CardContent className="min-h-0">
        {filteredClusters.length > 0 ? (
          <div className="h-full overflow-auto">
            <Table className="border-b rounded-lg">
              <TableHeader className="w-full">
                <TableRow>
                  <TableHead className="pl-4">Project</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Team Members</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="[&_tr:last-child]:border-x">
                {filteredClusters.map((cluster, index) => (
                  <React.Fragment key={cluster.id}>
                    {index > 0 && <TableRow className="h-8" />}

                    <ClusterHeaderRow
                      cluster={cluster}
                      isAdmin={isAdmin}
                      onVisibilityToggle={() => handleClusterProjectsVisible(cluster.id)}
                      onDeleteClick={() => setClusterToDelete(cluster.id)}
                      deleteDialogOpen={clusterToDelete === cluster.id && deleteDialogOpen}
                      onDeleteDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setClusterToDelete(null);
                        }
                        setDeleteDialogOpen(open);
                      }}
                      deleteDialogComponent={
                        clusterToDelete === cluster.id ? (
                          <DeleteClusterDialog
                            clusterName={cluster.name}
                            onOpenChange={setDeleteDialogOpen}
                            onConfirm={() => handleDeleteCluster(cluster.id)}
                            isDeleting={isDeleting}
                          />
                        ) : null
                      }
                      onAddProjectClick={() => handleAddProjectClick(cluster.id)}
                      addProjectDialogOpen={
                        selectedClusterId === cluster.id && addProjectDialogOpen
                      }
                      onAddProjectDialogOpenChange={(open: boolean) => {
                        if (!open) {
                          setSelectedClusterId(null);
                        }
                        setAddProjectDialogOpen(open);
                      }}
                      addProjectDialogComponent={
                        selectedClusterId === cluster.id ? (
                          <AddProjectDialog
                            open={addProjectDialogOpen}
                            onOpenChange={setAddProjectDialogOpen}
                            onConfirm={handleAddProject}
                            isAddingProject={isAddingProject}
                            availableProjects={initialAvailableProjects.filter(
                              (project) => !cluster.projects?.some((cp) => cp.id === project.id)
                            )}
                          />
                        ) : null
                      }
                      onEditClusterName={() => handleEditClusterName(cluster.id)}
                    />

                    <ClusterProjectsSection
                      clusterId={cluster.id}
                      isVisible={clusterVisibility[cluster.id]}
                      onRemoveProject={handleRemoveProject}
                      projects={cluster.projects || []}
                      isLoading={false}
                      error={null}
                    />
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <ClustersEmptyState isAdmin={isAdmin} />
        )}
      </CardContent>
    </div>
  );
}
