import { Button } from "@/components/ui";
import Link from "next/link";

type ClustersEmptyStateProps = {
  isAdmin: boolean;
};

export default function ClustersEmptyState({ isAdmin }: ClustersEmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-6">
      <h3 className="mb-2 text-lg font-medium">No clusters available</h3>
      <p className="mb-4 text-muted-foreground">
        This organization doesn&apos;t have any clusters yet. Create a new cluster to organize your
        projects.
      </p>
      {isAdmin && (
        <Link href="/home/<USER>/projects/new">
          <Button size="sm">Create Cluster</Button>
        </Link>
      )}
    </div>
  );
}
