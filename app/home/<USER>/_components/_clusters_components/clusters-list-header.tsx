import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { But<PERSON> } from "@/components/ui/button";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusIcon } from "lucide-react";
import Link from "next/link";

type ClustersListHeaderProps = {
  isAdmin: boolean;
  onSearchChange: (query: string) => void;
};

export function ClustersListHeader({ isAdmin, onSearchChange }: ClustersListHeaderProps) {
  return (
    <CardHeader className="py-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-2xl font-semibold">Clusters</CardTitle>
          <CardDescription>Manage clusters and their associated projects.</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <SearchInputWithVoice
            id="clusters-search"
            placeholder="Search clusters by name"
            className="w-[250px]"
            onChange={onSearchChange}
          />

          {isAdmin && (
            <Link href="/home/<USER>/projects/new">
              <Button variant="outline">
                <PlusIcon className="w-4 h-4" />
                New Cluster
              </Button>
            </Link>
          )}
        </div>
      </div>
    </CardHeader>
  );
}
