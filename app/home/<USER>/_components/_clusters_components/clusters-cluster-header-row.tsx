"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Table<PERSON>ell, TableRow } from "@/components/ui";
import { PenIcon, TrashIcon, UserPlusIcon } from "lucide-react";
import { ReactNode } from "react";
import { Cluster } from "./clusters-types";

type ClusterHeaderRowProps = {
  cluster: Cluster;
  isAdmin: boolean;
  onVisibilityToggle: () => void;
  onDeleteClick: () => void;
  deleteDialogOpen: boolean;
  onDeleteDialogOpenChange: (open: boolean) => void;
  deleteDialogComponent: ReactNode;
  onAddProjectClick: () => void;
  addProjectDialogOpen: boolean;
  onAddProjectDialogOpenChange: (open: boolean) => void;
  addProjectDialogComponent: ReactNode;
  onEditClusterName: () => void;
};

export default function ClusterHeaderRow({
  cluster,
  isAdmin,
  onVisibilityToggle,
  onDeleteClick,
  deleteDialogO<PERSON>,
  onDelete<PERSON><PERSON>og<PERSON><PERSON><PERSON><PERSON><PERSON>,
  deleteD<PERSON>og<PERSON><PERSON>ponent,
  onAddProjectClick,
  addProjectDialogOpen,
  onAddProjectDialogOpenChange,
  addProjectDialogComponent,
  onEditClusterName,
}: ClusterHeaderRowProps) {
  return (
    <TableRow className="w-full h-12 bg-muted/50 border-x group">
      <TableCell colSpan={6} className="py-2 pl-4">
        <div className="flex items-center justify-between">
          <div
            className="flex items-center flex-1 gap-3 cursor-pointer"
            onClick={onVisibilityToggle}
          >
            <span className="font-medium">{cluster.name}</span>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className="text-xs bg-white dark:bg-background text-muted-foreground"
              >
                {cluster.projects.length} projects
              </Badge>
            </div>
          </div>

          {isAdmin && (
            <div className="flex items-center gap-2 transition-opacity duration-300 opacity-0 group-hover:opacity-100">
              <Button
                variant="outline"
                size="sm"
                className="transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                onClick={onEditClusterName}
              >
                <PenIcon className="w-3.5 h-3.5" /> Edit name
              </Button>
              <Dialog open={addProjectDialogOpen} onOpenChange={onAddProjectDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={onAddProjectClick}>
                    <UserPlusIcon className="w-4 h-4" /> Add project
                  </Button>
                </DialogTrigger>
                {addProjectDialogComponent}
              </Dialog>
              <Dialog open={deleteDialogOpen} onOpenChange={onDeleteDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={onDeleteClick} disabled={!isAdmin}>
                    <TrashIcon className="w-4 h-4" />
                    Delete Cluster
                  </Button>
                </DialogTrigger>
                {deleteDialogComponent}
              </Dialog>
            </div>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}
