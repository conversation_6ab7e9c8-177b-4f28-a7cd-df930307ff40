"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui";
import { zodResolver } from "@hookform/resolvers/zod";
import { FolderKanbanIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type Project = {
  id: string;
  name: string;
  description: string | null;
};

// Form schema
const formSchema = z.object({
  projectId: z.string({
    required_error: "Please select a project",
  }),
});

type FormValues = z.infer<typeof formSchema>;

type AddProjectDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (data: { projectId: string }) => Promise<void>;
  isAddingProject: boolean;
  availableProjects?: Project[];
};

export function AddProjectDialog({
  onOpenChange,
  onConfirm,
  isAddingProject,
  availableProjects = [],
}: AddProjectDialogProps) {
  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      projectId: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    await onConfirm(data);
    form.reset();
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    onOpenChange(open);
  };

  return (
    <DialogContent
      className="sm:max-w-[425px]"
      onCloseAutoFocus={() => {
        form.reset();
      }}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4">
          <div
            className="flex size-12 shrink-0 items-center justify-center rounded-full border bg-background"
            aria-hidden="true"
          >
            <FolderKanbanIcon className="size-6 text-foreground/60" />
          </div>
          <DialogHeader>
            <DialogTitle className="text-center">Assign Project to Cluster</DialogTitle>
            <DialogDescription className="text-center">
              Select a project to assign to this cluster.
            </DialogDescription>
          </DialogHeader>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="projectId"
              render={({
                field,
              }: {
                field: { onChange: (value: string) => void; value: string };
              }) => (
                <FormItem>
                  <FormLabel>Project</FormLabel>
                  <FormControl>
                    <Select
                      disabled={isAddingProject}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a project">
                          {field.value &&
                            availableProjects.find((project) => project.id === field.value)?.name}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {availableProjects.length > 0 ? (
                          availableProjects.map((project) => (
                            <SelectItem key={project.id} value={project.id}>
                              <div className="flex flex-col gap-1">
                                <div>{project.name}</div>
                                {project.description && (
                                  <div className="text-xs text-muted-foreground">
                                    {project.description}
                                  </div>
                                )}
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <div className="relative flex items-center justify-center py-4 text-sm text-muted-foreground">
                            No projects available
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isAddingProject}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isAddingProject}>
                {isAddingProject ? "Adding..." : "Add Project"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </div>
    </DialogContent>
  );
}
