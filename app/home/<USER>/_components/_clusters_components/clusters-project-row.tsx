"use client";

import AvatarsGroupMock from "@/components/origin-ui/avatar-group-mock";
import { <PERSON>ton, Dialog, TableCell, TableRow } from "@/components/ui";
import { UnlinkIcon } from "lucide-react";
import { useState } from "react";
import { RemoveProjectDialog } from "./clusters-remove-project-dialog";
import { ClusterProject } from "./clusters-types";

type ClusterProjectRowProps = {
  project: ClusterProject;
  clusterId: string;
  onRemoveProject: (projectId: string, clusterId: string) => Promise<void>;
};

export default function ClusterProjectRow({
  project,
  clusterId,
  onRemoveProject,
}: ClusterProjectRowProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeletingProject, setIsDeletingProject] = useState(false);

  const handleRemoveProject = async () => {
    try {
      setIsDeletingProject(true);
      await onRemoveProject(project.id, clusterId);
      setIsDeletingProject(false);
    } catch (error) {
      console.info("Error removing project:", error);
    }
  };

  return (
    <TableRow className="bg-muted/20 border-x h-12">
      <TableCell className="pl-4">
        <div className="flex items-center gap-2">{project.name}</div>
      </TableCell>
      <TableCell className="text-muted-foreground">{project.description || "-"}</TableCell>
      <TableCell>{project.status}</TableCell>
      <TableCell>
        <AvatarsGroupMock />
      </TableCell>
      <TableCell>
        <Button variant="ghost" size="sm" onClick={() => setShowDeleteDialog(true)}>
          <UnlinkIcon className="w-4 h-4" />
        </Button>

        {/* Delete Project Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <RemoveProjectDialog
            projectName={project.name}
            onOpenChange={setShowDeleteDialog}
            onConfirm={handleRemoveProject}
            isDeleting={isDeletingProject}
          />
        </Dialog>
      </TableCell>
    </TableRow>
  );
}
