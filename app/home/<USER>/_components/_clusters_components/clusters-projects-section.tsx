"use client";

import { TableCell, TableRow } from "@/components/ui";
import ClusterProjectRow from "./clusters-project-row";
import { ClusterProject } from "./clusters-types";

type ClusterProjectsSectionProps = {
  clusterId: string;
  isVisible: boolean; // When true, component renders nothing (projects are hidden). When false, projects are displayed.
  projects: ClusterProject[];
  isLoading?: boolean;
  error?: string | null;
  onRemoveProject: (projectId: string, clusterId: string) => Promise<void>;
};

export function ClusterProjectsSection({
  clusterId,
  isVisible,
  projects,
  isLoading = false,
  error = null,
  onRemoveProject,
}: ClusterProjectsSectionProps) {
  // If not visible, don't render anything - this allows controlling visibility from parent
  if (isVisible) return null;

  if (isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="text-muted-foreground pl-8 h-12 border-x">
          Loading projects...
        </TableCell>
      </TableRow>
    );
  }

  if (error) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="pl-8 text-destructive border-x h-12">
          {error}
        </TableCell>
      </TableRow>
    );
  }

  if (projects.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={6} className="text-muted-foreground pl-8 border-x h-12">
          No projects assigned to this cluster yet.
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {projects.map((project) => (
        <ClusterProjectRow
          key={project.id}
          project={project}
          clusterId={clusterId}
          onRemoveProject={onRemoveProject}
        />
      ))}
    </>
  );
}
