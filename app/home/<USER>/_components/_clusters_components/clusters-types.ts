// Type definitions for clusters and projects
export type ClusterProject = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  status: string;
};

export type Cluster = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  projects: ClusterProject[];
};

// Type for tracking which clusters are expanded
export type ClusterVisibilityState = Record<string, boolean>;

// Type for tracking loading states
export type LoadingState = Record<string, { type: string; loading: boolean; success: boolean }>;
