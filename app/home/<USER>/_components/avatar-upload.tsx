"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Loader2, UploadCloud, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { updateAvatar } from "../_actions/update-avatar.action";

interface AvatarUploadProps {
  initialAvatarUrl?: string | null;
  userName: string;
}

export function AvatarUpload({ initialAvatarUrl, userName }: AvatarUploadProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [isUploading, setIsUploading] = useState(false);

  // const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (!file) return;

  //   try {
  //     setIsUploading(true);

  //     const formData = new FormData();
  //     formData.append("avatar", file);

  //     // Upload the file to storage
  //     const { url, error: uploadError } = await uploadAvatar(formData);

  //     if (uploadError) {
  //       toast.error("Upload failed", { description: uploadError });
  //       return;
  //     }

  //     if (url) {
  //       // Update the user's profile with the new avatar URL
  //       const { success, error: updateError } = await updateAvatar(url);

  //       if (!success) {
  //         toast.error("Profile update failed", {
  //           description: updateError || "Failed to update profile",
  //         });
  //         return;
  //       }

  //       setAvatarUrl(url);
  //       toast.success("Avatar updated", {
  //         description: "Your profile picture has been updated successfully.",
  //       });
  //     }
  //   } catch (err) {
  //     toast.error("Upload error", { description: "An error occurred while uploading the avatar" });
  //     console.info(err);
  //   } finally {
  //     setIsUploading(false);
  //   }
  // };

  const tmp_handleFileChange = async () => {
    toast.info("Upload is disabled for now", {
      description: "Sorry for the inconvenience",
    });
  };

  const handleRemoveAvatar = async () => {
    try {
      setIsUploading(true);

      // Update the user's profile with null for avatar URL
      const { success, error: updateError } = await updateAvatar(null);

      if (!success) {
        toast.error("Avatar removal failed", {
          description: updateError || "Failed to remove avatar",
        });
        return;
      }

      setAvatarUrl(null);
      toast.success("Avatar removed", { description: "Your profile picture has been removed." });
    } catch (err) {
      toast.error("Removal error", { description: "An error occurred while removing the avatar" });
      console.info(err);
    } finally {
      setIsUploading(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <Avatar className="w-24 h-24">
        {avatarUrl ? <AvatarImage src={avatarUrl} alt={userName} /> : null}
        <AvatarFallback className="text-lg">{getInitials(userName)}</AvatarFallback>
      </Avatar>

      <div className="flex gap-2">
        <div className="relative">
          <Input
            // type="file"
            id="avatar-upload"
            className="sr-only"
            onClick={tmp_handleFileChange}
            accept="image/*"
            disabled={isUploading}
          />
          <Label
            htmlFor="avatar-upload"
            className={cn(
              "cursor-pointer inline-flex items-center justify-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3",
              isUploading && "opacity-50 pointer-events-none"
            )}
          >
            {isUploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <UploadCloud className="w-4 h-4 mr-2" />
                Upload
              </>
            )}
          </Label>
        </div>

        {avatarUrl && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleRemoveAvatar}
                  disabled={isUploading || !avatarUrl}
                >
                  <X className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Remove avatar</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}
