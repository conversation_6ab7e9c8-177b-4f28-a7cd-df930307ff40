"use client";

import TeamSkeleton from "@/components/skeletons/team-skeleton";
import { useOrganizationTeamsData } from "@/app/home/<USER>/use-organization-teams";
import TeamsList from "./organization-teams-list";

export default function OrganizationTeamsContentClient() {
  const { teams, currentUserName, isAdmin, isLoading, isError, error } = useOrganizationTeamsData();

  if (isLoading) {
    return <TeamSkeleton />;
  }

  if (isError) {
    console.info("Error fetching organization teams:", error);
    return (
      <div className="py-4">
        <p className="text-red-500">Error loading teams. Please try again later.</p>
      </div>
    );
  }

  return <TeamsList teams={teams} currentUserName={currentUserName} isAdmin={isAdmin} />;
}
