"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Position } from "@/types/team.types";
import { UserProfileDB } from "@/types/user.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Check, ChevronDown, CopyIcon, Info, Loader2 } from "lucide-react";
import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { ProfileFormValues, updateProfile } from "../_actions/update-profile.action";
import { AvatarUpload } from "./avatar-upload";

// Array of positions from the Position type
const POSITIONS: Position[] = [
  "Developer",
  "Lead Developer",
  "Product Manager",
  "Project Manager",
  "UI/UX Designer",
  "QA Engineer",
  "QA Lead",
  "Engineering Manager",
  "CTO",
  "VP of Engineering",
  "CEO",
];

// Array of location regions
const LOCATIONS = [
  "North America",
  "South America",
  "Europe West",
  "Europe East",
  "Middle East",
  "Africa",
  "Asia Pacific",
  "Oceania",
];

// Create a schema for form validation
const formSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").optional(),
  position: z.string().optional(),
  department: z.string().optional(),
  location: z.string().optional(),
});

interface ProfileFormProps {
  profile: UserProfileDB;
}

export function ProfileForm({ profile }: ProfileFormProps) {
  const [isPending, startTransition] = useTransition();

  // Initialize form with user data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: profile.first_name || "",
      last_name: profile.last_name || "",
      email: profile.email || "",
      position: profile.position || "",
      department: profile.department || "",
      location: profile.location || "",
    },
  });

  const onSubmit = (values: ProfileFormValues) => {
    startTransition(async () => {
      const result = await updateProfile(values);

      if (result.success) {
        toast.success("Profile updated", {
          description: "Your profile has been updated successfully.",
        });
      } else {
        toast.error("Error", {
          description: result.error || "Failed to update profile",
        });
      }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-2 text-xl font-semibold tracking-tight">Your Profile</h2>
        <p className="text-sm text-muted-foreground">
          Update your personal information and profile settings.
        </p>
      </div>

      <Separator />

      <div className="grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-6">
        <div className="flex flex-col items-center gap-4">
          <AvatarUpload
            initialAvatarUrl={profile.avatar_url}
            userName={`${profile.first_name || ""} ${profile.last_name || ""}`}
          />
          <span className="flex flex-col items-center gap-1">
            <p className="flex items-center gap-2 text-sm text-center truncate text-muted-foreground">
              <CopyIcon className="w-3 h-3" />
              {profile.user_id}
            </p>
            <p className="text-sm text-muted-foreground">
              Joined on {new Date(profile.joined_date || profile.created_at || "").toDateString()}
            </p>
          </span>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>First name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" className="w-full" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Last name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" className="w-full" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Position</FormLabel>
                    <FormControl>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="justify-between w-full font-normal">
                            {field.value || "Select your position"}
                            <ChevronDown className="w-4 h-4 opacity-50" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-full min-w-[240px]">
                          {POSITIONS.map((position) => (
                            <DropdownMenuItem
                              key={position}
                              onClick={() => field.onChange(position)}
                              className={cn(
                                "flex items-center gap-2",
                                field.value === position && "bg-accent"
                              )}
                            >
                              {position}
                              {field.value === position && <Check className="w-4 h-4 ml-auto" />}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Engineering" className="w-full" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Location</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="w-4 h-4 cursor-pointer text-muted-foreground/90" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            We collect region data for analytics purposes only. Your exact location
                            will not be revealed.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormControl>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="justify-between w-full font-normal">
                          {field.value || "Select your region"}
                          <ChevronDown className="w-4 h-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full min-w-[240px]">
                        {LOCATIONS.map((location) => (
                          <DropdownMenuItem
                            key={location}
                            onClick={() => field.onChange(location)}
                            className={cn(
                              "flex items-center gap-2",
                              field.value === location && "bg-accent"
                            )}
                          >
                            {location}
                            {field.value === location && <Check className="w-4 h-4 ml-auto" />}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save changes"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
