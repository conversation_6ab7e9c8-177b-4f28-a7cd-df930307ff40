"use client";

import {
  <PERSON><PERSON>,
  AvatarFallback,
  AvatarImage,
  Button,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getAllOrganizationMembersAction } from "@/db/actions/organization.action";
import { getUserProfilePositionFromDb } from "@/db/profile.db";
import { cn } from "@/lib/utils";
import { Position, Role } from "@/types/team.types";
import { Check, ChevronsUpDown, InfoIcon, Loader2, User, X } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

// Define the interface to match what comes from getAllOrganizationMembersAction
interface OrganizationMember {
  id: string;
  userId: string;
  name: string;
  email: string;
  avatarUrl?: string | null;
  tenantId: string;
}

type AddTeamMemberDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (data: {
    userId: string;
    role: Role;
    position: Position;
    name: string;
    email: string;
    avatarUrl?: string;
    tenantId: string;
  }) => void;
  isAddingMember?: boolean;
};

export default function AddTeamMemberDialog({
  open,
  onOpenChange,
  onConfirm,
  isAddingMember = false,
}: AddTeamMemberDialogProps) {
  const [selectedMember, setSelectedMember] = useState<OrganizationMember | null>(null);
  const [role, setRole] = useState<Role | "">("");
  const [userPosition, setUserPosition] = useState<string | undefined>(undefined);
  const [openCombobox, setOpenCombobox] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // State for all members
  const [allMembers, setAllMembers] = useState<OrganizationMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all members when dialog opens
  useEffect(() => {
    if (open) {
      fetchAllMembers();
    }
  }, [open]);

  // Fetch user position when a member is selected
  useEffect(() => {
    const fetchUserPosition = async () => {
      if (selectedMember?.userId) {
        try {
          const position = await getUserProfilePositionFromDb(selectedMember.userId);
          // Check if position is a string directly or needs to be accessed as an object
          const positionValue = typeof position === "string" ? position : position?.position;
          setUserPosition(positionValue || "Not specified");
        } catch (error) {
          toast.error(`Error fetching user position: ${error}`);
          setUserPosition("Not available");
        }
      } else {
        setUserPosition(undefined);
      }
    };

    fetchUserPosition();
  }, [selectedMember]);

  // Filter members based on search query
  const filteredMembers = useMemo(() => {
    if (!searchQuery.trim()) return allMembers;

    const query = searchQuery.toLowerCase();
    return allMembers.filter(
      (member) =>
        member.name.toLowerCase().includes(query) || member.email.toLowerCase().includes(query)
    );
  }, [allMembers, searchQuery]);

  // Function to fetch all organization members using server action
  const fetchAllMembers = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the server action instead of fetch API
      const result = await getAllOrganizationMembersAction();

      if (result.data) {
        // The server action returns the data with the field names in our interface
        setAllMembers(result.data);
      } else {
        throw new Error("No members data returned");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load organization members";
      setError(errorMessage);
      console.info("Member fetch error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!selectedMember || !role) return;

    // Pass complete user data to the parent component
    onConfirm({
      userId: selectedMember.userId,
      role: role as Role,
      position:
        userPosition && userPosition !== "Select a member"
          ? (userPosition as Position)
          : ("Not specified" as Position),
      // Include additional user data needed for UI updates
      name: selectedMember.name,
      email: selectedMember.email,
      avatarUrl: selectedMember.avatarUrl || undefined,
      tenantId: selectedMember.tenantId,
    });
  };

  const handleReset = () => {
    setSelectedMember(null);
    setSearchQuery("");
    setRole("");
    setOpenCombobox(false);
    setUserPosition(undefined);
    // No need to clear allMembers as they will be refreshed when the dialog reopens
  };

  // Update roles to match the database schema (uppercase values)
  const roles: Role[] = ["admin", "member", "guest"];

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen) handleReset();
        onOpenChange(newOpen);
      }}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add team member</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="member-search">
              Search member
              <span className="ml-1 text-red-500">*</span>
            </Label>
            <Popover open={openCombobox} onOpenChange={setOpenCombobox}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openCombobox}
                  className="justify-between w-full"
                  disabled={isAddingMember}
                >
                  {selectedMember ? (
                    <div className="flex items-center gap-2 text-left">
                      {selectedMember.avatarUrl ? (
                        <Avatar className="w-6 h-6 rounded-full">
                          <AvatarImage src={selectedMember.avatarUrl} alt="User" />
                          <AvatarFallback>
                            {selectedMember.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      ) : (
                        <User className="w-4 h-4 opacity-50" />
                      )}
                      <span>{selectedMember.name}</span>
                    </div>
                  ) : (
                    "Select a member..."
                  )}
                  <div className="flex">
                    {selectedMember && (
                      <X
                        className="w-4 h-4 mr-2 opacity-50 shrink-0 hover:opacity-100"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedMember(null);
                          setSearchQuery("");
                        }}
                      />
                    )}
                    <ChevronsUpDown className="w-4 h-4 opacity-50 shrink-0" />
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0" align="start">
                <Command shouldFilter={false}>
                  <CommandInput
                    placeholder="Search by name or email..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                    disabled={isLoading}
                    autoFocus
                  />
                  {isLoading && (
                    <div className="flex items-center justify-center py-6">
                      <Loader2 className="w-6 h-6 animate-spin text-primary" />
                    </div>
                  )}
                  {error && (
                    <div className="py-6 text-sm text-center text-destructive">{error}</div>
                  )}
                  <CommandList>
                    <CommandEmpty>No members found.</CommandEmpty>
                    {filteredMembers.length > 0 && (
                      <CommandGroup>
                        {filteredMembers.map((member) => (
                          <CommandItem
                            key={member.id}
                            value={member.id}
                            onSelect={() => {
                              setSelectedMember(member);
                              setOpenCombobox(false);
                            }}
                            className="flex items-center gap-2"
                          >
                            <div className="flex items-center flex-1 gap-2">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src={member.avatarUrl || undefined} alt="User" />
                                <AvatarFallback>
                                  {member.name.substring(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex flex-col">
                                <span>{member.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {member.email}
                                </span>
                              </div>
                            </div>
                            <Check
                              className={cn(
                                "h-4 w-4",
                                selectedMember?.id === member.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="grid grid-cols-1 gap-2">
              <Label htmlFor="role">
                Role <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select
                value={role}
                onValueChange={(value) => setRole(value as Role)}
                disabled={isAddingMember}
              >
                <SelectTrigger id="role" className="w-full">
                  <SelectValue placeholder="Choose a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((roleOption) => (
                    <SelectItem key={roleOption} value={roleOption}>
                      {roleOption === "admin"
                        ? "Admin"
                        : roleOption === "member"
                        ? "Member"
                        : roleOption === "guest"
                        ? "Guest"
                        : roleOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-1 gap-2">
              <Label htmlFor="position" className="flex items-center justify-between gap-4">
                Position <InfoIcon className="w-3.5 h-3.5 text-muted-foreground/50" />
              </Label>
              <Input
                id="position"
                disabled={true}
                value={userPosition || "Select a member"}
                className="bg-muted"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isAddingMember}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!selectedMember || !role || isAddingMember}
            className={isAddingMember ? "opacity-50 cursor-not-allowed" : ""}
          >
            {isAddingMember ? "Adding..." : "Add"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
