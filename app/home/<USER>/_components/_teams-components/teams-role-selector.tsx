"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui";
import { Role } from "@/types/team.types";

type RoleSelectorProps = {
  memberId: string;
  currentRole: Role;
  isAdmin: boolean;
  isLoading: boolean;
  onRoleChange: (memberId: string, newRole: string) => void;
};

export default function RoleSelector({
  memberId,
  currentRole,
  isAdmin,
  isLoading,
  onRoleChange,
}: RoleSelectorProps) {
  return (
    <Select
      defaultValue={currentRole}
      disabled={!isAdmin || isLoading}
      onValueChange={(value) => onRoleChange(memberId, value)}
    >
      <SelectTrigger className="w-32">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="admin">Admin</SelectItem>
        <SelectItem value="member">Member</SelectItem>
        <SelectItem value="guest">Guest</SelectItem>
      </SelectContent>
    </Select>
  );
}
