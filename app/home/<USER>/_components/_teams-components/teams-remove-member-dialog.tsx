"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui";
import { AlertTriangleIcon } from "lucide-react";

type DeleteTeamDialogProps = {
  memberName: string;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
};

export function RemoveMemberDialog({
  memberName,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteTeamDialogProps) {
  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader className="gap-4">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
          <AlertTriangleIcon className="h-6 w-6 text-red-500" />
        </div>
        <DialogTitle className="text-center text-xl">Remove Member</DialogTitle>
        <DialogDescription className="text-center">
          Are you sure you want to remove member{" "}
          <span className="font-semibold text-foreground">{memberName}</span> from this team?
          <span className="mt-2 text-xs text-muted-foreground">
            Note: This action cannot be undone and may affect team members&apos; access.
          </span>
        </DialogDescription>
      </DialogHeader>
      <DialogFooter className="sm:justify-center gap-2 mt-2">
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={isDeleting}
          className="min-w-[100px]"
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={onConfirm}
          disabled={isDeleting}
          className="min-w-[100px]"
        >
          {isDeleting ? "Removing..." : "Remove Member"}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
