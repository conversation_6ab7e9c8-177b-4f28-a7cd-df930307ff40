import { Button } from "@/components/ui";
import Link from "next/link";

type TeamsEmptyStateProps = {
  isAdmin: boolean;
};

export default function TeamsEmptyState({ isAdmin }: TeamsEmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-6">
      <h3 className="mb-2 text-lg font-medium">You are not a member of any team</h3>
      <p className="mb-4 text-muted-foreground">
        There are no teams yet or no matching members for your search.
      </p>
      {isAdmin && (
        <Link href="/home/<USER>/teams/new">
          <Button size="sm">Create Team</Button>
        </Link>
      )}
    </div>
  );
}
