"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableCell, TableRow } from "@/components/ui";
import { cn } from "@/lib/utils";
import { TeamUI } from "@/types/team.types";
import { PenIcon, TrashIcon, UserPlusIcon } from "lucide-react";
import { ReactNode } from "react";

type TeamHeaderRowProps = {
  team: TeamUI;
  isAdmin: boolean;
  onVisibilityToggle: () => void;
  onDeleteClick: () => void;
  deleteDialogOpen: boolean;
  onDeleteDialogOpenChange: (open: boolean) => void;
  deleteDialogComponent: ReactNode;
  onAddMemberClick: () => void;
  addMemberDialogOpen: boolean;
  onAddMemberDialogOpenChange: (open: boolean) => void;
  addMemberDialogComponent: ReactNode;
  onEditTeamName: () => void;
};

export default function TeamHeaderRow({
  team,
  isAdmin,
  onVisibilityToggle,
  onDeleteClick,
  deleteD<PERSON>og<PERSON><PERSON>,
  onDeleteDialogOpenChange,
  deleteDialogComponent,
  onAddMemberClick,
  addMemberDialogOpen,
  onAddMemberDialogOpenChange,
  addMemberDialogComponent,
  onEditTeamName,
}: TeamHeaderRowProps) {
  return (
    <TableRow className="w-full h-12 bg-muted/50 border-x group">
      <TableCell colSpan={5} className="py-2 pl-4">
        <div className="flex items-center justify-between">
          <div
            className="flex items-center flex-1 gap-6 cursor-pointer"
            onClick={onVisibilityToggle}
          >
            <span className="group">{team.name}</span>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className="text-xs bg-white dark:bg-background text-muted-foreground"
              >
                {team.members.length} members
              </Badge>
              <Badge
                variant="outline"
                className="text-xs bg-white dark:bg-background text-muted-foreground"
              >
                {team.projects.length} projects
              </Badge>
            </div>
          </div>

          {isAdmin && (
            <div className="flex items-center gap-2 transition-opacity duration-300 opacity-0 group-hover:opacity-100">
              <Button
                variant="outline"
                size="sm"
                className="transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                onClick={onEditTeamName}
              >
                <PenIcon className="w-3.5 h-3.5" /> Edit name
              </Button>
              <Dialog open={addMemberDialogOpen} onOpenChange={onAddMemberDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={onAddMemberClick}>
                    <UserPlusIcon className="w-4 h-4" /> Add user
                  </Button>
                </DialogTrigger>
                {addMemberDialogComponent}
              </Dialog>
              <Dialog open={deleteDialogOpen} onOpenChange={onDeleteDialogOpenChange}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                      " cursor-not-allowed",
                      team.members.length > 0 || team.projects.length > 0
                        ? "cursor-not-allowed"
                        : ""
                    )}
                    onClick={onDeleteClick}
                  >
                    <TrashIcon className="w-4 h-4" />
                    Delete Team
                  </Button>
                </DialogTrigger>
                {deleteDialogComponent}
              </Dialog>
            </div>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}
