"use client";

import { TableCell, TableRow } from "@/components/ui";
import { TeamUI } from "@/types/team.types";
import React from "react";
import { LoadingState } from "../organization-teams-list";
import TeamMemberRow from "./teams-team-member-row";

type TeamMembersSectionProps = {
  team: TeamUI;
  teams: TeamUI[];
  isVisible: boolean;
  isAdmin: boolean;
  currentUserName: string;
  loadingState: LoadingState;
  onRoleChange: (memberId: string, newRole: string) => void;
  onPositionChange: (memberId: string, newPosition: string) => void;
  onMoveTeamMember: (memberId: string, newTeamId: string) => void;
  onMemberRemoved: (memberId: string, teamId: string) => void;
};

export default function TeamMembersSection({
  team,
  teams,
  isVisible,
  isAdmin,
  currentUserName,
  loadingState,
  onRoleChange,
  onPositionChange,
  onMoveTeamMember,
  onMemberRemoved,
}: TeamMembersSectionProps) {
  if (isVisible) return null;

  if (team.members.length === 0) {
    return (
      <TableRow className="border-x">
        <TableCell colSpan={5} className="py-4 text-center text-muted-foreground">
          Does not have any members
        </TableCell>
      </TableRow>
    );
  }

  return (
    <React.Fragment key={team.id + team.members.length}>
      {team.members.map((member) => (
        <TeamMemberRow
          key={member.id + team.id + team.members.length}
          member={member}
          team={team}
          teams={teams}
          isAdmin={isAdmin}
          currentUserName={currentUserName}
          loadingState={loadingState}
          onRoleChange={onRoleChange}
          onPositionChange={onPositionChange}
          onMoveTeamMember={onMoveTeamMember}
          onMemberRemoved={onMemberRemoved}
        />
      ))}
    </React.Fragment>
  );
}
