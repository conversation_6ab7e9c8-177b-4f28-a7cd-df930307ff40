"use client";

import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { Button, CardDescription, CardHeader, CardTitle } from "@/components/ui";
import { ArrowUpRightIcon, PlusIcon } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

type TeamsListHeaderProps = {
  isAdmin: boolean;
  onSearchChange: (query: string) => void;
};

export default function TeamsListHeader({ isAdmin, onSearchChange }: TeamsListHeaderProps) {
  const path = usePathname();
  const isMyTeamPage = path?.includes("/home/<USER>");
  return (
    <CardHeader className="py-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-2xl font-semibold">Team Members</CardTitle>
          <CardDescription>Manage team members and their roles.</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <SearchInputWithVoice
            id="team-members-search"
            placeholder="Search by name, email, role or position"
            className="w-[350px]"
            onChange={onSearchChange}
          />
          {isAdmin && (
            <>
              <Link href={isMyTeamPage ? "/home/<USER>" : "/home/<USER>/teams/new"}>
                <Button variant="outline">
                  {isMyTeamPage ? (
                    <ArrowUpRightIcon className="w-4 h-4" />
                  ) : (
                    <PlusIcon className="w-4 h-4" />
                  )}
                  {isMyTeamPage ? "Access all teams" : "New Team"}
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </CardHeader>
  );
}
