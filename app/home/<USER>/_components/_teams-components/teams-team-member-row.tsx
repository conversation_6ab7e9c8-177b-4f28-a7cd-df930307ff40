"use client";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Dialog,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  TableCell,
  TableRow,
} from "@/components/ui";
import { TeamMemberUI, TeamUI } from "@/types/team.types";
import { ArrowLeftRightIcon, UserMinusIcon } from "lucide-react";
import { useState } from "react";
import { LoadingState } from "../organization-teams-list";
import { PositionSelector, RoleSelector } from "./index";
import { RemoveMemberDialog } from "./teams-remove-member-dialog";

type TeamMemberRowProps = {
  member: TeamMemberUI;
  team: TeamUI;
  teams: TeamUI[];
  isAdmin: boolean;
  currentUserName: string;
  loadingState: LoadingState;
  onRoleChange: (memberId: string, newRole: string) => void;
  onPositionChange: (memberId: string, newPosition: string) => void;
  onMoveTeamMember: (memberId: string, newTeamId: string) => void;
  onMemberRemoved: (memberId: string, teamId: string) => void;
};

export default function TeamMemberRow({
  member,
  team,
  teams,
  isAdmin,
  currentUserName,
  loadingState,
  onRoleChange,
  onPositionChange,
  onMoveTeamMember,
  onMemberRemoved,
}: TeamMemberRowProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeletingMember, setIsDeletingMember] = useState(false);

  const handleMemberRemove = async () => {
    setIsDeletingMember(true);
    await onMemberRemoved(member.id, team.id);
    setIsDeletingMember(false);
    setShowDeleteDialog(false);
  };

  return (
    <TableRow className="border-x">
      {/* Member */}
      <TableCell>
        <div className="flex items-center gap-3 pl-2">
          <Avatar className="w-8 h-8">
            <AvatarImage src={member.avatarUrl} alt={member.name} />
            <AvatarFallback>
              {member.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col px-2">
            <span className="font-medium">{member.name}</span>
            <span className="text-sm text-muted-foreground">{member.email}</span>
          </div>
          {member.name === currentUserName && <Badge variant="default">You</Badge>}
        </div>
      </TableCell>
      {/* Date Added */}
      <TableCell>{new Date(member.createdAt).toDateString()}</TableCell>
      {/* Role */}
      <TableCell>
        <div className="flex items-center gap-2">
          <RoleSelector
            memberId={member.id}
            currentRole={member.role}
            isAdmin={isAdmin}
            isLoading={loadingState[member.id]?.type === "role" && loadingState[member.id]?.loading}
            onRoleChange={onRoleChange}
          />
        </div>
      </TableCell>
      {/* Position */}
      <TableCell>
        <div className="flex items-center gap-2">
          <PositionSelector
            memberId={member.id}
            currentPosition={member.position}
            isAdmin={isAdmin}
            isLoading={
              loadingState[member.id]?.type === "position" && loadingState[member.id]?.loading
            }
            onPositionChange={onPositionChange}
          />
        </div>
      </TableCell>
      {/* Actions */}
      <TableCell>
        {isAdmin && (
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ArrowLeftRightIcon className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="text-muted-foreground">Move to</DropdownMenuLabel>
                {teams
                  .filter((t) => t.id !== team.id)
                  .map((t) => (
                    <DropdownMenuItem key={t.id} onClick={() => onMoveTeamMember(member.id, t.id)}>
                      {t.name}
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="ghost" size="sm" onClick={() => setShowDeleteDialog(true)}>
              <UserMinusIcon className="w-4 h-4" />
            </Button>

            {/* Delete Member Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <RemoveMemberDialog
                memberName={member.name}
                onOpenChange={setShowDeleteDialog}
                onConfirm={handleMemberRemove}
                isDeleting={isDeletingMember}
              />
            </Dialog>
          </div>
        )}
        {!isAdmin && <span className="text-sm text-muted-foreground">Admin role required</span>}
      </TableCell>
    </TableRow>
  );
}
