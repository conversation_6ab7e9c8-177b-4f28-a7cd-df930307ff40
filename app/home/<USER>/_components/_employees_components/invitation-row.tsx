"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
  TableCell,
  TableRow,
} from "@/components/ui";
import {
  cancelOrganizationInvitationAction,
  resendOrganizationInvitationAction,
} from "@/db/actions/organization-invitations.action";
import { OrganizationInfoType } from "@/types/organization.types";
import { AlertTriangleIcon, MailIcon, RefreshCcwIcon, XCircleIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { Invitation } from "./invitation-types";

type InvitationRowProps = {
  invitation: Invitation;
  isAdmin: boolean;
  organization: OrganizationInfoType;
};

export default function InvitationRow({ invitation, isAdmin, organization }: InvitationRowProps) {
  const [isPending, startTransition] = useTransition();
  const [actionType, setActionType] = useState<"cancel" | "resend" | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const router = useRouter();

  // Format the invitation creation date
  const formattedDate = invitation.created_at
    ? new Date(invitation.created_at).toDateString()
    : "N/A";

  // Format the invitation expiration date
  const formattedExpiryDate = invitation.expires_at
    ? new Date(invitation.expires_at).toDateString()
    : "N/A";

  // Check if the invitation is expired
  const isExpired = invitation.expires_at ? new Date(invitation.expires_at) < new Date() : false;

  const handleCancelInvitation = () => {
    setActionType("cancel");
    startTransition(async () => {
      try {
        const result = await cancelOrganizationInvitationAction(invitation.id, organization);
        if (result.success) {
          toast.success("Invitation cancelled successfully");
          setShowCancelDialog(false);
          // Refresh to update UI with server data
          router.refresh();
        } else {
          toast.error(result.message || "Failed to cancel invitation");
        }
      } catch (error) {
        toast.error("An unexpected error occurred", {
          description: error instanceof Error ? error.message : "Unknown error",
        });
      } finally {
        setActionType(null);
      }
    });
  };

  const handleResendInvitation = () => {
    setActionType("resend");
    startTransition(async () => {
      try {
        const result = await resendOrganizationInvitationAction(invitation.id, organization);
        if (result.success) {
          toast.success(
            result.emailSent
              ? "Invitation resent successfully. The recipient will receive a new email."
              : "Invitation updated successfully, but the email could not be sent."
          );
          // Refresh to update UI with server data
          router.refresh();
        } else {
          toast.error(result.message || "Failed to resend invitation");
        }
      } catch (error) {
        toast.error("An unexpected error occurred", {
          description: error instanceof Error ? error.message : "Unknown error",
        });
      } finally {
        setActionType(null);
      }
    });
  };

  const isCancelling = isPending && actionType === "cancel";
  const isResending = isPending && actionType === "resend";

  return (
    <TableRow className="border-x">
      {/* Email */}
      <TableCell>
        <div className="flex items-center gap-3 pl-2">
          <MailIcon className="w-4 h-4 text-muted-foreground" />
          <span className="font-medium">{invitation.email}</span>
        </div>
      </TableCell>

      {/* Status */}
      <TableCell>
        <div className="flex items-center">
          <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
            {isExpired ? "Expired" : "Pending"}
          </span>
        </div>
      </TableCell>

      {/* Sent Date */}
      <TableCell>{formattedDate}</TableCell>

      {/* Expires */}
      <TableCell>{formattedExpiryDate}</TableCell>

      {/* Role */}
      <TableCell className="text-muted-foreground">Member</TableCell>

      {/* Actions */}
      <TableCell>
        {isAdmin && (
          <div className="flex items-center gap-2 max-w-12">
            <Button variant="ghost" size="sm" onClick={handleResendInvitation} disabled={isPending}>
              <RefreshCcwIcon className={`w-4 h-4 ${isResending ? "animate-spin" : ""}`} />
            </Button>

            <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" disabled={isPending}>
                  <XCircleIcon className="w-4 h-4" />
                </Button>
              </DialogTrigger>

              <DialogContent className="sm:max-w-md">
                <DialogHeader className="gap-4">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
                    <AlertTriangleIcon className="h-6 w-6 text-red-500" />
                  </div>
                  <DialogTitle className="text-center text-xl">Cancel Invitation</DialogTitle>
                  <DialogDescription className="text-center">
                    Are you sure you want to cancel the invitation sent to{" "}
                    <span className="font-semibold text-foreground">{invitation.email}</span>?
                  </DialogDescription>
                </DialogHeader>

                <DialogFooter className="sm:justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowCancelDialog(false)}
                    disabled={isCancelling}
                    className="min-w-[100px]"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleCancelInvitation}
                    disabled={isCancelling}
                    className="min-w-[100px]"
                  >
                    {isCancelling ? "Cancelling..." : "Cancel Invitation"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </TableCell>
    </TableRow>
  );
}
