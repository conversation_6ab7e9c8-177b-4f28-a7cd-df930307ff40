"use client";

import { Badge, TableCell, TableRow } from "@/components/ui";
import { useSubdomain } from "@/hooks/use-subdomain";

type OrganizationHeaderProps = {
  memberCount: number;
};

export default function OrganizationHeader({ memberCount = 0 }: OrganizationHeaderProps) {
  const subdomain = useSubdomain();
  const organizationName =
    (subdomain && subdomain?.charAt(0).toUpperCase() + subdomain?.slice(1)) || "Organization";
  return (
    <TableRow className="w-full h-12 bg-muted/50 border-x group">
      <TableCell colSpan={6} className="py-2 pl-4">
        <div className="flex items-center gap-2">
          <span className="font-medium">{organizationName}</span>
          <Badge
            variant="outline"
            className="text-xs bg-white dark:bg-background text-muted-foreground"
          >
            {memberCount} employee{memberCount !== 1 ? "s" : ""}
          </Badge>
        </div>
      </TableCell>
    </TableRow>
  );
}
