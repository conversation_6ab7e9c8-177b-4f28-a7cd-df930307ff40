"use client";

import {
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui";
import { Skeleton } from "@/components/ui/skeleton";
import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { OrganizationInfoType } from "@/types/organization.types";
import { useQuery } from "@tanstack/react-query";
import InvitationRow from "./invitation-row";

type InvitationsListProps = {
  isAdmin: boolean;
  organization: OrganizationInfoType;
};

// Hook to fetch pending invitations
function usePendingInvitations(organizationId: string, organizationName: string) {
  return useQuery({
    queryKey: ["home", "organization", "invitations", organizationName],
    queryFn: async (): Promise<Invitation[]> => {
      const response = await fetch(
        `/api/organization/invitations?organizationId=${organizationId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch pending invitations");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || "Failed to fetch pending invitations");
      }
      return result.invitations;
    },
    ...cacheConfigs.organization, // 2 hours stale time - invitations change rarely
    ...commonQueryOptions,
  });
}

// Type definitions for invitations
type Invitation = {
  id: string;
  email: string;
  organization_id: string;
  status: "pending" | "accepted" | "cancelled";
  token: string;
  created_at: string;
  expires_at: string;
};

// Loading component for Suspense fallback
function InvitationsListSkeleton() {
  return (
    <TableBody className="border-x">
      <TableRow>
        <TableCell colSpan={6} className="p-2">
          <Skeleton className="h-8 w-full" />
        </TableCell>
      </TableRow>
    </TableBody>
  );
}

// Component to display invitation count
function InvitationCount({ organization }: { organization: OrganizationInfoType }) {
  const {
    data: invitations = [],
    isLoading,
    isError,
  } = usePendingInvitations(organization.id, organization.name);

  if (isLoading) {
    return (
      <span className="inline-flex h-5 items-center rounded-full bg-muted px-2 text-xs">
        Loading...
      </span>
    );
  }

  if (isError) {
    return (
      <Badge
        variant="outline"
        className="text-xs bg-white dark:bg-background text-muted-foreground"
      >
        0 invitations
      </Badge>
    );
  }

  const invitationCount = invitations.length;

  return (
    <Badge variant="outline" className="text-xs bg-white dark:bg-background text-muted-foreground">
      {invitationCount} invitation{invitationCount !== 1 ? "s" : ""}
    </Badge>
  );
}

// Component to fetch and display invitations
function InvitationsListContent({ isAdmin, organization }: InvitationsListProps) {
  const {
    data: invitations = [],
    isLoading,
    isError,
    error,
  } = usePendingInvitations(organization.id, organization.name);

  if (isLoading) {
    return <InvitationsListSkeleton />;
  }

  if (isError) {
    return (
      <TableBody className="border-x">
        <TableRow>
          <TableCell colSpan={6} className="h-12 text-center">
            <span className="text-red-500">
              Error loading invitations: {error instanceof Error ? error.message : "Unknown error"}
            </span>
          </TableCell>
        </TableRow>
      </TableBody>
    );
  }

  return (
    <TableBody className="border-x">
      {invitations.length > 0 ? (
        invitations.map((invitation) => (
          <InvitationRow
            key={invitation.id}
            invitation={invitation}
            isAdmin={isAdmin}
            organization={organization}
          />
        ))
      ) : (
        <TableRow>
          <TableCell colSpan={6} className="h-12 text-center">
            <span className="text-muted-foreground">No pending invitations</span>
          </TableCell>
        </TableRow>
      )}
    </TableBody>
  );
}

export default function InvitationsList(props: InvitationsListProps) {
  return (
    <div className="flex flex-col w-full border-none px-6">
      <div className="min-h-0">
        <div className="h-full overflow-auto">
          <Table className="border-b rounded-lg">
            <TableHeader>
              <TableRow>
                <TableHead className="flex-1 pl-4">Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Sent Date</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
              <TableRow className="bg-muted/50 h-12 border-x">
                <TableCell colSpan={6} className="px-4 py-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Invited users</span>
                    <InvitationCount organization={props.organization} />
                  </div>
                </TableCell>
              </TableRow>
            </TableHeader>
            <InvitationsListContent {...props} />
          </Table>
        </div>
      </div>
    </div>
  );
}
