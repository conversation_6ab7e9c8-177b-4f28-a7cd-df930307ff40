"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui";

type PositionSelectorProps = {
  userId: string;
  currentPosition: string;
  isAdmin: boolean;
  isLoading: boolean;
  onPositionChange: (userId: string, newPosition: string) => void;
};

export default function EmployeePositionSelector({
  userId,
  currentPosition,
  isAdmin,
  isLoading,
  onPositionChange,
}: PositionSelectorProps) {
  return (
    <Select
      defaultValue={currentPosition || "Not specified"}
      disabled={!isAdmin || isLoading}
      onValueChange={(value) => onPositionChange(userId, value)}
    >
      <SelectTrigger className="w-48">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="Developer">Developer</SelectItem>
        <SelectItem value="Lead Developer">Lead Developer</SelectItem>
        <SelectItem value="Product Manager">Product Manager</SelectItem>
        <SelectItem value="Project Manager">Project Manager</SelectItem>
        <SelectItem value="UI/UX Designer">UI/UX Designer</SelectItem>
        <SelectItem value="QA Engineer">QA Engineer</SelectItem>
        <SelectItem value="QA Lead">QA Lead</SelectItem>
        <SelectItem value="Engineering Manager">Engineering Manager</SelectItem>
        <SelectItem value="CTO">CTO</SelectItem>
        <SelectItem value="VP of Engineering">VP of Engineering</SelectItem>
        <SelectItem value="CEO">CEO</SelectItem>
        <SelectItem value="Not specified">Not specified</SelectItem>
      </SelectContent>
    </Select>
  );
}
