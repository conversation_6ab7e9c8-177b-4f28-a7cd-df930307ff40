"use client";

import {
  <PERSON><PERSON>,
  AvatarFallback,
  Avatar<PERSON>mage,
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  TableCell,
  TableRow,
} from "@/components/ui";
import { AlertTriangleIcon, MailCheckIcon, MoreHorizontalIcon, UserXIcon } from "lucide-react";
import { ChangeEvent, useState } from "react";
import { toast } from "sonner";
import { Employee, LoadingState } from "./employee-types";
import EmployeePositionSelector from "./employees-position-selector";
import EmployeeRoleSelector from "./employees-role-selector";

type EmployeeRowProps = {
  employee: Employee;
  isAdmin: boolean;
  loadingState: LoadingState;
  onRoleChange: (userId: string, newRole: string) => void;
  onPositionChange: (userId: string, newPosition: string) => void;
  onDeleteEmployee: (userId: string) => Promise<void>;
};

export default function EmployeeRow({
  employee,
  isAdmin,
  loadingState,
  onRoleChange,
  onPositionChange,
  onDeleteEmployee,
}: EmployeeRowProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeletingEmployee, setIsDeletingEmployee] = useState(false);
  const [confirmationText, setConfirmationText] = useState("");

  const isDeleteConfirmed = confirmationText === employee.name;

  const handleDeleteEmployee = async () => {
    if (!isDeleteConfirmed) return;

    setIsDeletingEmployee(true);
    try {
      await onDeleteEmployee(employee.userId);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Error deleting employee:", error);
    } finally {
      setIsDeletingEmployee(false);
      setConfirmationText("");
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <TableRow>
      {/* Employee */}
      <TableCell>
        <div className="flex items-center gap-3 pl-2">
          <Avatar className="w-8 h-8">
            <AvatarImage src={employee.avatarUrl || ""} alt={employee.name} />
            <AvatarFallback>{getInitials(employee.name)}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col px-2">
            <span className="font-medium">{employee.name}</span>
          </div>
        </div>
      </TableCell>

      {/* Email */}
      <TableCell className="text-muted-foreground">{employee.email}</TableCell>

      {/* Joined Date */}
      <TableCell>{employee.joinedAt && new Date(employee.joinedAt).toDateString()}</TableCell>

      {/* Role */}
      <TableCell>
        <div className="flex items-center gap-2">
          <EmployeeRoleSelector
            userId={employee.userId}
            currentRole={employee.role || "-"}
            isAdmin={isAdmin}
            isLoading={
              loadingState[employee.userId]?.type === "role" &&
              loadingState[employee.userId]?.loading
            }
            onRoleChange={onRoleChange}
          />
        </div>
      </TableCell>

      {/* Position */}
      <TableCell>
        <div className="flex items-center gap-2">
          <EmployeePositionSelector
            userId={employee.userId}
            currentPosition={employee.position || "Not specified"}
            isAdmin={isAdmin}
            isLoading={
              loadingState[employee.userId]?.type === "position" &&
              loadingState[employee.userId]?.loading
            }
            onPositionChange={onPositionChange}
          />
        </div>
      </TableCell>

      {/* Actions */}
      <TableCell>
        {isAdmin && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                navigator.clipboard.writeText(employee.email);
                toast.success("Email copied to clipboard");
              }}
            >
              <MailCheckIcon className="w-4 h-4" />
            </Button>

            <Dialog
              open={showDeleteDialog}
              onOpenChange={(open) => {
                setShowDeleteDialog(open);
                if (!open) setConfirmationText("");
              }}
            >
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  <UserXIcon className="w-4 h-4" />
                </Button>
              </DialogTrigger>

              <DialogContent className="sm:max-w-md">
                <DialogHeader className="gap-4">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
                    <AlertTriangleIcon className="h-6 w-6 text-red-500" />
                  </div>
                  <DialogTitle className="text-center text-xl">Remove Employee</DialogTitle>
                  <DialogDescription className="text-center">
                    Are you sure you want to remove{" "}
                    <span className="font-semibold text-foreground">{employee.name}</span> from the
                    organization?
                    <span className="mt-2 block text-xs text-muted-foreground">
                      Note: This action cannot be undone and will revoke all access.
                    </span>
                  </DialogDescription>
                </DialogHeader>

                <div className="mt-4">
                  <label htmlFor="confirm-employee-name" className="text-sm font-medium block mb-2">
                    Type &quot;<span className="font-semibold">{employee.name}</span>&quot; to
                    confirm deletion:
                  </label>
                  <Input
                    id="confirm-employee-name"
                    value={confirmationText}
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setConfirmationText(e.target.value)
                    }
                    placeholder={`${employee.name}`}
                    className="w-full"
                    autoComplete="off"
                  />
                  {confirmationText && !isDeleteConfirmed && (
                    <p className="text-sm text-destructive mt-1">
                      Names don&apos;t match. Please enter the exact name.
                    </p>
                  )}
                </div>

                <DialogFooter className="sm:justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowDeleteDialog(false);
                      setConfirmationText("");
                    }}
                    disabled={isDeletingEmployee}
                    className="min-w-[100px]"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteEmployee}
                    disabled={isDeletingEmployee || !isDeleteConfirmed}
                    className="min-w-[100px]"
                  >
                    {isDeletingEmployee ? "Removing..." : "Remove Employee"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
        {!isAdmin && (
          <Button variant="ghost" size="sm">
            <MoreHorizontalIcon className="w-4 h-4" />
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
}
