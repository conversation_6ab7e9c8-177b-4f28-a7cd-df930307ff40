import MemberInvitation from "@/components/auth/member-invitation";
import { SearchInputWithVoice } from "@/components/origin-ui/search-input-with-voice";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui";
import { OrganizationInfoType } from "@/types/organization.types";

type EmployeesListHeaderProps = {
  isAdmin: boolean;
  onSearchChange: (query: string) => void;
  organization: OrganizationInfoType;
};

export default function EmployeesListHeader({
  isAdmin,
  onSearchChange,
  organization,
}: EmployeesListHeaderProps) {
  return (
    <CardHeader className="py-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-2xl font-semibold">Organization Employees</CardTitle>
          <CardDescription>Manage organization members and their roles.</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <SearchInputWithVoice
            id="employees-search"
            placeholder="Search by name, email, role or position"
            className="w-[350px]"
            onChange={onSearchChange}
          />
          {isAdmin && (
            <>
              <MemberInvitation size="sm" organization={organization} />
            </>
          )}
        </div>
      </div>
    </CardHeader>
  );
}
