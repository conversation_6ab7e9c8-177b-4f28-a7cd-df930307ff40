"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui";

type RoleSelectorProps = {
  userId: string;
  currentRole: string;
  isAdmin: boolean;
  isLoading: boolean;
  onRoleChange: (userId: string, newRole: string) => void;
};

export default function EmployeeRoleSelector({
  userId,
  currentRole,
  isAdmin,
  isLoading,
  onRoleChange,
}: RoleSelectorProps) {
  return (
    <Select
      defaultValue={currentRole || "member"}
      disabled={!isAdmin || isLoading}
      onValueChange={(value) => onRoleChange(userId, value)}
    >
      <SelectTrigger className="w-32">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="admin">Admin</SelectItem>
        <SelectItem value="member">Member</SelectItem>
        <SelectItem value="guest">Guest</SelectItem>
      </SelectContent>
    </Select>
  );
}
