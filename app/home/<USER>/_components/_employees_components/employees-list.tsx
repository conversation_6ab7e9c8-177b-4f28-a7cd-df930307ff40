"use client";

import { <PERSON><PERSON>ontent, Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui";
import {
  removeOrganizationMemberAction,
  updateOrganizationMemberRoleAction,
} from "@/db/actions/organization.action";
import { updateUserPositionAction } from "@/db/actions/user-profile.action";
import { OrganizationInfoType } from "@/types/organization.types";
import { useState } from "react";
import { toast } from "sonner";
import { Employee, LoadingState } from "./employee-types";
import { EmployeeRow, EmployeesListHeader, NoEmployees, OrganizationHeader } from "./index";

type EmployeesListProps = {
  employees: Employee[];
  isAdmin: boolean;
  organization: OrganizationInfoType;
  hideHeader?: boolean;
};

export default function EmployeesList({
  employees: initialEmployees,
  isAdmin,
  organization,
  hideHeader = false,
}: EmployeesListProps) {
  const [employees, setEmployees] = useState<Employee[]>(initialEmployees || []);
  const [searchQuery, setSearchQuery] = useState("");
  const [loadingState, setLoadingState] = useState<LoadingState>({});

  // Filter employees based on search query
  const filteredEmployees = employees.filter((employee) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      employee.name?.toLowerCase().includes(query) ||
      employee.email?.toLowerCase().includes(query) ||
      employee.role?.toLowerCase().includes(query) ||
      employee.position?.toLowerCase().includes(query)
    );
  });

  const handleRoleChange = async (userId: string, newRole: string) => {
    // Skip if already loading
    if (loadingState[userId]?.type === "role" && loadingState[userId]?.loading) return;

    // Set loading state
    setLoadingState((prev) => ({
      ...prev,
      [userId]: { type: "role", loading: true, success: false },
    }));

    // Add loading toast
    const toastId = toast.loading("Updating role...");

    try {
      // Call the server action to update the role
      const result = await updateOrganizationMemberRoleAction({
        userId,
        role: newRole,
      });

      if (result.success) {
        // Update local state with new role
        setEmployees((prevEmployees) =>
          prevEmployees.map((employee) =>
            employee.userId === userId ? { ...employee, role: newRole } : employee
          )
        );

        // Show success toast
        toast.success(result.message, { id: toastId });
      } else {
        toast.error(result.message, { id: toastId });
      }
    } catch (error) {
      toast.error("An unexpected error occurred while updating role", {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating role",
      });
    } finally {
      // Reset loading state
      setLoadingState((prev) => ({
        ...prev,
        [userId]: { type: "role", loading: false, success: true },
      }));
    }
  };

  const handlePositionChange = async (userId: string, newPosition: string) => {
    // Skip if already loading
    if (loadingState[userId]?.type === "position" && loadingState[userId]?.loading) return;

    // Set loading state
    setLoadingState((prev) => ({
      ...prev,
      [userId]: { type: "position", loading: true, success: false },
    }));

    // Add loading toast
    const toastId = toast.loading("Updating position...");

    try {
      // Call the server action to update the position
      const result = await updateUserPositionAction({
        userId,
        position: newPosition,
      });

      if (result.success) {
        // Update local state with new position
        setEmployees((prevEmployees) =>
          prevEmployees.map((employee) =>
            employee.userId === userId ? { ...employee, position: newPosition } : employee
          )
        );

        toast.success(result.message, { id: toastId });
      } else {
        toast.error(result.message, { id: toastId });
      }
    } catch (error) {
      toast.error("An unexpected error occurred while updating position", {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating position",
      });
    } finally {
      // Reset loading state
      setLoadingState((prev) => ({
        ...prev,
        [userId]: { type: "position", loading: false, success: true },
      }));
    }
  };

  const handleDeleteEmployee = async (userId: string) => {
    // Skip if already loading
    if (loadingState[userId]?.type === "delete" && loadingState[userId]?.loading) return;

    // Set loading state
    setLoadingState((prev) => ({
      ...prev,
      [userId]: { type: "delete", loading: true, success: false },
    }));

    // Add loading toast
    const toastId = toast.loading("Removing employee...");

    try {
      // Call the server action to remove the employee
      const result = await removeOrganizationMemberAction(userId);

      if (result.success) {
        // Update local state to remove the employee
        setEmployees((prevEmployees) =>
          prevEmployees.filter((employee) => employee.userId !== userId)
        );

        toast.success(result.message, { id: toastId });
      } else {
        toast.error(result.message, { id: toastId });
      }
    } catch (error) {
      toast.error("An unexpected error occurred while removing employee", {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while removing employee",
      });
    } finally {
      // Reset loading state
      setLoadingState((prev) => ({
        ...prev,
        [userId]: { type: "delete", loading: false, success: true },
      }));
    }
  };

  return (
    <div className="flex flex-col w-full border-none">
      {!hideHeader && (
        <EmployeesListHeader
          isAdmin={isAdmin}
          onSearchChange={setSearchQuery}
          organization={organization}
        />
      )}

      <CardContent className="min-h-0">
        {filteredEmployees.length > 0 ? (
          <div className="h-full overflow-auto">
            <Table className="border-b rounded-lg">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px] pl-4">Employee</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Joined Date</TableHead>
                  <TableHead>
                    Role <span className="text-xs text-muted-foreground">(Organization level)</span>
                  </TableHead>
                  <TableHead>
                    Position{" "}
                    <span className="text-xs text-muted-foreground">(Organization level)</span>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="border-x">
                <OrganizationHeader memberCount={employees.length} />
                {filteredEmployees.map((employee) => (
                  <EmployeeRow
                    key={employee.id}
                    employee={employee}
                    isAdmin={isAdmin}
                    loadingState={loadingState}
                    onRoleChange={handleRoleChange}
                    onPositionChange={handlePositionChange}
                    onDeleteEmployee={handleDeleteEmployee}
                  />
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <NoEmployees isAdmin={isAdmin} />
        )}
      </CardContent>
    </div>
  );
}
