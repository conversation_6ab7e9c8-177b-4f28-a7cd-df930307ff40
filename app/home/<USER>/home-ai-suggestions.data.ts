import { type LucideIcon } from "lucide-react";

export interface Suggestion {
  id: string;
  title: string;
  description: string;
  category: "deadline" | "priority" | "dependency" | "productivity" | "workload" | "documentation";
  categoryIcon: LucideIcon;
  priority: "high" | "medium" | "low";
  project?: string;
  actionText?: string;
  actionUrl?: string;
  createdAt: string;
}

export const mockSuggestions: Suggestion[] = [
  // {
  //   id: "1",
  //   title: "Review Marketing Campaign designs",
  //   description:
  //     "The design team completed their work on the Q2 Marketing Campaign assets yesterday. Your approval is needed before they can proceed to development.",
  //   category: "deadline",
  //   categoryIcon: Clock as LucideIcon,
  //   priority: "high",
  //   project: "Q2 Marketing Campaign",
  //   actionText: "Review designs",
  //   createdAt: new Date(Date.now() - 3600000).toISOString(),
  // },
  // {
  //   id: "2",
  //   title: "Update User Flow documentation",
  //   description:
  //     "The User Flow documentation hasn't been updated in 3 weeks. Based on recent changes to the onboarding process, it should be updated.",
  //   category: "documentation",
  //   categoryIcon: ListOrdered as LucideIcon,
  //   priority: "medium",
  //   project: "User Experience Enhancements",
  //   actionText: "Update docs",
  //   createdAt: new Date(Date.now() - 7200000).toISOString(),
  // },
  // {
  //   id: "3",
  //   title: "Schedule 1:1 with new team member",
  //   description:
  //     "Sarah joined your team yesterday. Based on your onboarding process, scheduling a 1:1 within the first week helps new team members integrate faster.",
  //   category: "workload",
  //   categoryIcon: UserRound as LucideIcon,
  //   priority: "low",
  //   actionText: "Schedule meeting",
  //   createdAt: new Date(Date.now() - 10800000).toISOString(),
  // },
  // {
  //   id: "4",
  //   title: "Unblock Frontend team on API requirements",
  //   description:
  //     "The Frontend team is waiting for your input on API requirements for the Dashboard feature. This is blocking their progress on sprint tasks.",
  //   category: "dependency",
  //   categoryIcon: Network as LucideIcon,
  //   priority: "high",
  //   project: "Dashboard Redesign",
  //   actionText: "Provide input",
  //   createdAt: new Date(Date.now() - 14400000).toISOString(),
  // },
];
