"use client";

import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTrigger,
} from "@/components/ui/stepper";
import { Textarea } from "@/components/ui/textarea";
import {
  addProjectsToExistingClusterAction,
  createClusterWithProjects,
} from "@/db/actions/cluster.action";
import { generateSlug } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Info, Loader2, <PERSON><PERSON>ircle, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Types (can be moved to a shared types file if used elsewhere)
type Cluster = {
  id: string;
  name: string;
};

const NewClusterSchema = z.object({
  name: z.string().min(3, "Cluster name must be at least 3 characters"),
  description: z.string().optional(),
});

const ProjectSchema = z.object({
  name: z.string().min(3, "Project name must be at least 3 characters long."),
  slug: z.string().min(3, "Slug must be at least 3 characters long."),
  description: z.string().optional(),
});

// Base schema for common fields
const BaseFormSchema = z.object({
  projects: z
    .array(ProjectSchema)
    .min(1, "At least one project is required.")
    .max(20, "You can create a maximum of 20 projects at once."),
});

// Schema for when "new" cluster is chosen
const NewClusterBranchSchema = BaseFormSchema.extend({
  clusterChoice: z.literal("new"),
  newCluster: NewClusterSchema, // newCluster is required and validated by its schema
  existingClusterId: z.string().optional(), // Can be undefined
});

// Schema for when "existing" cluster is chosen
const ExistingClusterBranchSchema = BaseFormSchema.extend({
  clusterChoice: z.literal("existing"),
  existingClusterId: z
    .string({ required_error: "Please select an existing cluster." })
    .min(1, "Please select an existing cluster."),
  newCluster: NewClusterSchema.deepPartial().optional(), // newCluster object itself is optional, and if present, its fields are optional
});

const FormSchema = z.discriminatedUnion("clusterChoice", [
  NewClusterBranchSchema,
  ExistingClusterBranchSchema,
]);

type FormData = z.infer<typeof FormSchema>;

const stepsConfig = [
  { id: 1, name: "Cluster Configuration" },
  { id: 2, name: "Define Projects" },
  { id: 3, name: "Review and Create" },
];

type ProjectCreationFormProps = {
  organizationId: string;
  initialExistingClusters: Cluster[];
  // Optional: Pass a specific error message if cluster fetching failed at page level
  clusterFetchError?: string | null;
};

export function ProjectCreationForm({
  organizationId,
  initialExistingClusters,
  clusterFetchError,
}: ProjectCreationFormProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Client-side state for existing clusters, initialized from props
  // This allows for potential client-side filtering/sorting in the future if needed,
  // but for now, it just mirrors the initial prop.
  const [existingClusters] = useState<Cluster[]>(initialExistingClusters);

  // Client-side state to manage if we are actively trying to "re-fetch" or "load" clusters
  // within the component, e.g., if a refresh button was added.
  // For initial load, page.tsx handles this.
  const [isLoadingClustersClient] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      clusterChoice: "new",
      newCluster: { name: "", description: "" },
      existingClusterId: undefined,
      projects: [{ name: "", slug: "", description: "" }],
      // If initialExistingClusters has items and no cluster is pre-selected,
      // you might want to default existingClusterId to the first one, or leave undefined.
      // existingClusterId: initialExistingClusters[0]?.id
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "projects",
  });

  const handleProjectNameChange = (index: number, name: string) => {
    const slug = generateSlug(name);
    form.setValue(`projects.${index}.name`, name, { shouldValidate: true });
    form.setValue(`projects.${index}.slug`, slug, { shouldValidate: true });
  };

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    toast.info("Creating projects...");

    // organizationId is passed as a prop and assumed to be valid by this point
    const confirmedOrganizationId: string = organizationId;
    console.info("Using organization ID:", confirmedOrganizationId);

    let clusterNameForAction: string | undefined = undefined;
    let clusterDescriptionForAction: string | undefined = undefined;
    let clusterIdForAction: string | undefined = undefined;

    if (data.clusterChoice === "new") {
      clusterNameForAction = data.newCluster!.name;
      clusterDescriptionForAction = data.newCluster!.description;
      console.info("Creating new cluster:", clusterNameForAction);
    } else if (data.clusterChoice === "existing") {
      clusterIdForAction = data.existingClusterId;
      console.info("Using existing cluster ID:", clusterIdForAction);
    }

    try {
      if (data.clusterChoice === "new" && clusterNameForAction) {
        const payload = {
          name: clusterNameForAction,
          description: clusterDescriptionForAction,
          organizationId: confirmedOrganizationId,
          projects: data.projects.map((p) => ({
            name: p.name,
            slug: p.slug,
            description: p.description,
          })),
        };
        console.info("Creating cluster with projects payload:", payload);
        const result = await createClusterWithProjects(payload);
        console.info("Create cluster result:", result);
        if (result.success) {
          toast.success("Cluster and projects created successfully!");
          // Redirect to organization page after successful creation
          router.push("/home/<USER>");
        } else {
          toast.error(result.error || "Failed to create cluster and projects.");
        }
      } else if (data.clusterChoice === "existing" && clusterIdForAction) {
        const payload = {
          clusterId: clusterIdForAction,
          organizationId: confirmedOrganizationId,
          projects: data.projects.map((p) => ({
            name: p.name,
            slug: p.slug,
            description: p.description,
          })),
        };
        console.info("Adding projects to existing cluster payload:", payload);
        const result = await addProjectsToExistingClusterAction(payload);
        console.info("Add projects to existing cluster result:", result);
        if (result.success) {
          toast.success("Projects added to existing cluster successfully!");
          // Redirect to organization page after successful creation
          router.push("/home/<USER>");
        } else {
          toast.error(result.error || "Failed to add projects to existing cluster.");
        }
      } else {
        console.error("Invalid form state:", { clusterChoice: data.clusterChoice, data });
        toast.error("Invalid configuration for creating projects.");
      }
    } catch (error) {
      console.error("Submission error:", error);
      toast.error("An unexpected error occurred during submission.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = async () => {
    let isValid = false;
    if (currentStep === 1) {
      isValid = await form.trigger(["clusterChoice", "newCluster", "existingClusterId"]);
    } else if (currentStep === 2) {
      isValid = await form.trigger("projects");
    }

    if (isValid) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(1, prev - 1));
  };

  const resetWizard = () => {
    form.reset({
      clusterChoice: "new",
      newCluster: { name: "", description: "" },
      existingClusterId: undefined,
      projects: [{ name: "", slug: "", description: "" }],
    });
    setCurrentStep(1);
    toast.info("Wizard has been reset.");
  };

  const watchedClusterChoice = form.watch("clusterChoice");
  const watchedProjects = form.watch("projects");
  const watchedNewCluster = form.watch("newCluster");
  const watchedExistingClusterId = form.watch("existingClusterId");

  // useEffect to handle side-effects of clusterChoice changing
  useEffect(() => {
    if (watchedClusterChoice === "existing") {
      // When switching to 'existing', clear 'newCluster' fields to prevent validation on them.
      // Setting to 'undefined' ensures they pass '.optional()' schema checks.
      form.setValue("newCluster.name", undefined, { shouldValidate: false });
      form.setValue("newCluster.description", undefined, { shouldValidate: false });
      form.clearErrors("newCluster.name");
      form.clearErrors("newCluster.description");
      form.clearErrors("newCluster"); // Clear any general error on the newCluster object itself

      // Ensure existingClusterId is validated if it's required (Zod schema handles this on trigger/submit)
    } else if (watchedClusterChoice === "new") {
      // When switching to 'new', clear 'existingClusterId' and its errors.
      form.setValue("existingClusterId", undefined, { shouldValidate: false });
      form.clearErrors("existingClusterId");

      // Ensure 'newCluster' fields are reset to their initial state for 'new' (e.g., empty strings)
      // if they were previously set to undefined (e.g., by switching to 'existing' then back to 'new').
      if (form.getValues("newCluster.name") === undefined) {
        form.setValue("newCluster.name", "", { shouldValidate: false });
      }
      if (form.getValues("newCluster.description") === undefined) {
        form.setValue("newCluster.description", "", { shouldValidate: false });
      }
    }
  }, [watchedClusterChoice, form.setValue, form.clearErrors, form.getValues, form]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Create New Projects</CardTitle>
        <CardDescription>
          Follow the steps below to set up a new cluster or use an existing one, and then define
          your projects.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Stepper value={currentStep} className="mb-8 w-full">
          <div className="flex justify-around items-start w-full">
            {stepsConfig.map((step) => (
              <StepperItem
                key={step.id}
                step={step.id}
                className="flex-1 last:flex-none flex flex-col items-center text-center"
              >
                <StepperTrigger className="pointer-events-none">
                  <div className="flex flex-col items-center">
                    <StepperIndicator />
                    <span className="mt-2 text-sm text-center">{step.name}</span>
                  </div>
                </StepperTrigger>
                {step.id < stepsConfig.length && <StepperSeparator className="flex-1 mb-7" />}
              </StepperItem>
            ))}
          </div>
        </Stepper>

        <form
          onSubmit={(e) => {
            // Prevent default form submission behavior which might be causing auto-submission
            e.preventDefault();
            // Only proceed with form submission when explicitly triggered by the submit button
            if (currentStep === 3) {
              form.handleSubmit(onSubmit)(e);
            }
          }}
        >
          {currentStep === 1 && (
            <div className="space-y-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Why Clusters?</AlertTitle>
                <AlertDescription>
                  Clusters help you group related projects together, for example, by client, product
                  line, or development stage. All projects must belong to a cluster.
                </AlertDescription>
              </Alert>

              <Controller
                name="clusterChoice"
                control={form.control}
                render={({ field }) => (
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-4"
                  >
                    <Label className="flex items-center space-x-2 p-3 border rounded-md hover:bg-accent has-[input:checked]:bg-accent has-[input:checked]:border-primary flex-1 cursor-pointer">
                      <RadioGroupItem value="new" id="new" />
                      <span>Create New Cluster</span>
                    </Label>
                    <Label className="flex items-center space-x-2 p-3 border rounded-md hover:bg-accent has-[input:checked]:bg-accent has-[input:checked]:border-primary flex-1 cursor-pointer">
                      <RadioGroupItem value="existing" id="existing" />
                      <span>Use Existing Cluster</span>
                    </Label>
                  </RadioGroup>
                )}
              />
              {form.formState.errors.clusterChoice && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.clusterChoice.message}
                </p>
              )}

              {watchedClusterChoice === "new" && (
                <Card className="p-4">
                  <CardTitle className="text-lg mb-2">New Cluster Details</CardTitle>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="newClusterName">Cluster Name</Label>
                      <Input
                        id="newClusterName"
                        {...form.register("newCluster.name")}
                        placeholder="e.g., Q4 Campaigns, Mobile App Suite"
                      />
                      {form.formState.errors.newCluster?.name && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.newCluster.name.message}
                        </p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="newClusterDescription">Cluster Description (Optional)</Label>
                      <Textarea
                        id="newClusterDescription"
                        {...form.register("newCluster.description")}
                        placeholder="A brief description of this cluster's purpose."
                      />
                    </div>
                  </div>
                </Card>
              )}

              {watchedClusterChoice === "existing" && (
                <Card className="p-4">
                  <CardTitle className="text-lg mb-2">Select Existing Cluster</CardTitle>
                  {isLoadingClustersClient ? ( // Use client-side loading state if implementing refresh
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span>Loading clusters...</span>
                    </div>
                  ) : clusterFetchError ? ( // Display error passed from page if any
                    <Alert variant="destructive" className="mt-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Error Loading Clusters</AlertTitle>
                      <AlertDescription>{clusterFetchError}</AlertDescription>
                    </Alert>
                  ) : existingClusters.length > 0 ? (
                    <Controller
                      name="existingClusterId"
                      control={form.control}
                      rules={{
                        required:
                          watchedClusterChoice === "existing" ? "Please select a cluster" : false,
                      }}
                      render={({ field }) => (
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="space-y-2"
                        >
                          {existingClusters.map((cluster) => (
                            <Label
                              key={cluster.id}
                              htmlFor={cluster.id}
                              className="flex items-center space-x-2 p-3 border rounded-md hover:bg-accent has-[input:checked]:bg-accent has-[input:checked]:border-primary cursor-pointer"
                            >
                              <RadioGroupItem value={cluster.id} id={cluster.id} />
                              <span>{cluster.name}</span>
                            </Label>
                          ))}
                        </RadioGroup>
                      )}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No existing clusters found. You can create a new one.
                    </p>
                  )}
                  {form.formState.errors.existingClusterId && (
                    <p className="text-sm text-red-500 mt-1">
                      {form.formState.errors.existingClusterId.message}
                    </p>
                  )}
                </Card>
              )}
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              {/* ADDED: Display selected/new cluster information */}
              <div className="text-sm font-medium pl-2">
                {watchedClusterChoice === "new" && watchedNewCluster?.name ? (
                  <div className="text-xs">
                    <span className="text-muted-foreground">Assigned to new cluster: </span>
                    <strong className="text-base font-semibold">{watchedNewCluster.name}</strong>
                    {watchedNewCluster.description && (
                      <span className="ml-2 text-muted-foreground">
                        ({watchedNewCluster.description})
                      </span>
                    )}
                  </div>
                ) : watchedClusterChoice === "existing" && watchedExistingClusterId ? (
                  <div className="text-xs">
                    <span className="text-muted-foreground">Assigned to existing cluster: </span>
                    <strong className="text-base font-semibold">
                      {existingClusters.find((c) => c.id === watchedExistingClusterId)?.name ||
                        "Selected cluster not found"}
                    </strong>
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Cluster information will appear here once configured in the previous step.
                  </p>
                )}
              </div>

              {fields.map((field, index) => (
                <Card key={field.id} className="p-4 relative">
                  <CardTitle className="text-lg mb-2">Project {index + 1}</CardTitle>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 text-muted-foreground hover:text-destructive"
                      onClick={() => remove(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                  <div className="space-y-3">
                    <div className="flex gap-4 w-full">
                      <div className="flex-1">
                        <Label htmlFor={`projectName-${index}`}>Project Name</Label>
                        <Input
                          id={`projectName-${index}`}
                          placeholder="e.g., Website Redesign, API Development"
                          value={form.watch(`projects.${index}.name`)}
                          onChange={(e) => handleProjectNameChange(index, e.target.value)}
                        />
                        {form.formState.errors.projects?.[index]?.name && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.projects[index]?.name?.message}
                          </p>
                        )}
                      </div>
                      <div className="flex-1">
                        <Label htmlFor={`projectSlug-${index}`}>Project Slug</Label>
                        <Input
                          id={`projectSlug-${index}`}
                          {...form.register(`projects.${index}.slug`)}
                          placeholder="auto-generated-slug"
                        />
                        {form.formState.errors.projects?.[index]?.slug && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.projects[index]?.slug?.message}
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor={`projectDescription-${index}`}>Description (Optional)</Label>
                      <Textarea
                        id={`projectDescription-${index}`}
                        {...form.register(`projects.${index}.description`)}
                        placeholder="Briefly describe this project."
                      />
                    </div>
                  </div>
                </Card>
              ))}
              {form.formState.errors.projects &&
                typeof form.formState.errors.projects === "object" &&
                !Array.isArray(form.formState.errors.projects) && (
                  <p className="text-sm text-red-500">
                    {(form.formState.errors.projects as { message: string }).message ||
                      "Error with projects list."}
                  </p>
                )}
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ name: "", slug: "", description: "" })}
                disabled={fields.length >= 20}
                className="flex items-center gap-2"
              >
                <PlusCircle className="h-4 w-4" /> Add Another Project
              </Button>
              {fields.length >= 20 && (
                <p className="text-sm text-muted-foreground">Maximum of 20 projects reached.</p>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">Review Your Configuration</h3>
              <Card>
                <CardHeader>
                  <CardTitle>Cluster Information</CardTitle>
                </CardHeader>
                <CardContent>
                  {watchedClusterChoice === "new" && watchedNewCluster ? (
                    <>
                      <p>
                        <span className="font-semibold">Type:</span> New Cluster
                      </p>
                      <p>
                        <span className="font-semibold">Name:</span> {watchedNewCluster.name}
                      </p>
                      <p>
                        <span className="font-semibold">Description:</span>{" "}
                        {watchedNewCluster.description || "N/A"}
                      </p>
                    </>
                  ) : watchedClusterChoice === "existing" && watchedExistingClusterId ? (
                    <>
                      <p>
                        <span className="font-semibold">Type:</span> Existing Cluster
                      </p>
                      <p>
                        <span className="font-semibold">Name:</span>{" "}
                        {existingClusters.find((c) => c.id === watchedExistingClusterId)?.name ||
                          "N/A"}
                      </p>
                    </>
                  ) : (
                    <p className="text-muted-foreground">No cluster information provided yet.</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Projects to Create ({watchedProjects.length})</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {watchedProjects.map((project, index) => (
                    <div key={index} className="p-3 border rounded-md bg-muted/50">
                      <p className="font-semibold text-md">
                        {project.name || `Project ${index + 1}`}
                      </p>
                      <p className="text-sm text-muted-foreground">Slug: {project.slug || "N/A"}</p>
                      <p className="text-sm text-muted-foreground">
                        Description: {project.description || "N/A"}
                      </p>
                      {index < watchedProjects.length - 1 && <Separator className="my-3" />}
                    </div>
                  ))}
                </CardContent>
              </Card>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Final Check!</AlertTitle>
                <AlertDescription>
                  Please review all details carefully. Once submitted, these projects will be
                  created. You can go back to previous steps to make changes.
                </AlertDescription>
              </Alert>
            </div>
          )}

          <div className="mt-10 flex justify-between items-center">
            <div>
              {currentStep === 1 && (
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
              )}
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrev}
                  disabled={isSubmitting}
                >
                  Previous
                </Button>
              )}
            </div>

            <div className="flex items-center gap-3">
              {currentStep === 3 && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={resetWizard}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" /> Reset Wizard
                </Button>
              )}

              {currentStep < 3 ? (
                <Button type="button" onClick={handleNext} disabled={isSubmitting}>
                  Next
                </Button>
              ) : (
                <Button type="button" onClick={form.handleSubmit(onSubmit)} disabled={isSubmitting}>
                  {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Create Projects
                </Button>
              )}
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
