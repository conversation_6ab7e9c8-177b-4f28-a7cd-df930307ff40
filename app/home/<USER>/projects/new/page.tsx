import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { getTenantId } from "@/db/tenant.db";
import { createClient } from "@/lib/supabase/server";
import { AlertCircle, Loader2 } from "lucide-react";
import { connection } from "next/server";
import { cache } from "react";
import { ProjectCreationForm } from "./_components/project-creation-form";

// Define Cluster type here or import from a shared location
type Cluster = {
  id: string;
  name: string;
};

// This function will now be a server-side utility for fetching clusters.
// It can remain here or be moved to a dedicated server actions/utils file.
const fetchExistingClusters = cache(async (organizationId: string): Promise<Cluster[]> => {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("clusters")
    .select("id, name")
    .eq("organization_id", organizationId);

  if (error) {
    console.error("Server: Error fetching clusters from Supabase:", error);
    throw new Error(`Failed to fetch clusters from database: ${error.message}`);
  }
  return data || [];
});

export default async function NewProjectPage() {
  await connection();

  let organizationId: string | null | undefined;
  let initialExistingClusters: Cluster[] = [];
  let orgIdError: string | null = null;
  let clusterFetchError: string | null = null;

  try {
    organizationId = await getTenantId();
    if (!organizationId) {
      orgIdError = "Organization ID could not be determined.";
    }
  } catch (error) {
    console.error("Error fetching tenant ID:", error);
    orgIdError = "Failed to determine organization identity.";
  }

  if (organizationId) {
    try {
      const allClusters = await fetchExistingClusters(organizationId);
      initialExistingClusters = allClusters.filter((cluster) => cluster.name !== "Uncategorized");
    } catch (error: unknown) {
      console.error("Error fetching initial clusters:", error);
      clusterFetchError =
        error instanceof Error ? error.message : "Failed to load existing clusters.";
    }
  }

  if (orgIdError) {
    return (
      <div className="mx-auto py-8 px-4 md:px-0 max-w-4xl text-center  overflow-y-scroll">
        <Card className="p-8">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-destructive flex items-center justify-center">
              <AlertCircle className="mr-2 h-6 w-6" /> Organization Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{orgIdError}</p>
            <p>Please ensure you are accessing this page correctly or contact support.</p>
            {/* Consider adding a button to go back or to the homepage */}
          </CardContent>
        </Card>
      </div>
    );
  }

  // If organizationId is still undefined here after try-catch, it implies getTenantId returned null
  // and orgIdError was set. This case is handled by the block above.
  // So, if we reach here and organizationId is defined, we can proceed.

  // Show a loading state if organizationId is determined but clusters are still being fetched (e.g. slow network for clusters)
  // This specific scenario is less likely if both fetches are fast, but good for robustness.
  // However, with `await` for both, this loading state might only flash briefly or not at all.
  // The primary loading is handled by Suspense if this page is wrapped in it, or by the browser until resolved.

  return (
    <div className=" mx-auto py-8 px-4 md:px-0 max-w-4xl h-[calc(100vh-7rem)] overflow-y-scroll">
      {organizationId ? (
        <ProjectCreationForm
          organizationId={organizationId}
          initialExistingClusters={initialExistingClusters}
          clusterFetchError={clusterFetchError}
        />
      ) : (
        // This fallback should ideally not be reached if orgIdError handling is comprehensive.
        // But as a safety net:
        <div className="mx-auto py-8 px-4 md:px-0 max-w-4xl text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
          <p className="mt-4 text-lg text-muted-foreground">Initializing...</p>
        </div>
      )}
    </div>
  );
}
