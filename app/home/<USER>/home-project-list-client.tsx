"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { useHomeData } from "../_hooks/use-home-data";
import { ProjectListRefresh } from "./home-project-list-refresh";
import { ProjectListViewToggle, ProjectsWithViewState } from "./home-project-view-controls";

// Client component for the project list with React Query
export function ProjectListClient() {
  const { clusters, allProjects, organization, isLoading, projectsError, organizationError } = useHomeData();

  // Handle loading state
  if (isLoading) {
    return (
      <div className="overflow-hidden xl:p-6 border rounded-lg w-full">
        <div className="grid gap-4">
          <div className="grid gap-2">
            <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
              <div>
                <h2 className="text-xl font-semibold">Your Projects</h2>
                <div className="text-sm text-muted-foreground">
                  <span className="text-xs">Loading projects...</span>
                </div>
              </div>
              <div className="flex items-center gap-2 ml-8">
                <ProjectListRefresh />
                <ProjectListViewToggle />
                <Button asChild variant="ghost">
                  <Link href="/home/<USER>/projects/new">
                    <PlusIcon className="w-4 h-4" />
                    New Project
                  </Link>
                </Button>
              </div>
            </div>
            <div className="p-0 overflow-hidden h-full">
              <div className="flex flex-col items-center justify-center h-80 p-12">
                <div className="w-8 h-8 border-t-2 border-b-2 border-gray-900 rounded-full animate-spin"></div>
                <p className="text-muted-foreground mt-2">Loading your projects...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (projectsError || organizationError) {
    return (
      <div className="overflow-hidden xl:p-6 border rounded-lg w-full">
        <div className="grid gap-4">
          <div className="grid gap-2">
            <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
              <div>
                <h2 className="text-xl font-semibold">Your Projects</h2>
                <div className="text-sm text-muted-foreground">
                  <span className="text-xs">Error loading projects</span>
                </div>
              </div>
              <div className="flex items-center gap-2 ml-8">
                <ProjectListRefresh />
                <Button asChild variant="ghost">
                  <Link href="/home/<USER>/projects/new">
                    <PlusIcon className="w-4 h-4" />
                    New Project
                  </Link>
                </Button>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center h-80 p-12 border border-dashed rounded-md">
              <p className="text-muted-foreground mb-2">Failed to load projects</p>
              <p className="text-xs text-muted-foreground">
                {projectsError?.message || organizationError?.message || "Please try refreshing the page"}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle empty state
  if (!clusters || clusters.length === 0 || !organization || !allProjects || allProjects.length === 0) {
    return (
      <div className="w-full xl:w-[55%] flex flex-col min-h-0 p-6 border rounded-lg">
        <div className="w-full overflow-hidden">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
                <div>
                  <h2 className="text-xl font-semibold">Your Projects</h2>
                  <div className="text-sm text-muted-foreground">
                    No projects assigned to your teams
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <ProjectListRefresh />
                  <Button asChild variant="ghost">
                    <Link href="/home/<USER>/projects/new">
                      <PlusIcon className="w-4 h-4" />
                      New Project
                    </Link>
                  </Button>
                </div>
              </div>
              <div className="flex flex-col items-center justify-center h-80 p-12 border border-dashed rounded-md">
                <p className="text-muted-foreground mb-2">
                  You don&apos;t have any projects assigned to your teams
                </p>
                <p className="text-xs text-muted-foreground">
                  Join a team or ask your administrator to assign projects to your teams
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render projects
  return (
    <div className="overflow-hidden xl:p-6 border rounded-lg w-full">
      <div className="grid gap-4">
        <div className="grid gap-2">
          <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
            <div>
              <h2 className="text-xl font-semibold">Your Projects</h2>
              <div className="text-sm text-muted-foreground">
                <span className="text-xs">Only showing projects from your teams only</span>
              </div>
            </div>
            <div className="flex items-center gap-2 ml-8">
              <ProjectListRefresh />
              <ProjectListViewToggle />
              <Button asChild variant="ghost">
                <Link href="/home/<USER>/projects/new">
                  <PlusIcon className="w-4 h-4" />
                  New Project
                </Link>
              </Button>
            </div>
          </div>
          <div className="p-0 overflow-hidden h-full">
            <ProjectsWithViewState projects={allProjects} />
          </div>
        </div>
      </div>
    </div>
  );
}
