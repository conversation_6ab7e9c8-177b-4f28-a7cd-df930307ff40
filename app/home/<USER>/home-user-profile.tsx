"use client";

import { UserProfileSkeleton } from "@/components/skeletons";
import { Card } from "@/components/ui";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { OrganizationInfoType } from "@/types/organization.types";
import { UserProfile as UserProfileProps } from "@/types/user.types";
import { useQuery } from "@tanstack/react-query";
import { Calendar, Mail, MapPin, UserIcon } from "lucide-react";

export function UserProfile() {
  // Fetch user profile using React Query
  const {
    data: user,
    isLoading: isUserLoading,
    error: userError,
  } = useQuery({
    queryKey: ["user", "profile"],
    queryFn: async () => {
      const { getUserProfileAction } = await import("@/db/actions/user-profile.action");
      return await getUserProfileAction();
    },
    ...cacheConfigs.user,
    ...commonQueryOptions,
  });

  // Fetch organization using React Query
  const {
    data: organization,
    isLoading: isOrgLoading,
    error: orgError,
  } = useQuery({
    queryKey: ["home", "organization"],
    queryFn: async () => {
      const { getCurrentOrganizationAction } = await import("@/db/actions/organization.action");
      return await getCurrentOrganizationAction();
    },
    ...cacheConfigs.organization, // Use standard organization cache config
    ...commonQueryOptions,
  });

  // Show loading state with proper skeleton
  if (isUserLoading || isOrgLoading) {
    return <UserProfileSkeleton />;
  }

  // Show error state
  if (userError || orgError || !user || !organization) {
    return (
      <div className="flex flex-col items-center justify-center p-6 text-muted-foreground text-center border rounded-lg xl:border-none xl:rounded-none gap-6">
        <UserIcon className="h-10 w-10 border rounded-full p-2" />
        <h1 className="text-lg font-semibold">Your Profile information</h1>
        <p className="text-sm">
          We were unable to get your profile information. <br /> Please try again later.
        </p>
      </div>
    );
  }

  return (
    <Card className="flex flex-col xl:flex-row justify-between p-6 w-full shadow-none">
      <div className="flex flex-col w-full h-full">
        <div className="flex flex-col w-full h-full items-center justify-between">
          <div className="flex flex-col w-full gap-6">
            <div className="flex items-start gap-6">
              <Avatar className="h-14 w-14 flex-shrink-0">
                <AvatarImage src={user?.avatar} alt={`${user?.name}'s Profile`} />
                <AvatarFallback>{user?.avatarFallback}</AvatarFallback>
              </Avatar>
              <UserInfo user={user} organization={organization} />
            </div>
          </div>
          <UserAdditionalInfo user={user} />
          <UserStats user={user} />
        </div>
      </div>
    </Card>
  );
}

function UserInfo({
  user,
  organization,
}: {
  user: UserProfileProps;
  organization: OrganizationInfoType;
}) {
  return (
    <div className="flex flex-col w-full flex-1 min-w-0">
      <div className="flex flex-row justify-between items-center w-full mb-2">
        <h2 className="text-xl font-semibold truncate">{user?.name}</h2>
        <Badge variant="secondary" className="ml-1 flex-shrink-0">
          {organization?.name}
        </Badge>
      </div>

      <div className="flex items-center gap-1 flex-wrap">
        <Badge variant="secondary" className="flex-shrink-0">
          {user?.department}
        </Badge>
        <span className="text-muted-foreground">•</span>
        <Badge variant="default" className="flex-shrink-0">
          {user?.role}
        </Badge>
        <span className="text-muted-foreground">•</span>
        <Badge variant="outline" className="text-xs">
          {user?.position}
        </Badge>
      </div>
    </div>
  );
}

function UserAdditionalInfo({ user }: { user: UserProfileProps }) {
  return (
    <div className="flex flex-col w-full h-full items-start justify-start gap-2 pt-6 pl-4">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Mail className="h-4 w-4 flex-shrink-0" />
        <span className="truncate">{user?.email}</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <MapPin className="h-4 w-4 flex-shrink-0" />
        <span>{user?.location}</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Calendar className="h-4 w-4 flex-shrink-0" />
        <span>Joined {user?.joinedDate}</span>
      </div>
    </div>
  );
}

function UserStats({ user }: { user: UserProfileProps }) {
  return (
    <div className="flex flex-row items-center justify-evenly w-full gap-8 border rounded-lg p-4 text-center mt-2 xl:mt-0">
      <div className="flex flex-col items-center justify-center">
        <span className="text-xl font-semibold">{user?.stats?.projects}</span>
        <span className="text-xs text-muted-foreground mt-1">Active Projects</span>
      </div>

      <Separator orientation="vertical" className="data-[orientation=vertical]:h-8 my-auto" />
      <div className="flex flex-col items-center justify-center">
        <span className="text-xl font-semibold">{user?.stats?.tasksDone}</span>
        <span className="text-xs text-muted-foreground mt-1">Tasks Done</span>
      </div>
      <Separator orientation="vertical" className="data-[orientation=vertical]:h-8 my-auto" />
      <div className="flex flex-col items-center justify-center">
        <span className="text-xl font-semibold">{user?.stats?.pullRequests}</span>
        <span className="text-xs text-muted-foreground mt-1">Pull Requests</span>
      </div>
    </div>
  );
}
