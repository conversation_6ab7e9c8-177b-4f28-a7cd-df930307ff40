import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ArrowUpRightIcon,
  GitPullRequestIcon,
  ListTodoIcon,
  MergeIcon,
  MessageCircleMoreIcon,
  PenIcon,
  RedoDotIcon,
} from "lucide-react";
import { Suspense } from "react";
import { PR_ITEMS, TASK_ITEMS, type TaskItemType } from "../_mock-data";
export function UserTasks() {
  return (
    <div className="w-full xl:max-w-[45%] flex flex-col min-h-0 p-6 border rounded-lg xl:border xl:rounded-lg">
      <div className="flex flex-col w-full h-full min-h-0">
        <h2 className="text-xl font-semibold">Tasks and Pull Requests</h2>
        <div className="text-sm text-muted-foreground mb-2">Your unfinished work.</div>
        <div className="flex flex-col w-full flex-1 overflow-y-auto min-h-0 gap-4 py-2">
          <Suspense
            fallback={
              <div className="text-sm text-muted-foreground">Loading your favorite tasks...</div>
            }
          >
            <TasksList />
            <PullRequestsList />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

function TasksList() {
  return (
    <div id="project-tasks" className="w-full">
      <h4 className="flex items-center gap-2 text-sm font-medium mb-2">
        <ListTodoIcon className="w-4 h-4" />
        Assigned Tasks
      </h4>
      {TASK_ITEMS.length === 0 && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 mb-4 p-2 rounded-md bg-muted/30 group">
          <p className="text-sm text-muted-foreground">No tasks assigned yet</p>
          <Button
            variant="outline"
            size="sm"
            className="text-xs font-medium flex items-center gap-1"
          >
            <span>The next best thing to do</span>
            <ArrowUpRightIcon className="w-3 h-3 group-hover:translate-x-0.5 group-hover:-translate-y-[1px] transition-transform duration-200" />
          </Button>
        </div>
      )}

      {TASK_ITEMS?.map((task: TaskItemType, index: number) => (
        <WorkItem
          key={`task-${index}`}
          type="task"
          tag={task?.tag}
          description={task?.description}
          status={task?.status}
          bgColor={task?.bgColor}
        />
      ))}
    </div>
  );
}

function PullRequestsList() {
  return (
    <div id="project-prs" className="w-full">
      <h4 className="flex items-center gap-2 text-sm font-medium mb-2">
        <GitPullRequestIcon className="w-4 h-4" />
        Assigned Pull Requests
      </h4>
      {PR_ITEMS.length === 0 && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 mb-4 p-2 rounded-md bg-muted/30 group">
          <p className="text-sm text-muted-foreground">No pull requests assigned</p>
          <Button
            variant="outline"
            size="sm"
            className="text-xs font-medium flex items-center gap-1"
          >
            <span>Review a PR for a colleague</span>
            <ArrowUpRightIcon className="w-3 h-3 group-hover:translate-x-0.5 group-hover:-translate-y-[1px] transition-transform duration-200" />
          </Button>
        </div>
      )}
      {PR_ITEMS.map((pr: TaskItemType, index: number) => (
        <WorkItem
          key={`pr-${index}`}
          type="pr"
          tag={pr.tag}
          description={pr.description}
          status={pr.status}
          bgColor={pr.bgColor}
        />
      ))}
    </div>
  );
}

type WorkItemProps = {
  type: string;
  tag: string;
  description: string;
  status: string;
  bgColor: string;
};

function WorkItem({ type, tag, description, status, bgColor }: WorkItemProps) {
  return (
    <div className="px-2 py-1.5 rounded-md hover:bg-gray-50 dark:hover:bg-muted/30 group">
      <div className="flex items-center justify-between">
        <div className="flex flex-row items-center sm:flex-row gap-2 w-full">
          <Badge variant="outline" className={cn(bgColor, "!text-tiny md:text-xs w-20")}>
            {status}
          </Badge>
          <Badge
            variant="outline"
            className={cn("!text-tiny md:text-xs w-16 hidden md:block md:text-center ")}
          >
            {tag}
          </Badge>
          <span className="text-xs truncate max-w-full">{description}</span>
        </div>
        <div className="hidden md:flex">
          {type === "task" && (
            <div className="flex items-center">
              <PenIcon className="w-3.5 h-3.5 text-muted-foreground/50 group-hover:text-muted-foreground mr-4" />
              <RedoDotIcon className="w-4 h-4 text-muted-foreground/50 group-hover:text-muted-foreground mr-4" />
            </div>
          )}
          {type === "pr" && (
            <div className="flex items-center">
              <MessageCircleMoreIcon className="w-4 h-4 text-muted-foreground/50 group-hover:text-muted-foreground mr-4" />
              <MergeIcon className="w-4 h-4 text-muted-foreground/50 group-hover:text-muted-foreground mr-4" />
            </div>
          )}
        </div>
        <ArrowUpRightIcon className="w-5 h-5 text-muted-foreground/50 group-hover:text-muted-foreground hidden sm:block" />
      </div>
    </div>
  );
}
