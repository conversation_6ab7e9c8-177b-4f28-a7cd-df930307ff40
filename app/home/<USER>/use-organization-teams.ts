"use client";

import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to fetch all organization teams (admin view) with React Query caching
 * Uses organization cache config since teams data changes very rarely
 */
export function useOrganizationTeams() {
  return useQuery({
    queryKey: ["home", "organization", "teams"],
    queryFn: async () => {
      const response = await fetch("/api/organization/teams");
      if (!response.ok) {
        throw new Error("Failed to fetch organization teams");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch organization teams");
      }
      return {
        teams: result.data.teams,
        currentUserName: result.data.currentUserName,
      };
    },
    ...cacheConfigs.organization, // 2 hours stale time - teams data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Hook to fetch user's teams with React Query caching
 * Uses organization cache config since teams data changes very rarely
 */
export function useUserTeams() {
  return useQuery({
    queryKey: ["home", "organization", "user-teams"],
    queryFn: async () => {
      const response = await fetch("/api/organization/user-teams");
      if (!response.ok) {
        throw new Error("Failed to fetch user teams");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch user teams");
      }
      return {
        teams: result.data.teams,
        currentUserName: result.data.currentUserName,
      };
    },
    ...cacheConfigs.organization, // 2 hours stale time - teams data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Hook to fetch current user's organization role with React Query caching
 * Uses organization cache config since role data changes very rarely
 */
export function useUserOrganizationRole() {
  return useQuery({
    queryKey: ["home", "organization", "user-role"],
    queryFn: async () => {
      // Get role data from the teams API (it includes user role)
      const response = await fetch("/api/organization/teams");
      if (!response.ok) {
        throw new Error("Failed to fetch user organization role");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch user organization role");
      }
      return result.data.userRole;
    },
    ...cacheConfigs.organization, // 2 hours stale time - role data rarely changes
    ...commonQueryOptions,
  });
}

/**
 * Combined hook for organization teams page data
 * Fetches both teams and user role in parallel
 */
export function useOrganizationTeamsData() {
  const teamsQuery = useOrganizationTeams();
  const userRoleQuery = useUserOrganizationRole();

  return {
    teams: teamsQuery.data?.teams || [],
    currentUserName: teamsQuery.data?.currentUserName || "",
    isAdmin: userRoleQuery.data?.isAdmin || false,
    userRole: userRoleQuery.data?.role || "unknown",
    isLoading: teamsQuery.isLoading || userRoleQuery.isLoading,
    isError: teamsQuery.isError || userRoleQuery.isError,
    error: teamsQuery.error || userRoleQuery.error,
    // Individual query states for more granular control
    teamsQuery,
    userRoleQuery,
  };
}

/**
 * Combined hook for user teams page data (my-teams)
 * Fetches both user teams and user role in parallel
 */
export function useUserTeamsData() {
  const userTeamsQuery = useUserTeams();
  const userRoleQuery = useUserOrganizationRole();

  return {
    teams: userTeamsQuery.data?.teams || [],
    currentUserName: userTeamsQuery.data?.currentUserName || "",
    isAdmin: userRoleQuery.data?.isAdmin || false,
    userRole: userRoleQuery.data?.role || "unknown",
    isLoading: userTeamsQuery.isLoading || userRoleQuery.isLoading,
    isError: userTeamsQuery.isError || userRoleQuery.isError,
    error: userTeamsQuery.error || userRoleQuery.error,
    // Individual query states for more granular control
    userTeamsQuery,
    userRoleQuery,
  };
}
