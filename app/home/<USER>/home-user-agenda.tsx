import { Card } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Clock, Plus, Users } from "lucide-react";
import { Meeting, TODAYS_MEETINGS } from "../_mock-data";

export function UserAgenda() {
  return (
    <Card className="flex flex-col xl:flex-row justify-between p-6 w-full shadow-none">
      <div className="w-full h-full flex items-center">
        <div className="flex flex-col md:flex-row justify-between gap-8 h-full w-full">
          <AgendaSection />
          <CalendarSection />
        </div>
      </div>
    </Card>
  );
}

function AgendaSection() {
  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex flex-row items-center justify-between w-full mb-3">
        <h3 className="text-lg font-semibold">Agenda</h3>
        <Button variant="outline" size="sm">
          <Plus className="h-3 w-3 mr-1" />
          <span className="text-xs">New Meeting</span>
        </Button>
      </div>
      <div
        id="agenda-section"
        className={cn(
          "flex-1",
          TODAYS_MEETINGS.length === 0
            ? "flex flex-col items-center justify-center space-y-1"
            : "flex flex-col justify-end"
        )}
      >
        {TODAYS_MEETINGS.length === 0 && (
          <div className="flex flex-col items-center justify-center h-52 xl:h-full w-full gap-2 p-2 rounded-md bg-muted/70 dark:bg-muted/30 group">
            <h4 className="text-xs font-medium">Today</h4>
            <p className="text-sm text-muted-foreground text-center px-2">
              You do not have any meetings
              <br />
              today 🎉
            </p>
          </div>
        )}
        <div className="space-y-1 mt-auto">
          {TODAYS_MEETINGS.length > 0 && <h4 className="text-xs font-medium">Today</h4>}
          {TODAYS_MEETINGS.map((meeting) => (
            <MeetingCard key={meeting.id} meeting={meeting} />
          ))}
        </div>
      </div>
    </div>
  );
}

function MeetingCard({ meeting }: { meeting: Meeting }) {
  return (
    <div className="w-full p-2 rounded-md border bg-muted/20 text-xs">
      <div className="flex flex-row items-center justify-between gap-1">
        <div className="font-medium truncate">{meeting.title}</div>
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3" />
          {meeting.attendees}
        </div>
      </div>
      <div className="flex items-center justify-between gap-2 text-muted-foreground">
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {meeting.time}
        </div>
        <div>{meeting.room}</div>
      </div>
    </div>
  );
}

function CalendarSection() {
  return (
    <div className="w-full md:w-1/2 flex items-center justify-center md:flex lg:flex xl:hidden 2xl:flex">
      <Calendar
        mode="single"
        selected={new Date()}
        className="rounded-md border shadow"
        classNames={{
          months: "w-full",
          month: "w-full",
          table: "w-full",
          head_cell: "text-[0.6rem] font-medium",
          cell: "text-[0.7rem] p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
          day: "h-6 w-6 p-0 aria-selected:opacity-100",
          nav_button: "h-6 w-6",
          nav_button_previous: "absolute left-1",
          nav_button_next: "absolute right-1",
          caption: "relative h-6 items-center",
        }}
      />
    </div>
  );
}
