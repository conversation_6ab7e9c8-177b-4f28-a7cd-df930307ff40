import { Button } from "@/components/ui/button";
import { getUserTeamProjectsAction } from "@/db/actions/get-user-team-projects.action";
import { getCurrentOrganizationAction } from "@/db/actions/organization.action";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { connection } from "next/server";
import { ProjectListRefresh } from "./home-project-list-refresh";
import { ProjectListViewToggle, ProjectsWithViewState } from "./home-project-view-controls";

// Server component for the project list
export async function ProjectList({}) {
  await connection();
  const clusters = await getUserTeamProjectsAction();
  const organization = await getCurrentOrganizationAction();

  // Combine all projects from all clusters into a single array
  const allProjects =
    clusters?.flatMap((cluster) =>
      cluster.projects.map((project) => ({
        ...project,
        clusterName: cluster.name,
        clusterSlug: cluster.slug,
        clusterId: cluster.id,
        clusterTenantId: cluster.tenant_id,
        clusterDescription: cluster.description,
      }))
    ) || [];

  if (!clusters || clusters.length === 0 || !organization || allProjects.length === 0) {
    return (
      <div className="w-full xl:w-[55%] flex flex-col min-h-0 p-6 border rounded-lg">
        <div className="w-full overflow-hidden">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
                <div>
                  <h2 className="text-xl font-semibold">Your Projects</h2>
                  <div className="text-sm text-muted-foreground">
                    No projects assigned to your teams
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <ProjectListRefresh />
                  <Button asChild variant="ghost">
                    <Link href="/home/<USER>/projects/new">
                      <PlusIcon className="w-4 h-4" />
                      New Project
                    </Link>
                  </Button>
                </div>
              </div>
              <div className="flex flex-col items-center justify-center h-80 p-12 border border-dashed rounded-md">
                <p className="text-muted-foreground mb-2">
                  You don&apos;t have any projects assigned to your teams
                </p>
                <p className="text-xs text-muted-foreground">
                  Join a team or ask your administrator to assign projects to your teams
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden xl:p-6 border rounded-lg w-full">
      <div className="grid gap-4">
        <div className="grid gap-2">
          <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center sm:justify-between ml-1">
            <div>
              <h2 className="text-xl font-semibold">Your Projects</h2>
              <div className="text-sm text-muted-foreground">
                <span className="text-xs">Only showing projects from your teams only</span>
              </div>
            </div>
            <div className="flex items-center gap-2 ml-8">
              <ProjectListRefresh />
              <ProjectListViewToggle />
              <Button asChild variant="ghost">
                <Link href="/home/<USER>/projects/new">
                  <PlusIcon className="w-4 h-4" />
                  New Project
                </Link>
              </Button>
            </div>
          </div>
          <div className="p-0 overflow-hidden h-full">
            <ProjectsWithViewState projects={allProjects} />
          </div>
        </div>
      </div>
    </div>
  );
}
