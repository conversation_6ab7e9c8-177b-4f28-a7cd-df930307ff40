"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { createTeamAction } from "@/db/actions/team.actions";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Form schema validation
const formSchema = z.object({
  name: z
    .string()
    .min(2, "Team name must be at least 2 characters")
    .max(50, "Team name must be less than 50 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

export type CreateTeamFormValues = z.infer<typeof formSchema>;

export function CreateTeamForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form
  const form = useForm<CreateTeamFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // Form submission handler
  const onSubmit = async (values: CreateTeamFormValues) => {
    setIsSubmitting(true);

    try {
      const result = await createTeamAction({
        name: values.name,
        description: values.description,
      });

      if (!result.success) {
        toast.error(result.message);
        return;
      }

      toast.success(result.message);

      // Redirect to teams page after successful creation
      router.push("/home/<USER>");
      router.refresh();
    } catch (error) {
      toast.error("Failed to create team. Please try again.");
      console.info("Error creating team:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full max-w-xl p-8 space-y-8 border rounded-lg shadow-sm"
        >
          <div className="space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Create a New Team</h1>
            <p className="text-sm text-muted-foreground">
              Fill out the form below to create your team
            </p>
          </div>

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-medium">Team Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter team name" className="mt-1.5" {...field} />
                </FormControl>
                <FormDescription className="text-xs mt-1.5">
                  Choose a clear name that represents your team&apos;s purpose.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-medium">Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe what this team does"
                    className="resize-none h-28 mt-1.5"
                    {...field}
                  />
                </FormControl>
                <FormDescription className="text-xs mt-1.5">
                  Provide a brief description of the team&apos;s responsibilities and focus.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              className="w-1/2"
              onClick={() => router.push("/home/<USER>")}
            >
              Cancel
            </Button>
            <Button type="submit" variant="default" className="w-1/2" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Team"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
