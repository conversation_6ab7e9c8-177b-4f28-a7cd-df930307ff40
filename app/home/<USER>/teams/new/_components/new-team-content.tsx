import NewTeamSkeleton from "@/components/skeletons/new-team-skeleton";
import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";
import { notFound } from "next/navigation";
import { connection } from "next/server";
import { Suspense } from "react";
import { CreateTeamForm } from "./create-team-form";

export default async function NewTeamContent() {
  // Mark this component as dynamic
  await connection();

  // Check if user has admin permissions
  const { data: userRole } = await getCurrentUserOrganizationRoleAction();
  const isAdmin = userRole?.isAdmin;

  // If not an admin, show 404
  if (!isAdmin) {
    notFound();
  }

  return (
    <Suspense fallback={<NewTeamSkeleton />}>
      <CreateTeamForm />
    </Suspense>
  );
}
