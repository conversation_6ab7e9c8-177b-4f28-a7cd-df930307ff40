import { Button } from "@/components/ui/button";
import { Home } from "lucide-react";
import Link from "next/link";

export function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center w-screen min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="w-full max-w-3xl px-4 py-8 text-center">
        <div className="relative">
          <h1 className="text-[150px] font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500 animate-fade-in md:text-[200px]">
            404
          </h1>
          <div className="absolute inset-0 blur-3xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse" />
        </div>

        <h2 className="mt-8 mb-4 text-2xl font-semibold text-slate-700 dark:text-slate-200 animate-fade-in">
          Oops! Page not found
        </h2>

        <p className="max-w-md mx-auto mb-8 text-slate-600 dark:text-slate-400 animate-fade-in">
          The page you&apos;re looking for doesn&apos;t exist or has been moved.
        </p>

        <Link href="/home" className="inline-block animate-fade-in">
          <Button variant="link" className="text-slate-600 dark:text-slate-400">
            <Home className="w-4 h-4 mr-2" />
            Back to your homepage
          </Button>
        </Link>

        <div className="mt-12 animate-fade-in">
          <svg
            className="w-48 h-48 mx-auto text-slate-300 dark:text-slate-700"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="0.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {/* Lost robot face */}
            <rect x="2" y="4" width="20" height="16" rx="2" />
            <circle cx="8" cy="10" r="2" />
            <circle cx="16" cy="10" r="2" />
            <path d="M7 15a6 6 0 0 1 10 0" />
            {/* Antenna */}
            <path d="M12 4V2" />
            <circle cx="12" cy="1" r="0.5" />
            {/* Circuit board patterns */}
            <path d="M4 8h2M4 12h3M4 16h2" />
            <path d="M18 8h2M17 12h3M18 16h2" />
            {/* Error sparks */}
            <path d="M20 3l1-1M3 3L2 2M20 21l1 1M3 21l-1 1" />
          </svg>
        </div>
      </div>
    </div>
  );
}

export default NotFound;
