import { getOrganizationMemberFromDb } from "@/db/organization-members.db";
import { getCurrentDomain, getCurrentSubdomain, goToMainDomain, goToSubdomain } from "@/lib/utils";
import { OrganizationType } from "@/types/organization.types";
import { UserProfileDB } from "@/types/user.types";
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

// Domain configuration
const isDev = process.env.NODE_ENV === "development";

// Common cookie options
const getCookieOptions = (isDev: boolean, domain: string): CookieOptions => ({
  ...(isDev ? {} : { domain: `.${domain}` }),
  maxAge: 60 * 60 * 24 * 30, // 30 days
  path: "/",
  sameSite: "lax",
  secure: !isDev,
});

/**
 * Types for our middleware functions
 */
type DomainInfo = {
  subdomain: string | null;
  isSubdomainRequest: boolean;
  pathname: string;
  hostname: string;
};

type UserContext = {
  user: UserProfileDB;
  request: NextRequest;
  supabaseResponse: NextResponse;
} & DomainInfo;

type OrganizationContext = {
  organizations: OrganizationType[];
} & UserContext;

/**
 * Initialize Supabase client with proper cookie handling
 */
function initializeSupabase(request: NextRequest) {
  let supabaseResponse = NextResponse.next({ request });

  // Get hostname and domain for cookie configuration
  const hostname = request.headers.get("host") || "";
  const currentDomain = getCurrentDomain(hostname);
  const cookieOptions = getCookieOptions(isDev, currentDomain);

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookieOptions,
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet: { name: string; value: string; options?: CookieOptions }[]) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
          supabaseResponse = NextResponse.next({ request });
          cookiesToSet.forEach(({ name, value, options }) => {
            const mergedOptions = { ...options, ...cookieOptions };
            supabaseResponse.cookies.set(name, value, mergedOptions);
          });
        },
      },
    }
  );

  return {
    supabase,
    supabaseResponse,
  };
}

/**
 * Extract domain information from the request
 */
function extractDomainInfo(request: NextRequest): DomainInfo {
  const hostname = request.headers.get("host") || "";
  const subdomain = getCurrentSubdomain(hostname);
  const isSubdomainRequest = subdomain !== null;
  const pathname = request.nextUrl.pathname;

  return {
    subdomain,
    isSubdomainRequest,
    pathname,
    hostname,
  };
}

/**
 * Handle unauthenticated users
 */
function handleUnauthenticatedUser(
  domainInfo: DomainInfo & {
    request: NextRequest;
  }
) {
  const { pathname, isSubdomainRequest, subdomain } = domainInfo;

  // First, handle the root route and no-access route
  if (pathname === "/" || pathname === "/no-access") {
    return null; // Allow access to home page and no-access page
  }

  // For auth routes when not authenticated:
  if (pathname?.startsWith("/auth")) {
    // Only allow direct access to /auth/login and /auth/signup
    if (pathname === "/auth/login" || pathname === "/auth/sign-up") {
      return null; // Allow access to these specific auth pages
    }

    // Any other auth routes should redirect to login
    return NextResponse.redirect(new URL(goToMainDomain("auth/login")));
  }

  // All other non-auth routes should redirect to login when not authenticated
  // If we have a subdomain, use it for the redirect, otherwise go to main domain
  const redirectUrl =
    isSubdomainRequest && subdomain
      ? goToSubdomain(subdomain, "/auth/login")
      : goToMainDomain("/auth/login");

  return NextResponse.redirect(new URL(redirectUrl));
}

/**
 * Handle authenticated users on main domain
 */
async function handleAuthenticatedMainDomain(context: UserContext) {
  const { user, pathname } = context;

  if (!user?.id) {
    return context.supabaseResponse;
  }

  // Get user's organizations
  const { data: organizations } = await getOrganizationMemberFromDb(user?.id);

  if (!organizations) {
    return context.supabaseResponse; // Return the error response
  }

  const extendedContext: OrganizationContext = {
    ...context,
    organizations,
  };

  // Handle auth routes on main domain
  if (pathname?.startsWith("/auth")) {
    return handleAuthRoutesMainDomain(extendedContext);
  }

  // For other routes on main domain
  return context.supabaseResponse;
}

/**
 * Handle auth routes on main domain
 */
function handleAuthRoutesMainDomain(context: OrganizationContext) {
  const { pathname, organizations } = context;

  // For login, redirect to organizations page
  if (pathname === "/auth/login") {
    return NextResponse.redirect(new URL(goToMainDomain("auth/organizations")));
  }

  // For organizations, auto-redirect if user has only one organization with a valid subdomain
  if (
    pathname === "/auth/organizations" &&
    organizations.length === 1 &&
    organizations[0].subdomain_id
  ) {
    // We've already checked that subdomain_id exists and is not null above
    const targetUrl = goToSubdomain(organizations[0].subdomain_id!, "home");
    return NextResponse.redirect(new URL(targetUrl));
  }

  // For other auth routes, allow access
  return context.supabaseResponse;
}

/**
 * Handle authenticated users on subdomain
 */
async function handleAuthenticatedSubdomain(context: UserContext) {
  const { user, subdomain, pathname } = context;

  // Always allow access to the no-access page, auth/login, auth/sign-up, even on subdomains
  if (pathname === "/no-access" || pathname === "/auth/login" || pathname === "/auth/sign-up") {
    return context.supabaseResponse;
  }

  // Subdomain is required for this function
  if (!subdomain) {
    return context.supabaseResponse;
  }

  if (!user?.id) {
    return context.supabaseResponse;
  }

  // Get user's organizations
  const { data: organizations, error } = await getOrganizationMemberFromDb(user?.id);

  // Only redirect to login if it's specifically an auth error
  if (error?.message?.includes("JWT")) {
    return NextResponse.redirect(new URL(goToMainDomain("auth/login")));
  }

  // For other errors, return the error response but stay on subdomain
  if (error || !organizations) {
    return context.supabaseResponse;
  }

  // Check if user has access to this subdomain - using find instead of some for optimization
  const userOrg = organizations.find((org) => org.subdomain_id === subdomain);
  const hasAccess = Boolean(userOrg);

  if (!hasAccess) {
    // Redirect to the no-access page, constructing a clean URL
    const host = context.request.headers.get("host") || "";
    const protocol = context.request.nextUrl.protocol;
    return NextResponse.redirect(new URL(`${protocol}//${host}/no-access`));
  }

  return context.supabaseResponse;
}

/**
 * Handle dev-only docs protection
 */
function handleDocsProtection(context: DomainInfo & { request: NextRequest }) {
  const { pathname } = context;
  const nodeEnv = process.env.NODE_ENV;

  // If docs route and in production, block access completely
  if (pathname?.startsWith("/docs") && nodeEnv === "production") {
    // Return 404 page or custom "docs not available in production" page
    return NextResponse.redirect(new URL("/404", context.request.url));
  }

  // For non-production environments (dev and test), continue with token protection
  if (pathname?.startsWith("/docs") && nodeEnv !== "production") {
    // Skip the access check for the verify-access page itself
    if (pathname === "/docs/verify-access") {
      return null;
    }

    // Check for a dev token in the cookie only
    const devToken = context.request.cookies.get("dev_token")?.value;

    // The secret token value that developers should know
    const validToken = process.env.DEV_DOCS_TOKEN || "renwudocs2025";

    if (devToken !== validToken) {
      // Simply redirect to verify-access page without parameters
      return NextResponse.redirect(new URL("/docs/verify-access", context.request.url));
    }
  }

  return null;
}

/**
 * Middleware function to handle authentication and authorization
 * This middleware handles three environments:
 * 1. Development (NODE_ENV === "development") - Bypasses auth checks, uses localhost:3000
 * 2. Test (NODE_ENV === "test") - Enforces auth, uses renwu.dev domain
 * 3. Production (NODE_ENV === "production") - Enforces auth, uses renwu.app domain
 *
 * @param request - The incoming request object
 * @returns A response object
 */
export async function middleware(request: NextRequest) {
  // Get domain information
  const domainInfo = extractDomainInfo(request);
  const { isSubdomainRequest } = domainInfo;

  // Check for docs route protection (even in development and pre-production)
  const docsProtection = handleDocsProtection({
    ...domainInfo,
    request,
  });
  if (docsProtection) return docsProtection;

  // Bypass other auth checks only in local development environment
  // Pre-production and production environments will enforce auth
  if (isDev) {
    return NextResponse.next();
  }

  // Initialize Supabase
  const { supabase, supabaseResponse } = initializeSupabase(request);

  // Get authenticated user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Handle unauthenticated users - with early return for simplified flow
  if (!user) {
    return (
      handleUnauthenticatedUser({
        ...domainInfo,
        request,
      }) || supabaseResponse
    );
  }

  // Create user context
  const userContext: UserContext = {
    ...domainInfo,
    user,
    request,
    supabaseResponse,
  };

  // Handle authenticated users based on domain
  return isSubdomainRequest
    ? handleAuthenticatedSubdomain(userContext)
    : handleAuthenticatedMainDomain(userContext);
}

/**
 * Configuration for the middleware
 * @type {import('next').NextConfig}
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|api).*)",
    "/docs/:path*",
  ],
};
