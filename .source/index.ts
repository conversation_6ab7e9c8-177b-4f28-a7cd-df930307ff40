// @ts-nocheck -- skip type checking
import * as docs_23 from "../docs/maintenance/removing-fumadocs.mdx?collection=docs&hash=1747180169829"
import * as docs_22 from "../docs/development/react-query-standardization.mdx?collection=docs&hash=1747180169829"
import * as docs_21 from "../docs/development/project-structure.mdx?collection=docs&hash=1747180169829"
import * as docs_20 from "../docs/development/performance-investigation-report.mdx?collection=docs&hash=1747180169829"
import * as docs_19 from "../docs/development/navigation-performance-fixes.mdx?collection=docs&hash=1747180169829"
import * as docs_18 from "../docs/development/navigation-cache-fix.mdx?collection=docs&hash=1747180169829"
import * as docs_17 from "../docs/development/mock-data-system.mdx?collection=docs&hash=1747180169829"
import * as docs_16 from "../docs/development/local-subdomain-setup.mdx?collection=docs&hash=1747180169829"
import * as docs_15 from "../docs/development/kanban-board.mdx?collection=docs&hash=1747180169829"
import * as docs_14 from "../docs/development/database-audit-report.mdx?collection=docs&hash=1747180169829"
import * as docs_13 from "../docs/development/data-fetching.mdx?collection=docs&hash=1747180169829"
import * as docs_12 from "../docs/development/3-tier-architecture-rollback-summary.mdx?collection=docs&hash=1747180169829"
import * as docs_11 from "../docs/business-plan/competitive-advantages.mdx?collection=docs&hash=1747180169829"
import * as docs_10 from "../docs/business-plan/business-plan.mdx?collection=docs&hash=1747180169829"
import * as docs_9 from "../docs/authentication/subdomain-auth.mdx?collection=docs&hash=1747180169829"
import * as docs_8 from "../docs/authentication/session-management.mdx?collection=docs&hash=1747180169829"
import * as docs_7 from "../docs/authentication/index.mdx?collection=docs&hash=1747180169829"
import * as docs_6 from "../docs/authentication/auth-flow.mdx?collection=docs&hash=1747180169829"
import * as docs_5 from "../docs/architecture/url-structure.mdx?collection=docs&hash=1747180169829"
import * as docs_4 from "../docs/architecture/tenant-isolation.mdx?collection=docs&hash=1747180169829"
import * as docs_3 from "../docs/architecture/scaling-strategy.mdx?collection=docs&hash=1747180169829"
import * as docs_2 from "../docs/architecture/organization-system.mdx?collection=docs&hash=1747180169829"
import * as docs_1 from "../docs/architecture/db-multi-tenant-strategy.mdx?collection=docs&hash=1747180169829"
import * as docs_0 from "../docs/index.mdx?collection=docs&hash=1747180169829"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/index.mdx"}, data: docs_0 }, { info: {"path":"architecture/db-multi-tenant-strategy.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/architecture/db-multi-tenant-strategy.mdx"}, data: docs_1 }, { info: {"path":"architecture/organization-system.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/architecture/organization-system.mdx"}, data: docs_2 }, { info: {"path":"architecture/scaling-strategy.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/architecture/scaling-strategy.mdx"}, data: docs_3 }, { info: {"path":"architecture/tenant-isolation.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/architecture/tenant-isolation.mdx"}, data: docs_4 }, { info: {"path":"architecture/url-structure.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/architecture/url-structure.mdx"}, data: docs_5 }, { info: {"path":"authentication/auth-flow.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/authentication/auth-flow.mdx"}, data: docs_6 }, { info: {"path":"authentication/index.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/authentication/index.mdx"}, data: docs_7 }, { info: {"path":"authentication/session-management.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/authentication/session-management.mdx"}, data: docs_8 }, { info: {"path":"authentication/subdomain-auth.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/authentication/subdomain-auth.mdx"}, data: docs_9 }, { info: {"path":"business-plan/business-plan.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/business-plan/business-plan.mdx"}, data: docs_10 }, { info: {"path":"business-plan/competitive-advantages.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/business-plan/competitive-advantages.mdx"}, data: docs_11 }, { info: {"path":"development/3-tier-architecture-rollback-summary.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/3-tier-architecture-rollback-summary.mdx"}, data: docs_12 }, { info: {"path":"development/data-fetching.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/data-fetching.mdx"}, data: docs_13 }, { info: {"path":"development/database-audit-report.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/database-audit-report.mdx"}, data: docs_14 }, { info: {"path":"development/kanban-board.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/kanban-board.mdx"}, data: docs_15 }, { info: {"path":"development/local-subdomain-setup.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/local-subdomain-setup.mdx"}, data: docs_16 }, { info: {"path":"development/mock-data-system.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/mock-data-system.mdx"}, data: docs_17 }, { info: {"path":"development/navigation-cache-fix.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/navigation-cache-fix.mdx"}, data: docs_18 }, { info: {"path":"development/navigation-performance-fixes.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/navigation-performance-fixes.mdx"}, data: docs_19 }, { info: {"path":"development/performance-investigation-report.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/performance-investigation-report.mdx"}, data: docs_20 }, { info: {"path":"development/project-structure.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/project-structure.mdx"}, data: docs_21 }, { info: {"path":"development/react-query-standardization.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/development/react-query-standardization.mdx"}, data: docs_22 }, { info: {"path":"maintenance/removing-fumadocs.mdx","absolutePath":"/Users/<USER>/Code/renwu/docs/maintenance/removing-fumadocs.mdx"}, data: docs_23 }], [])