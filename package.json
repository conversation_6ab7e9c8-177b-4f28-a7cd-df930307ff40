{"name": "renwu", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "postinstall": "fumadocs-mdx"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-use-controllable-state": "^1.1.0", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/cli": "^4.1.2", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.8", "@types/mdx": "^2.0.13", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "fumadocs-core": "^15.2.11", "fumadocs-mdx": "^11.6.1", "fumadocs-ui": "^15.2.11", "lucide-react": "^0.483.0", "mermaid": "^11.6.0", "motion": "^12.5.0", "next": "15.3.3", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "postcss": "^8.5.3", "react": "^19.1.0", "react-day-picker": "^9.6.3", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.1", "resend": "^4.5.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.3", "eslint-plugin-react-hooks": "^5.2.0", "html": "link:prettier/plugins/html", "prettier": "^3.5.3", "tailwindcss": "^4.1.2", "typescript": "^5.8.2"}}