# System Patterns: Renwu

## Architecture Overview

The application follows a multi-tenant architecture with subdomain-based organization isolation:

```mermaid
graph TD
    subgraph Client
        UI[User Interface]
        RC[React Components]
        CTX[Organization Context]
    end

    subgraph Auth Flow
        Login[Login on Main Domain]
        OrgSelect[Organization Selection]
        Redirect[Subdomain Redirect]
    end

    subgraph Middleware
        MW[Next.js Middleware]
        SR[Subdomain Router]
        AC[Auth Controller]
    end

    subgraph Server
        API[Next.js API]
        DB[Supabase/PostgreSQL]
        Auth[Supabase Auth]
    end

    subgraph Documentation
        FD[Fumadocs]
        MD[MDX Documents]
        DT[Documentation Tree]
    end

    UI --> RC
    RC --> CTX

    Login --> Auth
    Auth --> OrgSelect
    OrgSelect --> Redirect
    Redirect --> CTX

    CTX --> MW
    MW --> SR
    SR --> AC
    AC --> API
    API --> DB
    API --> Auth

    DT --> MD
    MD --> FD
    FD --> UI
```

## Core Patterns

### Authentication Flow

1. **Central Authentication**

   - Users authenticate on main domain (renwu.app)
   - All auth routes exist only on the main domain
   - Auth routes on subdomains are redirected to main domain

2. **Organization Selection**

   - After login, users see organizations they belong to on `/auth/organizations`
   - If user has only one organization, automatic redirect to that organization's subdomain
   - Organization cards show key info including role and subdomain

3. **Subdomain Access**
   - Each organization accessed via unique subdomain: `{organization}.renwu.app`
   - Sessions are maintained across subdomain switches through proper cookie configuration
   - Auth state preserved during navigation between domains

### User Profile Creation Pattern

1. **Robust Profile Creation**

   - User profiles are created during the sign-up process
   - Profile creation uses upsert operation instead of insert to handle existing users
   - Implemented with `onConflict: "user_id"` to update existing profiles
   - Ensures smooth profile creation regardless of authentication path (direct signup or invitation)

2. **Edge Case Handling**
   - Handles cases where users already exist in auth.users but need profile creation
   - Manages profile creation for users accepting organization invitations
   - Provides appropriate error handling for duplicate profile scenarios
   - Includes logging for debugging authentication flow issues

### Multi-tenancy Implementation

1. **Domain & Subdomain Structure**

   - Main domain: `renwu.app` (prod) or `localhost:3000` (dev)
   - Subdomains: `{organization}.renwu.app` (prod) or `{organization}.localhost:3000` (dev)

2. **Data Isolation**
   - Organization-specific data only accessible on corresponding subdomain
   - Authentication spans across all domains, but access control is per-organization
   - Row Level Security (RLS) policies enforce organization-based separation

### Domain Utilities

The application uses specialized utility functions to ensure consistent URL construction across domains:

```typescript
// src/lib/utils.ts

export function goToMainDomain(path: string) {
  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${domain}/${cleanPath}`;
}

export function goToSubdomain(subdomain: string, path: string) {
  if (!subdomain) {
    return goToMainDomain(path);
  }

  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${subdomain}.${domain}/${cleanPath}`;
}
```

### Middleware Implementation

The middleware handles all routing logic between main domain and subdomains:

```typescript
// middleware.ts

export async function middleware(request: NextRequest) {
  // Initialize Supabase
  const { supabase, supabaseResponse } = initializeSupabase(request);

  // Get authenticated user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Get domain information
  const hostname = request.headers.get("host") || "";
  const subdomain = getCurrentSubdomain(hostname);
  const isSubdomainRequest = subdomain !== null;
  const pathname = request.nextUrl.pathname;

  // Handle subdomain auth routes (redirect to main domain)
  const subdomainAuthRedirect = handleSubdomainAuthRoutes(domainInfo);
  if (subdomainAuthRedirect) return subdomainAuthRedirect;

  // Handle unauthenticated users
  if (!user) {
    const unauthRedirect = handleUnauthenticatedUser({
      ...domainInfo,
      request,
    });
    return unauthRedirect || supabaseResponse;
  }

  // Create user context
  const userContext = {
    ...domainInfo,
    user,
    request,
    supabaseResponse,
  };

  // Handle authenticated users based on domain
  if (isSubdomainRequest) {
    return handleAuthenticatedSubdomain(userContext);
  } else {
    return handleAuthenticatedMainDomain(userContext);
  }
}
```

### Cross-Domain Session Management

Session persistence is achieved through proper cookie configuration:

```typescript
// Cookie configuration for Supabase client
cookieOptions: {
  // For localhost, don't use domain setting at all in development
  ...(isDev ? {} : { domain: `.${currentDomain}` }),
  maxAge: 60 * 60 * 24 * 30, // 30 days
  path: "/",
  sameSite: "lax",
  secure: !isDev,
}
```

## Authentication Rules & Routing

### Main Domain Routes

1. `/auth/login`

   - Entry point for unauthenticated users
   - Redirects to `/auth/organizations` if already authenticated

2. `/auth/organizations`
   - Organization selection interface
   - Automatic redirect for single organization members
   - Central hub for organization switching

### Documentation Access Control

1. `/docs/verify-access`

   - Verifies developer token for accessing restricted documentation
   - Stores token in cookies for persistent access
   - Redirects to requested documentation page after verification

2. `/docs/access-denied`
   - Shown when unauthorized access to documentation is attempted
   - Provides form for entering developer token
   - Preserves original requested path via search parameters
   - Implements redirect loop prevention by checking referrer
   - Uses safe default return path if a loop is detected

### Subdomain Routes

1. `{organization}.renwu.app/*`

   - Only accessible to authenticated users who are members of the organization
   - Redirects unauthorized users appropriately

2. No auth routes on subdomains
   - All auth-related URLs redirect to main domain

## Redirect Loop Prevention Pattern

To prevent infinite redirects in authentication flows, the application implements a redirect loop detection mechanism:

```typescript
// Example from docs/access-denied page
// Check if we're in a potential redirect loop
const referrer = headers().get("referer") || "";
const isRedirectLoop = referrer.includes("/docs/access-denied");

// Set a safe return path if a loop is detected
const returnPath = isRedirectLoop ? "/docs" : searchParams.returnTo || "/docs";
```

This pattern:

1. Examines the HTTP referrer header to determine the previous page
2. Identifies potential loops by checking if the referrer contains the current route
3. Sets a safe default path to break any detected loops
4. Preserves the intended destination when no loop is detected

## Form Submission with Search Parameters

Next.js 15 requires special handling for search parameters in form submissions. The application implements the following pattern:

```typescript
// Access-denied page form handling
export default function AccessDeniedPage({ searchParams }: PageProps) {
  const returnTo = searchParams.returnTo || "/docs";

  return (
    <form action="/docs/verify-access" method="post">
      <input type="hidden" name="returnTo" value={returnTo} />
      <input type="text" name="token" placeholder="Developer token" />
      <button type="submit">Submit</button>
    </form>
  );
}
```

Key elements of this pattern:

1. Receive search parameters as props in the page component
2. Extract relevant parameters for redirect purposes
3. Preserve these parameters by including them as hidden form fields
4. Process the parameters on the target page to maintain state across redirects

This approach ensures that important context (like return paths) is maintained across form submissions and redirects.

## Link Component Optimization

To handle cross-domain navigation properly:

1. Use domain utility functions for all cross-domain links:

```jsx
<Link href={goToMainDomain("auth/organizations")}>Organizations</Link>
```

2. Disable prefetching to prevent CORS errors:

```jsx
<Link href={goToSubdomain(org.subdomain_id, "home")}>Access</Link>
```

## User Flow Diagrams

### Unauthenticated User Flow

```mermaid
graph TD
    A[User visits any URL] --> B{Is Authenticated?}
    B -->|No| C[Redirect to /auth/login]
    C --> D[User logs in]
    D --> E[Redirect to /auth/organizations]
    E --> F{Number of Organizations}
    F -->|One| G[Redirect to organization subdomain /home]
    F -->|Multiple| H[Show organization selection]
    H --> I[User selects organization]
    I --> G
```

### Authenticated User Flow

```mermaid
graph TD
    A[Authenticated User] --> B{Current Domain}
    B -->|Main Domain| C{Accessing Auth Routes?}
    C -->|Yes| D[Show /auth/organizations]
    C -->|No| E[Normal Flow]

    B -->|Subdomain| F{Has Access?}
    F -->|Yes| G[Allow Access]
    F -->|No| H[Redirect to Main Domain /auth/organizations]
```

## Error Handling

1. Invalid Subdomain

   - Redirect to main domain organization selection

2. Unauthorized Access

   - Redirect to main domain login if unauthenticated
   - Redirect to organization selection if authenticated but unauthorized

3. Missing Organization
   - Handle cases where organization data is not found
   - Provide appropriate error messages and redirects

## Demo Implementation

The application includes a comprehensive demo mode that allows users to explore the application's features without creating an account.

### Demo Architecture

1. **Route Structure**

   - Demo routes are isolated in `/app/demo/*`
   - Parallel structure to main application routes
   - Clear visual indicators to distinguish demo mode

2. **Data Handling**

   - Mock data is clearly labeled with "Demo Account" badges
   - Static data for demonstrations
   - Realistic but fictional content

3. **User Experience**
   - Easy access to demo mode from landing page
   - Clear visual indicators that user is in demo mode
   - Comprehensive example data across all features

### Demo Data Implementation

```typescript
// Demo data isolation pattern
export const DEMO_USER = {
  id: "demo-user-id",
  email: "<EMAIL>",
  name: "Demo User",
  // Other demo user properties
};

export const DEMO_ORGANIZATIONS = [
  {
    id: "demo-org-1",
    name: "Demo Organization",
    subdomain: "demo",
    // Other organization properties
  },
];

// Clearly marking demo content
export function DemoAccountBadge() {
  return (
    <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-md text-xs font-medium">
      <WarningIcon className="w-3 h-3" />
      <span>Demo Account</span>
    </div>
  );
}
```

### Demo Access Control

```typescript
// In middleware.ts or similar
function handleDemoAccess(request) {
  // Allow public access to demo routes
  if (request.nextUrl.pathname?.startsWith("/demo")) {
    return NextResponse.next();
  }

  // Normal authentication flow for non-demo routes
  // ...
}
```

## Development Guidelines

1. **Domain-Aware Components**

   - Use `goToMainDomain` and `goToSubdomain` for all cross-domain links
   - Disable prefetching for cross-domain links

2. **Authentication Flow**

   - All authentication happens on main domain only
   - Subdomains only handle organization-specific content

3. **Access Control**

   - Always verify organization membership for subdomain access
   - Provide clear feedback for access denial

4. **Error Handling**

   - Distinguish between authentication errors and access control errors
   - Prevent redirect loops with careful condition checking

5. **Demo Content**
   - Clearly mark all demo content with visual indicators
   - Maintain separation between demo and real user data
   - Ensure demo data covers all key features

## Technical Implementation

### Component Architecture

1. **Server Components**

   - Handle data fetching and business logic
   - Render static and dynamic content
   - Manage organization-specific data

2. **Client Components**
   - Handle user interactions
   - Manage local state
   - Implement real-time features

## Database Schema

```sql
-- Organizations table with subdomain support
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  subdomain TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User-Organization relationship
CREATE TABLE user_organizations (
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (user_id, organization_id)
);

-- RLS Policies
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can access their organizations"
  ON organizations
  FOR ALL
  USING (
    id IN (
      SELECT organization_id
      FROM user_organizations
      WHERE user_id = auth.uid()
    )
  );
```

## Performance Optimization

1. **Caching Strategy**

   - Organization data caching
   - Auth state persistence
   - API response caching
   - Route data caching

2. **Database Optimization**
   - Efficient queries
   - Proper indexing
   - Connection pooling
   - Query monitoring

## Cluster & Project Implementation

### Data Model

The application uses a hierarchical structure for organizing work:

```mermaid
graph TD
    Organization --> Clusters
    Clusters --> Projects
    Projects --> Tasks
```

1. **Clusters**

   - Top-level container for related projects
   - Each cluster belongs to an organization
   - Has properties: name, description, status
   - Identified by a unique slug for URL routing

2. **Projects**
   - Belongs to exactly one cluster
   - Has properties: name, description, status, priority, dates
   - Identified by a unique slug for URL routing
   - Can contain multiple tasks

### Routing Structure

The application uses dynamic routing based on slugs:

```
/{cluster-slug}/{project-slug}/dashboard
/{cluster-slug}/{project-slug}/board
/{cluster-slug}/{project-slug}/analytics
```

### Dynamic Navigation

To handle the dynamic routing structure in client components, the application implements:

1. **Route Parameter Extraction**

```tsx
// src/hooks/use-route-params.ts
"use client";

import { usePathname } from "next/navigation";

export function useRouteParams() {
  const pathname = usePathname();
  if (!pathname) return { clusterSlug: null, projectSlug: null };

  const parts = pathname?.split("/").filter(Boolean);

  if (parts.length < 2) return { clusterSlug: null, projectSlug: null };

  return {
    clusterSlug: parts[0],
    projectSlug: parts[1],
  };
}
```

2. **Dynamic URL Building**

```tsx
// src/lib/navigation-utils.ts
export function buildDynamicUrl(
  baseUrl: string,
  params?: { clusterSlug?: string | null; projectSlug?: string | null }
) {
  // Skip processing for special URLs like /home or external URLs
  if (baseUrl.startsWith("http") || baseUrl.startsWith("/home") || baseUrl.startsWith("#")) {
    return baseUrl;
  }

  // If we don't have cluster and project slugs, return the base URL
  if (!params?.clusterSlug || !params?.projectSlug) {
    return baseUrl;
  }

  // Build the dynamic URL
  return `/${params.clusterSlug}/${params.projectSlug}${
    baseUrl.startsWith("/") ? baseUrl : `/${baseUrl}`
  }`;
}
```

3. **Active Menu Detection**

The application enhances the `useActiveMenu` hook to handle dynamic routes:

```tsx
// Extract base path from dynamic route
const basePath = pathname?.substring(`/${clusterSlug}/${projectSlug}`.length) || "/";

// Compare with menu item URLs
if (basePath === url) return true;
```

This pattern allows:

- Client-side navigation to work with dynamic routes
- Active menu highlighting to function correctly
- Proper URL generation without requiring server components

### Creation Flow

The application uses a multi-step wizard for creating clusters and projects:

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant Server
    participant Database

    User->>UI: Initiates cluster/project creation
    UI->>UI: Step 1: Collect cluster details
    UI->>UI: Step 2: Collect project details (1-20)
    UI->>UI: Step 3: Show review screen
    User->>UI: Confirms creation
    UI->>Server: createClusterWithProjects()
    Server->>Database: Transaction: Create cluster
    Server->>Database: Transaction: Create projects
    Database-->>Server: Success/Failure
    Server-->>UI: Response
    UI->>User: Success notification & redirect
```

### UI Components

1. **Project Cards**

   - Visual representation of project status
   - Shows name, description, status badge, and progress
   - Links to project dashboard
   - Organized by cluster

2. **Cluster Section**

   - Groups projects by cluster
   - Shows cluster name and details
   - Collapsible for better organization

3. **Creation Wizard**
   - Multi-step form with validation
   - Dynamic project fields (1-20)
   - Review step before submission
   - Success/error handling

### Implementation Pattern

The application uses database transactions to ensure data integrity:

```typescript
// Transaction pattern
const result = await db.$transaction(async (tx) => {
  // Step 1: Create cluster
  const cluster = await tx.clusters.create({...});

  // Step 2: Create projects
  const projects = await Promise.all(
    data.projects.map(project =>
      tx.projects.create({
        cluster_id: cluster.id,
        ...project
      })
    )
  );

  return { cluster, projects };
});
```

### Slug Generation

For SEO-friendly URLs, the application generates slugs:

```typescript
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");
}

// Ensure slugs are unique
async function ensureUniqueSlug(db: dbClient, table: string, slug: string): Promise<string> {
  // Check for existing slug
  const exists = await db.$executeRaw`
    SELECT 1 FROM ${table} WHERE slug = ${slug} LIMIT 1
  `;

  if (!exists) return slug;

  // Add numeric suffix if needed
  let counter = 1;
  let newSlug = `${slug}-${counter}`;

  while (
    await db.$executeRaw`
    SELECT 1 FROM ${table} WHERE slug = ${newSlug} LIMIT 1
  `
  ) {
    counter++;
    newSlug = `${slug}-${counter}`;
  }

  return newSlug;
}
```

### Optimized Data Loading

For project routes, the application uses parallel data loading:

```tsx
// Parallel data loading in project layout
const [cluster, project] = await Promise.all([
  getClusterBySlug(params["cluster-slug"]),
  getProjectBySlug(params["project-slug"]),
]);
```

### Progressive Loading with Suspense

The application uses Suspense for a better loading experience:

```tsx
<Suspense
  fallback={
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array(6)
        .fill(0)
        .map((_, i) => (
          <Skeleton key={i} className="h-40 w-full" />
        ))}
    </div>
  }
>
  <ClusterProjectsList />
</Suspense>
```

## Toast Notification System

The application uses Sonner to provide toast notifications for user feedback:

```tsx
// Layout component with Toaster
import { Toaster } from "sonner";

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        {children}
        <Toaster position="bottom-right" />
      </body>
    </html>
  );
}

// Usage examples
import { toast } from "sonner";

// Success notification
toast.success("Project created successfully!");

// Error notification
toast.error("Failed to create project. Please try again.");

// Custom notification
toast("Operation in progress", {
  description: "Please wait while we process your request.",
  icon: <LoadingIcon className="animate-spin" />,
});
```

## Database Structure Optimization

The application has restructured database access files for improved organization and clarity:

```
src/db/
├── actions/          # Database operations grouped by domain
│   ├── clusters.ts   # Cluster-related operations
│   ├── projects.ts   # Project-related operations
│   └── users.ts      # User-related operations
├── schema/           # Database schema definitions
│   ├── cluster.ts
│   ├── project.ts
│   └── user.ts
└── index.ts          # Main database client export
```

This structure provides:

1. Better separation of concerns
2. Domain-driven organization of database operations
3. Improved maintainability
4. Clear module boundaries

### Documentation System

1. **Documentation Architecture**

   - Uses Fumadocs for MDX-based documentation
   - Organized into logical sections:
     - Architecture docs: tenant isolation, scaling strategy, URL structure, etc.
     - Authentication docs: auth flow, session management, subdomain auth
     - Development docs: project structure, data fetching, local setup
     - Maintenance docs: documentation procedures

2. **Multi-Tenant Data Architecture**

   - Tenant isolation using Row-Level Security (RLS) policies
   - Database triggers for automatic tenant_id assignment
   - Cross-table relationships maintain tenant isolation
   - Security in depth with multiple isolation checks

3. **URL Structure**
   - Subdomain-based organization access: `{organization}.renwu.app`
   - Hierarchical URL pattern: `{organization}.renwu.app/{cluster-slug}/{project-slug}/...`
   - Path-based routing within organization context
   - Short URLs for sharing: `renwu.app/s/{org}-{cluster}-{project}-{task}`

## Code Organization Patterns

### Separation of Responsibilities

The application follows a clear separation of responsibilities pattern:

```mermaid
graph TD
    DB[Database Files]
    Actions[Action Files]
    Components[Component Files]
    Pages[Page Files]

    DB --> Actions
    Actions --> Pages
    Actions --> Components
    Pages --> Components
```

1. **Database files (db/\*.db.ts)**

   - Located in `src/db` directory
   - Focus exclusively on database operations
   - Return raw data from the database without formatting
   - Handle low-level database errors
   - Export simple functions for CRUD operations

2. **Action files (app/_/\_actions/_.action.ts)**

   - Located in app directories alongside related components
   - Handle business logic and data transformation
   - Format data for UI components
   - Include error handling with user-friendly messages
   - Implement server actions with "use server" directive
   - Call database functions to perform data operations
   - Handle revalidation after mutations

3. **Page files (app/\*/page.tsx)**

   - Call action functions to fetch formatted data
   - Pass data to client components
   - Avoid direct database calls
   - Focus on layout and component composition

4. **Component files (app/_/\_components/_.tsx)**
   - Consume formatted data from pages or actions
   - Focus on UI rendering and user interactions
   - Use client-side state for UI-specific behavior

This pattern ensures:

- Clear responsibilities for each file type
- Consistent error handling
- Proper data formatting at the right level
- Simplified testing and maintenance
- Reuse of business logic across different UI components

### Example Implementation

```typescript
// Database file (src/db/team.db.ts)
export async function getTeamMembersFromDb(
  supabase: SupabaseClient,
  teamId: string,
  tenantId: string
): Promise<{ members: TeamMember[] }> {
  // Raw database query
  const { data, error } = await supabase.from("team_members").select(...)
  // Basic error handling and raw data return
}

// Action file (app/teams/_actions/fetch-teams.action.ts)
export async function fetchTeamsAction(): Promise<{ teams: Team[]; currentUserName: string }> {
  // Call database functions
  const teamsFromDb = await getTeamsWithMembersFromDb();

  // Format data for UI
  const formattedTeams = teamsFromDb.map(team => ({
    // Transform data
  }));

  // Return formatted data
  return {
    teams: formattedTeams,
    currentUserName: userMetadata?.name || "",
  };
}

// Page file (app/teams/page.tsx)
export default async function TeamsPage() {
  // Call action to get formatted data
  const { teams, currentUserName } = await fetchTeamsAction();

  // Pass to components
  return (
    <ProjectTeamMembersClient teams={teams} currentUserName={currentUserName} />
  );
}
```

## Teams Implementation

### Team Data Structure

The application implements a team management system that connects users to projects:

```mermaid
erDiagram
    Organizations ||--o{ Teams : has
    Teams ||--o{ TeamMembers : contains
    Teams ||--o{ TeamProjects : manages
    TeamMembers }o--|| Users : includes
    TeamProjects }o--|| Projects : connects
```

1. **Teams Table**

   - `id`: UUID primary key
   - `name`: Team name
   - `description`: Optional team description
   - `tenant_id`: Organization ID for tenant isolation
   - `created_at`: Timestamp for creation
   - `updated_at`: Timestamp for updates

2. **Team Members Table**

   - `id`: UUID primary key
   - `team_id`: Foreign key to Teams table
   - `user_id`: Foreign key to Users table (auth.users)
   - `role`: Member role (Admin, Member, Guest)
   - `position`: Optional job position
   - `tenant_id`: Organization ID for tenant isolation
   - `created_at`: Timestamp for creation
   - `updated_at`: Timestamp for updates

3. **Team Projects Table**
   - `id`: UUID primary key
   - `team_id`: Foreign key to Teams table
   - `project_id`: Foreign key to Projects table
   - `tenant_id`: Organization ID for tenant isolation
   - `created_at`: Timestamp for creation
   - `updated_at`: Timestamp for updates

### Team Access Control

The team system implements these access control mechanisms:

1. **Tenant Isolation**

   - All team-related tables include `tenant_id` for organization isolation
   - RLS policies ensure teams are only accessible within their organization

2. **Role-based Access**
   - Team members have roles (Admin, Member, Guest) determining their permissions
   - Team Admins can manage the team, its members, and its projects
   - Regular Members have access to team projects but limited management capabilities
   - Guests have read-only access to specified team resources

### Team Functions

The implementation includes:

1. **Team Management**

   - Creation, reading, updating, and deleting teams
   - Adding and removing team members
   - Managing team-project associations

2. **Team Member Operations**

   - Assigning roles and positions to team members
   - Updating member information
   - Removing members from teams

3. **Project Access Control**
   - Team-based project access through project_teams junction table
   - Permission propagation from teams to projects
   - Project access restricted to members of assigned teams

### Project-Team Relationship

The relationship between projects and teams is implemented as a many-to-many relationship:

1. **Junction Table**

   - `project_teams` table connects teams and projects
   - Contains `team_id`, `project_id`, and `tenant_id` for proper isolation
   - Maintains created_at and updated_at timestamps
   - Enforces uniqueness constraint on team_id + project_id combination

2. **Access Model**

   - Users belong to teams through the team_members table
   - Teams are assigned to projects through the project_teams table
   - Users can access a project if they belong to at least one team assigned to that project
   - This creates a hierarchical access control system: Organization → Team → Project

3. **Assignment Flow**
   - Teams can be assigned to projects during project creation
   - Teams can be added or removed from projects in project settings
   - Multiple teams can be assigned to the same project
   - A single team can be assigned to multiple projects

### Database Relationships

Database relationships are implemented with proper foreign keys:

```sql
-- Team Members foreign keys
CONSTRAINT team_members_team_id_fkey FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE
CONSTRAINT team_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
CONSTRAINT team_members_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE

-- Team Projects foreign keys
CONSTRAINT project_teams_team_id_fkey FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE
CONSTRAINT project_teams_project_id_fkey FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
CONSTRAINT project_teams_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES organizations(id) ON DELETE CASCADE
```

These relationships ensure data integrity and proper cascading operations for team-related data.

## UI Component Patterns

### Dialog Components

Dialog components follow these patterns for consistency and usability:

```typescript
// Example of a well-structured dialog component
export function CustomDialog({ onOpenChange, onConfirm, isLoading, data = [] }: DialogProps) {
  // Form setup with zod schema
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // ... default values
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    await onConfirm(data);
    form.reset();
  };

  // Handle dialog close
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    onOpenChange(open);
  };

  return (
    <DialogContent className="sm:max-w-[425px]" onCloseAutoFocus={() => form.reset()}>
      {/* Icon Header */}
      <div className="flex flex-col items-center gap-4">
        <div className="flex size-12 shrink-0 items-center justify-center rounded-full border bg-background">
          <Icon className="size-6 text-foreground/60" />
        </div>
        <DialogHeader>
          <DialogTitle className="text-center">Title</DialogTitle>
          <DialogDescription className="text-center">Description text</DialogDescription>
        </DialogHeader>
      </div>

      {/* Form Content */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Form fields */}
          <DialogFooter>
            <Button variant="outline" onClick={() => handleOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Loading..." : "Confirm"}
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </DialogContent>
  );
}
```

Key patterns:

1. Consistent maximum width (sm:max-w-[425px])
2. Centered icon header with consistent styling
3. Form reset on dialog close
4. Loading state handling
5. Clear visual hierarchy
6. Proper form validation with zod
7. Consistent button placement and styling

### Team Management Components

Team management follows these patterns:

1. **Team Selection**

```typescript
<Select disabled={isLoading} onValueChange={onChange} defaultValue={value}>
  <SelectTrigger className="w-full">
    <SelectValue placeholder="Select a team">
      {value && teams.find((team) => team.id === value)?.name}
    </SelectValue>
  </SelectTrigger>
  <SelectContent>
    {teams.map((team) => (
      <SelectItem key={team.id} value={team.id}>
        <div className="flex flex-col gap-1">
          <div>{team.name}</div>
          {team.description && (
            <div className="text-xs text-muted-foreground">{team.description}</div>
          )}
        </div>
      </SelectItem>
    ))}
  </SelectContent>
</Select>
```

2. **Team Assignment Flow**

- Select team from dropdown
- Confirm assignment
- Handle success/error states
- Update UI optimistically
- Revert on error

## UI Patterns

### Loading State Pattern

The application implements a consistent loading state pattern using Skeleton components from Shadcn UI. This pattern is used across different pages to provide a seamless user experience during data loading.

```tsx
// Example loading.tsx component for ideas page
import { Skeleton } from "@/components/ui/skeleton";

export default function IdeasLoading() {
  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-[200px]" />
          <Skeleton className="h-9 w-[120px]" />
        </div>
      </div>

      {/* Table */}
      <div className="rounded-lg border">
        {/* Table Header */}
        <div className="flex items-center gap-4 border-b p-4">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
          {/* Additional header skeletons */}
        </div>

        {/* Table Rows */}
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="flex items-center gap-4 border-b p-4">
            <Skeleton className="h-4 w-4" />
            <div className="flex flex-col gap-2 flex-1">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            {/* Additional row content skeletons */}
          </div>
        ))}
      </div>
    </div>
  );
}
```

Key aspects of this pattern:

1. **File Structure**:

   - Each page has a corresponding `loading.tsx` file in the same directory
   - Loading components are automatically used by Next.js during page transitions

2. **Component Structure**:

   - Loading components mirror the structure of the actual page
   - Skeleton components match the dimensions of actual content
   - Layout uses the same grid/flex structure as the real component

3. **Visual Consistency**:

   - Consistent spacing and sizing across different loading states
   - Appropriate use of borders and rounded corners
   - Proper hierarchy that matches the actual content

4. **Implementation Strategy**:
   - Use Array.from with map for repeating elements
   - Match the grid/flex structure of the actual component
   - Implement responsive design in loading states
   - Use appropriate skeleton sizes to match text and UI elements

This pattern enhances user experience by providing visual feedback during data loading and reducing perceived loading time.

### Avatar Status Indicators

- Avatars with status indicators follow these specifications:
  - Avatar size: `h-7 w-7` for member cards
  - Avatar text: `text-xs` for member initials
  - Status indicator position: `absolute -bottom-0.5 -right-1`
  - Status indicator size: `h-4 w-4`
  - Status colors:
    - Accepted: `bg-green-500`
    - Declined: `bg-red-500`
    - Pending: `bg-gray-500`
  - Status icons:
    - Accepted: "✓"
    - Declined: "✕"
    - Pending: "⃝"
  - Border: `border-2 border-white`

### Member Background Colors

- Member avatars use a consistent grayscale background pattern:
  ```typescript
  const bgColors: Record<string, string> = {
    "1": "bg-gray-300",
    "2": "bg-gray-400",
    "3": "bg-gray-500",
    "4": "bg-gray-600",
    "5": "bg-gray-700",
    "6": "bg-gray-800",
  };
  ```

## Daily Standup UI Patterns

### Member Card Structure

```tsx
<div className="rounded-lg p-4 pl-0 border-b pb-12">
  {/* Header with avatar and expand/collapse */}
  <div className="flex items-center justify-between">
    <div className="flex items-center gap-3">
      <Avatar />
      <h3>Member Name</h3>
    </div>
    <ExpandButton />
  </div>

  {/* Content Grid */}
  <div className="grid grid-cols-12 md:grid-cols-12 gap-2">
    {/* Progress Column */}
    <div className="col-span-12 md:col-span-7">
      <YesterdayTask />
      <TodayTask />
    </div>

    {/* Impediments Column */}
    <div className="hidden md:block md:col-span-3">
      <ImpedimentsCard />
    </div>

    {/* Status Column */}
    <div className="hidden md:block md:col-span-1">
      <StatusBadge />
    </div>

    {/* Actions Column */}
    <div className="hidden md:block md:col-span-1">
      <ActionButtons />
    </div>
  </div>
</div>
```

### Task Card Pattern

```tsx
<div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md">
  <div className="flex items-start gap-3">
    <TaskIcon />
    <div className="flex-1">
      <TaskTitle />
      <TaskDescription />
    </div>
  </div>
</div>
```

### Status Badge Pattern

```tsx
<Badge
  variant="outline"
  className={cn("flex items-center gap-1.5 px-2 py-1 font-medium", statusColors[status])}
>
  {statusIcons[status]}
  <span>{statusText}</span>
</Badge>
```

### Mobile Adaptations

- Full width layout on mobile devices
- Stack columns vertically
- Show impediments below progress
- Combine status and actions into mobile-optimized footer
- Use appropriate touch targets for mobile interaction

### Responsive Breakpoints

- Default (mobile): Single column layout
- md (768px): Multi-column grid layout
- Show/hide elements based on screen size
- Maintain content hierarchy across breakpoints

### Dark Mode Support

- Use semantic color tokens
- Implement proper contrast ratios
- Support system preference
- Maintain readability in both modes

### Data Fetching and Aggregation

- **Current Pattern (Post-Refactor)**: Server Actions (`src/db/actions/*.action.ts`) now leverage more advanced Supabase relational queries (e.g., nested selects, direct joins in views or functions like `getClustersAndProjectsWithUserTeamsFromDb`) and helper functions (e.g., `_mapTeamsDataToTeamUI` in `team.actions.ts`) to consolidate data fetching. This has significantly reduced N+1 query patterns and shifted more data aggregation logic towards the database or to more centralized helper functions within the actions layer.
  - Multiple database calls have been reduced to fewer, often single, more comprehensive queries.
  - Client-side data stitching in actions has been minimized.
- **Previous Pattern (Pre-Refactor)**: Server Actions often performed multiple sequential database calls using basic helper functions in `src/db/*.db.ts`. Data aggregation and transformation primarily occurred within these action files on the application server through JavaScript manipulation.

## Clusters Management System

The Clusters Management System provides a way to organize projects into logical groups called clusters. This feature follows a pattern of component and business logic separation:

### Architecture Pattern

```mermaid
graph TD
    subgraph "UI Layer"
        OCP[Organization Clusters Page]
        OCC[OrganizationClustersContent]
        CL[ClustersList]
        subgraph "UI Components"
            CH[ClusterHeaderRow]
            PR[ProjectRow]
            AD[AddProjectDialog]
            DD[DeleteClusterDialog]
            RD[RemoveProjectDialog]
        end
    end

    subgraph "State Management"
        CS[Client State]
        SF[Search Filtering]
        EVH[Event Handlers]
    end

    subgraph "Server Actions"
        GC[getClusterProjects]
        DC[deleteClusterAction]
        AP[assignProjectToClusterAction]
        RP[removeProjectFromClusterAction]
    end

    subgraph "Database Layer"
        DB[Supabase/PostgreSQL]
    end

    OCP --> OCC
    OCC --> GC
    OCC --> CL
    CL --> CS
    CL --> SF
    CL --> CH
    CL --> PR
    CL --> AD
    CL --> DD
    CL --> RD

    CH --> EVH
    PR --> EVH
    AD --> EVH
    DD --> EVH
    RD --> EVH

    EVH --> DC
    EVH --> AP
    EVH --> RP

    GC --> DB
    DC --> DB
    AP --> DB
    RP --> DB
```

### Component Structure

1. **Server Components**

   - `OrganizationClustersContent`: Fetches data using server actions
   - Passes data to client components for rendering
   - Handles server-side error states

2. **Client Components**

   - `ClustersList`: Main client component that manages state
   - Handles user interactions like search, expand/collapse
   - Updates local state based on server action results

3. **UI Components**
   - Smaller specialized components for different UI elements
   - `ClusterHeaderRow`: Displays cluster information and actions
   - `ClusterProjectRow`: Shows project information within a cluster
   - Dialog components for various actions (add, remove, delete)

### Data Flow

1. **Data Fetching**

   - Server component fetches clusters and projects
   - Data is formatted and passed to client components
   - Client components store this data in local state

2. **User Interactions**

   - User actions (expand, search, add, remove, delete) trigger state changes
   - Operations that modify data call server actions
   - Local state is updated optimistically before server confirmation
   - Errors are handled with proper feedback using toast notifications

3. **State Management**
   - Each cluster tracks its expanded/collapsed state
   - Search filtering happens client-side for responsiveness
   - Loading states are tracked for individual operations

### Separation of Responsibilities

1. **Server Actions (`cluster.action.ts`)**

   - `getClusterProjects`: Fetches clusters with their projects
   - `deleteClusterAction`: Deletes a cluster if it has no projects
   - `assignProjectToClusterAction`: Adds a project to a cluster
   - `removeProjectFromClusterAction`: Moves a project to "Uncategorized" cluster

2. **Client State Management**

   - `ClustersList` handles filtering, visibility, and CRUD operations
   - Uses React's `useState` for managing state
   - Implements proper error handling and loading states

3. **UI Components**
   - Each component handles a specific part of the UI
   - Components receive props for data and callbacks for actions
   - Clear division between display logic and business logic

### Access Control

- Admin-only access for cluster management operations
- Display-only for regular users
- Admin status check happens at the page level
- UI adapts based on admin status (hiding action buttons)
