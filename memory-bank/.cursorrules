# Renwu Project Intelligence

## Project Patterns

### Build Quality Standards

- Zero tolerance for build warnings - even NODE_NO_WARNINGS environment variable is forbidden
- All warnings must be fixed at the source, not suppressed
- Custom implementations are preferred over libraries that generate warnings
- Example: Custom search implementation replacing Orama to avoid synchronous insert warnings
- Maintain clean build output for consistent deployment quality
- Prioritize code quality over convenience

### Component Structure

- Default to React Server Components whenever possible
- Use 'use client' only when component requires interactivity
- Follow naming convention: PascalCase for components
- Use TypeScript props with explicit typing
- Place components in appropriate directories based on their purpose
- Organize feature-specific components in domain folders

### Styling Approach

- Use Tailwind CSS for all styling
- Follow mobile-first responsive design
- Use shadcn/ui components with customization when needed
- Avoid inline styling
- Use Tailwind's @apply for complex repeated patterns
- Implement responsive design with Tailwind breakpoints

### State Management

- Use React hooks for local component state
- Use Context API for global state that doesn't need persistence
- Use Supabase for persistent state
- Prefer Server Components for data fetching
- Implement Server Actions for data mutations

### Data Fetching

- Use Server Components for data fetching
- Handle loading states with Suspense and skeleton loaders
- Implement error boundaries for graceful error handling
- Use typed data structures for all API responses
- Implement parallel data loading with Promise.all

### Authentication

- Use Supabase Auth for authentication
- Implement proper auth state management
- Handle protected routes consistently
- Use Row Level Security for data access control
- Manage cross-domain session persistence
- Redirect auth routes on subdomains to main domain

### Form Handling

- Use react-hook-form for form state and validation
- Use zod for schema validation
- Implement consistent error messaging
- Create reusable form components for common patterns
- Add client-side validation for immediate feedback

### Toast Notifications

- Use Sonner for toast notifications
- Provide clear feedback for user actions
- Use appropriate toast types (success, error, info)
- Keep toast messages concise and actionable
- Position toasts consistently at bottom-right

### Performance Optimization

- Minimize client-side JavaScript
- Implement proper code splitting
- Use appropriate caching strategies
- Optimize images and assets
- Use Suspense for progressive loading
- Implement parallel data fetching
- Use Turbopack for faster development builds

### Project Directory Structure

- Follow Next.js app router conventions
- Group related components and utilities
- Maintain clear separation of concerns
- Use barrel exports for clean imports
- Organize feature-specific components in domain folders
- Keep demo routes separate in /app/demo/\*

### Demo Implementation

- Clearly mark all demo content with visual indicators
- Use demo account badges for mock data
- Keep demo data separate from real user data
- Provide comprehensive example data
- Create parallel routes for demo functionality
- Allow seamless exploration without authentication

### Cluster-Project Structure

- Implement hierarchical data organization
- Use slug-based routing for clusters and projects
- Implement multi-step creation wizard
- Group projects by cluster
- Use transaction-based creation for data integrity
- Generate SEO-friendly slugs
- Ensure slug uniqueness

## User Preferences

- Clean, minimalist interface
- Fast, responsive interactions
- Keyboard shortcuts for power users
- Consistent design language
- Clear visual hierarchy for organization

## Known Challenges

- Managing real-time updates efficiently
- Balancing feature richness with simplicity
- Ensuring optimal performance on all devices
- Handling complex recurring task patterns
- Managing cross-domain navigation
- Maintaining clear separation between demo and real data

## Evolution Tracking

- Start with core task management
- Add projects and organization
- Implement collaboration features
- Enhance with additional productivity tools
- Create comprehensive demo experience
- Add analytics and reporting

## Tool Usage

- VS Code as primary IDE
- GitHub for version control
- Vercel for deployment
- Supabase for backend services
- Turbopack for faster builds

## avatar-status-indicators

description: ENFORCE consistent avatar status indicators across the application
globs: \*_/_.tsx

---

## Context

- Avatar status indicators are used to show user participation status
- Must maintain consistent styling and behavior across all components
- Used in member cards, participant lists, and user profiles

## Rules

1. Avatar Sizing

   - Default size: `h-7 w-7`
   - Text size: `text-xs` for initials
   - Status indicator: `h-4 w-4`

2. Status Indicator Positioning

   - Position: `absolute -bottom-0.5 -right-1`
   - Border: `border-2 border-white`
   - Shape: `rounded-full`

3. Status Colors

   ```typescript
   const statusColors = {
     accepted: "bg-green-500",
     declined: "bg-red-500",
     pending: "bg-gray-500",
   };
   ```

4. Status Icons

   ```typescript
   const statusIcons = {
     accepted: "✓",
     declined: "✕",
     pending: "⃝",
   };
   ```

5. Background Colors
   ```typescript
   const bgColors: Record<string, string> = {
     "1": "bg-gray-300",
     "2": "bg-gray-400",
     "3": "bg-gray-500",
     "4": "bg-gray-600",
     "5": "bg-gray-700",
     "6": "bg-gray-800",
   };
   ```

## Example

```tsx
<div className="relative">
  <Avatar className="h-7 w-7">
    <AvatarFallback className={`text-xs text-white ${bgColors[id]}`}>{initials}</AvatarFallback>
  </Avatar>
  <div
    className={`absolute -bottom-0.5 -right-1 h-4 w-4 rounded-full 
      ${statusColors[status]} border-2 border-white flex items-center justify-center`}
  >
    <span className="text-white text-[8px]">{statusIcons[status]}</span>
  </div>
</div>
```
