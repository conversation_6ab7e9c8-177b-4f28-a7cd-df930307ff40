# Technical Context: Renwu

## Technology Stack

### Frontend

- **Framework**: Next.js 15.3.1 (React 19.1.0)
- **Language**: TypeScript 5.8.2
- **Styling**: Tailwind CSS 4.1.2
- **Component Library**: Shadcn/UI
- **State Management**: React hooks, Context API
- **Form Handling**: React Hook Form 7.55.0 with Zod 3.24.2 validation
- **Documentation**: Fumadocs for MDX-based technical documentation
  - Custom search implementation (replaced Orama due to build warnings)
- **UI Libraries**:
  - Radix UI components
  - Sonner 2.0.1 for toast notifications
  - Framer Motion 12.5.0 for animations
  - @remixicon/react 4.6.0 for icons
  - Lucide React 0.483.0 for icons

### Backend

- **Framework**: Next.js API routes / Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Serverless Functions**: Supabase Edge Functions
- **Realtime**: Supabase Realtime

### DevOps

- **Hosting**: Vercel
- **CI/CD**: GitHub Actions
- **Version Control**: Git/GitHub
- **Package Manager**: pnpm
- **Environment Management**: dotenv

## Development Setup

### Prerequisites

- Node.js 18+
- pnpm 7+
- Git
- Supabase account
- Vercel account (for production deployment)

### Local Environment Configuration

1. Clone repository
2. Run `pnpm install` to install dependencies
3. Configure Supabase credentials in `.env.local`
4. Run `pnpm dev --turbopack` to start development server with Turbopack
5. Access application at `http://localhost:3000`

### Environment Variables

Required environment variables:

- `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key (for admin operations)
- `NEXT_PUBLIC_SITE_URL`: Site URL (for authentication callbacks)
- `NEXT_PUBLIC_PROD_DOMAIN`: Root domain for production (e.g., "renwu.app")
- `NEXT_PUBLIC_DEV_DOMAIN`: Local domain for development (e.g., "localhost:3000")

## Technical Constraints

### Browser Support

- Modern evergreen browsers (Chrome, Firefox, Safari, Edge)
- No IE11 support
- Mobile browsers (iOS Safari, Chrome for Android)

### API Limits

- Supabase free tier limitations:
  - Database: 500MB storage
  - Auth: 50K MAU
  - Edge Functions: 500K invocations/month
  - Limited connections for realtime subscriptions

### Performance Targets

- First Contentful Paint: < 1s
- Time to Interactive: < 2s
- Lighthouse Performance Score: > 90
- Core Web Vitals passing threshold

## Dependencies

### Core Dependencies

- `next`: 15.3.1 - React framework with Turbopack support
- `react`, `react-dom`: 19.1.0 - UI library
- `typescript`: 5.8.2 - Static typing
- `tailwindcss`: 4.1.2 - Utility-first CSS
- `@supabase/supabase-js`: 2.49.4 - Supabase client
- `@supabase/ssr`: 0.6.1 - Server-side rendering helpers
- `zod`: 3.24.2 - Schema validation
- `react-hook-form`: 7.55.0 - Form handling
- `date-fns`: 4.1.0 - Date utilities
- `fumadocs-ui`: - Documentation UI framework
- `fumadocs-mdx`: - MDX processing for documentation
- `class-variance-authority`, `clsx`, `tailwind-merge` - Tailwind utilities
- `lucide-react`: 0.483.0 - Icon library
- `@remixicon/react`: 4.6.0 - Icon library
- `sonner`: 2.0.1 - Toast notifications
- `framer-motion`, `motion`: 12.5.0 - Animation library
- `@dnd-kit`: Drag-and-drop functionality
- `@tanstack/react-table`: Data table components

### Dev Dependencies

- `eslint`: 9.x - Code linting
- `eslint-config-next`: 15.2.3 - Next.js ESLint config
- `postcss`: 8.5.3 - CSS processing
- `tailwindcss`: 4.1.2 - CSS framework

## Coding Standards

### TypeScript

- Strict type checking enabled
- Interfaces/types for all data structures
- Function parameter and return types defined
- No `any` type except where absolutely necessary
- Zero tolerance for build warnings (even NODE_NO_WARNINGS is forbidden)

### Components

- Server Components by default
- Client Components only when needed (interactivity)
- Shared UI components in `/components/ui`
- Feature-specific components organized by domain
- Server actions for data mutations

### Styling

- Tailwind CSS for styling
- Component variants with class-variance-authority
- Mobile-first responsive design
- Theme defined in `tailwind.config.ts`

### Project Structure

- Next.js App Router (`/app` directory)
- Demo routes in `/app/demo`
- Authentication routes in `/app/auth`
- Dynamic routes with slugs for projects and clusters
- API routes for server-side operations
- Middleware for auth and subdomain handling
- Documentation in `/docs` directory with MDX files

### Data Handling

- Server Components for data fetching
- Server Actions for data mutations
- Suspense for loading states
- Parallel data loading for performance
- Type-safe data structures

### Security Considerations

- Authentication via Supabase Auth
- Multi-tenancy data isolation:
  - Explicit tenant_id fields on all tables (referencing organization.id)
  - Automatic tenant_id propagation via PostgreSQL triggers
  - Row Level Security (RLS) policies with dual-layer security checks
- Input validation with Zod
- Content Security Policy
- XSS protection

## Deployment Workflow

1. Code pushed to GitHub
2. GitHub Actions runs tests and linting
3. If tests pass, preview deployment created
4. Pull request reviewed and merged to main
5. Production deployment triggered from main branch
6. Vercel builds and deploys application

## Performance Optimizations

- Turbopack for faster development builds
- React Server Components for reduced client JavaScript
- Suspense for progressive loading
- Image optimization with Next.js Image component
- Parallel data fetching
- Optimized subdomain routing
- Selective client hydration
- Hardware-accelerated animations:
  - transform: translateZ(0) for GPU rendering
  - transform-gpu class for Tailwind GPU acceleration
  - will-change properties for browser optimization hints
  - backfaceVisibility: "hidden" for additional hardware acceleration
  - perspective: 1000 for improved 3D transforms
  - contain: "paint layout" to optimize repainting
  - Optimized spring physics with proper stiffness/damping values
  - Simplified DOM structure to reduce rendering overhead

## Custom Implementations

### Documentation Search

- Custom search implementation in `/app/api/search/route.ts` replaces Fumadocs' default Orama search
- Simple string matching against page titles and descriptions
- Avoids Orama's synchronous insert operations that caused build warnings
- Maintains the same API interface for client-side search components
- Prioritizes clean builds over advanced search features (fuzzy matching, ranking)
- Implemented to comply with strict build quality requirements that forbid any warnings
