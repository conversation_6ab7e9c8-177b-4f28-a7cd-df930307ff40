# Authentication & Subdomain Implementation

## Overview

This document outlines the implementation details of our authentication system and subdomain management for the multi-tenant architecture. The system allows users to authenticate on the main domain and access organization-specific content on subdomains while maintaining session persistence.

## Domain Structure

- **Main Domain**: `renwu.app` (production) or `localhost:3000` (development)
- **Subdomains**: `{organization-id}.renwu.app` or `{organization-id}.localhost:3000`

## Key Components

### 1. Domain Utilities

Two utility functions handle cross-domain navigation:

```typescript
// src/lib/utils.ts
export function goToMainDomain(path: string) {
  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${domain}/${cleanPath}`;
}

export function goToSubdomain(subdomain: string, path: string) {
  if (!subdomain) {
    return goToMainDomain(path);
  }

  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${subdomain}.${domain}/${cleanPath}`;
}
```

### 2. Middleware Implementation

The middleware (middleware.ts) handles all authentication and routing logic:

- Detects subdomain vs. main domain requests
- Handles auth route redirects from subdomains to main domain
- Verifies user authentication
- Checks organization membership for subdomain access
- Applies smart redirects based on user state and location

```typescript
// Key middleware functions
export async function middleware(request: NextRequest) {
  // Initialize Supabase
  const { supabase, supabaseResponse } = initializeSupabase(request);

  // Get authenticated user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Get domain information
  const hostname = request.headers.get("host") || "";
  const subdomain = getCurrentSubdomain(hostname);
  const isSubdomainRequest = subdomain !== null;
  const pathname = request.nextUrl.pathname;

  // Handle subdomain auth routes (redirect to main domain)
  if (isSubdomainRequest && pathname?.startsWith("/auth")) {
    return NextResponse.redirect(new URL(goToMainDomain(pathname)));
  }

  // Handle user based on authentication state
  if (!user) {
    // Logic for unauthenticated users
    // Redirect to login for protected routes
  } else {
    // Handle authenticated users based on domain
    if (isSubdomainRequest) {
      // Check organization membership for subdomain access
    } else {
      // Handle main domain logic for authenticated users
    }
  }
}
```

### 3. Cross-Domain Session Management

Sessions persist across domains through proper cookie configuration:

```typescript
// Cookie configuration to share across subdomains
cookieOptions: {
  // For localhost, don't use domain setting at all in development
  ...(isDev ? {} : { domain: `.${currentDomain}` }),
  maxAge: 60 * 60 * 24 * 30, // 30 days
  path: "/",
  sameSite: "lax",
  secure: !isDev,
}
```

### 4. Organization Selection Interface

The `/auth/organizations` page displays all organizations the user belongs to:

- Shows organization details (name, ID, subdomain, role)
- Provides "Access" button to navigate to organization subdomain
- Auto-redirects if user belongs to only one organization

### 5. Link Component Optimization

To handle cross-domain navigation properly:

```jsx
// For links to main domain
<Link href={goToMainDomain("auth/organizations")} >
  Switch Organization
</Link>

// For links to subdomain
<Link href={goToSubdomain(org.subdomain_id, "home")} >
  Access
</Link>
```

Disabling prefetch prevents CORS errors when hovering over cross-domain links.

## Authentication Rules

### Main Domain Routes

1. `/auth/login`

   - Entry point for unauthenticated users
   - Redirects to `/auth/organizations` if already authenticated

2. `/auth/organizations`
   - Organization selection interface
   - Automatic redirect for single organization members
   - Central hub for organization switching

### Subdomain Routes

1. `{organization}.renwu.app/*`

   - Only accessible to authenticated users who are members of the organization
   - Redirects unauthorized users appropriately

2. No auth routes on subdomains
   - All auth-related URLs redirect to main domain

## User Flows

### Unauthenticated User Flow

1. Visit any URL
2. Redirect to `/auth/login` on main domain
3. Login with credentials
4. Redirect to `/auth/organizations`
5. Organization selection or auto-redirect to subdomain

### Authenticated User Flow

1. On main domain:
   - Auth routes show organization selection
   - Other routes function normally
2. On subdomain:
   - Verify organization membership
   - Allow access if member, redirect if not

## Implementation Challenges and Solutions

### Challenge 1: Cross-Domain Cookie Sharing

**Solution**: Use domain setting with leading dot (`.renwu.app`) in production, no domain setting in development.

### Challenge 2: Link Prefetching Issues

**Solution**: Disable prefetching for cross-domain links to prevent CORS errors.

### Challenge 3: Path Normalization

**Solution**: Ensure consistent URL construction by normalizing paths (removing leading slashes).

### Challenge 4: Middleware Bypass in Development

**Solution**: Develop with middleware enabled to catch issues early, using specific auth bypass for development needs.

### Challenge 5: Organizations Data Access

**Solution**: Fetch organizations data once in middleware and pass through context to avoid redundant database calls.

## Best Practices

1. Always use domain utility functions for cross-domain navigation
2. Disable prefetching for cross-domain links
3. Keep all auth routes on main domain only
4. Verify organization membership for all subdomain access
5. Use meaningful error messages and redirects for different auth scenarios
6. Handle edge cases like no organizations, single organization, etc.
