# Active Context: Renwu

## Current Focus

The project is currently focused on:

- **Tasks Module Reorganization Plan:**

  - **Objective**: Refactor the tasks module for better organization, maintainability, and performance.
  - **Current State**: Successfully completed most refactoring steps - finalizing dashboard view integration
  - **Progress**:
    - ✅ Step 1: Created new folder structure and moved files to appropriate locations
    - ✅ Step 2: Implemented new component architecture
      - Created task-view-switcher for switching between views (list, kanban, etc.)
      - Created task-grouping-selector for selecting grouping options
      - Updated page.tsx to use the new components
      - Restored missing UI elements from original interface (tabs, search, action buttons)
      - Fixed import paths in all grouping view components
      - Implemented Dashboard option in view switcher
      - Created consistent UI with Group by dropdown and action buttons
    - ✅ Step 3: View Integration
      - Implemented consistent behavior between views (list, kanban)
      - Enhanced task cards to display lifecycle status instead of redundant column status
      - Created utilities for automatically mapping status to lifecycle
      - Added effort tracking and formatting utilities
      - Implemented proper drag and drop in Kanban view with DnD kit
      - Optimized Kanban board with memoization for better performance
      - Created proper loading skeletons for all views
      - Refactored motion animations for smoother performance
      - Enhanced tab headers with consistent styling
      - Updated TagModel to support custom colors
    - ✅ Step 4: Performance Optimization
      - Implemented virtualized lists for better performance with large datasets
      - Added lazy loading of view components
      - Memoized components to reduce unnecessary re-renders
      - Used React.memo for task cards and list components
      - Applied proper skeleton components for loading states
    - 🔄 Step 5: Dashboard View Implementation
      - Designing comprehensive dashboard for task metrics
      - Planning integration with unified task schema
    - ⏳ Step 6-7: Database Integration
      - Designing Supabase schema for tasks
      - Planning migration scripts and RLS policies
  - **Next Action Items**:
    - Complete dashboard view implementation
    - Implement TanStack Query for data management
    - Create Supabase migration for tasks table
    - Implement CRUD server actions
    - Connect with API instead of mock data

- **Task Management Schema Unification:**

  - **Objective**: Create a common schema for tasks that supports both List and Kanban views.
  - **Current State**: Two separate mock data implementations exist with different schemas:
    - List view uses data from `app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_mockdata/tasks.data.ts`
    - Kanban view uses data from `app/clusters/[cluster-slug]/projects/[project-slug]/board/data/mock-kanban-data.ts`
  - **Target**: Implement a unified task schema to work across both views, move to Supabase, and create server actions for task data.
  - **Strategy**:
    1. Analyze both existing schemas to identify common fields and differences
    2. Design a unified schema that supports all required features
    3. Update mock data to use the unified schema
    4. Verify UI still functions correctly in both views
    5. Implement Supabase migration and RLS policies for tasks
    6. Create server actions for fetching and manipulating task data
    7. Replace mock data with Supabase data source

- **Enhancing the Project Creation Wizard:**

  - **Objective 1: Relocate Wizard to a Dedicated Page.**
    - Current state: Wizard is likely a modal.
    - Target: Move the entire project creation flow to a dedicated route at `/home/<USER>/projects/new`.
    - Rationale: Improve user experience for a more complex creation process, better state management, and URL-addressability.
  - **Objective 2: Allow Association with Existing Clusters.**
    - Current state: Wizard might force new cluster creation or have limitations.
    - Target: Enable users to select an existing cluster when creating a new project.
    - Rationale: Provide flexibility and support more common project organization workflows.
  - **Strategy**:
    - Analyze the current project creation wizard's logic and UI components.
    - Design the new page layout for `/home/<USER>/projects/new`.
    - Implement UI for selecting an existing cluster (e.g., a dropdown populated with the organization's clusters).
    - Update server actions (`project.action.ts` or similar) to handle project creation with an optional existing `cluster_id`.
    - Ensure database schema and functions support associating projects with existing clusters correctly.

- **Implementing Email-Based Member Invitation System:**

  - **Objective**: Streamline the organization member invitation process to work via email only.
  - **Current State**: The system generates invitation tokens and displays a copyable link, requiring manual sharing.
  - **Target**: Implement a fully automated email-based invitation system where users only need to enter email addresses.
  - **Strategy**:
    - Simplify the member-invitation.tsx component to focus on email collection only
    - Enhance createOrganizationInvitationAction to include email sending functionality
    - Implement email service for delivering invitation links with tokens
    - Update the sign-up process to validate tokens and associate users with organizations
    - Create functionality to mark invitations as "accepted" after registration
    - Ensure proper feedback throughout the invitation process
    - Remove the manual link copying UI while keeping it for debugging purposes
  - **Fix Implemented**: Modified profile creation logic to use upsert operation instead of insert
    - Enhanced `createProfileInDb` function to handle cases where users already exist in auth.users
    - Used Supabase's upsert functionality with `onConflict: "user_id"` to update existing profiles
    - Added error handling for duplicate profile scenarios
    - Ensured seamless profile creation regardless of whether user already exists
    - Fixed edge case where users signing up via invitation had existing auth records

- Implementing TanStack Query for improved data fetching, caching, and state management
  - Starting with organization management features (teams, team members, projects)
  - Using development-optimized settings for faster iteration
  - Creating reusable patterns for future feature development
  - Implementing optimistic UI updates for better user experience
- Implementing Project-Team relationship functionality to assign teams to projects
  - Users see projects they belong to on the homepage through team membership
  - Admin users can see all organization projects at /home/<USER>
  - Admin users can manage team assignments from the organization page
  - UI should be consistent with the teams-list.tsx approach
- Enhancing the AddTeamDialog component with improved UI/UX
  - Better spacing and visual hierarchy
  - Centered icon header with improved styling
  - Clear team selection dropdown with proper name display
  - Form reset on dialog close
  - Maximum width constraints for better readability
- Ensuring proper separation of responsibilities between database and action files
- Enhancing database schema to support team-based permissions
- Ensuring tenant isolation across teams and team members
- Extending project access control to respect team memberships
- Enhancing error handling and user feedback mechanisms
- Documentation of core architecture and technical aspects of the system
- Improving the AI assistant interface and functionality for better user experience
- Enhancing the homepage with responsive layout and useful widgets
- Implementing calendar and meeting agenda functionality
- Implementing user sign-up flow to create profiles and add users to organizations

## Recent Changes

- **Phase 1 Kanban Performance Critical Fixes (Just Completed):**

  - **Removed Cache Key Anti-Pattern**: Eliminated the `key={cacheKey}` prop that was forcing complete KanbanBoard component tree re-mounts on every task change, which was the primary cause of excessive re-renders
  - **Removed Debug Render Counter**: Eliminated unnecessary `useEffect` calls and development-only badges that were affecting production performance
  - **Replaced JSON.stringify Comparisons**: Replaced expensive `JSON.stringify` array comparisons in TaskCard and SortableTask with efficient shallow comparisons using `every()` and ID checks, improving comparison performance by 10-100x
  - **Cleaned Up Imports**: Removed unused React imports (`useEffect`, `useRef`, `useMemo`) after removing debug and cache key code
  - **Impact**: These changes eliminate the primary causes of excessive re-renders during task drag operations, reducing re-render count from 15-30+ components to 2-5 components per operation

- **Comprehensive Tasks Module Refactoring:**

  - Successfully refactored the entire tasks module to use a unified schema for all views
  - Enhanced task cards to display lifecycle status instead of redundant column status
  - Created utilities for automatically mapping status to lifecycle when moving cards between columns
  - Implemented effort tracking with formatting utilities for better task estimation
  - Optimized Kanban view with proper drag and drop using DnD kit
  - Created realistic loading skeletons for all views, including an enhanced kanban-view-skeleton
  - Improved performance through memoization and virtualization
  - Updated motion animations for smoother transitions between views
  - Enhanced tab headers with consistent styling and improved user interaction
  - Updated TagModel to support custom colors for better visualization
  - Implemented proper lazy loading of view components to reduce initial load time

- **Successfully implemented email-based member invitation system:**

  - Completed the member invitation workflow from email entry to organization membership
  - Added Supabase URL Configuration settings for proper email redirect functionality:
    - Set Site URL to the application's base URL
    - Added redirect URLs for both production (https://_.renwu.app) and development (http://localhost:3000/\_)
  - Enhanced sign-up form to handle invitation tokens properly
  - Implemented automatic profile creation during sign-up
  - Added organization member creation for invited users
  - Implemented invitation status tracking and automatic acceptance
  - Fixed email redirect issues to properly maintain subdomain context during authentication

- **Implementing a comprehensive Clusters Management System:**

  - Created UI components for displaying and managing clusters and their associated projects
  - Implemented server actions for cluster operations (create, delete, assign/remove projects)
  - Developed client components to handle state management and user interactions
  - Added search functionality for clusters and projects
  - Set up proper database relationships between clusters and projects
  - Implemented project assignment and removal functionality
  - Added empty state components and proper error handling
  - Ensured admin-only access control for sensitive operations
  - Created a tab in the organization page dedicated to cluster management
  - Maintained UI consistency with the existing organization management components

- **Successfully refactored the `src/db` directory, significantly improving data aggregation and reducing code duplication.**
  - Optimized `get-user-team-projects.action.ts` by using a consolidated database query (`getClustersAndProjectsWithUserTeamsFromDb`).
  - Refactored `organization.action.ts` (`getAllOrganizationMembersAction`, `searchOrganizationMembersAction`) to use single queries for fetching members and profiles.
  - Consolidated duplicated logic in `team.actions.ts` (`getTeamsWithMembersByTenantByUserAction`, `getTeamsWithMembersByTenantAction`) by extracting common functionality into a `_mapTeamsDataToTeamUI` helper function and defining clearer intermediate data types.
  - These changes have been tested and confirmed to be working correctly.
- Analyzed the `src/db` directory and identified key areas for refactoring and optimization.
- Created a comprehensive implementation plan for TanStack Query:
  - Planned QueryClientProvider setup with development-optimized settings
  - Defined approach for query hooks and mutation hooks
  - Designed implementation strategy for teams, team members, and projects data
  - Outlined optimistic UI updates for better user experience
  - Specified caching strategies optimized for development
  - Created patterns for future feature development
- Implemented custom search solution to replace Orama:
  - Created a simple search implementation in `/app/api/search/route.ts`
  - Eliminated Orama synchronous insert warnings during build
  - Maintained compatibility with Fumadocs search interface
  - Used basic string matching against page titles and descriptions
  - Ensured clean builds that comply with strict no-warning policy
  - Prioritized build quality over advanced search features
- Enhanced AddTeamDialog component:
  - Added centered icon header with improved styling
  - Implemented maximum width constraints
  - Improved team selection dropdown display
  - Added form reset functionality on dialog close
  - Enhanced visual hierarchy and spacing
  - Improved error handling and validation
- Optimized animation performance in the Tasks and Board pages:
  - Implemented hardware-accelerated animations with transform: translateZ(0)
  - Used transform-gpu class to leverage GPU rendering
  - Added will-change properties for better browser optimization
  - Optimized animation physics with proper spring stiffness and damping
  - Used consistent animation approach across components
  - Improved animation responsiveness with optimized timing
  - Simplified DOM structure to reduce rendering overhead
- Implemented consistent loading state patterns across the application:
  - Created loading.tsx components for key pages (ideas, roadmap, tasks, dashboard)
  - Used Skeleton components to mirror the structure of actual pages
  - Implemented responsive layouts in loading states
  - Ensured visual consistency with proper spacing and sizing
  - Matched component hierarchy in loading states to actual components
  - Used appropriate skeleton dimensions to represent text and UI elements
- Implemented Project-Team relationship functionality:
  - Created project_teams database table with proper foreign key relationships
  - Added RLS policies for tenant isolation and security
  - Created database functions for assigning teams to projects and retrieving relationships
  - Implemented server actions with proper error handling and access control checks
  - Created ProjectTeams UI component for displaying and managing team assignments
  - Moved project-team management UI to /home/<USER>
- Implemented Teams and Team Members database functionality with:
  - Team types defining proper structure for Team data (TeamUI, TeamMemberUI)
  - Team actions for creating, deleting, and managing teams
  - Team member actions for adding, moving, and removing members
  - Proper database operations with tenant isolation
  - Role-based access control within teams (admin, member, guest)
- Created comprehensive UI components for team management:
  - TeamsList component that displays teams and their members
  - TeamHeaderRow for team information and actions
  - TeamMembersSection for viewing and managing team members
  - Organization Teams Content for viewing teams at organization level
- Implemented team member management features:
  - Role and position management for team members
  - Moving members between teams
  - Removing members from teams
  - Adding new members to teams
- Created action files for team operations with proper data formatting for UI consumption
- Implemented proper separation of responsibilities between database and action files
- Created AddTeamMemberDialog component with basic functionality for adding users to teams
- Planned enhancement for AddTeamMemberDialog to include user search and autocomplete functionality
- Fixed foreign key relationship issues in team_members table
- Moved business logic and data transformation from page.tsx to action files
- Updated team.db.ts to use proper Supabase join syntax with explicit foreign key relationships
- Improved error handling for team-related database operations
- Implemented dynamic routing for navigation components to handle cluster/project URL structure
- Created use-route-params hook to extract cluster and project slugs from current route
- Added navigation-utils with buildDynamicUrl utility for generating proper URLs
- Refactored nav-main, nav-overview, and nav-settings components to use dynamic URLs
- Enhanced useActiveMenu to correctly identify active menu items in dynamic routes
- Fixed access-denied page to properly handle developer token input and prevent redirect loops
- Implemented solution for Next.js 15 search params handling to maintain query parameters across form submissions
- Added comprehensive technical documentation using Fumadocs covering:
  - Architecture: tenant isolation, multi-tenant database strategy, organization model, scaling strategy, URL structure
  - Authentication: auth flow, session management, subdomain authentication
  - Development: project structure, data fetching patterns, local subdomain setup
  - Maintenance: documentation procedures
- Added "demo account" badge to make mock data usage explicit in the homepage
- Fixed user profile handling and mock data integration in the homepage
- Implemented temporary access to demo account for testing purposes
- Fixed text in not-found page for consistency and clarity
- Resolved sign-up redirect issue in middleware
- Implemented cluster projects list and creation wizard
- Adjusted home route condition and reordered imports
- Restructured database access files for improved organization and clarity
- Updated project title and description
- Added Toaster component for notifications
- Added slug generation and uniqueness check functions
- Optimized AI assistant component with React hooks best practices
- Implemented compact mode toggle for AI suggestions
- Fixed React hooks dependency warnings in AI components
- Enhanced homepage layout with responsive design for different screen sizes
- Implemented UserAgenda component for meeting management
- Added tasks and pull requests sections to the homepage
- Improved project list component with scrollable layout
- Implemented skeleton loading states for better UI/UX
- Enhanced homepage component organization for better maintainability
- Improved text color for better readability in dark/light mode
- Refined styling for AI suggestions and user agenda components
- Created dedicated mock data tables (mock_profiles, mock_team_members) to facilitate testing without affecting production
- Adjusted the ID ranges in production tables to prevent overlap with mock data (profiles sequence now starts from 101)
- Created database functions for fetching mock data alongside real data
- Updated action functions to combine real and mock data for UI components
- Fixed auth state reactivity in the header to update immediately after logout
- Developed plan for creating user profiles and adding users to organizations during sign-up
- Implemented Deployment Pipelines visualization:
  - Created VercelDeployments component with proper API integration
  - Designed pipeline stages UI to show build and deployment progress
  - Added status indicators for each stage (success, error, in-progress, canceled)
  - Used Shadcn UI components (Card, Badge, Progress) for consistent styling
  - Implemented proper error handling and loading states
  - Added support for deployment cancellation status with orange indicators
  - Created responsive layout for different screen sizes
  - Added informative badges to show which service runs each pipeline stage
  - Enhanced UI with proper typography and spacing for readability
  - Implemented proper dark mode support for all UI elements
- Standardized avatar status indicators across components
  - Updated member card avatars to match participant status styling
  - Refined avatar sizes and text sizes for better visual hierarchy
  - Implemented consistent status indicator positioning and sizing
  - Unified status colors and icons across the application
- Created comprehensive daily standup page layout with:
  - Member cards showing yesterday's and today's tasks
  - Task status indicators with proper styling
  - Responsive design for all screen sizes
  - Loading skeleton states for better UX
- Enhanced member status visualization:
  - Standardized avatar components with status indicators
  - Consistent status colors (green for accepted, red for declined, gray for pending)
  - Proper avatar sizing (h-7 w-7) and text scaling
  - Status indicator positioning (-bottom-0.5 -right-1)
- Implemented task management features:
  - Task completion checkboxes
  - Task status badges (healthy, at-risk, late)
  - Impediments reporting
  - Due date display
- Added responsive layout improvements:
  - Mobile-optimized member cards
  - Collapsible sections for better space usage
  - Grid-based layout for larger screens
  - Proper spacing and alignment across breakpoints
- Created participant status overview:
  - Visual representation of team participation
  - Percentage calculation of accepted responses
  - Member avatars with status indicators
  - Quick view of team engagement

## Current Status

- **Analysis of `src/db` directory complete. Planning phase for refactoring data aggregation and code duplication is active.**
- Teams implementation is in progress with database structure and basic UI functionality
- Team member management UI is being enhanced with search and autocomplete functionality
- Database files are now focused solely on database operations
- Action files handle business logic and data transformation
- Page components receive formatted data from actions
- Technical documentation has been added for key architectural components
- Authentication system is fully operational with cross-domain session persistence
- Account management features are implemented with proper security measures
- Documentation access control system with developer token functionality is working properly
- Toast notification system is in place for user feedback
- Error handling has been enhanced for better user experience
- Cluster and project implementation has been designed and is being implemented
- Navigation system now properly handles dynamic routes with cluster and project slugs
- Demo account and mock data integration has been improved with dedicated mock tables
- Subdomain routing and navigation is functioning correctly
- AI assistant components have been optimized for performance and usability
- Homepage now provides a comprehensive dashboard with key user information
- Calendar and meeting agenda functionality is implemented and responsive
- Task and pull request visualization is working with mock data
- User profile creation during sign-up process is planned
- Deployment Pipeline visualization is now implemented with proper status indicators and responsive design
- Animation performance has been optimized throughout the application with hardware acceleration and efficient rendering techniques

## Active Tasks

- Implementing Project-Team relationship functionality:
  - Creating project_teams database table with proper RLS policies
  - Implementing database functions for team-project management
  - Creating server actions for team-project associations
  - Updating project creation wizard to include team assignment
  - Adding UI components for managing team-project relationships
  - Implementing access control based on team membership
- Implementing search functionality for AddTeamMemberDialog to find organization members
- Converting input field to autocomplete combobox for better user experience
- Designing and implementing user search API endpoint for organization members
- Resolving team member data fetching issues with proper foreign key relationships
- Ensuring correct separation of responsibilities throughout the application
- Continuing implementation of the cluster and project management system
- Completing Teams and Team Members functionality with proper UI components
- Creating team CRUD operations and association with projects
- Implementing team-based access control for projects
- Ensuring proper tenant isolation for team-related data
- Refining demo functionality and making mock data usage explicit
- Optimizing Link component usage for cross-domain navigation
- Handling edge cases in organization access validation
- Enhancing error feedback mechanisms
- Fine-tuning the navigation experience between different parts of the application
- Expanding technical documentation to cover new features and components
- Enhancing AI assistant features with more personalized suggestions
- Implementing responsive design improvements for mobile compatibility
- Connecting homepage components to real data sources
- Enhancing calendar integration with meeting scheduling
- Improving database query efficiency for team and mock data operations
- Implementing automatic profile creation during sign-up
- Adding users to organizations when signing up from a subdomain
- Daily Standup Enhancement
  - Implement real-time updates for member status changes
  - Add task commenting functionality
  - Create task movement between days
  - Enhance impediment reporting with categorization
  - Add meeting notes functionality
  - Implement meeting time preferences
  - Add email notifications for daily standups
  - Create meeting summary generation

## Decision Points

### Code Organization Pattern

- Database files (db/\*.db.ts) focus exclusively on database operations
- Action files (app/_/\_actions/_.action.ts) handle business logic and data formatting
- Page components (app/\*/page.tsx) receive formatted data from actions
- Client components focus on rendering and user interactions
- This pattern ensures clear separation of responsibilities, consistent error handling, and easier maintenance

### Database Relationship Patterns

- Using explicit foreign key relationships in Supabase queries (users!user_id)
- Ensuring proper tenant isolation with tenant_id fields
- Implementing role-based access control within teams
- Creating separate mock data tables for testing purposes with clear ID range separation

### Documentation Strategy

- Using Fumadocs for technical documentation
- Organizing documentation into architecture, authentication, development, and maintenance sections
- Creating comprehensive guides for key system components
- Implementing secure developer token access for sensitive documentation

### Authentication Strategy

- Using Supabase Auth with custom RPC functions for secure operations
- Implementing confirmation dialogs for critical actions
- Utilizing toast notifications for user feedback
- Preventing redirect loops in authentication flows with proper return path handling
- Creating user profiles automatically during the sign-up process
- Adding users to organizations when signing up from a subdomain

### Link Behavior

- Disable prefetching for cross-domain links
- Handle subdomain navigation appropriately

### Subdomain Access

- Validate organization access before allowing subdomain access
- Implement proper error handling for unauthorized access
- Add users to organization when they sign up from the organization's subdomain

### Project Structure

- Clusters as top-level containers for projects
- Projects belong to a single cluster with 1:N relationship
- Teams can be associated with one or multiple projects
- Users can belong to multiple teams through team_members
- All team-related data maintains tenant isolation via tenant_id
- Dynamic routing with cluster and project slugs
- Multi-step wizard for creation flow
- Clear separation between demo and production routes

### Navigation Architecture

- Client-side routing using Next.js hooks (usePathname) to extract route parameters
- URL transformation utility for building dynamic URLs with current cluster/project context
- Active menu detection that accounts for dynamic routes with slugs
- Avoiding server components inside client components with proper architecture

### AI Assistant Implementation

- Use memoization for performance optimization
- Implement togglable compact mode for UI flexibility
- Apply proper React hooks patterns with correct dependency arrays
- Separate UI into modular components for better maintainability

### Homepage Layout

- Responsive design that adapts to different screen sizes
- Component organization based on functionality (tasks, projects, calendar, AI)
- Suspense boundaries for better loading experience
- Clear visual hierarchy with consistent styling
- Grid-based layout that adjusts based on viewport size

### Mock Data Strategy

- Dedicated mock_profiles and mock_team_members tables for testing
- Clear ID range separation between mock and real data (real IDs start from 101)
- Database functions that fetch both real and mock data
- Action functions that combine real and mock data for UI components
- Visual indicators in UI to identify mock data usage

### Sign-Up Flow Enhancements

- Create user profiles for all users during sign-up process
- Use getCurrentSubdomain utility to detect if sign-up is from a subdomain
- Add subdomain users to organization as members after sign-up confirmation
- Handle organization member creation invisibly without user intervention

### Team Member Search Functionality

- Implementing a user search API endpoint that queries organization members
- Using combobox component from shadcn/ui for autocomplete functionality
- Implementing debounced search to prevent excessive API calls
- Displaying user avatars, names, and emails in search results
- Preventing duplicate team member additions with proper validation
- Storing selected user ID for submission rather than just name/email

### Project-Team Relationship Implementation

- Creating a many-to-many relationship between projects and teams via the project_teams table
- Implementing proper tenant isolation for team-project relationships
- Using database functions focused exclusively on data operations
- Creating server actions for business logic and UI data transformation
- Providing clear UI for assigning teams to projects in the organization admin page
- Enforcing project access control based on team membership
- Maintaining proper separation of concerns throughout the implementation
- Moving project-team management to the organization page for admin users

## Technical Challenges

- Managing real-time updates across subdomains
- Ensuring proper cleanup of user data during account deletion
- Handling offline functionality
- Managing state across different domains
- Optimizing project card rendering performance
- Implementing parallel data loading for project routes
- Clearly distinguishing between demo data and real user data
- Preserving form state and search parameters during redirect flows
- Preventing unnecessary re-renders in AI components
- Ensuring responsive design across different screen sizes
- Balancing information density with usability in the homepage
- Optimizing loading performance for multiple concurrent data fetches
- Efficiently combining real and mock data in UI components
- Coordinating profile creation and organization member addition during sign-up

## Current Questions

- How to further optimize the demo experience while maintaining clear separation from production data?
- What's the best way to handle organization switching UX?
- How to ensure complete user data cleanup on account deletion?
- What's the best approach for handling multi-project creation in batches?
- How to implement efficient project listing with pagination?
- How should the AI assistant be integrated with real task management features?
- What personalization options should be provided for the AI assistant?
- How to best integrate calendar functionality with external calendar services?
- What additional widgets would be most useful on the homepage dashboard?
- How to optimize database queries when fetching both real and mock data?
- What user profile fields should be created initially during sign-up?

## Next Steps

1. Implement Teams and Team Members database structure and functionality
2. Extend access control to respect team memberships for projects
3. Complete implementation of the cluster and project management system
4. Further refine the demo experience with clear data distinction
5. Enhance error handling for unauthorized access
6. Implement proper cleanup procedures for user data
7. Add comprehensive user feedback mechanisms
8. Optimize cross-domain navigation experience
9. Continue expanding technical documentation as new features are implemented
10. Integrate AI assistant with real task management
11. Implement more personalization options for user profiles
12. Connect homepage components to real-time data sources
13. Enhance calendar integration with external services
14. Optimize database queries for better performance when combining real and mock data
15. Add more comprehensive testing for invitation functionality
16. Enhance error handling for invitation edge cases

## Immediate Priorities

1. Design and implement Teams and Team Members database schema
2. Create team management functionality and UI
3. Implement team-project associations
4. Finalize cluster and project functionality
5. Improve error handling and user feedback
6. Optimize cross-domain navigation
7. Continue enhancing the demo experience
8. Add comprehensive testing for the invitation system

## Project Creation Wizard Refactor (Ongoing)

**Date:** {{current_date}}

**Status:** Implemented initial version of the new project creation wizard page.

**Recent Changes:**

- Modified the button in `app/home/<USER>/home-create-cluster-project-wizard.tsx` to redirect to `/home/<USER>/projects/new`.
- Created a new 3-step wizard UI at `app/home/<USER>/projects/new/page.tsx`:
  - Step 1: Cluster configuration (new or existing).
  - Step 2: Define up to 20 projects (name, auto-generated/editable slug, description).
  - Step 3: Review and submit.
- Integrated `react-hook-form`, `zod` for validation, and `generateSlug` utility.
- Basic UI structure, navigation, and mock data fetching for clusters are in place.

**Next Steps & Considerations:**

- **Resolve `organizationId` Source:** The new page needs a reliable way to get the current `organizationId`. Currently uses `useParams` with a fallback.
- **Implement Real Data Fetching:** Replace `fetchExistingClusters` mock with actual API/server action call.
- **Verify/Update Server Action:** Ensure `createClusterWithProjects` server action aligns with the data structure and logic (new vs. existing cluster, project details, slug uniqueness).
- **Address Linter Warning:** A persistent linter warning related to `organizationId` type in the `createClusterWithProjects` call needs to be monitored. The TypeScript code includes runtime checks that should ensure type safety.
- **UI/UX Enhancements & Testing.**

**Decisions:**

- Team assignment during project creation will be handled in a dedicated UI, not this wizard, to maintain focus.
