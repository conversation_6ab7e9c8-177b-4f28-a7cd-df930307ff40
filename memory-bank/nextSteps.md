# Next Steps

## Completed Phases

### Phase 1-9: Subdomain Infrastructure & Auth

✅ Implemented basic subdomain infrastructure, authentication integration, database schema enhancement, route migration, organization features, testing, performance optimization, and production preparation.

### Phase 10: Cluster & Project Initial Implementation

✅ Set up database structure with cluster and project tables
✅ Implemented core components for project management
✅ Created slug generation utilities
✅ Built project creation wizard
✅ Implemented database access organization

### Phase: Clusters Management System Implementation

✅ **Goal**: Create a comprehensive system for managing clusters and their associated projects.

- ✅ **UI Components**: Created a complete set of components for displaying and managing clusters
- ✅ **Server Actions**: Implemented server-side functions for cluster operations (create, delete, assign/remove projects)
- ✅ **Client Components**: Developed client components for state management and user interactions
- ✅ **Database Integration**: Established proper relationships between clusters and projects
- ✅ **Search Functionality**: Added ability to search clusters and projects
- ✅ **Access Control**: Implemented admin-only access for sensitive operations
- ✅ **UI Integration**: Added a new tab in the organization page for cluster management
- ✅ **Project Management**: Implemented project assignment and removal functionality
- ✅ **Empty States**: Added components for better UX when no clusters exist

### Phase: `src/db` Refactoring

✅ **Goal**: Improved Performance and Maintainability of the Data Layer (`src/db`).

- ✅ **Data Aggregation Refactored**: Optimized actions like `getUserTeamProjectsAction`, `getAllOrganizationMembersAction`, `searchOrganizationMembersAction`, `getTeamsWithMembersByTenantByUserAction`, and `getTeamsWithMembersByTenantAction` to reduce database round trips and simplify logic using more direct Supabase queries and helper functions.
- ✅ **Duplicated Code Consolidated**: Reduced redundancy in `team.actions.ts` by extracting common logic into `_mapTeamsDataToTeamUI`.

### Phase: Email-Based Member Invitation System

✅ **Goal**: Transform the current manual invitation link system into a streamlined email-based invitation process.

- ✅ **Component & Action Updates**: Simplified member-invitation.tsx, enhanced organization-invitations.action.ts
- ✅ **Email Service Implementation**: Created reusable email service utility with templates
- ✅ **Registration & Validation Flow**: Updated token validation system and sign-up process
- ✅ **Organization Membership Processing**: Implemented automatic organization joining after registration
- ✅ **Email Configuration**: Properly configured Supabase URL settings for email redirects
- ✅ **Profile Creation Fix**: Implemented upsert logic for createProfileInDb function to handle existing users
  - Replaced insert with upsert operation using onConflict: "user_id"
  - Fixed edge case where users already exist in auth.users but need profile creation
  - Added proper error handling for duplicate profile scenarios
  - Ensured seamless profile creation for all signup paths
  - Thoroughly tested with various signup scenarios

## Current Phase: Task Schema Unification, Project Creation Wizard Enhancement & TanStack Query Implementation

This phase focuses on unifying the task data schema, improving the project creation experience, and continuing the rollout of TanStack Query.

### Tasks Module Reorganization Plan

**Goal**: Refactor the tasks module with a clearer structure for better maintainability, scalability, and performance.

**Step 1: Create Folder Structure & Move Files** ✅ COMPLETED

- [x] Create new directory structure:
  ```
  tasks/
  ├── _components/          (existing, keep shared components)
  ├── _views/               (new, for main view containers)
  │   ├── list-view/
  │   ├── kanban-view/
  │   ├── timeline-view/
  │   └── calendar-view/
  ├── _grouping/            (new, for grouping strategies)
  │   ├── assignee/
  │   ├── due-date/
  │   ├── effort/
  │   ├── lifecycle/
  │   ├── status/
  │   ├── tag/
  │   └── value/
  └── _task-item/           (new, for task representation)
  ```
- [x] Move existing files to their appropriate locations
- [x] Create index.tsx files for proper exports
- [x] Update initial import paths

**Step 2: Code Reorganization & New Components** 🔄 IN PROGRESS

- [x] Create a dedicated task-view-switcher component
- [x] Create task-grouping-selector component
- [x] Update list-view component to handle grouping
- [x] Restore missing UI elements from original interface (tabs, search, action buttons)
- [ ] Fix any broken references and imports
- [ ] Review code for edge cases and potential bugs

**Step 3: UI Consistency Verification** ⏳ PENDING

- [ ] Compare the UI of the original implementation with the refactored version
- [ ] Ensure all functionality is maintained
- [ ] Test with different screen sizes and dark/light modes

**Step 4: Component Modularization** ⏳ PENDING

- [ ] Break down large components into smaller, reusable units
- [ ] Implement optimizations like virtualization for large lists
- [ ] Add proper error boundaries and fallbacks

**Step 5: TanStack Query Implementation** ⏳ PENDING

- [ ] Set up query client provider
- [ ] Create hooks for fetching task data
- [ ] Implement optimistic updates for task modifications
- [ ] Add proper caching strategies

**Step 6: API Integration** ⏳ PENDING

- [ ] Create server actions for tasks
- [ ] Replace mock data with API calls
- [ ] Add proper error handling for API calls
- [ ] Implement data validation with Zod

**Step 7: Testing** ⏳ PENDING

- [ ] Write unit tests for key components
- [ ] Test all user interactions
- [ ] Test error scenarios
- [ ] Test performance with large datasets

### Priority 0: Task Schema Unification and Database Migration

**Goal**: Create a unified task schema that works across List and Kanban views, and migrate task data to Supabase.

**Phase 1: Analysis and Schema Design**

1. **Analyze Existing Schemas**

   - Compare `tasks.data.ts` (List view) and `mock-kanban-data.ts` (Kanban view)
   - Identify common fields and mandatory requirements for both views
   - Document schema differences and feature requirements
   - Determine properties needed to support all existing functionality

2. **Design Unified Schema**
   - Create comprehensive TypeScript types for the unified schema
   - Ensure backward compatibility with both UI implementations
   - Define relationships with other entities (projects, users, teams)
   - Design proper status workflow and state transitions
   - Add support for metadata like creation date, modified date, etc.

**Phase 2: Mock Data Implementation**

3. **Update Mock Data Structure**

   - Create a new unified mock data file based on the schema
   - Ensure all existing features (filtering, grouping, sorting) are supported
   - Test data with various task states and properties
   - Document sample task data for reference

4. **Update UI Components**
   - Modify List view components to use the unified schema
   - Update Kanban view components to use the unified schema
   - Verify grouping functionality works correctly
   - Test sorting and filtering with the new schema
   - Ensure UI functionality is preserved with no regressions

**Phase 3: Database Implementation**

5. **Create Supabase Migration**

   - Design tasks table with appropriate columns and relationships
   - Implement foreign key constraints (project_id, assignee_id, etc.)
   - Add necessary indexes for performance optimization
   - Create migration script with proper up/down methods

6. **Implement RLS Policies**

   - Set up proper row-level security policies for tasks
   - Ensure tenant isolation with tenant_id field
   - Implement access control based on project membership
   - Add policies for different user roles (admin, member, guest)

7. **Create Database Helper Functions**
   - Implement getTasksByProjectIdFromDb function
   - Create updateTaskInDb function for task modifications
   - Add createTaskInDb function for new task creation
   - Implement deleteTaskFromDb function for task removal

**Phase 4: Server Actions Implementation**

8. **Create Task Actions**

   - Implement getTasksAction for fetching tasks with proper filtering
   - Create updateTaskAction with validation and error handling
   - Add createTaskAction with required field validation
   - Implement deleteTaskAction with proper confirmation checks

9. **Replace Mock Data**
   - Update UI components to use server actions instead of mock data
   - Implement proper loading states with Suspense
   - Add error handling for failed API requests
   - Create fallback UI for error states

**Phase 5: Testing & Optimization**

10. **Comprehensive Testing**

    - Test all task operations (create, read, update, delete)
    - Verify UI components work correctly with real data
    - Test filtering, sorting, and grouping functionality
    - Validate performance with large task datasets

11. **Optimization**
    - Implement caching for better performance
    - Add pagination for large task lists
    - Optimize queries for efficiency
    - Implement batched updates for multiple tasks

### Priority 1: Project Creation Wizard Enhancement

**Goal**: Create a more robust, user-friendly, and flexible project creation process.

**Sub-Phase 1.1: Relocate Wizard to Dedicated Page**

- **Objective**: Move the project creation flow from a modal (presumed) to a full-page experience.
- **Tasks**:
  - Design the UI/UX for the new dedicated page at `/home/<USER>/projects/new`.
  - Analyze the existing project creation modal to identify reusable components and logic.
  - Implement the page structure, forms, and navigation for the new route.
  - Ensure all existing wizard functionality is preserved and enhanced on the new page.
- **Rationale**: A dedicated page provides more space for a potentially multi-step or complex form, better state management, and a shareable/bookmarkable URL.

**Sub-Phase 1.2: Enable Association with Existing Clusters**

- **Objective**: Allow users to create a new project and assign it to an already existing cluster within their organization.
- **Tasks**:
  - Modify the project creation form (on the new dedicated page) to include a cluster selection mechanism (e.g., a searchable dropdown).
  - Populate the cluster selection with a list of the current organization's existing clusters (fetched via an action).
  - Update the server action responsible for project creation (e.g., `createProjectAction` in `project.action.ts`) to accept an optional `existing_cluster_id`.
  - If `existing_cluster_id` is provided, associate the new project with that cluster.
  - If no `existing_cluster_id` is provided (or if a "create new cluster" option is chosen), maintain the existing logic for creating a new cluster alongside the project (if applicable).
  - Verify database schema and foreign key constraints correctly support this association.
- **Rationale**: Increases flexibility, aligns with common user workflows where projects are added to pre-defined groupings, and avoids unnecessary cluster duplication.

### Priority 2: TanStack Query Implementation (Ongoing)

1. **Setup TanStack Query Provider**

   - Install required packages: @tanstack/react-query
   - Create QueryClientProvider component in src/providers/query-client-provider.tsx
   - Configure development-optimized settings:
     - Reduced staleTime (10 seconds instead of 60 seconds for faster refreshing during development)
     - Simplified error handling for quicker debugging
     - Enhanced visibility of query status in React DevTools
     - More verbose logging in development

2. **Define Query Hooks for Data Fetching**

   - Create organization teams queries:
     - Implement useOrganizationTeams hook
     - Handle loading, error, and data states
     - Configure caching with shorter staleTime for development
   - Create organization projects queries:
     - Implement useOrganizationProjects hook
     - Configure proper query keys for invalidation
   - Create team members queries:
     - Implement useTeamMembers hook
     - Configure development-friendly refetching

3. **Implement Mutation Hooks**

   - Team management mutations:
     - Create hooks for team CRUD operations
     - Implement proper cache invalidation
   - Team member mutations:
     - Create hooks for team member operations
     - Setup optimistic updates
   - Project team mutations:
     - Create hooks for project-team relationship
     - Implement cache updates

4. **Refactor Components to Use TanStack Query**

   - Update organization page to use QueryClientProvider
   - Refactor Teams List component:
     - Replace direct API calls with query hooks
     - Replace loading/error state management with query states
     - Implement optimistic UI for team operations
   - Refactor Projects List component:
     - Replace direct API calls with query hooks
     - Implement optimistic UI for project operations
   - Refactor Team creation flow:
     - Update create-team-form.tsx
     - Replace direct API calls with mutation hooks

5. **Implement Advanced Features**
   - Add prefetching for better UX
   - Setup consistent query key structure
   - Implement targeted invalidation to minimize refetches
   - Create patterns for future feature development

### Demo Experience Enhancement

1. **Improve Demo Data Clarity**

   - ✅ Add demo account badges to clearly identify mock data
   - Enhance visual indicators across all demo pages
   - Create comprehensive demo data set
   - Add tooltips explaining demo limitations

2. **Optimize Demo Navigation**

   - Improve demo route organization
   - Add clear return paths to production routes
   - Create consistent demo header across all pages
   - Add demo mode indicator in browser title

3. **Enhance Demo Content**

   - Create realistic but fictional datasets for all features
   - Implement sample projects with varying completion states
   - Add example tasks with different priorities and statuses
   - Create sample reports and analytics

4. **Demo Feature Access**
   - Clearly indicate which features are available in demo mode
   - Provide feedback for unavailable features
   - Create guided tour of key features
   - Add sample notifications and activity

### Cluster & Project Completion

1. **Finalize Project Dashboard**

   - Complete core dashboard layout
   - Implement project statistics display
   - Add activity timeline
   - Create status overview section

2. **Enhance Project Settings**

   - Complete project settings pages
   - Add user role management
   - Implement notification preferences
   - Create archive/delete functionality

3. **Project Analytics**

   - Implement basic analytics charts
   - Add progress tracking
   - Create time-based performance views
   - Build reporting functionality

4. **Cross-Project Features**
   - Implement project comparison tools
   - Add multi-project view
   - Create project templates
   - Build batch operations for projects

## Project-Team Relationship Implementation

The project requires a proper relationship between teams and projects to implement proper access control and effective collaboration:

1. Database Layer:

   - Many-to-many relationship between teams and projects with project_teams junction table
   - Proper tenant isolation with tenant_id on all relationships
   - RLS policies to ensure security

2. Access Control:

   - Users should access projects they belong to through team membership
   - Admin users should have access to all organization projects
   - Projects should respect team-based access control

3. UI Layer:

   - Admin users can manage team assignments from the organization page
   - Users see only projects they belong to on the homepage
   - Teams list and project-teams UI should have consistent designs
   - Project creation wizard should include team selection

4. Implementation Status:

   - ✓ Database schema and RLS policies
   - ✓ Database functions and server actions
   - ✓ Basic UI for managing team-project relationships
   - ✓ Project-team management UI in organization page
   - ⏳ Project creation wizard team selection
   - ⏳ Access control checks based on team membership

5. Next Steps:
   - Update project creation wizard to include team selection
   - Implement access control checks for projects based on team membership
   - Create tests for team-project functionality
   - Add user interface for filtering and searching teams in projects

## Upcoming Phases

### Phase 11: Sign-Up Flow Enhancement

1. **Database Functionality**

   - Create profile.db.ts for user profile database operations
   - Implement createProfileInDb function with minimal required fields
   - Ensure proper error handling for database operations
   - Add tenant isolation for profile data

2. **Server Actions**

   - Develop subdomain-signup.action.ts for coordinating sign-up flow
   - Create actions for profile creation across all domains
   - Implement organization member addition for subdomain sign-ups
   - Handle post-sign-up processing seamlessly

3. **Sign-Up Flow Integration**

   - Modify sign-up form to detect subdomain using getCurrentSubdomain
   - Store subdomain information during sign-up process
   - Trigger appropriate actions after sign-up is confirmed
   - Ensure smooth user experience with no additional steps

4. **Testing & Optimization**
   - Test profile creation in regular sign-up flow
   - Verify organization member addition in subdomain sign-up
   - Optimize database operations for efficient processing
   - Ensure proper error handling throughout the flow

### Phase 12: Teams & Team Members Implementation

1. **Team Data Structure**

   - Design team and team_members database schema
   - Implement tenant isolation via tenant_id
   - Create relationships with projects and users
   - Set up appropriate RLS policies for security

2. **Team Management UI**

   - Build team creation interface
   - Implement team settings and configuration
   - Create team member invitation system
   - Design team dashboard view

3. **Team Member Management Enhancements**

   - Improve AddTeamMemberDialog with search functionality:
     - Create API endpoint for searching organization members by name/email
     - Convert regular input to combobox component from shadcn/ui
     - Add debounced search functionality to prevent excessive API calls
     - Display matching user results with avatar, name, and email
     - Handle empty results and loading states properly
     - Add keyboard navigation for better accessibility
     - Implement user selection and store user ID for submission
     - Prevent adding users who are already team members
     - Add proper error handling and UX improvements
   - Implement team member role management
   - Add position assignment and management
   - Create team member removal confirmation flow

4. **Team-Project Association**

   - Create UI for associating teams with projects
   - Implement team-based access controls
   - Build project access validation based on team membership
   - Create UI for viewing team-project relationships

5. **Team Access Control**
   - Implement team-based permissions
   - Create role management within teams
   - Build user interface for team role assignment
   - Extend existing RLS policies for team-based access

### Phase 13: Task Management System

1. **Task Data Model**

   - Design comprehensive task schema
   - Set up relationships with projects
   - Create status workflow
   - Implement priority system

2. **Task UI Components**

   - Build task card component
   - Create task detail view
   - Implement task creation form
   - Design task board interface

3. **Task Functionality**

   - Add assignee management
   - Implement due date tracking
   - Create comment system
   - Build activity history

4. **Task List Views**
   - Implement board view (kanban)
   - Create list view
   - Add calendar view
   - Build timeline view

### Phase 14: Collaboration Features

1. **Real-time Updates**

   - Implement Supabase realtime
   - Add change indicators
   - Create notification system
   - Build presence indicators

2. **Comments & Discussion**

   - Add rich text comments
   - Implement @mentions
   - Create threaded discussions
   - Build emoji reactions

3. **Sharing & Permissions**

   - Design granular permission system
   - Add sharing functionality
   - Implement external sharing
   - Create view-only modes

4. **Activity Tracking**
   - Build comprehensive activity log
   - Create digest notifications
   - Implement user activity metrics
   - Add audit trail for critical actions

### Phase 15: Advanced Analytics

1. **Dashboard Improvements**

   - Create customizable dashboards
   - Add drag-and-drop widget system
   - Implement data filtering
   - Build comparison tools

2. **Performance Metrics**

   - Add velocity tracking
   - Implement burndown charts
   - Create cumulative flow diagrams
   - Build forecasting tools

3. **Custom Reports**

   - Add report builder
   - Implement report scheduling
   - Create export functionality
   - Build report templates

4. **Integration Points**
   - Add API for external tools
   - Implement webhook system
   - Create data connectors
   - Build third-party integration hooks

### Phase 16: Mobile Optimization

1. **Responsive Enhancements**

   - Audit all components for mobile usability
   - Optimize touch interactions
   - Improve navigation on small screens
   - Enhance form factors for mobile input

2. **Mobile-specific Features**

   - Add offline capability
   - Implement push notifications
   - Create mobile-optimized views
   - Build quick-action shortcuts

3. **Performance Optimizations**

   - Reduce bundle size for mobile
   - Implement code splitting
   - Optimize images and assets
   - Reduce network requests

4. **Mobile Testing**
   - Cross-device compatibility testing
   - Touch interaction testing
   - Offline functionality validation
   - Performance benchmarking

## Implementation Priorities

1. Complete demo experience enhancement
2. Finalize cluster-project implementation
3. Implement sign-up flow enhancements
4. Begin task management system development
5. Implement basic collaboration features
6. Add core analytics functionality

## Technical Debt & Improvements

1. **Refactoring Opportunities**

   - Streamline authentication flow
   - Improve error handling consistency
   - Enhance form validation patterns
   - Standardize component props

2. **Performance Optimizations**

   - Implement more aggressive caching
   - Optimize database queries
   - Reduce unnecessary re-renders
   - Add code splitting for large features

3. **Documentation Needs**

   - Create comprehensive API documentation
   - Update user guides
   - Document database schema
   - Add developer onboarding docs

4. **Testing Improvements**
   - Increase unit test coverage
   - Add integration tests
   - Implement end-to-end tests
   - Create performance test suite
