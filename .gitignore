# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# public assets - supabase schema
/public/supabase-schema*.png

# .source/index.ts
###
# IMPORTANT
# This flag allows you to ignore changes to a file, so it doesn't show up in git status
###
# git update-index --assume-unchanged .source/index.ts     ->  ignore changes to this file
# git update-index --no-assume-unchanged .source/index.ts  ->  unset the flag
