---
title: Renwu Documentation
description: Welcome to the Renwu technical documentation
---

# Renwu Technical Documentation

Welcome to Renwu, an AI-powered platform for project management at scale. This documentation covers technical aspects of the system architecture, development guides, and implementation patterns.

**Renwu: Project at a Glance**

---

> **Cluster-First. AI-Powered. Enterprise PM**

---

## Problem ↔ Solution

| Problem                            | Renwu Solution                                       |
| ---------------------------------- | ---------------------------------------------------- |
| Disjointed multi-project workflows | Cluster-centric containers for 1000+ projects        |
| Manual roadmap & resource planning | AI‑Manager: predictive roadmaps & workload balancing |
| Fragmented reporting & budgets     | Unified cluster dashboard & budget forecasting       |

---

## Key Metrics & Highlights

- **1000+ Projects/Cluster**: Seamless visibility across large portfolios
- **AI‑Manager™**: First-in-class AI for predictive planning
- **100% Tenant Isolation**: RLS & tenant_id enforcement
- **\<200ms Avg. API Latency**: High-performance Next.js + Supabase
- **Responsive UI**: Shadcn & OriginUI with Skeleton loading

---

## Architecture Snapshot

```
[Cluster] ⇄ [Project Teams] ⇄ [Projects] ⇄ [Team Members]
       ↳ AI‑Manager (roadmap, balance, forecast)
```

---

## Architecture

Learn about the overall system architecture and design decisions:

- [Business Plan](/docs/business-plan/business-plan) - Business plan for Renwu
- [Authentication Flow](/docs/authentication/auth-flow) - Multi-tenant authentication flow with session persistence and robust user profile creation
- [Multi-Tenant Database Strategy](/docs/architecture/db-multi-tenant-strategy) - Phased approach to database scaling
- [Tenant Isolation System](/docs/architecture/tenant-isolation) - Database isolation implementation
- [URL Structure](/docs/architecture/url-structure) - URL design for multi-tenant applications
- [Organization Management System](/docs/architecture/organization-system) - Domain model and specifications
- [Scaling Strategy](/docs/architecture/scaling-strategy) - Phased approach to scaling the application

## Development Guides

Resources for developers working on the project:

- [Performance Investigation Report](/docs/development/performance-investigation-report) - Performance investigation report
- [Project Structure](/docs/development/project-structure) - File and directory organization
- [Local Subdomain Setup](/docs/development/local-subdomain-setup) - Setting up local development
- [Data Fetching Patterns](/docs/development/data-fetching) - Best practices for data fetching
- [Mock Data System](/docs/development/mock-data-system) - Implementation and usage of the mock data system

## Maintenance

Information about maintaining and updating the project:

- [Removing Fumadocs](/docs/maintenance/removing-fumadocs) - How to remove the documentation system

---

## Use Cases

- **Engineering PMO**: Coordinate interdependent software releases
- **Construction Oversight**: Manage multi-site build portfolios
- **R\&D Leaders**: Balance cross-project budgets & timelines

---

## Next Steps

- **Request a Demo**: Engage with an enterprise POC
- **14‑Day Trial**: Experience AI‑Manager insights firsthand
- **Whitepaper Download**: Deep dive into architecture & ROI
