---
title: Tenant Isolation System
description: Detailed overview of the database tenant isolation implementation using Row-Level Security in Supabase
---

# Tenant Isolation System

## Overview

This document outlines the tenant isolation system implemented in our Supabase database to ensure data segregation between different organizations. The system uses a combination of Row-Level Security (RLS) policies and database triggers to enforce tenant boundaries.

## Database Schema

- Add: Database Schema showing tenant_id relationships schema

_The schema shows how tenant_id is implemented across tables with relationships between organizations, organization_members, clusters, projects, and tasks._

## Core Concepts

### Tenant ID

- Each record in multi-tenant tables contains a `tenant_id` field
- The `tenant_id` is set to the organization's ID to which the data belongs
- This identifier is used by RLS policies to restrict data access

### Tables with Tenant Isolation

The following tables implement tenant isolation:

1. `clusters`
2. `organization_members`
3. `projects`
4. `tasks`

## Implementation Components

### 1. Row-Level Security (RLS) Policies

Each multi-tenant table has an RLS policy that enforces isolation:

```sql
CREATE POLICY "Enforce tenant isolation on [table_name]" ON [table_name]
USING (
  tenant_id IN (
    SELECT organization_id
    FROM organization_members
    WHERE user_id = auth.uid()
  )
);
```

This policy ensures users can only access records where:

- The `tenant_id` matches an organization they belong to
- Their user ID is present in the `organization_members` table for that organization

### 2. Database Triggers

Automatic tenant ID assignment is handled through database triggers:

| Trigger Name                       | Table                | Function                              |
| ---------------------------------- | -------------------- | ------------------------------------- |
| clusters_set_tenant_id             | clusters             | set_tenant_id_on_clusters             |
| organization_members_set_tenant_id | organization_members | set_tenant_id_on_organization_members |
| projects_set_tenant_id             | projects             | set_tenant_id_on_projects             |
| tasks_set_tenant_id                | tasks                | set_tenant_id_on_tasks                |

Each trigger fires BEFORE INSERT operations and sets the `tenant_id` field automatically.

### 3. Database Functions

The functions called by the triggers ensure the `tenant_id` is set correctly:

| Function Name                         | Purpose                                             |
| ------------------------------------- | --------------------------------------------------- |
| set_tenant_id_on_clusters             | Sets `tenant_id` on new cluster records             |
| set_tenant_id_on_organization_members | Sets `tenant_id` on new organization member records |
| set_tenant_id_on_projects             | Sets `tenant_id` on new project records             |
| set_tenant_id_on_tasks                | Sets `tenant_id` on new task records                |

## How It Works

1. **Record Creation**:

   - When a new record is created in a multi-tenant table
   - The BEFORE INSERT trigger fires
   - The associated function sets the `tenant_id` field

2. **Data Access**:

   - When a user attempts to query a multi-tenant table
   - RLS policies evaluate the user's organization memberships
   - Records are filtered to show only those with matching `tenant_id` values

3. **Cross-Table Relations**:
   - Relationships between tables maintain tenant isolation
   - Records in child tables include the `tenant_id` from their parent entities

## Security Considerations

- All data access must go through the Supabase API to enforce RLS policies
- Direct database access bypassing RLS should be restricted to administrative functions
- Test tenant isolation thoroughly after any schema changes
- Ensure new tables that need tenant isolation implement the appropriate RLS policy and trigger

## Best Practices

1. **Adding New Tables**:

   - Always include a `tenant_id` field (type: uuid)
   - Create an RLS policy for tenant isolation
   - Implement a trigger to set `tenant_id` automatically

2. **Data Migrations**:

   - Ensure any data migration scripts account for tenant isolation
   - Verify `tenant_id` is correctly set during migrations

3. **Application Development**:
   - Client-side code should not attempt to set or modify `tenant_id` values
   - Rely on the database triggers to handle tenant assignment

## Testing

To verify tenant isolation is working correctly:

1. Create two test organizations
2. Create test users in each organization
3. Create records in multi-tenant tables from both users
4. Verify each user can only see their organization's records
5. Attempt cross-tenant access and verify it's properly restricted
