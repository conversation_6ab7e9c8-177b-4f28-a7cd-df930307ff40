---
title: Multi-Tenant URL Structure
description: Design and implementation of URL structure in a multi-tenant application with subdomains, clusters, and projects
---

# Multi-Tenant URL Structure

## Subdomain vs Path-Based Routing

This section explains the differences between using **subdomains** and **path-based routing** for multi-tenant applications in our React, Next.js environment.

### Quick Comparison

| Feature            | Subdomain (`company.domain.com`)         | Path (`domain.com/company`)              |
| ------------------ | ---------------------------------------- | ---------------------------------------- |
| Cookie Isolation   | ✅ Automatic per-subdomain               | ❌ Manual, must scope by path            |
| Routing in Next.js | ❌ Needs middleware or custom server     | ✅ Native support with file-based routes |
| DNS Setup          | ❌ Requires wildcard DNS + TLS           | ✅ Simple DNS and TLS                    |
| Custom Domains     | ✅ Easy with subdomains                  | ❌ More complex with path rewriting      |
| SEO Benefits       | ✅ Separate per tenant                   | ❌ Shared SEO footprint                  |
| Analytics          | ✅ Easy per subdomain                    | ❌ Requires filtering by path            |
| White-labeling     | ✅ Branded URLs like `portal.client.com` | ❌ Always shows main domain              |
| Dev Simplicity     | ❌ Needs proxy/host config               | ✅ Straightforward with `localhost`      |

### Isolation & Security

#### Cookie Scoping and Same‑Site Policies

- **Subdomain**: Cookies are scoped per subdomain (e.g. `acme.domain.com`), offering strong isolation.
- **Path**: All tenants share `domain.com`, so you must manually configure cookie paths (`Path=/acme/`).

#### CORS & Same-Origin Policies

- **Subdomain**: Naturally isolated due to different origins.
- **Path**: Shares the same origin; extra validation required at app/middleware level.

### Implementation in Next.js

#### Subdomain Routing (Our Approach)

Requires parsing the `Host` header via middleware:

```ts
// middleware.ts
import { NextRequest, NextResponse } from "next/server";

export function middleware(req: NextRequest) {
  const host = req.headers.get("host") || "";
  const subdomain = host.split(".")[0];
  return NextResponse.rewrite(new URL(`/${subdomain}${req.nextUrl.pathname}`, req.url));
}
```

#### Path-Based Routing (Alternative)

- Can be implemented with dynamic routes: `pages/[tenant]/dashboard.tsx`
- No middleware or DNS setup needed

### When to Use What

#### Use **Subdomains** if (Our Choice):

- You need strong tenant isolation and cookie security.
- You expect tenants to use their own custom domains.
- You want per-tenant SEO and analytics.
- You are okay managing wildcard DNS/TLS (or using Vercel, Cloudflare, etc.).

#### Use **Path Routing** if:

- You want a simple and fast setup with minimal infrastructure overhead.
- You don't need custom domains.
- SEO and cookie isolation are not concerns.
- You want easier local development.

## Cluster and Project URL Structure

Our application uses a hierarchical model:

- Organization → Clusters[] → Projects[] → Tasks[]

For our URL structure, we use:

```
https://{organization}.renwu.app/{cluster-slug}/{project-slug}/board/tasks/{task-id}
```

Components:

- **Organization**: Subdomain (e.g., `acme.renwu.app`)
- **Cluster (slug)**: First path segment (e.g., `engineering`)
- **Project (slug)**: Second path segment (e.g., `mobile-app`)
- **View type**: Third path segment (e.g., `board`, `dashboard`)
- **Resource**: Fourth path segment (e.g., `tasks`)
- **Resource ID**: Fifth path segment (e.g., `5471`)

### Recommendations

#### URL Pattern Consistency

We use a RESTful structure where resources are represented as path segments:

```
https://{organization}.renwu.app/clusters/{cluster-slug}/projects/{project-slug}/{view}/{resource}/{id}
```

For simplicity, we offer a more concise version:

```
https://{organization}.renwu.app/{cluster-slug}/{project-slug}/{view}/{resource}/{id}
```

#### Slugs vs IDs

For better user experience and URL readability, we use:

- Human-readable slugs for clusters and projects
- IDs for individual resources like tasks

Example:

```
https://acme.renwu.app/engineering/mobile-app/board/tasks/5471
```

#### Next.js Route Structure

Our application directory structure aligns with the URL pattern, and we use middleware to handle subdomain routing.

#### Short URLs for Sharing

For easier sharing, we implement short URLs:

```
https://renwu.app/s/{org}-{cluster}-{project}-{task}
```

Example: `https://renwu.app/s/acme-eng-mobile-5471`
