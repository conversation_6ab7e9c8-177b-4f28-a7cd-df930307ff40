---
title: Multi-Tenant Database Strategy
description: Phased approach to database strategy for scaling a multi-tenant SaaS platform
---

# Multi-Tenant Database Strategy

## Summary

We recommend a three-phase database strategy for our JIRA-like multi-tenant platform:

1. **MVP**: start on Supabase Free Tier using shared tables + Row-Level Security (tenant discriminator).
2. **Early-Scale**: offload heavy or standalone tenants to Neon Free Plan (10 isolated DBs).
3. **Advanced**: migrate to per-tenant Aurora PostgreSQL clusters or custom Postgres when compliance, geo-isolation, or scale demands it.

This keeps costs and ops minimal early on, while providing a smooth upgrade path to isolated, region-aware databases.

## MVP Phase: Supabase Free + Shared Tables

- **Pattern**: single database, shared schema, add `tenant_id` + enforce with RLS.
- **Why**: minimal infra—one set of migrations, no extra DBs or schemas to manage.
- **Supabase Free Tier**: unlimited API, 500 MB Postgres, 1 GB file storage, 50,000 MAUs at $0/mo.
- **Scale envelope**: comfortably supports hundreds of tenants and millions of rows before performance or isolation becomes an issue.

## Early-Scale Phase: Neon Free Plan

- **When**: as soon as you need per-customer isolation—e.g. top 5–10 tenants, beta users, or dev/test environments.
- **Neon Free Plan**:
  - **10** independent Postgres "projects" (databases)
  - **0.5 GB-month** storage total
  - **190** compute-hours/mo, autoscaling to 2 vCPU, scale-to-zero
- **Benefit**: true isolation per tenant without self-hosting; branch/time-travel support; global regions via CLI/API.

## Advanced Options: AWS Aurora & Custom Postgres

- **AWS Aurora PostgreSQL**
  - **vCPU-hour**: $0.10–$0.20 per vCPU-hour (US-East/OH)
  - **Storage**: $0.10/GB-month; I/O-optimized tiers available
  - **Features**: Multi-AZ, global read-replicas, serverless v2 (no scale-to-zero), compliance controls
- **Custom Postgres** (self-hosted or managed)
  - Full control over extensions, sharding (Citus), encryption keys, but heavy ops burden

Use Aurora or self-hosted only when you need strict compliance, per-region residency, or horizontal sharding beyond what Neon/Supabase can offer.

## Multi-Tenancy Patterns Overview

| Pattern                             | Isolation       | Ops Overhead | Scale Sweet Spot    |
| ----------------------------------- | --------------- | ------------ | ------------------- |
| **Shared Tables (tenant_id + RLS)** | Low (row-level) | Minimal      | Millions of tenants |
| **Schema-per-Tenant**               | Medium          | Moderate     | Hundreds of tenants |
| **Database-per-Tenant**             | High            | High         | Tens of tenants     |

- **Don't rush** to schema-per-tenant; stick with shared tables until you hit migration bottlenecks (>100 schemas) or compliance needs.
- **Database-per-tenant** only when strict isolation or custom scaling per customer demands it.

## Final Recommendations

1. **MVP**: keep using Supabase Free + shared tables + Better-Auth.
2. **Early growth**: script Neon project creation for top tenants—no cost lock-in, up to 10 DBs free.
3. **Validation & shift**: monitor usage; spin off heavy or compliance-sensitive customers to Neon Launch ($19/mo) or Aurora clusters.
4. **Long term**: adopt infrastructure-as-code (Terraform), implement metrics (Prometheus/Grafana), and plan for Citus-based sharding once you exceed schema-per-tenant limits.

This phased, cost-effective approach lets us move fast on our JIRA competitor MVP, while preserving an upgrade path to fully isolated, production-grade databases as we scale.
