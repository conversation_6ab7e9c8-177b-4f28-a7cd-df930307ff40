---
title: Removing Fumadocs ␡
description: Complete guide to removing Fumadocs documentation system from the project
---

# Removing Fumadocs

This guide provides a comprehensive overview of how to completely remove the Fumadocs documentation system from the Renwu project.

## Overview

Fumadocs is integrated into several parts of the project:

- Package dependencies
- Configuration files
- App router components
- MDX processing
- CSS styles
- API routes

## Step-by-Step Removal Process

### 1. Remove Package Dependencies

Remove the following packages from `package.json`:

```bash
pnpm remove fumadocs-core fumadocs-mdx fumadocs-ui @types/mdx
```

Also remove the `postinstall` script that references fumadocs-mdx.

### 2. Remove Configuration Files

Delete the following configuration files:

- `source.config.ts` - Defines the documentation source
- `.source/` directory - Contains compiled documentation data

### 3. Remove MDX Processing from Next.js Config

Update `next.config.ts` to remove the MDX processor:

```typescript
// Remove this import
import { createMDX } from "fumadocs-mdx/next";

// Remove the MDX initialization
const withMDX = createMDX();

// Export the config directly without the MDX wrapper
export default nextConfig;
```

### 4. Remove App Router Components

Delete the following directories and files:

- `app/docs/` - The entire docs directory
- `app/api/search/route.ts` - The search API endpoint

### 5. Clean Up Layout Files

Remove Fumadocs from the main app layout in `app/layout.tsx`:

```typescript
// Remove this import
import { RootProvider } from "fumadocs-ui/provider";

// Update the layout JSX to remove RootProvider
return (
  <html lang="en" suppressHydrationWarning>
    <body className="antialiased flex flex-col min-h-screen">
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
      <Toaster />
      <SpeedInsights />
    </body>
  </html>
);
```

Delete the layout configuration file:

- `app/layout.config.tsx`

### 6. Remove CSS Imports

Remove Fumadocs CSS imports from `app/globals.css`:

```css
/* Remove these lines */
@import "fumadocs-ui/css/vitepress.css";
@import "fumadocs-ui/css/preset.css";
```

### 7. Clean Up Source Files

Delete the following:

- `lib/source.ts` - Handles loading documentation
- `mdx-components.tsx` - Overwrites default MDX components

### 8. Remove Documentation Content

Delete the content directory:

- `docs/` - Contains all the markdown documentation files

### 9. Update Middleware

Update the middleware to remove docs protection:

```typescript
// In middleware.ts

// Remove the handleDevDocsProtection function
// and its reference in the middleware function

// Update the config.matcher to remove "/docs/:path*"
export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|api).*)",
    // Remove "/docs/:path*",
  ],
};
```

### 10. Clean Up TypeScript Types

Check for any remaining type references to fumadocs in your codebase and remove them.

## Alternative Documentation Solutions

If you need to replace Fumadocs with another documentation system, consider:

1. **Next.js MDX**: Use the built-in Next.js MDX support
2. **Nextra**: A popular Next.js-based documentation site generator
3. **Docusaurus**: React-based static site generator for documentation
4. **Custom solution**: Build a custom documentation system using Next.js pages

## Testing After Removal

After removing Fumadocs, thoroughly test:

1. Application builds successfully (`pnpm build`)
2. Application runs in development mode (`pnpm dev`)
3. All routes work correctly except for removed documentation routes
4. No console errors related to missing Fumadocs components

## References

- [Fumadocs GitHub Repository](https://github.com/fuma-nama/fumadocs)
- [Next.js Documentation](https://nextjs.org/docs)
