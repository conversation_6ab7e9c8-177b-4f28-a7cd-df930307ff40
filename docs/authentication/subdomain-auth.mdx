---
title: Subdomain Authentication
description: Technical implementation of subdomain-based multi-tenant authentication
---

# Subdomain Authentication

## Overview

Our multi-tenant application uses subdomains to separate organizations, with each organization having its own subdomain in the format `{organization-subdomain-id}.renwu.app`. This document explains how authentication works across these subdomains.

## Domain Strategy

- **Main Domain**: `renwu.app` - Hosts authentication, organization selection, and public content
- **Subdomains**: `{organization-subdomain-id}.renwu.app` - Organization-specific tenant workspaces

## Authentication Flow Across Domains

### 1. Authentication on Main Domain

All authentication happens on the main domain only:

```
https://renwu.app/auth/login
https://renwu.app/auth/sign-up
https://renwu.app/auth/forgot-password
```

After successful authentication, users are redirected to the organization selection page (`/auth/organizations`) where they can choose which organization to access.

### 2. Cross-Domain Navigation

When a user selects an organization, they are redirected to the subdomain using a URL function utility:

```typescript
// Function to generate subdomain URL
function goToSubdomain(subdomain: string, path: string = "") {
  const domain = getCurrentDomain();
  const protocol = getProtocol();
  const isDev = process.env.NODE_ENV === "development";

  if (isDev) {
    // In development, subdomain.localhost doesn't resolve correctly without setup
    // Using URL parameters or cookies to simulate subdomains is an option
    return `${protocol}://${domain}/${path}?tenant=${subdomain}`;
  }

  return `${protocol}://${subdomain}.${domain}/${path}`;
}
```

### 3. Subdomain Access Control

When accessing a subdomain, the middleware checks if the authenticated user has access to that organization:

```typescript
// Simplified middleware for subdomain access control
if (hostname.includes(".")) {
  // Extract subdomain from hostname
  const subdomain = hostname.split(".")[0];

  // Verify user has access to this organization
  const { data: organizations } = await supabase
    .from("organization_members")
    .select("organizations!inner(*)")
    .eq("user_id", user.id);

  const hasAccess = organizations.some((org) => org.organizations.subdomain_id === subdomain);

  if (!hasAccess) {
    // Redirect to organization selection on main domain
    return NextResponse.redirect(goToMainDomain("auth/organizations"));
  }
}
```

## Technical Implementation

### Domain Detection in Middleware

The middleware must detect whether the current request is on the main domain or a subdomain:

```typescript
export async function middleware(request: NextRequest) {
  // Get hostname from request (e.g., "myorg.renwu.app" or "renwu.app")
  const { hostname } = request.nextUrl;

  // Determine if this is a subdomain request
  const isSubdomain = hostname.includes(".") && hostname !== process.env.NEXT_PUBLIC_PROD_DOMAIN;

  // Different handling for main domain vs subdomain
  if (isSubdomain) {
    // Subdomain handling
    // ...
  } else {
    // Main domain handling
    // ...
  }
}
```

### User Profile Creation Pattern

During the sign-up process, particularly for invitation-based sign-ups, the system needs to handle cases where users might already exist in `auth.users` but require profile creation:

```typescript
// Robust profile creation with upsert logic
export async function createProfileInDb(
  userId: string,
  profileData: Partial<UserProfileDB>
): Promise<{ success: boolean; error: Error | null }> {
  if (!userId) {
    return {
      success: false,
      error: new Error("User ID is required to create a profile"),
    };
  }

  try {
    const { adminClient } = await import("@/lib/supabase/admin");

    // Prepare profile data
    const profilePayload = {
      user_id: userId,
      position: profileData.position || "Member",
      first_name: profileData.first_name || "",
      last_name: profileData.last_name || "",
      email: profileData.email || "",
      // Other profile fields...
    };

    // Use upsert instead of insert to handle existing users
    const { data, error } = await adminClient.from("profiles").upsert(profilePayload, {
      onConflict: "user_id", // Key for conflict resolution
      returning: "minimal",
    });

    if (error) {
      console.error("Error creating profile:", error);
      return { success: false, error: new Error(error.message) };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error("Failed to create profile:", error);
    return {
      success: false,
      error: error instanceof Error ? error : new Error("Unknown error"),
    };
  }
}
```

This pattern ensures that profile creation works reliably in all scenarios:

- When a new user signs up directly
- When a user accepts an organization invitation
- When a user already exists in the system but needs profile creation

The `onConflict: "user_id"` option tells Supabase to update the existing record if there's a conflict on the user_id field, rather than throwing an error.

### Local Development Considerations

Subdomains don't work easily with localhost. Options for local development:

#### 1. Edit /etc/hosts for Local Subdomains

```
127.0.0.1 localhost
127.0.0.1 myorg.localhost
127.0.0.1 anotherorg.localhost
```

#### 2. Use Query Parameters

For development environments, use query parameters to simulate subdomains:

```typescript
// Detect environment and use appropriate strategy
if (process.env.NODE_ENV === "development") {
  // Use query parameter for tenant in dev
  const tenant = request.nextUrl.searchParams.get("tenant");
  // ...validation logic
} else {
  // Use subdomain in production
  const subdomain = hostname.split(".")[0];
  // ...validation logic
}
```

## Security Considerations

### 1. Preventing Subdomain Takeover

- Validate subdomain names during organization creation
- Reserve common subdomains (admin, api, www, etc.)
- Implement strict validation rules for subdomain names

### 2. Cross-Site Request Forgery Protection

- Configure CSRF protection to work across subdomains
- Use proper cookie settings (SameSite, Secure)
- Implement proper token validation

### 3. CORS Configuration

Enable CORS for specific subdomains if needed:

```typescript
// CORS configuration for API routes
export const corsHeaders = {
  "Access-Control-Allow-Origin": "*.renwu.app",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};
```

## Troubleshooting

### Common Issues

1. **Session Not Shared Between Domains**

   - Ensure cookies use the root domain with a leading dot (`.renwu.app`)
   - Check that Supabase client is properly configured

2. **CORS Errors When Accessing APIs**

   - Configure proper CORS headers for cross-subdomain requests
   - Use proxy endpoints if needed

3. **Authentication Flow Breaks**
   - Ensure all redirects use the proper domain helpers
   - Verify middleware correctly identifies subdomains vs. main domain
