---
title: Session Management
description: How session management works in the multi-tenant application
---

# Session Management

## Overview

Our multi-tenant application uses Supabase Auth for session management with custom middleware handling to support cross-subdomain authentication and access control.

## Cookie Configuration

Cookies are configured differently between development and production environments:

```typescript
const cookieOptions = {
  ...(isDev ? {} : { domain: `.${currentDomain}` }),
  maxAge: 60 * 60 * 24 * 30, // 30 days
  path: "/",
  sameSite: "lax",
  secure: !isDev,
};
```

### Production Environment

In production, cookies are set with the domain `.renwu.app` (with a leading dot), which ensures:

- Cookies are shared across all subdomains
- A single login provides access to all organizations the user belongs to
- Sessions persist when navigating between the main domain and subdomains

### Development Environment

In development, cookies are set without a domain specification, which allows them to work with `localhost:3000`. This simplifies local development but requires some environment-specific code paths to handle cookie sharing correctly.

## Session Lifecycle

### 1. Session Creation

When a user logs in:

```typescript
const {
  data: { session },
  error,
} = await supabase.auth.signInWithPassword({
  email,
  password,
});

// Session cookies are automatically handled by Supabase Auth
```

### 2. User Profile Creation

After successful sign-up, we use a robust profile creation pattern:

```typescript
// Upsert handles both new users and existing users
const { data, error } = await supabase.from("profiles").upsert(
  {
    user_id: userId,
    first_name: firstName,
    last_name: lastName,
    email: email,
  },
  {
    onConflict: "user_id", // Specify the column to determine conflicts
    returning: "minimal", // Reduce data transfer
  }
);
```

This pattern ensures:

- New users get a complete profile
- Existing users (from invitations or previous signups) are properly handled
- Session can be correctly associated with the user's profile
- Resilience against race conditions during concurrent signups

### 3. Session Validation

Session validation happens in the middleware for all routes:

```typescript
// Simplified middleware session check
export async function middleware(request: NextRequest) {
  const { supabase, response } = createMiddlewareClient(request);

  // This refreshes the session if needed and returns the user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Session validation logic follows
  if (!user) {
    // Handle unauthenticated user...
  }

  // Continue with authorized request
  return response;
}
```

### 4. Session Termination

Logging out:

```typescript
await supabase.auth.signOut();
// Redirects to login page after signout
```

## Secure Access Patterns

### Tenant Isolation

Even though session cookies are shared across subdomains, each tenant's data is isolated using Row Level Security (RLS) policies:

```sql
-- Example RLS policy for tenant isolation (simplified)
CREATE POLICY "Tenant isolation" ON "organizations"
  USING (id = auth.jwt() -> 'tenant_id');
```

### Session Security Measures

1. **JWT Verification**: Sessions use JWTs that are cryptographically signed and verified
2. **Refresh Tokens**: Long-lived sessions use secure refresh tokens to obtain new access tokens
3. **Secure Cookie Attributes**: Production cookies use secure and same-site attributes
4. **Token Expiration**: Access tokens expire regularly, requiring refresh

## Troubleshooting Common Issues

### Session Not Persisting Across Subdomains

- Ensure cookies are set with the correct domain (`.renwu.app` in production)
- Check that the auth middleware is correctly configured
- Verify the Supabase client is initialized properly with cookie handling

### Session Expiring Too Quickly

- Check maxAge setting in cookie configuration
- Ensure refresh tokens are working correctly
- Verify that Supabase is correctly refreshing the session

### CORS Issues with Sessions

- Add necessary CORS headers for cross-subdomain requests
- Ensure API endpoints respect the authentication from different subdomains

## Implementation Reference

The session management is implemented across several files:

- `src/lib/supabase/server.ts`: Main Supabase client configuration
- `middleware.ts`: Session validation and route protection
- `app/auth/login/page.tsx`: Login and session creation
- `app/auth/organizations/page.tsx`: Cross-tenant navigation
