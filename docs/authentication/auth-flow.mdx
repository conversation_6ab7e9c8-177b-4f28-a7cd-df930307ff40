---
title: Authentication Flows
description: Documentation for the multi-tenant authentication flow handling main domain and subdomain authentication with session persistence
---

# Authentication Flow Documentation

## Overview

This document outlines the authentication flow for our multi-tenant application, handling both main domain and subdomain authentication with session persistence across domains.

## Domain Structure

- Main Domain: `renwu.app` (prod) or `localhost:3000` (dev)
- Subdomains: `{organization-id}.renwu.app` (prod) or `{organization-id}.localhost:3000` (dev)

## Authentication Rules

### Main Domain Routes (`renwu.app`)

1. `/auth/login`

   - Only exists on main domain
   - Redirects to `/auth/organizations` if user is authenticated
   - Entry point for unauthenticated users

2. `/auth/organizations`

   - Only exists on main domain
   - Shows list of organizations user has access to
   - If user is member of only 1 organization, automatic redirect to that organization's subdomain homepage `{organization-id}.renwu.app/home`
   - Accessible to authenticated users to switch between organizations without having to login again

### Subdomain Routes (`{organization-id}.renwu.app`)

1. `{organization-id}.renwu.app/*`

   - Accessible only to users authenticated and that belong to this organization, otherwise they should be redirected to the main domain login page `renwu.app/auth/login`

2. `/home`

   - Landing page for authenticated users in an organization
   - Requires user to be a member of the organization

3. `/auth/*`

   - No auth routes should exist on subdomains
   - All auth-related routes should redirect to main domain

## User Profile Creation

During the sign-up process, user profiles are created using a robust pattern that handles various edge cases:

1. **Upsert Pattern**

   - The system uses an upsert operation instead of a simple insert
   - Implemented with Supabase's `upsert` method using `onConflict: "user_id"`
   - This ensures smooth profile creation even if users already exist in auth.users
   - Particularly important for invitation flows where users might already exist

2. **Implementation Details**

   ```typescript
   // Profile creation with upsert logic
   const { data, error } = await supabase.from("profiles").upsert(
     {
       user_id: userId,
       ...profileData,
     },
     {
       onConflict: "user_id",
       returning: "minimal",
     }
   );
   ```

3. **Error Handling**
   - Comprehensive error handling for duplicate profile scenarios
   - Appropriate error messages and status codes returned to the client
   - Logging for debugging authentication flow issues

This approach ensures that profile creation works reliably in all signup scenarios, including direct sign-ups, invitation-based sign-ups, and when users already exist in the system.

## Session Behavior

1. Session Persistence

   - Cookies are shared across subdomains using `.renwu.app` domain (in production)
   - User only needs to log in once to access all organizations
   - Session persists across domain switches

2. Cookie Management
   - Development: No domain specified for cookies to work with localhost
   - Production: Cookies set with `.renwu.app` domain for subdomain sharing

## Flow Diagrams

### Unauthenticated User Flow

<Mermaid
  chart="graph TD
    A[User visits any URL] --> B{Is Authenticated?}
    B -->|No| C[Redirect to /auth/login]
    C --> D[User logs in]
    D --> E[Redirect to /auth/organizations]
    E --> F{Number of Organizations}
    F -->|One| G[Redirect to organization subdomain /home]
    F -->|Multiple| H[Show organization selection]
    H --> I[User selects organization]
    I --> G
"
/>

---

### Authenticated User Flow

<Mermaid chart="graph TD
    A[Authenticated User] --> B{Current Domain}
    B -->|Main Domain| C{Accessing Auth Routes?}
    C -->|Yes| D[Show /auth/organizations]
    C -->|No| E[Normal Flow]

    B -->|Subdomain| F{Has Access?}
    F -->|Yes| G[Allow Access]
    F -->|No| H[Redirect to Main Domain /auth/organizations]

"/>

## Implementation Details

### URL Management

1. Main Domain to Subdomain

```typescript
// When redirecting to an organization
const orgUrl = goToSubdomain(org.subdomain_id, "home");
// Results in: https://{subdomain}.renwu.app/home
```

2. Subdomain to Main Domain

```typescript
// When redirecting back to organization selection
const mainUrl = goToMainDomain("auth/organizations");
// Results in: https://renwu.app/auth/organizations
```

### Access Control

1. Organization Access Check

```typescript
const hasAccess = organizations.some((org) => org.subdomain_id === subdomain);
if (!hasAccess) {
  // Redirect to organization selection on main domain
  return NextResponse.redirect(goToMainDomain("auth/organizations"));
}
```

2. Single Organization Auto-redirect

```typescript
if (organizations.length === 1) {
  // Auto-redirect to the only organization's subdomain
  return NextResponse.redirect(goToSubdomain(organizations[0].subdomain_id, "home"));
}
```

### Link Component Optimization

For cross-domain navigation:

```jsx
// For links to main domain
<Link href={goToMainDomain("auth/organizations")} >
  Switch Organization
</Link>

// For links to subdomain
<Link href={goToSubdomain(org.subdomain_id, "home")} >
  Access
</Link>
```

Disabling prefetch prevents CORS errors when hovering over cross-domain links.
