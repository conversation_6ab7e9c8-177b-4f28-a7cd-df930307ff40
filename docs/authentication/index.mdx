---
title: Authentication
description: Overview of the authentication system in Renwu
---

# Authentication Documentation

Welcome to the authentication documentation for Renwu. This section covers all aspects of our authentication system, from user flows to technical implementation details.

## Topics Covered

- [Authentication Flow](/docs/authentication/auth-flow) - The complete authentication flow for multi-tenant authentication
- [Subdomain Authentication](/docs/authentication/subdomain-auth) - How authentication works across subdomains
- [Session Management](/docs/authentication/session-management) - Details about session persistence and cookie management

## Authentication Architecture

Our authentication system is built on Supabase Auth with custom middleware to handle multi-tenant organization access. The system supports:

- Email/password authentication
- Magic link sign-in
- Social login providers
- Session persistence across subdomains
- Role-based access control

## Getting Started

For developers working on authentication features, start with the [Authentication Flow](/docs/authentication/auth-flow) document to understand the overall system design.

For implementing new features, refer to the appropriate section based on the component you're working on.
