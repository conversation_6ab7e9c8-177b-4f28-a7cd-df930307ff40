---
title: Mock Data System
description: How the mock data system works and how to use it for testing
---

# Mock Data System

## Overview

Renwu implements a dedicated mock data system that helps developers test and demonstrate functionality without affecting production data. This approach allows for safe testing and clear separation between real and demo data, while still providing realistic user experiences in demo and testing environments.

## Architecture

The mock data system is designed with the following key principles:

1. **Clear Separation**: Mock data is stored in dedicated database tables with names prefixed by `mock_`.
2. **ID Range Separation**: Production and mock data IDs use different ranges to prevent conflicts.
3. **Parallel Access**: Database functions fetch both real and mock data when appropriate.
4. **Unified Presentation**: Action functions combine real and mock data for UI components.
5. **Visual Indicators**: The UI includes indicators to identify when mock data is being displayed.

### Database Structure

The mock data system includes the following key tables:

| Mock Table          | Production Equivalent | Purpose                                    |
| ------------------- | --------------------- | ------------------------------------------ |
| `mock_profiles`     | `profiles`            | Store user profile information for testing |
| `mock_team_members` | `team_members`        | Store team membership data for testing     |

### ID Range Management

To prevent overlap between real and mock data:

- Production tables (like `profiles`) use IDs starting from 101
- Mock tables use IDs starting from lower values (1-100)

This clear separation makes it easy to identify the source of data and prevents any accidental mixing of real and mock records.

## Implementation Details

### Database Functions

When working with data that might include mock records, we implement paired database functions:

```typescript
/**
 * Get user profiles by user IDs
 */
export async function getUserProfilesByIds(userIds: string[]) {
  const supabase = await createClient();
  return await supabase
    .from("profiles")
    .select("id, user_id, full_name, display_name, email, avatar_url")
    .in("user_id", userIds);
}

/**
 * Get mock user profiles by user IDs
 */
export async function getMockUserProfilesByIds(userIds: string[]) {
  const supabase = await createClient();
  return await supabase
    .from("mock_profiles")
    .select("id, user_id, full_name, display_name, email, avatar_url")
    .in("user_id", userIds);
}
```

### Action Functions

Action functions combine the real and mock data into a unified format for UI components:

```typescript
// Fetch both real and mock user profiles
const [
  { data: userProfiles, error: userProfilesError },
  { data: mockUserProfiles, error: mockUserProfilesError },
] = await Promise.all([getUserProfilesByIds(userIds), getMockUserProfilesByIds(userIds)]);

// Create a unified map of profiles
const userProfilesMap = [...(userProfiles || []), ...(mockUserProfiles || [])].reduce(
  (acc, profile) => {
    acc[profile.user_id] = profile;
    return acc;
  },
  {} as Record<string, any>
);
```

## Usage

### When to Use Mock Data

Use the mock data system when:

1. **Developing new features** without affecting production data
2. **Testing UI components** with consistent and predictable data
3. **Creating demos** without relying on production data
4. **Performing QA** in a controlled environment

### Extending Mock Data

To extend the mock data system:

1. Create a new mock table with the same structure as its production equivalent
2. Prefix the table name with `mock_`
3. Ensure IDs stay within the mock range (typically 1-100)
4. Create parallel database functions for fetching data
5. Update relevant action functions to combine real and mock data

Example migration for creating a new mock table:

```sql
CREATE TABLE IF NOT EXISTS "mock_projects" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "team_id" UUID REFERENCES "teams" (id),
  "status" TEXT DEFAULT 'active',
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Seed with initial test data
INSERT INTO "mock_projects" (name, description, status)
VALUES
  ('Mock Project 1', 'This is a test project', 'active'),
  ('Mock Project 2', 'Another test project', 'planning');
```

## Best Practices

1. **Clear Indicators**: Always provide visual indicators when displaying mock data
2. **Consistent Structure**: Keep mock tables in sync with production schema changes
3. **Limited Scope**: Only create mock data that's actually needed for testing
4. **Documentation**: Document the purpose and content of mock data tables
5. **ID Ranges**: Respect the ID range separation between mock and real data
6. **Error Handling**: Implement proper error handling for both real and mock data fetching
7. **Performance**: Be mindful of performance implications when fetching both real and mock data

## Testing Considerations

When writing tests that involve the mock data system:

1. **Isolated Tests**: Use dedicated test fixtures instead of the mock tables directly
2. **Clear Expectations**: Tests should know whether they're testing with mock or real data
3. **Clean Up**: Reset mock data to a known state after tests
4. **Avoid Assumptions**: Don't assume specific mock data exists unless you create it
