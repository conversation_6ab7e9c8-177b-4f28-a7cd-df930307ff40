---
title: Local Subdomain Setup
description: Guide for setting up and testing subdomains in your local development environment
---

# Local Subdomain Development Guide

This guide helps you set up and test organization subdomains locally during development.

## Overview

In our application, each organization gets its own subdomain. This document explains how to test this functionality on your local machine.

## Setting up for local subdomain testing

### 1. Edit your hosts file

Add the following entries to your `/etc/hosts` file (requires admin privileges):

```
127.0.0.1 localhost
127.0.0.1 renwu.local
127.0.0.1 test-org.renwu.local
127.0.0.1 another-org.renwu.local
# Add more subdomains as needed for testing
```

**Windows**: The hosts file is located at `C:\Windows\System32\drivers\etc\hosts`  
**Mac/Linux**: The hosts file is located at `/etc/hosts`

### 2. Configure your local environment

Update your `.env.local` file to include:

```
NEXT_PUBLIC_PROD_DOMAIN=renwu.local
NEXT_PUBLIC_DEV_DOMAIN=localhost:3000
```

### 3. Alternative Setup for \*.localhost Subdomains

Modern browsers support wildcard subdomains for `localhost`. This is the easiest way to test subdomains locally without editing your hosts file:

```
NEXT_PUBLIC_PROD_DOMAIN=renwu.app
NEXT_PUBLIC_DEV_DOMAIN=localhost:3000
```

This allows you to access subdomains like:

- http://test-org.localhost:3000
- http://another-org.localhost:3000

### 4. Run the development server

Start your development server with:

```bash
pnpm dev
```

Your Next.js application will be accessible at:

- Main app: http://localhost:3000
- Organization subdomains:
  - http://your-org-subdomain.localhost:3000 (easiest)
  - http://your-org-subdomain.renwu.local:3000 (requires hosts file setup)

## Testing Subdomains

### Prerequisites

1. Create an organization with a subdomain (e.g., "test-org") in your database
2. Add yourself as a member of the organization

### Steps to Test

1. Navigate to the subdomain URL:
   - http://test-org.localhost:3000 (using localhost wildcard - recommended)
   - http://test-org.renwu.local:3000 (using hosts file)
2. You should see the organization dashboard if:

   - The organization with that subdomain exists
   - You are logged in
   - You are a member of that organization

3. Test negative cases:
   - Try accessing a non-existent organization subdomain (should redirect to http://localhost:3000/organizations)
   - Try accessing an organization you're not a member of (should redirect to http://localhost:3000/organizations)
   - Try accessing without being logged in (should redirect to http://localhost:3000/auth/login)

## Advanced Debugging

### Viewing Redirect Logs

Check the console logs in your terminal where Next.js is running. We've added detailed logging to help debug subdomain redirections.

Look for log messages like:

- `Middleware - Hostname: test-org.localhost:3000`
- `Middleware - Subdomain detected: test-org`
- `Redirecting to: http://localhost:3000/organizations`

### Testing Redirects Manually

To test that redirects work properly, you can:

1. Create a test organization with subdomain "test-org"
2. Navigate to http://test-org.localhost:3000
3. In a new private/incognito window, visit http://fake-org.localhost:3000
   - You should be redirected to http://localhost:3000/organizations

## Troubleshooting

### Subdomain Not Recognized

If your subdomain is not being recognized:

1. Make sure you're using the correct format:

   - `http://subdomain.localhost:3000` (not `https`)
   - The subdomain should be a valid organization subdomain_id in the database

2. Check the console logs for:

   - `Middleware - Subdomain detected: null` (indicates subdomain parsing issue)
   - `Organization not found for subdomain: your-subdomain` (indicates database issue)

3. Try accessing with a different browser - some browsers handle subdomains differently

### Redirect Problems

If you're being redirected to the wrong URL (e.g., staying on the subdomain):

1. Make sure your middleware.ts and route.ts files are properly updated with the latest `goToMainDomain` function
2. Check the console logs for the actual redirect URL
3. Clear your browser cache and cookies
4. Try with a private/incognito window

### Cookie Issues

If you experience authentication problems when switching between subdomains:

1. Make sure cookies are being set at the domain level:
   - For localhost: Set with domain `.localhost`
   - For custom domains: Set with domain `.renwu.local`
2. Try enabling Chrome's insecure cookies for localhost:
   - Navigate to chrome://flags/#allow-insecure-localhost
   - Enable "Allow invalid certificates for resources loaded from localhost"
3. Try using incognito/private browsing mode for testing

## Production Considerations

In production, you'll need to:

1. Configure a wildcard DNS record (\*.renwu.app) pointing to your application
2. Ensure SSL certificates cover wildcard subdomains (Let's Encrypt supports this)
3. Configure your hosting provider to handle subdomain routing
4. Make sure cookie settings are properly configured for cross-subdomain authentication
