---
title: Navigation Cache Preservation Fix
description: Solution for React Query cache clearing during programmatic navigation
---

# Navigation Cache Preservation Fix

## Problem Description

Users experienced cache clearing when navigating via sidebar links (programmatic navigation) while browser back/forward navigation preserved cache. This caused:

- Homepage data refetching when returning from project pages via sidebar navigation
- Instant loading when using browser back/forward buttons
- Poor UX with unnecessary loading states

## Root Cause Analysis

The issue occurred because:

1. **Browser Navigation**: Preserves React Query cache as it restores previous page state
2. **Programmatic Navigation**: Next.js App Router treats `router.push()` and `Link` components as fresh page loads
3. **Component Unmounting**: Navigation causes React components to unmount/remount, potentially triggering cache invalidation
4. **Cache Timing**: React Query's `gcTime` and `staleTime` weren't optimized for navigation patterns

## Solution Implementation

### 1. Enhanced React Query Configuration

Updated global cache settings for better navigation UX:

```typescript
// src/lib/react-query-provider.tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 15 * 60 * 1000, // 15 minutes (increased from 10)
      gcTime: 4 * 60 * 60 * 1000, // 4 hours (increased from 2)
      refetchOnMount: false, // Don't refetch if data exists and is not stale
      // CRITICAL: Prevent cache clearing during navigation
      notifyOnChangeProps: "all",
      structuralSharing: true,
    },
  },
});
```

### 2. Navigation Cache Protection Hook

Created `useNavigationCacheProtection` hook to protect cache during navigation:

```typescript
// src/hooks/use-navigation-cache-protection.ts
export function useNavigationCacheProtection() {
  const protectCacheBeforeNavigation = useCallback(() => {
    const allQueries = queryClient.getQueryCache().getAll();
    const now = Date.now();

    allQueries.forEach((query) => {
      if (query.state.data) {
        // Update timestamp to prevent cache eviction
        queryClient.setQueryData(query.queryKey, query.state.data, {
          updatedAt: now,
        });
      }
    });
  }, [queryClient]);

  return { protectCacheBeforeNavigation };
}
```

### 3. Protected Link Component

Created `ProtectedLink` component that automatically protects cache:

```typescript
// src/components/ui/protected-link.tsx
export const ProtectedLink = forwardRef<HTMLAnchorElement, ProtectedLinkProps>(
  ({ children, onClick, ...props }, ref) => {
    const { protectCacheBeforeNavigation } = useNavigationCacheProtection();

    const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
      // Protect cache before navigation
      protectCacheBeforeNavigation();
      onClick?.(e);
    };

    return (
      <Link ref={ref} onClick={handleClick} {...props}>
        {children}
      </Link>
    );
  }
);
```

### 4. Updated Navigation Components

- **Sidebar Navigation**: Updated `nav-main.tsx` to use `ProtectedLink`
- **Project Switcher**: Added cache protection to `nav-team-switcher.tsx`
- **Router Navigation**: Protected `router.push()` calls with cache preservation

### 5. Optimized Cache Configurations

Increased cache times for stable data:

```typescript
// Home page data - increased for navigation UX
home: {
  staleTime: 60 * 60 * 1000, // 1 hour (was 30 minutes)
  gcTime: 4 * 60 * 60 * 1000, // 4 hours (was 2 hours)
},

// Organization data - very stable
organization: {
  staleTime: 4 * 60 * 60 * 1000, // 4 hours (was 2 hours)
  gcTime: 8 * 60 * 60 * 1000, // 8 hours (was 4 hours)
},
```

## Implementation Details

### Before Navigation Protection

```typescript
// In navigation components
const handleNavigation = (href: string) => {
  // Protect cache before navigation
  const allQueries = queryClient.getQueryCache().getAll();
  const now = Date.now();
  
  allQueries.forEach((query) => {
    if (query.state.data) {
      queryClient.setQueryData(query.queryKey, query.state.data, {
        updatedAt: now,
      });
    }
  });
  
  router.push(href);
};
```

### Global Navigation Protection

```typescript
// In QueryClientProvider
useEffect(() => {
  const handleBeforeUnload = () => {
    // Mark all queries as recently accessed before navigation
    const allQueries = queryClient.getQueryCache().getAll();
    const now = Date.now();
    
    allQueries.forEach((query) => {
      if (query.state.data) {
        queryClient.setQueryData(query.queryKey, query.state.data, {
          updatedAt: now,
        });
      }
    });
  };

  window.addEventListener('beforeunload', handleBeforeUnload);
  return () => window.removeEventListener('beforeunload', handleBeforeUnload);
}, [queryClient]);
```

## Expected Results

After implementation:

1. **Instant Navigation**: Homepage loads instantly when returning from project pages
2. **Preserved Cache**: All navigation methods (sidebar, browser buttons) preserve cache
3. **Better UX**: No unnecessary loading states during navigation
4. **Background Updates**: Data still refreshes in background when stale

## Testing Checklist

- [ ] Navigate from homepage to project page via sidebar
- [ ] Return to homepage via sidebar - should load instantly
- [ ] Navigate using browser back/forward - should load instantly
- [ ] Verify cache is preserved across multiple navigation cycles
- [ ] Check that stale data still refreshes in background
- [ ] Test with different project switches

## Rollback Plan

If issues arise, revert these files:
- `src/lib/react-query-provider.tsx`
- `src/hooks/use-navigation-cache-protection.ts`
- `src/components/ui/protected-link.tsx`
- `src/components/navbar/nav-main.tsx`
- `src/components/navbar/nav-team-switcher.tsx`
- `app/home/<USER>/use-home-data.ts`
- `src/lib/query-keys.ts`
