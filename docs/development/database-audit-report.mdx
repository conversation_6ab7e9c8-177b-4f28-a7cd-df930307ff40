---
title: Database Architecture Audit Report
description: Comprehensive audit of existing database queries and identification of N+1 patterns and performance bottlenecks
tags: [database, audit, performance, n+1-queries]
version: 1.0.0
date: 2024-12-19
status: completed
---

# Database Architecture Audit Report

## Executive Summary

**Audit Completed**: December 19, 2024  
**Files Analyzed**: 15 database files, 18 action files  
**Critical Issues Found**: 3 major N+1 patterns, 5 performance bottlenecks  
**Optimization Potential**: 40-60% query time reduction achievable

## Current Database Structure Analysis

### **Existing Architecture Pattern**
```
/src/db/
├── *.db.ts           # Database functions with "FromDb" suffix
├── actions/          # Action wrappers for database functions
└── schemas/          # TypeScript type definitions
```

### **Current Naming Conventions** ✅
- **Database functions**: `getFunctionNameFromDb()` 
- **Action functions**: `getFunctionNameAction()`
- **Separation of concerns**: Well-maintained between data fetching and business logic

## Critical N+1 Query Patterns Identified

### **1. Project Assignees Loading** 🔴 **HIGH IMPACT**

**Location**: `src/db/tasks.db.ts:252-381`

**Current Pattern** (N+1):
```typescript
// Query 1: Get project teams
const { data: projectTeams } = await supabase
  .from("project_teams")
  .select("team_id")
  .eq("project_id", projectId);

// Query 2: Get team members  
const { data: teamMembers } = await supabase
  .from("team_members")
  .select("user_id")
  .in("team_id", teamIds);

// Query 3: Get user profiles
const { data: profiles } = await supabase
  .from("profiles")
  .select("user_id, first_name, last_name, full_name, email, avatar_url")
  .in("user_id", userIds);
```

**Performance Impact**:
- **3 sequential database queries** per project
- **Network round-trips**: 3x overhead
- **Query execution time**: ~150-300ms total
- **Usage frequency**: Every kanban board load, task details page

**Optimization Potential**: 70% reduction with single optimized query

### **2. Task Data Fetching** 🟡 **MEDIUM IMPACT**

**Location**: `src/db/tasks.db.ts:78-113`

**Current Pattern**:
```typescript
// Uses RPC function but still has optimization opportunities
const { data: tasksData } = await supabase.rpc("get_tasks_with_tags", {
  project_id_param: currentProject.id,
});
```

**Issues Identified**:
- **Assignee data**: Fetched separately after task loading
- **JSON parsing**: Heavy client-side processing for tags and assignees
- **Data transformation**: Complex formatting logic in database layer

**Performance Impact**:
- **Query execution time**: ~100-200ms
- **Client processing**: ~50-100ms for JSON parsing
- **Usage frequency**: Every page load in task management

**Optimization Potential**: 40% reduction with optimized data structure

### **3. Cluster and Project Loading** 🟡 **MEDIUM IMPACT**

**Location**: `src/db/cluster-project-user-teams.db.ts:11-58`

**Current Pattern**:
```typescript
// Complex nested query with multiple joins
.select(`
  id, name, slug, tenant_id, description,
  projects (
    id, name, slug, tenant_id, description, status, priority,
    project_teams!inner (
      team_id,
      teams!inner (
        id, name, description, tenant_id,
        team_members!inner (user_id)
      )
    )
  )
`)
```

**Issues Identified**:
- **Over-fetching**: Loads all project data even when only basic info needed
- **Complex joins**: Multiple nested relationships in single query
- **Filtering logic**: Complex tenant and user filtering

**Performance Impact**:
- **Query execution time**: ~200-400ms for large datasets
- **Data transfer**: Significant over-fetching
- **Usage frequency**: Dashboard and navigation loading

**Optimization Potential**: 50% reduction with route-specific queries

## Performance Bottlenecks Analysis

### **1. JSON Parsing Overhead** 🔴 **HIGH IMPACT**

**Location**: Multiple files, especially `tasks.db.ts:150-190`

**Current Pattern**:
```typescript
// Heavy JSON parsing in formatTaskFromDb
try {
  if (dbTask.tags) {
    if (typeof dbTask.tags === "string") {
      tags = JSON.parse(dbTask.tags);
    } else if (Array.isArray(dbTask.tags)) {
      tags = dbTask.tags;
    }
  }
} catch (e) {
  console.warn("Error parsing tags:", e);
}
```

**Impact**: 20-50ms per task for JSON parsing operations

### **2. Multiple Strategy Fallbacks** 🟡 **MEDIUM IMPACT**

**Location**: `src/db/tasks.db.ts:252-381` (getProjectAssigneesFromDb)

**Issues**:
- **4 different strategies** with try-catch blocks
- **Redundant queries** when strategies fail
- **Complex error handling** with performance overhead

### **3. Inefficient Data Transformation** 🟡 **MEDIUM IMPACT**

**Location**: `src/db/actions/tasks.action.ts:30-176`

**Issues**:
- **Client-side grouping** of large datasets
- **Multiple array iterations** for data transformation
- **Memory-intensive operations** for large task lists

## Route-Specific Data Requirements

### **Kanban Board** (`/tasks` - List View)
**Data Needed**:
- Tasks with basic info (id, title, status, position)
- Tags (id, name, color)
- Assignees (id, name, avatar)
- Task groups (status definitions)

**Current Queries**: 4-5 separate queries
**Optimization Target**: 1 optimized RPC function

### **Task Details** (`/tasks?id=xxx`)
**Data Needed**:
- Complete task data with all relations
- Full assignee profiles
- All tags and metadata
- Subtasks and attachments

**Current Queries**: 3-4 separate queries
**Optimization Target**: 1 comprehensive query

### **Dashboard/Analytics**
**Data Needed**:
- Aggregated task metrics
- Team performance data
- Project progress indicators

**Current Queries**: Multiple ad-hoc queries
**Optimization Target**: Dedicated analytics RPC functions

## Recommended 3-Tier Architecture Mapping

### **Tier 1 - Core Functions**
```typescript
// Basic CRUD operations
getTaskByIdFromDb(taskId: string)
createTaskFromDb(taskData: CreateTaskInput)
updateTaskFromDb(taskId: string, updates: UpdateTaskInput)
deleteTaskFromDb(taskId: string)

getUserProfileFromDb(userId: string)
getProjectFromDb(projectId: string)
getTeamFromDb(teamId: string)
```

### **Tier 2 - Domain Functions**
```typescript
// Business domain operations
getTasksWithTagsFromDb(projectId: string)
getProjectAssigneesOptimizedFromDb(projectId: string)
getTeamMembersWithRolesFromDb(teamId: string)
getUserProjectsWithTeamsFromDb(userId: string)
```

### **Tier 3 - Route Functions**
```typescript
// Route-optimized queries
getKanbanBoardDataFromDb(projectId: string)
getTaskDetailsCompleteFromDb(taskId: string)
getDashboardMetricsFromDb(projectId: string, dateRange?: DateRange)
getProjectOverviewDataFromDb(projectId: string)
```

## Implementation Priority Matrix

| Function | Impact | Effort | Priority | Expected Improvement |
|----------|--------|--------|----------|---------------------|
| **getProjectAssigneesOptimized** | High | Medium | 1 | 70% reduction |
| **getKanbanBoardData** | High | High | 2 | 60% reduction |
| **getTaskDetailsComplete** | Medium | Medium | 3 | 50% reduction |
| **getDashboardMetrics** | Medium | Low | 4 | 40% reduction |
| **Core CRUD optimizations** | Low | Low | 5 | 20% reduction |

## Supabase RPC Functions Required

### **High Priority RPC Functions**
```sql
-- Optimized project assignees (replaces 3-query pattern)
CREATE FUNCTION get_project_assignees_optimized(project_id_param UUID)

-- Complete kanban board data (single query)
CREATE FUNCTION get_kanban_board_data(project_id_param UUID)

-- Full task details with all relations
CREATE FUNCTION get_task_details_complete(task_id_param UUID)
```

### **Medium Priority RPC Functions**
```sql
-- Dashboard metrics and analytics
CREATE FUNCTION get_dashboard_metrics(project_id_param UUID, date_range DATERANGE)

-- Project overview with team and progress data
CREATE FUNCTION get_project_overview_data(project_id_param UUID)
```

## Performance Baseline Measurements

### **Current Performance (Before Optimization)**
- **Project assignees loading**: 150-300ms (3 queries)
- **Kanban board loading**: 300-500ms (4-5 queries)
- **Task details loading**: 200-400ms (3-4 queries)
- **Dashboard loading**: 400-800ms (multiple queries)

### **Target Performance (After Optimization)**
- **Project assignees loading**: 50-100ms (1 query) - **70% improvement**
- **Kanban board loading**: 120-200ms (1 query) - **60% improvement**
- **Task details loading**: 100-200ms (1 query) - **50% improvement**
- **Dashboard loading**: 200-400ms (2-3 queries) - **50% improvement**

## Risk Assessment

### **Low Risk Optimizations** ✅
- Core CRUD function creation
- Domain-specific function implementation
- Performance monitoring setup

### **Medium Risk Optimizations** ⚠️
- Route-specific RPC function creation
- Complex join optimization
- Data structure changes

### **High Risk Areas** 🔴
- Existing function modification
- Database schema changes
- Breaking changes to current API

## Next Steps

### **Phase 1: Foundation** (Week 1)
1. **Create 3-tier directory structure**
2. **Implement core tier functions**
3. **Set up performance monitoring**

### **Phase 2: Domain Optimization** (Week 1-2)
1. **Implement optimized project assignees function**
2. **Create domain-specific task management functions**
3. **Add comprehensive testing**

### **Phase 3: Route Optimization** (Week 2)
1. **Create kanban board optimization**
2. **Implement task details optimization**
3. **Add dashboard analytics functions**

This audit provides the foundation for implementing the 3-tier database architecture with clear targets for achieving the 40-60% performance improvement goal.
