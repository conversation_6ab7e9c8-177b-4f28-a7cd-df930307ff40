---
title: React Query Cache Key Standardization
description: Comprehensive standardization of React Query cache keys for optimal performance and consistency
---

# React Query Cache Key Standardization

## Overview

This document outlines the comprehensive standardization of React Query cache keys across the application to eliminate cache fragmentation, improve performance, and ensure consistency.

## Problems Identified

### 🔴 Critical Issues

1. **Duplicate Organization Keys**: `["current-organization"]` vs `["home", "current-organization"]`
2. **Inconsistent User Keys**: `["user-profile"]` vs `["currentUser"]`
3. **Mixed Naming Conventions**: camelCase vs kebab-case vs snake_case
4. **Cache Fragmentation**: Similar data cached under different keys

### 🟡 Performance Issues

1. **Suboptimal Stale Times**: Some queries had unnecessarily short cache times
2. **Inconsistent Project Data**: Multiple keys for similar project data

## Standardization Rules

### Naming Convention

- **Use kebab-case** for all query keys
- **Use hierarchical structure** where appropriate
- **Keep keys consistent** across the application
- **Avoid duplication** of similar data under different keys

### Cache Configuration Guidelines

- **User data**: 1 hour stale time (rarely changes)
- **Organization data**: 2 hours stale time (very rarely changes)
- **Project data**: 1 hour stale time (rarely changes)
- **Task data**: 10 minutes stale time (changes moderately)
- **Tags/Assignees**: 15 minutes stale time (changes infrequently)
- **Completed tasks**: 30 minutes stale time (changes less frequently)

## Changes Made

### User-Related Keys

| **Before**         | **After**              | **Files Updated**                                          |
| ------------------ | ---------------------- | ---------------------------------------------------------- |
| `["user-profile"]` | `["user", "profile"]`  | ✅ Kept optimal config                                     |
| `["currentUser"]`  | `["user", "profile"]`  | use-crud-tasks.tsx, tasks-provider.tsx, client-wrapper.tsx |
| `["userProjects"]` | `["user", "projects"]` | client-project-switcher.tsx, home-project-list-refresh.tsx |

### Organization-Related Keys

| **Before**                         | **After**          | **Files Updated**                               |
| ---------------------------------- | ------------------ | ----------------------------------------------- |
| `["current-organization"]`         | `["organization"]` | home-user-profile.tsx                           |
| `["home", "current-organization"]` | `["organization"]` | use-home-data.ts, home-project-list-refresh.tsx |

### Task-Related Keys

| **Before**                         | **After**                             | **Files Updated**          |
| ---------------------------------- | ------------------------------------- | -------------------------- |
| `["tasks-and-groups", projectId]`  | `["tasks", "list", projectId]`        | client-wrapper.tsx         |
| `["taskGroups", projectId]`        | `["tasks", "groups", projectId]`      | use-tasks-data.tsx         |
| `["completed-tasks", projectSlug]` | `["tasks", "completed", projectSlug]` | completed-tasks-client.tsx |

### Project-Related Keys

| **Before**                         | **After**                             | **Files Updated**                      |
| ---------------------------------- | ------------------------------------- | -------------------------------------- |
| `["tags", projectId]`              | `["project", "tags", projectId]`      | use-crud-tasks.tsx, client-wrapper.tsx |
| `["projectAssignees", projectId]`  | `["project", "assignees", projectId]` | use-crud-tasks.tsx, client-wrapper.tsx |
| `["project-tags", projectId]`      | `["project", "tags", projectId]`      | client-wrapper.tsx                     |
| `["project-assignees", projectId]` | `["project", "assignees", projectId]` | client-wrapper.tsx                     |

## Performance Improvements

### Cache Time Optimizations

1. **Completed Tasks**: Increased from 5 minutes → 30 minutes stale time
2. **User Projects**: Maintained optimal 1 hour stale time
3. **Organization Data**: Maintained optimal 2 hours stale time
4. **User Profile**: Maintained optimal 1 hour stale time
5. **🚀 MAJOR: Task Data gcTime**: Extended from 30 minutes → **2 hours** for navigation UX
6. **🚀 MAJOR: Project Metadata gcTime**: Extended from 30 minutes → **2 hours** for navigation UX

### Cache Deduplication

- **Organization data** now uses single `["organization"]` key instead of multiple variants
- **User profile** data consolidated under `["user", "profile"]`
- **Project metadata** consistently uses `["project", "tags/assignees", projectId]` pattern

## Standardized Query Keys Structure

```typescript
// User-related queries
["user", "profile"][("user", "projects")][ // User profile data // User's accessible projects
  // Organization-related queries
  "organization"
][ // Current organization data
  // Home page specific queries
  ("home", "user-team-projects")
][ // Team-filtered projects for home page
  // Task-related queries
  ("tasks", "list", projectId)
][("tasks", "groups", projectId)][("tasks", "completed", projectSlug)][ // Task list for project // Task groups/columns // Completed tasks
  // Project-related queries
  ("project", "tags", projectId)
][("project", "assignees", projectId)]; // Project tags // Project assignees
```

## Benefits Achieved

### 🚀 Performance

- **Eliminated cache fragmentation** - no more duplicate data under different keys
- **Optimized stale times** - longer cache retention for rarely-changing data
- **Reduced API calls** - better cache hit rates with consistent keys
- **🎯 MAJOR: Extended gcTime to 2 hours** - project data persists across navigation
- **⚡ Instant project switching** - cached data available when returning to projects

### 🔧 Maintainability

- **Consistent naming** - all keys follow kebab-case hierarchical pattern
- **Predictable structure** - developers can easily find and use correct keys
- **Centralized configuration** - cache settings defined in `/src/lib/query-keys.ts`

### 🐛 Bug Prevention

- **No more cache misses** due to key inconsistencies
- **Proper cache invalidation** - refresh functions now target correct keys
- **Type safety** - standardized key structure prevents typos

## Verification Checklist

- [x] All query keys follow kebab-case convention
- [x] No duplicate cache keys for same data
- [x] Cache invalidation uses correct standardized keys
- [x] Optimal stale times preserved from best-performing implementations
- [x] All components use consistent query key patterns
- [x] Performance optimizations maintained during migration

## Navigation UX Optimization Strategy

### The Problem

**Before**: Short gcTime (30 minutes) caused cache eviction

1. User visits Project A → data cached with 30min gcTime
2. User navigates to homepage or Project B → Project A cache countdown starts
3. After 30 minutes → Project A cache **garbage collected**
4. User returns to Project A → **full refetch required** (poor UX!)

### The Solution

**After**: Extended gcTime (2 hours) for persistent navigation

1. User visits Project A → data cached with **2-hour gcTime**
2. User navigates away → Project A cache **persists**
3. User returns within 2 hours → **instant navigation** with cached data
4. Background refresh if data is stale (10-15 min staleTime)

### Benefits

- **⚡ Instant navigation** between projects
- **📉 Reduced API calls** when switching between projects
- **🎯 Better UX** for multi-project workflows
- **🔄 Fresh data** via background refresh when needed

## Future Maintenance

1. **Use the standardized query keys** from `/src/lib/query-keys.ts`
2. **Follow the naming convention** when adding new queries
3. **Verify cache invalidation** uses correct hierarchical keys
4. **Test cache behavior** to ensure no regressions
5. **Monitor memory usage** in multi-project scenarios (2-hour cache retention)
