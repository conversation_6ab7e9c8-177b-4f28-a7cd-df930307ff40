---
title: Kanban Board
description: A detailed guide about the Kanban board and its optimizations
---

# What makes the Kanban board so optimized and smooth?

## Memoization Everywhere

- Components are wrapped with `memo()` to prevent unnecessary re-renders
- `useMemo` hooks for computed values like task IDs and styles
- `useCallback` for event handlers to maintain referential equality

## Efficient State Management

- Single state updates for complex operations (moving tasks between columns)
- Atomic state updates that handle multiple concerns at once
- Careful state normalization to avoid redundancy

## Optimized Drag and Drop

- Using `@dnd-kit` with proper configuration for smooth drag operations
- Activation constraints (minimum distance of 2px) to prevent accidental drags
- Hardware-accelerated transforms for smooth animations

## Smart Rendering Strategies

- Component splitting to isolate re-renders (`KanbanColumn` as separate component)
- Lazy loading for `SortableTask` components with proper Suspense boundaries
- Skeleton loading states during component loading

## Performance-Focused DOM Operations

- Using CSS transforms instead of layout properties for animations
- `will-change` properties to hint browser about upcoming animations
- Touch manipulation optimizations for mobile devices

## Efficient Data Structures

- Dummy tasks to handle empty states without conditional rendering
- Proper key usage for React reconciliation
- Immutable data patterns to avoid unnecessary renders

## Reduced Layout Thrashing

- Fixed heights where possible instead of dynamic calculations
- Batched DOM operations through <PERSON>act's reconciliation
- Proper overflow handling to avoid reflows

## Optimized Event Handling

- Debounced event handlers for frequent events
- Event delegation patterns to reduce event listener count
- Careful management of event propagation with `stopPropagation()`

## Smart UI/UX Design

- Visual feedback during drag operations (opacity changes, z-index adjustments)
- Smooth transitions between states
- Proper loading states and fallbacks
