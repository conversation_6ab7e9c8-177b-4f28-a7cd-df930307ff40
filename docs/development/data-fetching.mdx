---
title: Data Fetching Patterns
description: Best practices for implementing data fetching in Next.js 15 with server and client caching strategies
---

# Data Fetching Patterns in Next.js 15

## Overview

Modern Next.js applications can leverage both server-side and client-side caching to optimize performance, reduce database load, and improve user experience. This guide explains the recommended patterns for implementing a complete data flow from database to UI.

## Architecture Layers

### 1. Database Access Layer (`src/db/`)

Database-specific code that directly interacts with your data source.

```ts
// src/db/task.ts
import { createClient } from "@/lib/supabase/server";
import { Task } from "@/types";

export async function getTasksFromDb(boardId: string): Promise<Task[]> {
  const supabase = await createClient();

  const { data, error } = await supabase.from("tasks").select("*").eq("board_id", boardId);

  if (error) throw new Error(error.message);
  return data;
}

export async function updateTaskStatusInDb(taskId: string, status: string) {
  const supabase = await createClient();

  const { error } = await supabase.from("tasks").update({ status }).eq("id", taskId);

  if (error) throw new Error(error.message);
  return true;
}
```

### 2. Server Actions Layer (`app/*/actions/`)

Business logic and server-side caching layer with structured responses.

```ts
// app/board/actions/task.action.ts
"use server";

import { getTasksFromDb, updateTaskStatusInDb } from "@/db/task";
import { unstable_cache } from "next/cache";
import { revalidatePath } from "next/cache";

// Server-side caching with Next.js unstable_cache
export const getTasksAction = unstable_cache(
  async (boardId: string) => {
    try {
      const tasks = await getTasksFromDb(boardId);
      return { success: true, data: tasks };
    } catch (error) {
      console.info("Failed to fetch tasks:", error);
      return { success: false, error: "Failed to load tasks" };
    }
  },
  // Cache key configuration
  ["tasks"],
  { revalidate: 60 } // Cache for 60 seconds
);

export async function updateTaskStatusAction(taskId: string, status: string, boardId: string) {
  try {
    await updateTaskStatusInDb(taskId, status);

    // Invalidate the cache and revalidate the board page
    revalidatePath(`/board/${boardId}`);

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: "Failed to update task status",
    };
  }
}
```

### 3. TanStack Query Provider Setup

Set up TanStack Query to manage client-side state:

```tsx
// src/components/providers/query-provider.tsx
"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useState, type ReactNode } from "react";

export function QueryProvider({ children }: { children: ReactNode }) {
  // Create a client for each browser session
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            refetchOnWindowFocus: false,
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === "development" && <ReactQueryDevtools />}
    </QueryClientProvider>
  );
}
```

### 4. Custom Hooks for Data Access

Create reusable hooks to encapsulate query logic:

```tsx
// app/dashboard/hooks/use-dashboard-metrics.ts
"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getDashboardMetricsAction, updateMetricAction } from "../actions/metrics.action";
import { toast } from "sonner";
import { type DashboardMetrics } from "@/types/dashboard";

export function useDashboardMetrics() {
  const queryClient = useQueryClient();

  // Query for fetching dashboard metrics
  const metricsQuery = useQuery({
    queryKey: ["dashboard", "metrics"],
    queryFn: async () => {
      const result = await getDashboardMetricsAction();
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
  });

  // Mutation for updating a metric
  const updateMetricMutation = useMutation({
    mutationFn: async ({ metricId, value }: { metricId: string; value: number }) => {
      const result = await updateMetricAction(metricId, value);
      if (!result.success) throw new Error(result.error);
      return result;
    },
    // Optimistic updates
    onMutate: async ({ metricId, value }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["dashboard", "metrics"] });

      // Snapshot the previous value
      const previousMetrics = queryClient.getQueryData<DashboardMetrics>(["dashboard", "metrics"]);

      // Optimistically update the UI
      if (previousMetrics) {
        queryClient.setQueryData<DashboardMetrics>(["dashboard", "metrics"], {
          ...previousMetrics,
          items: previousMetrics.items.map((item) =>
            item.id === metricId ? { ...item, value } : item
          ),
        });
      }

      return { previousMetrics };
    },
    onError: (err, variables, context) => {
      // Revert to the previous state if there was an error
      if (context?.previousMetrics) {
        queryClient.setQueryData(["dashboard", "metrics"], context.previousMetrics);
      }
      toast.error(err.message || "Failed to update metric");
    },
    onSuccess: () => {
      toast.success("Metric updated successfully");
    },
    onSettled: () => {
      // Always invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["dashboard", "metrics"] });
    },
  });

  return {
    metrics: metricsQuery.data,
    isLoading: metricsQuery.isLoading,
    isError: metricsQuery.isError,
    error: metricsQuery.error,
    updateMetric: updateMetricMutation.mutate,
    isUpdating: updateMetricMutation.isPending,
  };
}
```

### 5. Client Component Implementation

Use the custom hook in your components:

```tsx
// app/dashboard/_components/metrics-section.tsx
"use client";

import { useDashboardMetrics } from "../hooks/use-dashboard-metrics";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function MetricsSection() {
  const { metrics, isLoading, isError, updateMetric, isUpdating } = useDashboardMetrics();

  // Loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <p className="text-destructive">Failed to load dashboard metrics</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {metrics?.items.map((metric) => (
        <MetricCard
          key={metric.id}
          metric={metric}
          onUpdate={(value) => updateMetric({ metricId: metric.id, value })}
          isUpdating={isUpdating}
        />
      ))}
    </div>
  );
}
```

## How Caching Works

### Server-Side Caching

1. **Implementation**: Uses `unstable_cache` from Next.js
2. **Purpose**: Reduces database queries for frequently accessed data
3. **Invalidation**: Via `revalidatePath` when data changes
4. **Benefits**:
   - Reduces database load
   - Improves server response time
   - Works automatically with both SSR and RSC (React Server Components)

### Client-Side Caching

1. **Implementation**: Uses TanStack Query (React Query)
2. **Purpose**: Provides a responsive UI and optimistic updates
3. **Invalidation**: Via `queryClient.invalidateQueries()` when data changes
4. **Benefits**:
   - Prevents redundant network requests
   - Enables optimistic UI updates
   - Handles loading/error states
   - Provides automatic background refetching

## Best Practices

### 1. Separate Concerns

- Keep DB access logic in separate files
- Use server actions for data fetching/mutations
- Create custom hooks to abstract query logic
- Build UI components that consume the hooks

### 2. Type Safety

- Define strict types for all data structures
- Use discriminated unions for success/error responses
- Ensure type consistency between server and client

### 3. Error Management

- Implement comprehensive error handling at each layer
- Provide meaningful error messages to users
- Log detailed errors server-side for debugging

### 4. Performance Optimization

- Set appropriate `staleTime` to reduce unnecessary refetches
- Implement `Suspense` boundaries for progressive loading
- Use skeleton loaders for better user experience during loading
- Implement optimistic updates for responsive UI

### 5. Query Invalidation Strategies

- Invalidate related queries after mutations complete
- Use appropriate query keys with proper scoping
- Consider relationships between data entities when invalidating

## Common Pitfalls

1. **Over-fetching**: Fetching too much data or too frequently
2. **Insufficient error handling**: Not accounting for all failure modes
3. **Cache key collisions**: Using overly simple cache keys
4. **Stale data**: Not invalidating related queries after mutations
5. **Missing loading states**: Poor UX during loading or error states

## When to Use This Pattern

This multi-layered caching approach is ideal for:

1. **Real-time collaborative features**: Kanban boards, task managers, chat applications
2. **Data-heavy dashboards**: Analytics, reporting, monitoring
3. **Applications with frequent updates**: Social feeds, content management
