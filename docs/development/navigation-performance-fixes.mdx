---
title: Navigation Performance Optimization
description: Comprehensive fixes for 5-second navigation delays in the Renwu application
---

# Navigation Performance Optimization

## Problem Analysis

### Root Causes Identified

1. **Blocking Server Components**: Pages making multiple sequential database calls before rendering
2. **Direct Database Calls in Client Components**: Components bypassing React Query cache
3. **Missing Progressive Loading**: No proper Suspense boundaries for data fetching

### Performance Impact

- **Before**: 5-second navigation delays due to blocking database calls
- **Target**: \<100ms instant navigation with progressive loading

## Implementation Changes

### 1. Tasks Page Optimization

**File**: `app/clusters/[cluster-slug]/projects/[project-slug]/tasks/page.tsx`

**Before**: 6 sequential database calls blocking navigation

```typescript
// ❌ BLOCKING: All data fetched before page renders
const { tasks, taskGroups } = await getTasksAndGroupsAction(props);
const [tags, projectAssignees, userProfile, userId] = await Promise.all([
  getTagsByProjectAction(project.id),
  getProjectAssigneesAction(project.id),
  getUserProfileAction().catch(() => null),
  getUserId(false).catch(() => null),
]);
```

**After**: Minimal server-side data, React Query for everything else

```typescript
// ✅ INSTANT: Only essential data for routing
const project = await getProjectBySlugAndTenantId(params["project-slug"], tenantId);

return (
  <Suspense fallback={<TasksPageSkeleton />}>
    <ClientWrapper projectId={project.id} />
  </Suspense>
);
```

### 2. Client Wrapper Optimization

**File**: `app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_components/client-wrapper.tsx`

**Changes**:

- Converted to client component with React Query
- Parallel data fetching with proper caching
- Progressive loading states

```typescript
// ✅ React Query with optimized caching
const { data: tasksData, isLoading } = useQuery({
  queryKey: ["tasks-and-groups", projectId],
  queryFn: async () => {
    /* ... */
  },
  staleTime: 10 * 60 * 1000, // 10 minutes
  refetchOnMount: false,
});
```

### 3. User Profile Component Fix

**File**: `app/home/<USER>/home-user-profile.tsx`

**Before**: Blocking server component

```typescript
// ❌ BLOCKING: Server-side database calls
export async function UserProfile() {
  const user = await getUserProfileAction();
  const organization = await getCurrentOrganizationAction();
```

**After**: Client component with React Query

```typescript
// ✅ INSTANT: Client-side with caching
export function UserProfile() {
  const { data: user, isLoading } = useQuery({
    queryKey: ["user-profile"],
    queryFn: getUserProfileAction,
    staleTime: 15 * 60 * 1000,
  });
```

### 4. User Avatar Component Fix

**File**: `src/components/origin-ui/user-avatar.tsx`

**Before**: Direct Supabase calls in useEffect

```typescript
// ❌ BLOCKING: Direct database calls on every render
useEffect(() => {
  const supabase = createClient();
  const { data } = await supabase.from("profiles").select("*");
}, []);
```

**After**: React Query integration

```typescript
// ✅ CACHED: Shared cache with other components
const { data: user, isLoading } = useQuery({
  queryKey: ["user-profile"],
  queryFn: getUserProfileAction,
  staleTime: 15 * 60 * 1000,
});
```

## Performance Benefits

### 1. Instant Navigation Path

1. **Click menu item** → Immediate navigation (0ms)
2. **Show loading.tsx** → Skeleton UI appears instantly
3. **Progressive loading** → Content appears as data loads
4. **Background revalidation** → Fresh data without blocking

### 2. Cached Data Path

1. **Click menu item** → Immediate navigation (0ms)
2. **Show cached data** → Complete page appears instantly
3. **Background refresh** → Data updates if stale

### 3. Optimized Caching Strategy

```typescript
// Standardized React Query configuration
{
  staleTime: 10 * 60 * 1000,    // 10 minutes for tasks
  staleTime: 15 * 60 * 1000,    // 15 minutes for user/org data
  gcTime: 30 * 60 * 1000,       // 30 minutes cache retention
  refetchOnWindowFocus: false,   // Prevent unnecessary refetches
  refetchOnMount: false,         // Use cached data if available
  retry: 1,                      // Fast failure feedback
}
```

## Loading States Implementation

### 1. Page-Level Loading

- `loading.tsx` files for instant skeleton display
- Proper Suspense boundaries around data-dependent components

### 2. Component-Level Loading

- Progressive loading within components
- Skeleton components matching actual content structure

### 3. Error Handling

- Graceful error states with retry options
- Non-blocking error handling for non-critical data

## Monitoring & Validation

### Performance Metrics to Track

1. **Time to First Byte (TTFB)**: Should be \<100ms
2. **First Contentful Paint (FCP)**: Should be \<200ms
3. **Largest Contentful Paint (LCP)**: Should be \<500ms
4. **Cumulative Layout Shift (CLS)**: Should be \<0.1

### Testing Checklist

- [ ] Navigation feels instant (\<100ms)
- [ ] Loading states appear immediately
- [ ] Cached pages load instantly
- [ ] Background revalidation works
- [ ] Error states are handled gracefully
- [ ] No console errors or warnings

## Future Optimizations

### 1. Prefetching Strategy

- Implement route prefetching for common navigation paths
- Prefetch data for likely next pages

### 2. Server-Side Caching

- Implement `unstable_cache` for server components
- Add Redis caching layer for frequently accessed data

### 3. Code Splitting

- Dynamic imports for heavy components
- Route-based code splitting optimization

## Rollback Plan

If issues arise, revert these files:

1. `app/clusters/[cluster-slug]/projects/[project-slug]/tasks/page.tsx`
2. `app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_components/client-wrapper.tsx`
3. `app/home/<USER>/home-user-profile.tsx`
4. `src/components/origin-ui/user-avatar.tsx`

All changes maintain 100% API compatibility and can be safely reverted.
