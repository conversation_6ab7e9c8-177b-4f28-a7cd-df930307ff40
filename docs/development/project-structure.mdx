---
title: Project Structure
description: File and directory organization for the multi-tenant application with subdomain routing
---

# Project Structure

This document outlines the file structure for our multi-tenant Next.js application with subdomain-based routing.

## Root Level Files

```
.env.local                      # Local environment variables
.env.production                 # Production environment variables
middleware.ts                   # Subdomain routing middleware
next.config.ts                  # Next.js configuration
package.json                    # Dependencies
pnpm-workspace.yaml             # PNPM workspaces configuration
source.config.ts                # Fumadocs source configuration
tsconfig.json                   # TypeScript config
mdx-components.tsx              # MDX component configuration
postcss.config.mjs              # PostCSS configuration
eslint.config.mjs               # ESLint configuration
components.json                 # Shadcn UI components configuration
```

## App Directory (Next.js App Router)

```
app/
  favicon.ico                   # Site favicon
  globals.css                   # Global styles
  layout.config.tsx             # Layout configuration
  layout.tsx                    # Root layout component
  not-found.tsx                 # 404 not found page
  page.tsx                      # Root page

  # API Routes
  api/
    search/
      route.ts                  # Search API route for docs

  # Authentication Pages
  auth/
    layout.tsx                  # Auth layout wrapper
    confirm/                    # Email confirmation
    error/                      # Auth error handling
    forgot-password/            # Password recovery
    login/                      # Login page
    organizations/              # Organizations management
    sign-up/                    # Registration
    sign-up-success/            # Registration success
    update-password/            # Password update

  # Documentation
  docs/
    [[...slug]]/                # Documentation pages
    access-denied/              # Auth-protected docs page

  # Cluster & Project Routes
  [cluster-slug]/
    [project-slug]/
      dashboard/                # Project dashboard

  # Demo Section
  demo/
    (analytics)/                # Analytics demo pages
    (delivery)/                 # Delivery demo pages
    (discovery)/                # Discovery demo pages
    (homepage)/                 # Homepage demo pages
    (overview)/                 # Overview demo pages
    (settings)/                 # Settings demo pages

  # Homepage
  (homepage)/
    home/                       # Main homepage
```

## Source Directory

This codebase leverages a src directory for core application code:

```
src/
  # Components
  components/
    auth/                       # Authentication components
    event-calendar/             # Calendar components
    landing-page/               # Landing page components
    mdx/                        # Markdown rendering components
    navbar/                     # Navigation components
    origin-ui/                  # Original UI components
    skeletons/                  # Loading skeleton components
    svg/                        # SVG icons and graphics
    ui/                         # Shadcn UI components
    avatar-mock.tsx             # Mock avatar component
    theme-provider.tsx          # Theme context provider

  # Database Access
  db/
    actions/                    # Database action helpers
    schema/                     # Database schema definitions
    organization.db.ts          # Organization database queries
    organization-member.db.ts   # Organization member database queries
    user.db.ts                  # User database queries
    cluster-project.db.ts       # Cluster and project database queries
    tenant-context.db.ts        # Multi-tenant context handling
    team.db.ts                  # Team database queries (including mock data functions)

  # React Hooks
  hooks/
    use-active-menu.ts          # Active menu state hook
    use-mobile.ts               # Mobile detection hook

  # Utility Libraries
  lib/
    supabase/                   # Supabase client utilities
    utils.ts                    # Common utility functions

  # Application Data
  data/                         # Static data and fixtures

  # TypeScript Definitions
  types/                        # TypeScript type definitions
```

## Library Directory

```
lib/
  source.ts                     # Fumadocs library configuration
```

## Public Assets

```
public/
  logo/                         # Application logos
  # Other static assets like images, fonts, etc.
```

## Documentation

```
docs/
  architecture/                 # System architecture documentation
  development/                  # Development guides
    project-structure.mdx       # This file - project structure docs
    data-fetching.mdx           # Data fetching patterns
    local-subdomain-setup.mdx   # Local subdomain setup
    mock-data-system.mdx        # Mock data implementation docs
```

## Additional Directories

```
documentations/                 # Raw documentation markdown files
memory-bank/                    # Memory bank for context storage
.source/                        # Generated Fumadocs source files
```

## Database Structure

The application uses PostgreSQL via Supabase with the following key tables:

```
# Core Tables
profiles                        # User profiles
organizations                   # Organizations/tenants
organization_members            # Organization membership
teams                           # Teams within organizations
team_members                    # Team membership
projects                        # Projects
clusters                        # Project clusters

# Mock Data Tables
mock_profiles                   # Mock user profiles for testing (IDs: 1-100)
mock_team_members               # Mock team members for testing (IDs: 1-100)
```

### Mock Data Structure

The application implements a dedicated mock data system with separate tables:

- Production tables use IDs starting from 101
- Mock tables use IDs from 1-100
- Database functions handle fetching from both real and mock tables
- Action functions combine data for UI components
- See [Mock Data System](/docs/development/mock-data-system) for details

This structure follows modern Next.js 15+ App Router conventions, with a clear separation between the app router pages and the core application code in the src directory. The application uses a hybrid approach with both the /app directory (for routing) and a /src directory (for components, hooks, and utilities).

## Special Files

### Route Files

- `page.tsx`: Defines a route segment and is the primary UI for a route
- `layout.tsx`: Shared UI for a segment and its children
- `loading.tsx`: Loading UI for a segment and its children during data fetching
- `error.tsx`: Error UI for a segment and its children when errors occur
- `not-found.tsx`: UI for 404 errors

### Loading States

Next.js automatically uses `loading.tsx` files during data loading. We implement a consistent loading state pattern using Skeleton components:

```tsx
// Example loading.tsx component
import { Skeleton } from "@/components/ui/skeleton";

export default function PageLoading() {
  return (
    <div className="flex flex-col gap-6">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-[200px]" />
          <Skeleton className="h-9 w-[120px]" />
        </div>
      </div>

      {/* Content skeleton matching the actual page structure */}
      <div className="rounded-lg border">{/* Content structure with appropriate skeletons */}</div>
    </div>
  );
}
```

Key principles for loading components:

- Mirror the structure of the actual page
- Use Skeleton components with dimensions matching real content
- Maintain consistent spacing and visual hierarchy
- Implement responsive design in loading states
- Use Array.from with map for repeating elements
