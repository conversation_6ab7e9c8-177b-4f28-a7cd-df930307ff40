import { createClient } from "@/lib/supabase/server";
import { ClusterWithProjects } from "@/types/cluster-project.types";

export async function getClusterProjectsByTenantIdFromDb(tenantId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("clusters")
    .select(
      `
          id,
          name,
          slug,
          tenant_id,
          description,
          projects (
            id,
            name,
            slug,
            tenant_id,
            description,
            status,
            priority,
            created_at,
            updated_at
          )
        `
    )
    .eq("tenant_id", tenantId)
    .order("name");

  if (error) throw error;

  return data as ClusterWithProjects[];
}
