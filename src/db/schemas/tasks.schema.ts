import { LucideIcon } from "lucide-react";

// Common enums and types
export type Value = "Huge" | "High" | "Normal" | "Low" | "Not set";
export type ColumnStatus = "Backlog" | "To do" | "In progress" | "In review" | "Done";
export type SubtaskStatus = "To do" | "In progress" | "In review" | "Done";
export type LifeCycleStatus =
  | "created"
  | "ready"
  | "started"
  | "dev done"
  | "test done"
  | "deploy ready"
  | "delivered";

// Column style interface for kanban board
export interface ColumnStyle {
  header: string;
  title: string;
  icon: LucideIcon;
  iconColor: string;
}

// Database schema types
export type TagModel = {
  id: string;
  name: string;
  projectId?: string;
  createdAt?: string;
  updatedAt?: string;
};

// User/Assignee model
export type UserModel = {
  id: string;
  name: string;
  avatar?: string;
};

export type TaskAttachment = {
  id: string;
  taskId: string;
  name: string;
  url: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
  createdAt: string;
};

// Simplified SubtaskModel
export type SubtaskModel = {
  id: string;
  title: string;
  isCompleted: boolean;
  assignee_id?: string;
};

// Main Task model - single source of truth
export interface TaskModel {
  id: string;
  key: string;
  title: string;
  description: string;
  value: Value;
  status: string;
  lifeCycleStatus: LifeCycleStatus;
  tags: TagModel[]; // Array of tag objects (populated via JOIN)
  sprint: string;
  week: number;
  dueDate: string | null;
  doneDate: string | null;
  deliveredDate: string | null;
  assignees: UserModel[];
  effort: number;
  positionInColumn: number;
  attachments?: TaskAttachment[];
  createdAt: string;
  updatedAt: string;
  subtasks: SubtaskModel[];
  visibleToTeams: string[];
  isDummy?: boolean;
  isOptimistic?: boolean;
}

// Task group model
export type TaskGroupModel = {
  id: string;
  name: string;
  count: number;
  tasks: TaskModel[];
  color: string;
  icon: string;
};

// Status style type for consistent styling across components
export interface StatusStyle {
  color: string; // Base color name like 'blue', 'green'
  iconName: string; // Icon name (used for lookups)
  bg: {
    light: string; // Background in light mode
    dark: string; // Background in dark mode
  };
  text: {
    light: string; // Text color in light mode
    dark: string; // Text color in dark mode
  };
  border: {
    light: string; // Border color in light mode
    dark: string; // Border color in dark mode
  };
  badge: {
    light: string; // Badge style in light mode
    dark: string; // Badge style in dark mode
  };
}

// Get style information based on column status
export function getStatusStyle(status: string): StatusStyle {
  // Normalize the status for case-insensitive comparison
  const normalizedStatus = status.toLowerCase().trim();

  switch (normalizedStatus) {
    case "to do":
      return {
        color: "blue",
        iconName: "circle",
        bg: {
          light: "bg-blue-50/80",
          dark: "dark:bg-blue-500/20",
        },
        text: {
          light: "text-blue-800",
          dark: "dark:text-blue-400",
        },
        border: {
          light: "border-blue-200",
          dark: "dark:border-blue-800/60",
        },
        badge: {
          light: "bg-blue-100 text-blue-700 hover:bg-blue-200",
          dark: "dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-900/70",
        },
      };
    case "in progress":
      return {
        color: "amber",
        iconName: "circle-dot",
        bg: {
          light: "bg-amber-50/80",
          dark: "dark:bg-amber-500/20",
        },
        text: {
          light: "text-amber-700",
          dark: "dark:text-amber-400",
        },
        border: {
          light: "border-amber-200",
          dark: "dark:border-amber-800/60",
        },
        badge: {
          light: "bg-amber-100 text-amber-700 hover:bg-amber-200",
          dark: "dark:bg-amber-900/50 dark:text-amber-300 dark:hover:bg-amber-900/70",
        },
      };
    case "in review":
      return {
        color: "purple",
        iconName: "circle-ellipsis",
        bg: {
          light: "bg-purple-50/80",
          dark: "dark:bg-purple-500/20",
        },
        text: {
          light: "text-purple-800",
          dark: "dark:text-purple-400",
        },
        border: {
          light: "border-purple-200",
          dark: "dark:border-purple-800/60",
        },
        badge: {
          light: "bg-purple-100 text-purple-700 hover:bg-purple-200",
          dark: "dark:bg-purple-900/50 dark:text-purple-300 dark:hover:bg-purple-900/70",
        },
      };
    case "done":
      return {
        color: "green",
        iconName: "check-circle",
        bg: {
          light: "bg-green-50/80",
          dark: "dark:bg-green-500/20",
        },
        text: {
          light: "text-green-800",
          dark: "dark:text-green-400",
        },
        border: {
          light: "border-green-200",
          dark: "dark:border-green-800/60",
        },
        badge: {
          light: "bg-green-100 text-green-700 hover:bg-green-200",
          dark: "dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-900/70",
        },
      };
    case "backlog":
    default:
      return {
        color: "gray",
        iconName: "circle-dashed",
        bg: {
          light: "bg-gray-50/80",
          dark: "dark:bg-gray-800/90",
        },
        text: {
          light: "text-gray-800",
          dark: "dark:text-gray-300",
        },
        border: {
          light: "border-gray-200",
          dark: "dark:border-gray-800/60",
        },
        badge: {
          light: "bg-gray-100 text-gray-700 hover:bg-gray-200",
          dark: "dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700",
        },
      };
  }
}

// Color map type for consistent styling
export interface ColorMap {
  hover: string;
  bg: string;
  border: string;
  text: string;
  badge: string;
}

// Generate a map of color styles for all supported colors
export function generateColorMap(): Record<string, ColorMap> {
  const colorMap: Record<string, ColorMap> = {};

  const allColors = ["gray", "amber", "blue", "green", "red", "purple", "indigo", "pink", "orange"];

  allColors.forEach((color) => {
    // Get the status style for this color if it has one, or use a default

    colorMap[color] = {
      hover: `hover:bg-${color}-50 dark:hover:bg-${color}-900/40`,
      bg: `bg-${color}-50 dark:bg-${color}-900/30`,
      border: `border-${color}-200 dark:border-${color}-800/60`,
      text: `text-${color}-700 dark:text-${color}-300`,
      badge: `bg-${color}-100 text-${color}-700 hover:bg-${color}-200 dark:bg-${color}-900/70 dark:text-${color}-200 dark:hover:bg-${color}-900/70`,
    };
  });

  return colorMap;
}
