import { z } from "zod";

// Schema for team creation and validation
export const TeamSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters"),
  description: z.string().optional(),
});

// Member schema for the team_members table
export const TeamMemberSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  team_id: z.string().uuid(),
  tenant_id: z.string().uuid(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  role: z.enum(["owner", "admin", "member", "guest"]),
  position: z
    .enum([
      "Developer",
      "Lead Developer",
      "Product Manager",
      "Project Manager",
      "UI/UX Designer",
      "QA Engineer",
      "QA Lead",
      "Engineering Manager",
      "CTO",
      "VP of Engineering",
      "CEO",
    ])
    .optional(),
});

export const TeamProjectSchema = z.object({
  team_id: z.string().uuid(),
  project_id: z.string().uuid(),
});

export type TeamData = z.infer<typeof TeamSchema>;
export type TeamMember = z.infer<typeof TeamMemberSchema>;
export type TeamProjectData = z.infer<typeof TeamProjectSchema>;

// Form configuration for client-side use
export const teamFormConfig = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  description: {
    required: false,
  },
};
