import { z } from "zod";

// Schema for user stats validation
export const UserStatsSchema = z.object({
  projects: z.number().int().nonnegative(),
  tasksDone: z.number().int().nonnegative(),
  pullRequests: z.number().int().nonnegative(),
});

// Schema for user profile validation
export const UserProfileSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters"),
  email: z.string().email("Invalid email address"),
  location: z.string().optional(),
  avatar: z.string().url("Invalid avatar URL").optional(),
  avatarFallback: z.string().optional(),
  department: z.string().optional(),
  role: z.string().optional(),
  position: z.string().optional(),
  joinedDate: z.string().datetime("Invalid date format"),
  stats: UserStatsSchema,
});

// Schema for user registration and authentication
export const UserAuthSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must include uppercase, lowercase, number and special character"
    ),
});

// Export type definitions derived from schemas
export type UserStats = z.infer<typeof UserStatsSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type UserAuth = z.infer<typeof UserAuthSchema>;

// Form configuration for client-side use
export const userProfileFormConfig = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  email: {
    required: true,
    type: "email",
  },
  location: {
    required: false,
  },
  department: {
    required: false,
  },
  role: {
    required: false,
  },
  position: {
    required: false,
  },
};
