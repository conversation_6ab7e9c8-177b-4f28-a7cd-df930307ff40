import { z } from "zod";

// Add this type above the form component
export type SubdomainAvailability = "available" | "unavailable" | "checking" | "invalid" | null;

// List of reserved subdomains that shouldn't be allowed
const RESERVED_SUBDOMAINS = [
  "admin",
  "api",
  "app",
  "auth",
  "blog",
  "dashboard",
  "dev",
  "docs",
  "help",
  "mail",
  "staging",
  "status",
  "support",
  "test",
  "www",
] as const;

// Schema for organization invitations
export const OrganizationInvitationSchema = z.object({
  id: z.string().uuid().optional(),
  organization_id: z.string().uuid(),
  email: z.string().email("Invalid email address"),
  token: z.string(),
  status: z.enum(["pending", "accepted", "cancelled"]).default("pending"),
  expires_at: z.date(),
  created_at: z.date().optional(),
});

export type OrganizationInvitation = z.infer<typeof OrganizationInvitationSchema>;

// Define the schema locally for client-side validation
export const organizationSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(20, "Name must be less than 20 characters"),
  description: z.string().optional(),
  subdomain_id: z
    .string()
    .min(3, "Subdomain must be at least 3 characters")
    .max(16, "Subdomain must be less than 16 characters")
    .trim()
    .toLowerCase()
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      "Subdomain can only contain lowercase letters, numbers, and single hyphens between characters"
    )
    .refine(
      (value) => !value.startsWith("-") && !value.endsWith("-"),
      "Subdomain cannot start or end with a hyphen"
    )
    .refine((value) => !value.includes("--"), "Subdomain cannot contain consecutive hyphens")
    .refine(
      (value) => !RESERVED_SUBDOMAINS.includes(value as (typeof RESERVED_SUBDOMAINS)[number]),
      "This subdomain is reserved and cannot be used"
    ),
  max_users: z.number().int().min(1),
});
// Organization schema for validation
export const OrganizationSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(16, "Name must be less than 16 characters"),
  description: z.string().optional(),
  subdomain_id: z
    .string()
    .min(3, "Subdomain must be at least 3 characters")
    .max(16, "Subdomain must be less than 16 characters")
    .regex(/^[a-z0-9]+$/, "Subdomain can only contain lowercase letters and numbers")
    .transform((val) => val.toLowerCase()), // Force lowercase
  max_users: z.number().int().min(1),
});

export type OrganizationData = z.infer<typeof OrganizationSchema>;

// Form schema configuration (for client use)
export const organizationFormConfig = {
  name: {
    required: true,
    minLength: 3,
    maxLength: 16,
  },
  description: {
    required: false,
  },
  subdomain_id: {
    required: true,
    minLength: 3,
    maxLength: 16,
    pattern: "^[a-z0-9]+$",
    patternMessage: "Subdomain can only contain lowercase letters and numbers",
  },
  max_users: {
    required: true,
    min: 1,
  },
};
