"use server";

import { createClient } from "@/lib/supabase/server";
import { UserProfileDB } from "@/types/user.types";

/**
 * Create a user profile in the database
 * @param userId - The user ID to create a profile for
 * @param profileData - The profile data to create
 * @returns An object containing success status and any error
 */
export async function createProfileInDb(
  userId: string,
  profileData: Partial<UserProfileDB>
): Promise<{ success: boolean; error: Error | null }> {
  if (!userId) {
    return {
      success: false,
      error: new Error("User ID is required to create a profile"),
    };
  }

  try {
    // Dynamically import admin client to ensure it's properly initialized
    const { adminClient } = await import("@/lib/supabase/admin");

    // First check if profile already exists
    const { data: existingProfile } = await adminClient
      .from("profiles")
      .select("id, user_id")
      .eq("user_id", userId)
      .maybeSingle();

    // Prepare profile data
    const profilePayload = {
      user_id: userId,
      position: profileData.position || "Member",
      ...profileData,
      // Add explicit timestamps
      updated_at: new Date().toISOString(),
    };

    // Only set created_at and joined_date for new records
    if (!existingProfile) {
      profilePayload.created_at = new Date().toISOString();
      profilePayload.joined_date = new Date().toISOString();
    }

    // Use the admin client to bypass RLS policies with upsert instead of insert
    const { error } = await adminClient
      .from("profiles")
      .upsert(profilePayload, {
        onConflict: "user_id",
      })
      .select()
      .maybeSingle();

    if (error) {
      console.error("createProfileInDb - Supabase error:", error);
      throw error;
    }
    return {
      success: true,
      error: null,
    };
  } catch (error) {
    console.error("createProfileInDb - Caught exception:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return {
      success: false,
      error:
        error instanceof Error ? error : new Error(`Failed to create profile: ${errorMessage}`),
    };
  }
}

export async function getUserProfilePositionFromDb(userId: string): Promise<UserProfileDB | null> {
  const supabase = await createClient();

  const { data, error } = await supabase.from("profiles").select("position").eq("user_id", userId);

  if (error) throw error;

  return data[0]?.position || null;
}

/**
 * Update a user's position in the profiles table
 * @param userId - The user ID to update the position for
 * @param position - The new position value
 * @returns An object containing success status, data, and any error
 */
export async function updateUserPositionInDb(userId: string, position: string) {
  if (!userId) {
    return {
      success: false,
      message: "User ID is required to update position",
      data: null,
    };
  }

  try {
    // Dynamically import admin client to ensure it's properly initialized
    const { adminClient } = await import("@/lib/supabase/admin");

    const { data, error } = await adminClient
      .from("profiles")
      .upsert(
        {
          user_id: userId,
          position: position,
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: "user_id",
        }
      )
      .select()
      .maybeSingle();

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: "Position updated successfully",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to update position",
      data: null,
    };
  }
}

/**
 * Update or create profile specifically for invitation acceptance
 * This handles the case where a user already has a profile but is joining a new organization
 */
export async function updateProfileForInvitationInDb(
  userId: string,
  profileData: Partial<UserProfileDB>
): Promise<{ success: boolean; error: Error | null }> {
  if (!userId) {
    return {
      success: false,
      error: new Error("User ID is required to update profile"),
    };
  }

  try {
    const { adminClient } = await import("@/lib/supabase/admin");

    // Check if profile exists
    const { data: existingProfile, error: checkError } = await adminClient
      .from("profiles")
      .select("*")
      .eq("user_id", userId)
      .maybeSingle();

    if (checkError) {
      console.error(
        "updateProfileForInvitationInDb - Error checking existing profile:",
        checkError
      );
      throw checkError;
    }

    if (existingProfile) {
      // Profile exists - update only specific fields that might have changed
      const updatePayload = {
        // Only update these fields if they're provided and different
        ...(profileData.first_name &&
          profileData.first_name !== existingProfile.first_name && {
            first_name: profileData.first_name,
          }),
        ...(profileData.last_name &&
          profileData.last_name !== existingProfile.last_name && {
            last_name: profileData.last_name,
          }),
        ...(profileData.email &&
          profileData.email !== existingProfile.email && { email: profileData.email }),
        // Always update the full_name if first/last names are provided
        ...(profileData.first_name &&
          profileData.last_name && {
            full_name: `${profileData.first_name} ${profileData.last_name}`.trim(),
          }),
        updated_at: new Date().toISOString(),
      };

      const { error } = await adminClient
        .from("profiles")
        .update(updatePayload)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) {
        console.error("updateProfileForInvitationInDb - Update error:", error);
        throw error;
      }

      return { success: true, error: null };
    } else {
      // Profile doesn't exist - create new one

      return await createProfileInDb(userId, profileData);
    }
  } catch (error) {
    console.error("updateProfileForInvitationInDb - Caught exception:", error);
    return {
      success: false,
      error: error instanceof Error ? error : new Error(`Failed to update profile: ${error}`),
    };
  }
}
