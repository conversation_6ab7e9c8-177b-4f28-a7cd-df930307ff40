import { createClient } from "@/lib/supabase/server";

type Organization = {
  id: string;
  name: string;
  description: string | null;
  subdomain_id: string;
  max_users: number;
  created_at: string;
  updated_at: string;
};

type OrganizationWithMember = Organization & {
  member_role: string;
};

/**
 * Get all organizations for a user
 */
export async function getOrganizationMemberFromDb(userId: string): Promise<{
  data: OrganizationWithMember[] | null;
  error: Error | null;
}> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("organization_members")
    .select("*, organizations!organization_members_organization_id_fkey(*)")
    .eq("user_id", userId);

  if (error) {
    return { data: null, error };
  }

  // Transform the data to flatten the organization object
  const organizations =
    data?.map((item) => ({
      ...item.organizations,
      member_role: item.role,
    })) || null;

  return {
    data: organizations,
    error: null,
  };
}

// Note: For adding organization members, use the addOrganizationMemberInDb
// function from organization.db.ts which properly sets the tenant_id

/**
 * Get the current user's organization role
 */
export async function getCurrentUserOrganizationRoleFromDb(tenantId: string) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { data: null, error: new Error("Not authenticated") };
    }

    if (!tenantId) {
      return { data: null, error: new Error("No active organization context") };
    }

    // Get the user's organization role filtered by the current tenant
    const { data: orgMember, error: orgError } = await supabase
      .from("organization_members")
      .select("role")
      .eq("user_id", user.id)
      .eq("organization_id", tenantId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid error if no record found

    if (orgError) {
      return { data: null, error: orgError };
    }

    // If no member record found, user is not part of this organization
    if (!orgMember) {
      return {
        data: { role: "none" },
        error: null,
      };
    }

    // Return the role data
    return {
      data: { role: orgMember.role || "member" },
      error: null,
    };
  } catch (error) {
    console.info("[getCurrentUserOrganizationRoleFromDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}
