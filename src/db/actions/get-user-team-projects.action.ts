"use server";

import { getClustersAndProjectsWithUserTeamsFromDb } from "@/db/cluster-project-user-teams.db";
import { getTenantId } from "@/db/tenant.db";
import { getUserId } from "@/db/user.db";
import { ClusterWithProjects } from "@/types/cluster-project.types";
// Team type is implicitly handled by the return type of getClustersAndProjectsWithUserTeamsFromDb
// and the ClusterWithProjects type definition, so direct import might not be needed here.

/**
 * Get projects that belong to teams the current user is a member of
 * and include team information for each project.
 * This version is refactored to use a more optimized database query.
 */
export async function getUserTeamProjectsAction(): Promise<ClusterWithProjects[]> {
  try {
    const userId = await getUserId();
    const tenantId = await getTenantId();

    if (!userId || !tenantId) {
      console.info("[getUserTeamProjectsAction] User ID or Tenant ID not found.");
      return [];
    }

    // Fetch clusters, their projects, and user-specific teams for those projects in a single call
    const clustersWithUserProjectsAndTeams = await getClustersAndProjectsWithUserTeamsFromDb(
      tenantId,
      userId
    );

    if (!clustersWithUserProjectsAndTeams || clustersWithUserProjectsAndTeams.length === 0) {
      console.info("[getUserTeamProjectsAction] No clusters found for the user and tenant.");
      return [];
    }

    // The new DB function already filters projects to those associated with the user's teams.
    // Now, we just need to filter out clusters that might have ended up with no projects after this filtering.
    const filteredClusters = clustersWithUserProjectsAndTeams.filter(
      (cluster) => cluster.projects.length > 0
    );

    return filteredClusters;
  } catch (error) {
    console.error("[getUserTeamProjectsAction] Error:", error);
    // The error is already logged in getClustersAndProjectsWithUserTeamsFromDb
    // console.info("[getUserTeamProjectsAction] Error:", error);
    return [];
  }
}
