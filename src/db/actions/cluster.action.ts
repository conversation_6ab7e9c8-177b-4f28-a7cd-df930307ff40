"use server";

import { getClusterProjectsByTenantIdFromDb } from "@/db/cluster-project.db";
import { createClient } from "@/lib/supabase/server";
import { generateSlug } from "@/lib/utils";
import { revalidatePath } from "next/cache";
import { getTenantId } from "../tenant.db";

export async function createClusterWithProjects(data: {
  name: string;
  description?: string;
  organizationId: string;
  projects: Array<{
    name: string;
    description?: string;
  }>;
}) {
  try {
    // Generate cluster slug
    const supabase = await createClient();
    const clusterSlug = generateSlug(data.name);

    // Create cluster
    const { data: clusterData, error: clusterError } = await supabase
      .from("clusters")
      .insert({
        name: data.name,
        description: data.description,
        organization_id: data.organizationId,
        slug: clusterSlug,
        status: "active",
        tenant_id: data.organizationId, // Ensure tenant_id is set on cluster
      })
      .select()
      .single();

    if (clusterError) throw clusterError;

    // Create all projects - if cluster creation succeeds
    const projectPromises = data.projects.map(async (project) => {
      const projectSlug = generateSlug(project.name);

      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .insert({
          name: project.name,
          description: project.description,
          cluster_id: clusterData.id,
          slug: projectSlug,
          status: "active",
          tenant_id: data.organizationId, // Use organization ID as tenant_id for consistency
          // team_id is now nullable, so we don't need to set it here
        })
        .select()
        .single();

      if (projectError) {
        console.info(`Error creating project "${project.name}":`, projectError);
        throw new Error(`Failed to create project "${project.name}": ${projectError.message}`);
      }
      return projectData;
    });

    // Wait for all projects to be created
    const projects = await Promise.all(projectPromises);

    // Revalidate homepage path to refresh data
    revalidatePath("/home");
    return {
      success: true,
      data: {
        cluster: clusterData,
        projects,
      },
    };
  } catch (error) {
    console.info("Failed to create cluster with projects:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create cluster with projects",
    };
  }
}

/**
 * Add projects to an existing cluster
 */
export async function addProjectsToExistingClusterAction(data: {
  clusterId: string;
  organizationId: string;
  projects: Array<{
    name: string;
    slug: string;
    description?: string;
  }>;
}) {
  try {
    // First verify the cluster exists and belongs to the organization
    const supabase = await createClient();

    const { data: clusterData, error: clusterError } = await supabase
      .from("clusters")
      .select("id, name, tenant_id")
      .eq("id", data.clusterId)
      .eq("organization_id", data.organizationId)
      .single();

    if (clusterError) {
      console.info("Failed to verify cluster:", clusterError);
      return { success: false, error: "Cluster not found or access denied" };
    }

    // Use the cluster's tenant_id if available, otherwise use organizationId
    const tenantId = clusterData.tenant_id || data.organizationId;

    // Create all projects
    const projectPromises = data.projects.map(async (project) => {
      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .insert({
          name: project.name,
          description: project.description,
          cluster_id: data.clusterId,
          slug: project.slug,
          status: "active",
          tenant_id: tenantId, // Use the determined tenant_id consistently
          // team_id is now nullable, so we don't need to set it here
        })
        .select()
        .single();

      if (projectError) {
        console.info(`Error creating project "${project.name}":`, projectError);
        throw new Error(`Failed to create project "${project.name}": ${projectError.message}`);
      }
      return projectData;
    });

    // Wait for all projects to be created
    const projects = await Promise.all(projectPromises);

    // Revalidate homepage path to refresh data
    revalidatePath("/home");

    return {
      success: true,
      data: {
        cluster: clusterData,
        projects,
      },
    };
  } catch (error) {
    console.info("Failed to add projects to existing cluster:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add projects to existing cluster",
    };
  }
}

export async function getClusterProjects() {
  const tenantId = await getTenantId();

  if (!tenantId) {
    return null;
  }

  const clusterProjects = await getClusterProjectsByTenantIdFromDb(tenantId);

  return clusterProjects;
}

export async function deleteClusterAction(clusterId: string) {
  try {
    const supabase = await createClient();

    // First check if the cluster exists and belongs to the current tenant
    const tenantId = await getTenantId();

    if (!tenantId) {
      return { success: false, message: "Tenant ID not found" };
    }

    const { data: cluster, error: clusterError } = await supabase
      .from("clusters")
      .select("id")
      .eq("id", clusterId)
      .eq("tenant_id", tenantId)
      .single();

    if (clusterError || !cluster) {
      return {
        success: false,
        message: "Cluster not found or you don't have permission to delete it",
      };
    }

    // Check if the cluster has any projects
    const { data: projects, error: projectsError } = await supabase
      .from("projects")
      .select("id")
      .eq("cluster_id", clusterId);

    if (projectsError) {
      return { success: false, message: "Failed to check projects in cluster" };
    }

    if (projects && projects.length > 0) {
      // Optional: handle deletion of associated projects
      // For safety, we might want to prevent deletion if projects exist
      // or provide an option to move projects to another cluster
      return {
        success: false,
        message:
          "Cluster has associated projects. Remove projects first or use force delete option.",
      };
    }

    // Delete the cluster
    const { error: deleteError } = await supabase.from("clusters").delete().eq("id", clusterId);

    if (deleteError) {
      return { success: false, message: "Failed to delete cluster: " + deleteError.message };
    }

    // Revalidate paths to refresh data
    revalidatePath("/home");
    revalidatePath("/home/<USER>");

    return { success: true, message: "Cluster deleted successfully" };
  } catch (error) {
    console.info("Failed to delete cluster:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to delete cluster",
    };
  }
}

export async function assignProjectToClusterAction(projectId: string, clusterId: string) {
  try {
    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      return { success: false, message: "Tenant ID not found" };
    }

    // Verify both the project and cluster exist and belong to the tenant
    const { data: cluster, error: clusterError } = await supabase
      .from("clusters")
      .select("id")
      .eq("id", clusterId)
      .eq("tenant_id", tenantId)
      .single();

    if (clusterError || !cluster) {
      return {
        success: false,
        message: "Cluster not found or you don't have permission to access it",
      };
    }

    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("id, cluster_id")
      .eq("id", projectId)
      .eq("tenant_id", tenantId)
      .single();

    if (projectError || !project) {
      return {
        success: false,
        message: "Project not found or you don't have permission to access it",
      };
    }

    // Check if project is already in this cluster
    if (project.cluster_id === clusterId) {
      return { success: false, message: "Project is already in this cluster" };
    }

    // Update the project to assign it to the new cluster
    const { data: updatedProject, error: updateError } = await supabase
      .from("projects")
      .update({ cluster_id: clusterId })
      .eq("id", projectId)
      .select()
      .single();

    if (updateError) {
      return {
        success: false,
        message: "Failed to assign project to cluster: " + updateError.message,
      };
    }

    // Revalidate paths to refresh data
    revalidatePath("/home");
    revalidatePath("/home/<USER>");

    return {
      success: true,
      message: "Project assigned to cluster successfully",
      data: updatedProject,
    };
  } catch (error) {
    console.info("Failed to assign project to cluster:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to assign project to cluster",
    };
  }
}

export async function removeProjectFromClusterAction(projectId: string, clusterId: string) {
  try {
    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      return { success: false, message: "Tenant ID not found" };
    }

    // Verify the project exists, belongs to the tenant, and is in the specified cluster
    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("id, cluster_id")
      .eq("id", projectId)
      .eq("tenant_id", tenantId)
      .eq("cluster_id", clusterId)
      .single();

    if (projectError || !project) {
      return {
        success: false,
        message: "Project not found in this cluster or you don't have permission to access it",
      };
    }

    // For now, we'll just disassociate the project from the cluster
    // In a real system, you might want to move it to a default cluster
    // or implement a different strategy

    // Create a new cluster for orphaned projects if needed
    const { data: orphanedCluster, error: orphanedClusterError } = await supabase
      .from("clusters")
      .select("id")
      .eq("name", "Uncategorized")
      .eq("tenant_id", tenantId)
      .single();

    let orphanedClusterId: string;

    if (orphanedClusterError || !orphanedCluster) {
      // Create a new cluster for orphaned projects
      const { data: newCluster, error: newClusterError } = await supabase
        .from("clusters")
        .insert({
          name: "Uncategorized",
          description: "Projects that are not assigned to any specific cluster",
          tenant_id: tenantId,
          organization_id: tenantId,
          slug: "uncategorized",
          status: "active",
        })
        .select()
        .single();

      if (newClusterError || !newCluster) {
        return {
          success: false,
          message:
            "Failed to create uncategorized cluster: " +
            (newClusterError ? newClusterError.message : "Unknown error"),
        };
      }

      orphanedClusterId = newCluster.id;
    } else {
      orphanedClusterId = orphanedCluster.id;
    }

    // Move the project to the "Uncategorized" cluster
    const { data: updatedProject, error: updateError } = await supabase
      .from("projects")
      .update({ cluster_id: orphanedClusterId })
      .eq("id", projectId)
      .select()
      .single();

    if (updateError) {
      return {
        success: false,
        message: "Failed to remove project from cluster: " + updateError.message,
      };
    }

    // Revalidate paths to refresh data
    revalidatePath("/home");
    revalidatePath("/home/<USER>");

    return {
      success: true,
      message: "Project removed from cluster successfully",
      data: updatedProject,
    };
  } catch (error) {
    console.info("Failed to remove project from cluster:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to remove project from cluster",
    };
  }
}

// New types for the hierarchical organization view
export type OrganizationProject = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  status: string;
  progress: number;
  statusColor: string;
  dueDate: string;
  startDate: string;
  department: string;
  priority: string;
  members: Array<{
    name: string;
    avatar: string;
    initials: string;
  }>;
  tasks: {
    completed: number;
    total: number;
  };
  budget: {
    allocated: number;
    spent: number;
    remaining: number;
  };
};

export type OrganizationCluster = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  status: string;
  projects: OrganizationProject[];
  // Aggregated metrics (calculated from projects)
  aggregatedMetrics: {
    progress: number; // average of all projects
    totalProjects: number;
    completedProjects: number;
    inProgressProjects: number;
    atRiskProjects: number;
    totalMembers: number;
    totalTasks: {
      completed: number;
      total: number;
    };
    totalBudget: {
      allocated: number;
      spent: number;
      remaining: number;
    };
  };
};

/**
 * Get all clusters with their projects and calculated metrics for organization overview
 */
export async function getAllClustersWithProjectsForOrganizationAction(): Promise<{
  success: boolean;
  data: OrganizationCluster[];
  currentClusterId?: string;
  currentClusterSlug?: string;
  error?: string;
}> {
  try {
    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      return { success: false, data: [], error: "Tenant ID not found" };
    }

    // Fetch all clusters for the organization
    const { data: clusters, error: clustersError } = await supabase
      .from("clusters")
      .select(
        `
        id,
        name,
        description,
        slug,
        status,
        projects (
          id,
          name,
          description,
          slug,
          status
        )
      `
      )
      .eq("tenant_id", tenantId)
      .order("name");

    if (clustersError) {
      console.error("Error fetching clusters:", clustersError);
      return { success: false, data: [], error: clustersError.message };
    }

    if (!clusters) {
      return { success: true, data: [], currentClusterId: undefined };
    }

    // Transform the data to include mock metrics and aggregations
    const organizationClusters: OrganizationCluster[] = clusters.map((cluster) => {
      // Generate mock project data with metrics (similar to existing mock data)
      const mockProjects: OrganizationProject[] = (cluster.projects || []).map((project, index) => {
        // Mock data generation (similar to existing components)
        const mockProgress = [75, 40, 60, 25, 90, 100, 50, 80, 30][index % 9] || 50;
        const mockStatuses = ["In Progress", "Planning", "Final Review", "Completed"];
        const mockStatusColors = ["bg-yellow-500", "bg-blue-500", "bg-purple-500", "bg-green-500"];
        const statusIndex = index % mockStatuses.length;

        const mockDepartments = [
          "Engineering",
          "Marketing",
          "Sales",
          "Product",
          "HR",
          "IT",
          "Operations",
        ];
        const mockPriorities = ["High", "Medium", "Low"];

        const mockTasksCompleted = Math.floor(mockProgress / 5);
        const mockTasksTotal = 20;

        return {
          id: project.id,
          name: project.name,
          description: project.description,
          slug: project.slug,
          status: project.status,
          progress: mockProgress,
          statusColor: mockStatusColors[statusIndex],
          dueDate: new Date(
            Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000
          ).toLocaleDateString(),
          startDate: new Date(
            Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000
          ).toLocaleDateString(),
          department: mockDepartments[index % mockDepartments.length],
          priority: mockPriorities[index % mockPriorities.length],
          members: [
            { name: "John Doe", avatar: "/placeholder-user.jpg", initials: "JD" },
            { name: "Sarah Johnson", avatar: "/placeholder.svg", initials: "SJ" },
            { name: "Michael Chen", avatar: "/placeholder.svg", initials: "MC" },
          ].slice(0, Math.floor(Math.random() * 3) + 1),
          tasks: {
            completed: mockTasksCompleted,
            total: mockTasksTotal,
          },
          budget: {
            allocated: 200000 + Math.random() * 800000,
            spent: mockProgress * 1000 + Math.random() * 100000,
            remaining: 0, // Will be calculated
          },
        };
      });

      // Calculate budget remaining for each project
      mockProjects.forEach((project) => {
        project.budget.remaining = project.budget.allocated - project.budget.spent;
      });

      // Calculate aggregated metrics for the cluster
      const aggregatedMetrics = {
        progress:
          mockProjects.length > 0
            ? Math.round(mockProjects.reduce((sum, p) => sum + p.progress, 0) / mockProjects.length)
            : 0,
        totalProjects: mockProjects.length,
        completedProjects: mockProjects.filter((p) => p.progress === 100).length,
        inProgressProjects: mockProjects.filter((p) => p.progress > 0 && p.progress < 100).length,
        atRiskProjects: mockProjects.filter((p) => p.progress < 30).length,
        totalMembers: mockProjects.reduce((sum, p) => sum + p.members.length, 0),
        totalTasks: {
          completed: mockProjects.reduce((sum, p) => sum + p.tasks.completed, 0),
          total: mockProjects.reduce((sum, p) => sum + p.tasks.total, 0),
        },
        totalBudget: {
          allocated: mockProjects.reduce((sum, p) => sum + p.budget.allocated, 0),
          spent: mockProjects.reduce((sum, p) => sum + p.budget.spent, 0),
          remaining: mockProjects.reduce((sum, p) => sum + p.budget.remaining, 0),
        },
      };

      return {
        id: cluster.id,
        name: cluster.name,
        description: cluster.description,
        slug: cluster.slug,
        status: cluster.status,
        projects: mockProjects,
        aggregatedMetrics,
      };
    });

    return {
      success: true,
      data: organizationClusters,
      currentClusterId: clusters[0]?.id, // We'll improve this to get the actual current cluster
      currentClusterSlug: clusters[0]?.slug, // Add slug for matching
    };
  } catch (error) {
    console.error("Failed to fetch organization clusters:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Failed to fetch organization clusters",
    };
  }
}
