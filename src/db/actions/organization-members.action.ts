import { getCurrentUserOrganizationRoleFromDb } from "@/db/organization-members.db";
import { connection } from "next/server";
import { getCurrentTenantIdFromDb } from "../team.db";
import { getUserId } from "../user.db";

export async function getCurrentUserOrganizationRoleAction() {
  await connection();
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in",
        data: { isAdmin: false, role: "unknown" },
      };
    }

    const tenantId = await getCurrentTenantIdFromDb();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
        data: { isAdmin: false, role: "unknown" },
      };
    }

    const { data, error } = await getCurrentUserOrganizationRoleFromDb(tenantId);

    if (error) {
      return {
        success: false,
        message: error.message,
        data: { isAdmin: false, role: "unknown" },
      };
    }

    // Check explicitly if role is admin or owner
    const isAdmin = data?.role === "admin" || data?.role === "owner";

    return {
      success: true,
      message: "Admin status checked successfully",
      data: {
        isAdmin,
        role: data?.role || "unknown",
      },
    };
  } catch (error) {
    console.info("[getCurrentUserOrganizationRoleAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      data: { isAdmin: false, role: "unknown" },
    };
  }
}
