"use server";

import { addOrganizationMemberInDb as addOrgMemberFromOrgDb } from "@/db/organization.db";
import { createProfileInDb } from "@/db/profile.db";
import { createClient } from "@/lib/supabase/server";
import { UserProfileDB } from "@/types/user.types";

/**
 * Create a user profile after sign-up
 * @param userId - The user ID to create a profile for
 * @param profileData - The profile data to create
 * @returns An object containing success status and any error message
 */
export async function createUserProfileAction(
  userId: string,
  profileData: Partial<UserProfileDB>
): Promise<{ success: boolean; error: string | null }> {
  if (!userId) {
    return {
      success: false,
      error: "User ID is required to create a profile",
    };
  }

  try {
    const { success, error } = await createProfileInDb(userId, profileData);

    if (!success) {
      throw error;
    }

    return {
      success: true,
      error: null,
    };
  } catch (error) {
    console.info("Error creating user profile:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create profile",
    };
  }
}

/**
 * Add a user to an organization by subdomain
 * @param userId - The user ID to add as a member
 * @param subdomain - The subdomain of the organization
 * @param role - The role of the user in the organization (default: "member")
 * @returns An object containing success status and any error message
 */
export async function addUserToOrganizationBySubdomainAction(
  userId: string,
  subdomain: string,
  role: "admin" | "member" = "member"
): Promise<{ success: boolean; error: string | null }> {
  if (!userId || !subdomain) {
    return {
      success: false,
      error: "User ID and subdomain are required",
    };
  }

  const supabase = await createClient();

  try {
    // First, get the organization ID from the subdomain
    const { data: organization, error: orgError } = await supabase
      .from("organizations")
      .select("id")
      .eq("subdomain_id", subdomain)
      .single();

    if (orgError) {
      throw orgError;
    }

    if (!organization) {
      return {
        success: false,
        error: "Organization not found for the given subdomain",
      };
    }

    // Use the existing function from organization.db.ts
    const { success, error } = await addOrgMemberFromOrgDb(organization.id, userId, role);

    if (!success) {
      throw error;
    }

    return {
      success: true,
      error: null,
    };
  } catch (error) {
    console.info("Error adding user to organization:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add user to organization",
    };
  }
}

/**
 * Coordinate the entire subdomain sign-up process
 * @param userId - The user ID that just signed up
 * @param subdomain - The subdomain from the sign-up request
 * @param profileData - The profile data to create
 * @returns An object containing success status and any error message
 */
export async function processSubdomainSignupAction(
  userId: string,
  subdomain: string | null,
  profileData: Partial<UserProfileDB>
): Promise<{ success: boolean; error: string | null }> {
  if (!userId) {
    return {
      success: false,
      error: "User ID is required for sign-up processing",
    };
  }

  try {
    // Step 1: Create user profile (for all users)
    const { success: profileSuccess, error: profileError } = await createUserProfileAction(
      userId,
      profileData
    );

    if (!profileSuccess) {
      throw new Error(profileError || "Failed to create user profile");
    }

    // Step 2: If sign-up was from a subdomain, add user to that organization
    if (subdomain) {
      const { success: orgSuccess, error: orgError } = await addUserToOrganizationBySubdomainAction(
        userId,
        subdomain
      );

      if (!orgSuccess) {
        throw new Error(orgError || "Failed to add user to organization");
      }
    }

    return {
      success: true,
      error: null,
    };
  } catch (error) {
    console.info("Error processing subdomain sign-up:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to process sign-up",
    };
  }
}
