"use server";

import { getAllClustersWithProjectsForOrganizationAction } from "@/db/actions/cluster.action";
import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";

/**
 * Server action to get organization clusters data
 * Can be called from client components
 */
export async function getOrganizationClustersAction() {
  return await getAllClustersWithProjectsForOrganizationAction();
}

/**
 * Server action to get current user's organization role
 * Can be called from client components
 */
export async function getUserOrganizationRoleAction() {
  return await getCurrentUserOrganizationRoleAction();
}
