"use server";

import { getUserProjectsFromDb, UserProjectWithCluster } from "@/db/user-projects.db";
import { createClient } from "@/lib/supabase/server";

/**
 * Get all projects for the current user
 */
export async function getUserProjects(): Promise<UserProjectWithCluster[]> {
  try {
    // Get the current user from Supabase auth
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user?.id) {
      return [];
    }

    return getUserProjectsFromDb();
  } catch (error) {
    console.info("Error fetching user projects:", error);
    return [];
  }
}
