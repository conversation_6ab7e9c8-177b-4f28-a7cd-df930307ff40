import {
  createTeamMemberInDb,
  removeTeamMemberFromTeamInDb,
  updateTeamMemberDetailsInDb,
  updateTeamOfTeamMemberInDb,
} from "@/db/team-members.db";

export type MoveTeamMemberResult = {
  success: boolean;
  message: string;
  error?: Error | null;
  data?: {
    teamMemberId: string;
    oldTeamId: string;
    newTeamId: string;
    userId: string;
    newTeamMemberId?: string;
  };
};

export type RemoveTeamMemberResult = {
  success: boolean;
  message: string;
  error?: Error | null;
  data?: {
    teamMemberId: string;
    teamId: string;
  };
};

/**
 * Create a new team member
 */
export const createTeamMemberAction = async (data: {
  userId: string;
  teamId: string;
  role: string;
  position: string;
  tenantId: string;
}): Promise<{
  success: boolean;
  message: string;
  error?: Error | null;
  data?: {
    teamMemberId: string;
    userId: string;
    teamId: string;
    role: string;
    position: string;
    tenantId: string;
  };
}> => {
  try {
    const { data: teamMember, error } = await createTeamMemberInDb(data);

    if (error) {
      return {
        success: false,
        message: error.message,
        error,
      };
    }

    return {
      success: true,
      message: "Team member successfully created",
      data: teamMember,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while creating the team member";

    return {
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error : new Error(errorMessage),
    };
  }
};

/**
 * Move a team member to a new team
 * This creates a new record in the destination team and deletes the old record
 */
export const moveTeamMemberToNewTeamAction = async (
  teamMemberId: string,
  newTeamId: string
): Promise<MoveTeamMemberResult> => {
  if (!teamMemberId || !newTeamId) {
    return {
      success: false,
      message: `Missing required parameter: ${!teamMemberId ? "teamMemberId" : "newTeamId"}`,
      error: new Error(
        `Missing required parameter: ${!teamMemberId ? "teamMemberId" : "newTeamId"}`
      ),
    };
  }

  try {
    const { data, error } = await updateTeamOfTeamMemberInDb(teamMemberId, newTeamId);

    if (error) {
      return {
        success: false,
        message: error.message,
        error,
      };
    }

    return {
      success: true,
      message: "Team member successfully moved to new team",
      data: {
        teamMemberId: data.id,
        oldTeamId: data.oldTeamId,
        newTeamId: data.newTeamId,
        userId: data.user_id,
        newTeamMemberId: data.id,
      },
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while moving the team member";

    return {
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error : new Error(errorMessage),
    };
  }
};

/**
 * Remove a team member from a team
 */
export const removeTeamMemberFromTeamAction = async (
  teamMemberId: string,
  teamId: string
): Promise<RemoveTeamMemberResult> => {
  if (!teamMemberId || !teamId) {
    return {
      success: false,
      message: `Missing required parameter: ${!teamMemberId ? "teamMemberId" : "teamId"}`,
      error: new Error(`Missing required parameter: ${!teamMemberId ? "teamMemberId" : "teamId"}`),
    };
  }

  try {
    const { data, error } = await removeTeamMemberFromTeamInDb(teamMemberId, teamId);

    if (error) {
      return {
        success: false,
        message: error.message,
        error,
      };
    }

    return {
      success: true,
      message: "Team member successfully removed from team",
      data: {
        teamMemberId: data.teamMemberId,
        teamId: data.teamId,
      },
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while removing the team member";

    return {
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error : new Error(errorMessage),
    };
  }
};
/**
 * Update a team member's role and position
 */
export const updateTeamMemberDetailsAction = async (
  teamMemberId: string,
  data: { role?: string; position?: string }
): Promise<{
  success: boolean;
  message: string;
  error?: Error | null;
  data?: {
    teamMemberId: string;
    role: string;
    position: string;
  };
}> => {
  if (!teamMemberId) {
    return {
      success: false,
      message: "Team member ID is required",
      error: new Error("Team member ID is required"),
    };
  }

  try {
    const { data: updatedMember, error } = await updateTeamMemberDetailsInDb(teamMemberId, data);

    if (error) {
      return {
        success: false,
        message: error.message,
        error,
      };
    }

    return {
      success: true,
      message: "Team member details successfully updated",
      data: updatedMember,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while updating the team member";

    return {
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error : new Error(errorMessage),
    };
  }
};
