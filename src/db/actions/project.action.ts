"use server";

import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";
import { isUserAdminInAnyTeam } from "../team.db";
import { getTenantId } from "../tenant.db";
import { getUserId } from "../user.db";

type ActionResult<T = unknown> = {
  success: boolean;
  message: string;
  data?: T;
  error?: Error | null;
};

export async function getCurrentProject(projectSlug: string) {
  const tenantId = await getTenantId();

  if (!tenantId) {
    return null;
  }

  const supabase = await createClient();

  // Get project with tenant verification
  const { data, error } = await supabase
    .from("projects")
    .select(
      `
      id,
      name,
      slug,
      description,
      status,
      priority,
      tenant_id,
      cluster:clusters!inner(
        id,
        name,
        slug,
        tenant_id
      )
    `
    )
    .eq("slug", projectSlug)
    .eq("tenant_id", tenantId)
    .eq("cluster.tenant_id", tenantId)
    .single();

  if (error) {
    console.info("Error fetching project:", error);
    return null;
  }

  return {
    ...data,
    cluster: data.cluster[0],
  };
}

/**
 * Delete a project and all its team relationships
 */
export async function deleteProjectAction(projectId: string): Promise<ActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to delete a project",
      };
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    // Check if user is an admin
    const isAdmin = await isUserAdminInAnyTeam(userId, tenantId);
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to delete a project",
      };
    }

    const supabase = await createClient();

    // First, verify the project exists and belongs to the tenant
    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("id, name")
      .eq("id", projectId)
      .eq("tenant_id", tenantId)
      .single();

    if (projectError || !project) {
      console.info("[deleteProjectAction] Error finding project:", projectError);
      return {
        success: false,
        message:
          projectError?.message || "Project not found or you do not have permission to delete it",
        error: projectError,
      };
    }

    // Delete the project's team relationships first (due to foreign key constraints)
    const { error: teamRelationshipsError } = await supabase
      .from("project_teams")
      .delete()
      .eq("project_id", projectId);

    if (teamRelationshipsError) {
      console.info(
        "[deleteProjectAction] Error removing team relationships:",
        teamRelationshipsError
      );
      return {
        success: false,
        message: teamRelationshipsError.message || "Failed to remove team relationships",
        error: teamRelationshipsError,
      };
    }

    // Then delete the project itself
    const { error: deleteError } = await supabase
      .from("projects")
      .delete()
      .eq("id", projectId)
      .eq("tenant_id", tenantId);

    if (deleteError) {
      console.info("[deleteProjectAction] Error deleting project:", deleteError);
      return {
        success: false,
        message: deleteError.message || "Failed to delete project",
        error: deleteError,
      };
    }

    // Revalidate relevant paths to refresh UI
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>

    return {
      success: true,
      message: "Project deleted successfully",
    };
  } catch (error) {
    console.info("[deleteProjectAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Update project details
 */
export async function updateProjectDetailsAction(
  projectId: string,
  details: {
    name?: string;
    description?: string;
    status?: string;
    priority?: string;
  }
): Promise<ActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to update a project",
      };
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    // Check if user is an admin
    const isAdmin = await isUserAdminInAnyTeam(userId, tenantId);
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to update a project",
      };
    }

    // Make sure the project belongs to this tenant
    const supabase = await createClient();
    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("id")
      .eq("id", projectId)
      .eq("tenant_id", tenantId)
      .single();

    if (projectError || !project) {
      console.info("[updateProjectDetailsAction] Error finding project:", projectError);
      return {
        success: false,
        message:
          projectError?.message || "Project not found or you do not have permission to update it",
        error: projectError,
      };
    }

    // Update the project
    const { data, error } = await supabase
      .from("projects")
      .update({
        ...details,
        updated_at: new Date().toISOString(),
      })
      .eq("id", projectId)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.info("[updateProjectDetailsAction] Error updating project:", error);
      return {
        success: false,
        message: error.message || "Failed to update project",
        error,
      };
    }

    // Revalidate relevant paths to refresh UI
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>
    revalidatePath(`/[cluster-slug]/[project-slug]`);

    return {
      success: true,
      message: "Project updated successfully",
      data,
    };
  } catch (error) {
    console.info("[updateProjectDetailsAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Get a project by slug and tenant ID without requiring multiple lookups
 * More efficient for when we already know the tenant ID
 */
export async function getProjectBySlugAndTenantId(projectSlug: string, tenantId: string) {
  if (!tenantId || !projectSlug) {
    return null;
  }

  const supabase = await createClient();

  // Get project with tenant verification
  const { data, error } = await supabase
    .from("projects")
    .select(
      `
      id,
      name,
      slug,
      description,
      status,
      priority,
      tenant_id,
      cluster:clusters!inner(
        id,
        name,
        slug,
        tenant_id
      )
    `
    )
    .eq("slug", projectSlug)
    .eq("tenant_id", tenantId)
    .eq("cluster.tenant_id", tenantId)
    .single();

  if (error) {
    console.info("Error fetching project:", error);
    return null;
  }

  return {
    ...data,
    cluster: data.cluster[0],
  };
}
