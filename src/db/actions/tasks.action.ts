"use server";

import { <PERSON><PERSON><PERSON><PERSON>tatus, TaskGroupModel, TaskModel } from "@/db/schemas/tasks.schema";
import { getTaskGroupsByProjectFromDb, getTasksByProjectFromDb } from "@/db/tasks.db";
import { PageParams } from "@/types/next.types";

/**
 * Get raw tasks and task groups separately
 * This is the recommended approach for better data manipulation
 *
 * @param props - Page params containing project and cluster slugs
 * @returns Object containing tasks array and taskGroups array
 */
export async function getTasksAndGroupsAction(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
): Promise<{ tasks: TaskModel[]; taskGroups: TaskGroupModel[] }> {
  const tasks = await getTasksByProjectFromDb(props);
  const taskGroups = await getTaskGroupsByProjectFromDb(props);

  return { tasks, taskGroups };
}

/**
 * Transform tasks data for status-grouped view (Kanban)
 *
 * @param tasks - Array of tasks
 * @param taskGroups - Array of task groups
 * @returns Task groups with tasks filtered by status
 */
export async function transformForStatusGroupedView(
  tasks: TaskModel[],
  taskGroups: TaskGroupModel[]
): Promise<TaskGroupModel[]> {
  return taskGroups.map((group) => {
    const groupTasks = tasks.filter(
      (task) => task.status.toLowerCase() === group.name.toLowerCase()
    );

    return {
      ...group,
      count: groupTasks.length,
      tasks: groupTasks,
    };
  });
}

/**
 * Transform tasks data for lifecycle-grouped view
 *
 * @param tasks - Array of tasks
 * @returns Tasks grouped by lifecycle status
 */
export async function transformForLifecycleGroupedView(
  tasks: TaskModel[]
): Promise<TaskGroupModel[]> {
  // Define the lifecycle stages and their display properties
  const lifecycleStages: { id: string; name: LifeCycleStatus; color: string; icon: string }[] = [
    { id: "created", name: "created", color: "gray", icon: "circle-dashed" },
    { id: "ready", name: "ready", color: "blue", icon: "circle" },
    { id: "started", name: "started", color: "amber", icon: "circle-dot" },
    { id: "dev-done", name: "dev done", color: "indigo", icon: "circle-ellipsis" },
    { id: "test-done", name: "test done", color: "purple", icon: "circle-ellipsis" },
    { id: "deploy-ready", name: "deploy ready", color: "pink", icon: "circle-ellipsis" },
    { id: "delivered", name: "delivered", color: "green", icon: "check-circle" },
  ];

  return lifecycleStages.map((stage) => {
    const stageTasks = tasks.filter(
      (task) => task.lifeCycleStatus.toLowerCase() === stage.name.toLowerCase()
    );

    return {
      id: stage.id,
      name: stage.name,
      count: stageTasks.length,
      tasks: stageTasks,
      color: stage.color,
      icon: stage.icon,
    };
  });
}

/**
 * Transform tasks data for assignee-grouped view
 *
 * @param tasks - Array of tasks
 * @returns Tasks grouped by assignee
 */
export async function transformForAssigneeGroupedView(
  tasks: TaskModel[]
): Promise<TaskGroupModel[]> {
  // Create a map to store unique assignees
  const assigneeMap = new Map<string, { id: string; name: string; avatar?: string }>();

  // Extract all unique assignees
  tasks.forEach((task) => {
    // Ensure assignees is an array before processing
    if (Array.isArray(task.assignees)) {
      task.assignees.forEach((assignee) => {
        assigneeMap.set(assignee.id, assignee);
      });
    }
  });

  // Initialize groups array with unassigned group
  const groups: TaskGroupModel[] = [];

  // Add unassigned group if there are unassigned tasks
  const unassignedTasks = tasks.filter(
    (task) => !Array.isArray(task.assignees) || task.assignees.length === 0
  );
  if (unassignedTasks.length > 0) {
    groups.push({
      id: "unassigned",
      name: "Unassigned",
      count: unassignedTasks.length,
      tasks: unassignedTasks,
      color: "gray",
      icon: "user",
    });
  }

  // Add assignee groups
  Array.from(assigneeMap.entries()).forEach(([assigneeId, assigneeData]) => {
    const assigneeTasks = tasks.filter(
      (task) =>
        Array.isArray(task.assignees) &&
        task.assignees.some((assignee) => assignee.id === assigneeId)
    );

    if (assigneeTasks.length > 0) {
      groups.push({
        id: assigneeId,
        name: assigneeData.name || `User ${assigneeId}`,
        count: assigneeTasks.length,
        tasks: assigneeTasks,
        color: "blue",
        icon: "user",
      });
    }
  });

  return groups;
}

/**
 * Transform tasks data for value-grouped view
 *
 * @param tasks - Array of tasks
 * @returns Tasks grouped by value
 */
export async function transformForValueGroupedView(tasks: TaskModel[]): Promise<TaskGroupModel[]> {
  // Define value groups with their display properties
  const valueGroups = [
    { id: "huge", name: "Huge", color: "purple", icon: "chevrons-up" },
    { id: "high", name: "High", color: "red", icon: "chevron-up" },
    { id: "normal", name: "Normal", color: "blue", icon: "minus" },
    { id: "low", name: "Low", color: "green", icon: "chevron-down" },
    { id: "not-set", name: "Not set", color: "gray", icon: "dot" },
  ];

  return valueGroups.map((group) => {
    const groupTasks = tasks.filter(
      (task) => task.value.toLowerCase() === group.name.toLowerCase()
    );

    return {
      id: group.id,
      name: group.name,
      count: groupTasks.length,
      tasks: groupTasks,
      color: group.color,
      icon: group.icon,
    };
  });
}

/**
 * Get tags for a specific project
 *
 * @param projectId - The project ID to fetch tags for
 * @returns Array of tags for the project
 */
export async function getTagsByProjectAction(projectId: string) {
  const { getTagsByProjectFromDb } = await import("@/db/tasks.db");
  return getTagsByProjectFromDb(projectId);
}

/**
 * Get project team members (potential assignees)
 *
 * @param projectId - The project ID to fetch team members for
 * @returns Array of team members for the project
 */
export async function getProjectAssigneesAction(projectId: string) {
  const { getProjectAssigneesFromDb } = await import("@/db/tasks.db");
  return getProjectAssigneesFromDb(projectId);
}
