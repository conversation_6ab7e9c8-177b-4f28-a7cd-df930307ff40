"use server";

import {
  assignTeamToProjectInDb,
  getProjectsByTeamIdFromDb,
  getTeamsByProjectIdFromDb,
  removeTeamFromProjectInDb,
} from "@/db/team.db";
import { getTenantId } from "@/db/tenant.db";
import { getUserId } from "@/db/user.db";
import { revalidatePath } from "next/cache";
import { getCurrentUserOrganizationRoleAction } from "./organization-members.action";

// Database types that match the actual return types
type DbTeam = {
  id: string;
  name: string;
  description: string | null;
  tenant_id: string;
  created_at: string;
  updated_at: string;
};

type DbProject = {
  id: string;
  name: string;
  description: string | null;
  status: string;
  tenant_id: string;
  cluster_id: string;
  created_at: string;
  updated_at: string;
};

type ActionResult<T = undefined> = {
  success: boolean;
  message: string;
  data?: T;
  error?: Error | null;
};

// Define specific return types
type TeamActionResult = ActionResult<DbTeam>;
type TeamsActionResult = ActionResult<DbTeam[]>;
type ProjectsActionResult = ActionResult<DbProject[]>;

/**
 * Assign a team to a project
 */
export async function assignTeamToProjectAction(
  teamId: string,
  projectId: string
): Promise<TeamActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to assign teams to projects",
      };
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    // Check if user is an admin
    const response = await getCurrentUserOrganizationRoleAction();
    const isAdmin = response.data?.isAdmin;
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to assign teams to projects",
      };
    }

    const { data, error } = await assignTeamToProjectInDb(teamId, projectId, tenantId);

    if (error) {
      console.info("[assignTeamToProjectAction] Error assigning team to project:", error);
      return {
        success: false,
        message: error.message || "Failed to assign team to project",
        error,
      };
    }

    // Revalidate relevant paths to refresh UI
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>

    return {
      success: true,
      message: "Team assigned to project successfully",
      data,
    };
  } catch (error) {
    console.info("[assignTeamToProjectAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Remove a team from a project
 */
export async function removeTeamFromProjectAction(
  teamId: string,
  projectId: string
): Promise<ActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to remove teams from projects",
      };
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    // Check if user is an admin
    const response = await getCurrentUserOrganizationRoleAction();
    const isAdmin = response.data?.isAdmin;
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to remove teams from projects",
      };
    }

    const { data, error } = await removeTeamFromProjectInDb(teamId, projectId);

    if (error) {
      console.info("[removeTeamFromProjectAction] Error removing team from project:", error);
      return {
        success: false,
        message: error.message || "Failed to remove team from project",
        error,
      };
    }

    // Revalidate relevant paths to refresh UI
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>
    revalidatePath(`/home/<USER>

    return {
      success: true,
      message: "Team removed from project successfully",
      data,
    };
  } catch (error) {
    console.info("[removeTeamFromProjectAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Get teams by project ID
 */
export async function getTeamsByProjectAction(projectId: string): Promise<TeamsActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to view teams for a project",
        data: [],
      };
    }

    const { data, error } = await getTeamsByProjectIdFromDb(projectId);

    if (error) {
      console.info("[getTeamsByProjectAction] Error fetching teams:", error);
      return {
        success: false,
        message: error.message || "Failed to fetch teams for project",
        error,
        data: [],
      };
    }

    // Flatten the nested array and ensure it matches DbTeam type
    const teams = data.flat().map(
      (team): DbTeam => ({
        id: team.id,
        name: team.name,
        description: team.description,
        tenant_id: team.tenant_id,
        created_at: team.created_at,
        updated_at: team.updated_at,
      })
    );

    return {
      success: true,
      message: "Teams fetched successfully",
      data: teams,
    };
  } catch (error) {
    console.info("[getTeamsByProjectAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
      data: [],
    };
  }
}

/**
 * Get projects by team ID
 */
export async function getProjectsByTeamAction(teamId: string): Promise<ProjectsActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to view projects for a team",
        data: [],
      };
    }

    const { data, error } = await getProjectsByTeamIdFromDb(teamId);

    if (error) {
      console.info("[getProjectsByTeamAction] Error fetching projects:", error);
      return {
        success: false,
        message: error.message || "Failed to fetch projects for team",
        error,
        data: [],
      };
    }

    // Flatten the nested array and ensure it matches DbProject type
    const projects = data.flat().map(
      (project): DbProject => ({
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        tenant_id: project.tenant_id,
        cluster_id: project.cluster_id,
        created_at: project.created_at,
        updated_at: project.updated_at,
      })
    );

    return {
      success: true,
      message: "Projects fetched successfully",
      data: projects,
    };
  } catch (error) {
    console.info("[getProjectsByTeamAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
      data: [],
    };
  }
}
