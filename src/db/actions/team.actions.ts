"use server";

import { createTeamMemberAction } from "@/db/actions/team-members.action";
import { getUserProfileAction } from "@/db/actions/user-profile.action";
import {
  createTeamInDb,
  deleteTeamFromDb,
  getCurrentTenantIdFromDb,
  getProjectsByTeamIdsFromDb,
  getTeamMembersByTeamIdsFromDb,
  getTeamsByTenantIdFromDb,
  getTeamsByUserAndTenantIdFromDb,
  getUserProfilesByIdsFromDb,
  isUserAdminInAnyTeam,
} from "@/db/team.db";
import { getCurrentUser, getUserId } from "@/db/user.db";
import { Position, Role, TeamMemberUI, TeamUI } from "@/types/team.types";
import { User as SupabaseUser } from "@supabase/supabase-js";

export type TeamWithDetails = {
  id: string;
  name: string;
  description: string | null;
  members: {
    id: string;
    user_id: string;
    role: Role;
    position: Position;
    name: string;
    email: string;
    avatarUrl: string;
    createdAt: string;
  }[];
  projects: {
    id: string;
    name: string;
    description: string | null;
    status: string;
  }[];
};

type ProfileApiData = {
  id: string;
  user_id: string;
  full_name?: string | null;
  email?: string | null;
  avatar_url?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  created_at?: string;
};

type RawTeamWithMembers = {
  id: string;
  name: string;
  description: string | null;
  tenant_id: string;
  team_members: { user_id: string }[];
};

type RawTeamFromTenant = {
  id: string;
  name: string;
  description: string | null;
  tenant_id: string;
};

type InputTeamData = {
  id: string;
  name: string;
  description: string | null;
  tenant_id: string;
  team_members?: { user_id: string }[];
};

type ProjectWithDirectTeamId = {
  id: string;
  name: string;
  status: string;
  team_id: string;
};

type ActionResult<T = { id: string }> = {
  success: boolean;
  message: string;
  data?: T;
  error?: Error | null;
};

/**
 * Private helper function to map raw team data to TeamUI structure.
 * Fetches and attaches members (with profiles) and projects.
 */
async function _mapTeamsDataToTeamUI(
  inputTeamsData: InputTeamData[],
  tenantIdForContext: string,
  currentAuthUser: SupabaseUser | null
): Promise<TeamUI[]> {
  if (!inputTeamsData || inputTeamsData.length === 0) {
    return [];
  }

  const teamsUI: TeamUI[] = inputTeamsData.map((team) => ({
    id: team.id,
    name: team.name,
    description: team.description || null,
    tenantId: team.tenant_id,
    members: [],
    projects: [],
  }));

  const teamIds = inputTeamsData.map((team) => team.id);

  const { data: teamMembersData, error: teamMembersError } = await getTeamMembersByTeamIdsFromDb(
    teamIds,
    tenantIdForContext
  );

  if (teamMembersError) {
    console.info("[_mapTeamsDataToTeamUI] Error fetching team members:", teamMembersError);
  }

  const allTeamMembers = teamMembersData || [];

  if (allTeamMembers.length > 0) {
    const userIds = [...new Set(allTeamMembers.map((member) => member.user_id))];
    const { data: userProfiles, error: userProfilesError } = await getUserProfilesByIdsFromDb(
      userIds
    );

    if (userProfilesError) {
      console.info("[_mapTeamsDataToTeamUI] Error fetching user profiles:", userProfilesError);
    }

    const userProfilesMap = (userProfiles || []).reduce((acc, profile: ProfileApiData) => {
      acc[profile.user_id] = profile;
      return acc;
    }, {} as Record<string, ProfileApiData>);

    const membersByTeam = allTeamMembers.reduce((acc, member) => {
      if (!acc[member.team_id]) {
        acc[member.team_id] = [];
      }
      const userProfile = userProfilesMap[member.user_id];
      const isCurrentUser = currentAuthUser && member.user_id === currentAuthUser.id;
      const shortUserIdFallback = member.user_id.substring(0, 6);
      const formattedUserIdFallback =
        shortUserIdFallback.charAt(0).toUpperCase() + shortUserIdFallback.slice(1);

      acc[member.team_id].push({
        id: member.id,
        userId: member.user_id,
        name:
          userProfile?.full_name ||
          userProfile?.first_name ||
          userProfile?.last_name ||
          (isCurrentUser
            ? currentAuthUser?.user_metadata?.name ||
              currentAuthUser?.user_metadata?.full_name ||
              currentAuthUser?.email?.split("@")[0]
            : `${member.position || "User"} ${formattedUserIdFallback}`),
        email:
          userProfile?.email ||
          (isCurrentUser ? currentAuthUser?.user_metadata?.email : "No email available"),
        role: member.role as Role,
        position: member.position as Position,
        createdAt: member.created_at,
        avatarUrl:
          userProfile?.avatar_url ||
          (isCurrentUser ? currentAuthUser?.user_metadata?.avatar_url : undefined),
      });
      return acc;
    }, {} as Record<string, TeamMemberUI[]>);

    teamsUI.forEach((team) => {
      team.members = membersByTeam[team.id] || [];
    });
  }

  const { data: rawProjectsData, error: projectsError } = await getProjectsByTeamIdsFromDb(teamIds);
  const projectsData = rawProjectsData as ProjectWithDirectTeamId[] | undefined;

  if (!projectsError && projectsData && projectsData.length > 0) {
    const projectsByTeam = projectsData.reduce((acc, project: ProjectWithDirectTeamId) => {
      if (!acc[project.team_id]) {
        acc[project.team_id] = [];
      }
      acc[project.team_id].push({
        id: project.id,
        name: project.name,
        color: project.status,
      });
      return acc;
    }, {} as Record<string, { id: string; name: string; color: string }[]>);

    teamsUI.forEach((team) => {
      team.projects = projectsByTeam[team.id] || [];
    });
  }
  return teamsUI;
}

/**
 * Create a new team
 */
export async function createTeamAction(data: {
  name: string;
  description?: string;
}): Promise<ActionResult<{ id: string }>> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to create a team",
      };
    }

    const userProfile = await getUserProfileAction();
    if (!userProfile) {
      return {
        success: false,
        message: "Unable to determine your profile",
      };
    }

    const tenantId = await getCurrentTenantIdFromDb();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    const isAdmin = await isUserAdminInAnyTeam(userId, tenantId);
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to create a team",
      };
    }

    const { data: newTeam, error } = await createTeamInDb(tenantId, data);

    if (error || !newTeam) {
      console.info("[createTeamAction] Error creating team:", error);
      return {
        success: false,
        message: error?.message || "Failed to create team",
        error,
      };
    }

    const teamMemberResult = await createTeamMemberAction({
      userId,
      teamId: newTeam.id,
      role: "admin" as Role,
      position: (userProfile.position as Position) || ("Developer" as Position),
      tenantId,
    });

    if (!teamMemberResult.success) {
      console.info("[createTeamAction] Error adding team member:", teamMemberResult.error);
      return {
        success: true,
        message: `Team created successfully, but failed to add you as a team member. Error: ${teamMemberResult.message}`,
        data: { id: newTeam.id },
      };
    }

    return {
      success: true,
      message: "Team created successfully and you were added as an admin",
      data: { id: newTeam.id },
    };
  } catch (error) {
    console.info("[createTeamAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Delete a team
 */
export async function deleteTeamAction(teamId: string): Promise<ActionResult> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "You must be logged in to delete a team",
      };
    }

    const tenantId = await getCurrentTenantIdFromDb();
    if (!tenantId) {
      return {
        success: false,
        message: "Unable to determine your organization",
      };
    }

    const isAdmin = await isUserAdminInAnyTeam(userId, tenantId);
    if (!isAdmin) {
      return {
        success: false,
        message: "You need admin permissions to delete a team",
      };
    }

    const { success, error } = await deleteTeamFromDb(teamId, tenantId);

    if (!success || error) {
      console.info("[deleteTeamAction] Error deleting team:", error);
      return {
        success: false,
        message: error?.message || "Failed to delete team",
        error,
      };
    }

    return {
      success: true,
      message: "Team deleted successfully",
    };
  } catch (error) {
    console.info("[deleteTeamAction] Unexpected error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Check if the current user is an admin in any team within the current tenant
 */
export async function isCurrentUserAdminAction(): Promise<ActionResult<{ isAdmin: boolean }>> {
  try {
    const userId = await getUserId();
    if (!userId) {
      return { success: false, message: "User not authenticated", data: { isAdmin: false } };
    }

    const tenantId = await getCurrentTenantIdFromDb();
    if (!tenantId) {
      return { success: false, message: "Organization not found", data: { isAdmin: false } };
    }

    const isAdmin = await isUserAdminInAnyTeam(userId, tenantId);

    return {
      success: true,
      message: isAdmin ? "User is admin" : "User is not admin",
      data: { isAdmin },
    };
  } catch (error) {
    console.info("[isCurrentUserAdminAction] Error:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to check admin status",
      data: { isAdmin: false },
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Get all teams for the current tenant that the user is a member of, with their members and projects
 * Returns the data in a format compatible with the TeamsList component
 */
export async function getTeamsWithMembersByTenantByUserAction(): Promise<{
  teams: TeamUI[];
  currentUserName: string;
}> {
  try {
    const userProfile = await getUserProfileAction();
    const tenantId = await getCurrentTenantIdFromDb();
    const userId = await getUserId();

    if (!tenantId || !userId) {
      return { teams: [], currentUserName: userProfile?.name || userProfile?.email || "" };
    }

    const { data: authData } = await getCurrentUser();
    const currentAuthUser = authData.user;

    const { data: rawTeams, error: teamsError } = await getTeamsByUserAndTenantIdFromDb(
      userId,
      tenantId
    );
    const teamsData = rawTeams as RawTeamWithMembers[] | undefined;

    if (teamsError) {
      console.info("[GTWMBTBUserAction] Error fetching teams for user:", teamsError);
      return { teams: [], currentUserName: userProfile?.name || userProfile?.email || "" };
    }

    const teams = await _mapTeamsDataToTeamUI(teamsData || [], tenantId, currentAuthUser);

    return {
      teams,
      currentUserName: userProfile?.name || userProfile?.email || "",
    };
  } catch (error) {
    console.info("[GTWMBTBUserAction] Overall error:", error);
    return {
      teams: [],
      currentUserName: "",
    };
  }
}

/**
 * Get all teams for the current tenant with their members and projects (admin view)
 * Returns the data in a format compatible with the TeamsList component
 */
export async function getTeamsWithMembersByTenantAction(): Promise<{
  teams: TeamUI[];
  currentUserName: string;
}> {
  try {
    const userProfile = await getUserProfileAction();
    const tenantId = await getCurrentTenantIdFromDb();

    if (!tenantId) {
      return { teams: [], currentUserName: userProfile?.name || userProfile?.email || "" };
    }

    const { data: authData } = await getCurrentUser();
    const currentAuthUser = authData.user;

    const { data: rawTeams, error: teamsError } = await getTeamsByTenantIdFromDb(tenantId);
    const teamsData = rawTeams as RawTeamFromTenant[] | undefined;

    if (teamsError) {
      console.info("[GTWMBTAction] Error fetching all teams for tenant:", teamsError);
      return { teams: [], currentUserName: userProfile?.name || userProfile?.email || "" };
    }

    const teams = await _mapTeamsDataToTeamUI(teamsData || [], tenantId, currentAuthUser);

    return {
      teams,
      currentUserName: userProfile?.name || userProfile?.email || "",
    };
  } catch (error) {
    console.info("[GTWMBTAction] Overall error:", error);
    return {
      teams: [],
      currentUserName: "",
    };
  }
}
