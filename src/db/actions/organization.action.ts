"use server";

import { getOrganizationMemberFromDb } from "@/db/organization-members.db";
import { getTenantId } from "@/db/tenant.db";
import { getUserId, getUserMetadataFromDb } from "@/db/user.db";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { getCurrentOrganization } from "../organization.db";

/**
 * Get the user's organizations
 */
export async function getUserOrganizationsAction() {
  const userId = await getUserId();
  if (!userId) {
    redirect("/auth/login");
  }

  const { data: organizations, error } = await getOrganizationMemberFromDb(userId);

  if (error) {
    console.info("Error fetching organizations:", error);
    throw new Error("Failed to fetch organizations");
  }
  // TODO: Manage auth flow for personal accounts
  // if (!organizations || organizations.length === 0) {
  //   redirect("/auth/organizations/new");
  // }

  return organizations;
}

/**
 * Get the user's metadata
 */
export async function getUserMetadataAction() {
  const userId = await getUserId();
  if (!userId) {
    redirect("/auth/login");
  }

  const userMetadata = await getUserMetadataFromDb();

  if (!userMetadata) {
    return null;
  }

  return userMetadata;
}

/**
 * Get the current organization
 */
export async function getCurrentOrganizationAction() {
  const tenantId = await getTenantId();

  if (!tenantId) {
    return null;
  }

  const organization = await getCurrentOrganization(tenantId);

  return organization;
}

/**
 * Get all members of the current user's organization (tenant)
 */
export async function getAllOrganizationMembersAction() {
  try {
    const supabase = await createClient();

    // Get the current user's tenant_id
    const tenantId = await getTenantId();

    if (!tenantId) {
      throw new Error("User not found in any organization");
    }

    // Fetch all organization members first
    const { data: members, error: membersError } = await supabase
      .from("organization_members")
      .select("id, user_id, role, tenant_id")
      .eq("tenant_id", tenantId);

    if (membersError) {
      console.info("[Fetch All Organization Members] Database query error", membersError);
      throw new Error(`Error fetching members: ${membersError.message}`);
    }

    if (!members || members.length === 0) {
      return {
        data: [],
        message: "No members found",
      };
    }

    // Get all user IDs from the members
    const userIds = members.map((member) => member.user_id);

    // Fetch profiles for these users in a separate query
    const { data: profiles, error: profilesError } = await supabase
      .from("profiles")
      .select("id, user_id, full_name, email, avatar_url, joined_date, position")
      .in("user_id", userIds);

    if (profilesError) {
      console.info("[Fetch All Organization Members] Profiles query error", profilesError);
      throw new Error(`Error fetching profiles: ${profilesError.message}`);
    }

    // Create a map of user_id to profile for easy lookup
    const profileMap = new Map();
    profiles?.forEach((profile) => {
      profileMap.set(profile.user_id, profile);
    });

    // Transform the data to match our client-side interface
    const transformedData = members.map((member) => {
      const profile = profileMap.get(member.user_id);

      return {
        id: profile?.id || member.id,
        userId: member.user_id,
        name: profile?.full_name || "Unknown User",
        email: profile?.email || "No email",
        avatarUrl: profile?.avatar_url,
        tenantId: member.tenant_id,
        role: member.role,
        joinedAt: profile?.joined_date,
        position: profile?.position,
      };
    });

    return {
      data: transformedData,
      message: "Members found",
    };
  } catch (error) {
    console.info("[Fetch All Organization Members] Unexpected error:", error);
    throw new Error(`Failed to fetch organization members: ${(error as Error).message}`);
  }
}

/**
 * Search organization members by name or email
 */
export async function searchOrganizationMembersAction(searchQuery: string) {
  try {
    if (!searchQuery || searchQuery.length < 2) {
      return {
        data: [],
        message: "Search query must be at least 2 characters",
      };
    }

    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      throw new Error("User not found in any organization");
    }

    // Fetch organization_members and their profiles
    // The search query will be applied during the transformation step
    const { data: membersWithProfiles, error: fetchError } = await supabase
      .from("organization_members")
      .select(
        `
        id,
        user_id,
        role,
        tenant_id,
        profiles (
          id,
          user_id,
          full_name,
          email,
          avatar_url
        )
      `
      )
      .eq("tenant_id", tenantId);

    if (fetchError) {
      console.info("[Search Organization Members] Database query error", fetchError);
      throw new Error(`Error searching members: ${fetchError.message}`);
    }

    if (!membersWithProfiles || membersWithProfiles.length === 0) {
      return {
        data: [],
        message: "No members found in this organization",
      };
    }

    const lowerSearchQuery = searchQuery.toLowerCase();

    // Transform the data and filter by search query
    const transformedAndFilteredData = membersWithProfiles
      .map((member) => {
        const profile = Array.isArray(member.profiles) ? member.profiles[0] : member.profiles;
        return {
          id: profile?.id || member.id,
          userId: member.user_id,
          name: profile?.full_name || "Unknown User",
          email: profile?.email || "No email",
          avatarUrl: profile?.avatar_url,
          tenantId: member.tenant_id,
          role: member.role,
          // Keep profile data for filtering
          _profileFullName: profile?.full_name?.toLowerCase(),
          _profileEmail: profile?.email?.toLowerCase(),
        };
      })
      .filter(
        (member) =>
          member._profileFullName?.includes(lowerSearchQuery) ||
          member._profileEmail?.includes(lowerSearchQuery)
      )
      .map(({ ...rest }) => rest) // Remove temporary fields
      .slice(0, 10); // Limit results as before

    if (transformedAndFilteredData.length === 0) {
      return {
        data: [],
        message: "No members found matching your search query",
      };
    }

    return {
      data: transformedAndFilteredData,
      message: "Members found",
    };
  } catch (error) {
    console.info("[Search Organization Members] Unexpected error:", error);
    throw new Error(`Failed to search organization members: ${(error as Error).message}`);
  }
}

/**
 * Update the role of an organization member
 * @param memberId - The ID of the organization member to update
 * @param role - The new role to assign
 */
export async function updateOrganizationMemberRoleAction({
  userId,
  role,
}: {
  userId: string;
  role: string;
}) {
  try {
    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      return {
        success: false,
        message: "User not found in any organization",
      };
    }

    // Verify the role is valid
    if (!["admin", "member", "guest"].includes(role)) {
      return {
        success: false,
        message: "Invalid role. Role must be one of: admin, member, guest",
      };
    }

    // Update the member's role
    const { error } = await supabase
      .from("organization_members")
      .update({ role })
      .eq("user_id", userId)
      .eq("tenant_id", tenantId);

    if (error) {
      console.info("[Update Organization Member Role] Database update error", error);
      return {
        success: false,
        message: `Failed to update member role: ${error.message}`,
      };
    }

    return {
      success: true,
      message: "Member role updated successfully",
    };
  } catch (error) {
    console.info("[Update Organization Member Role] Unexpected error:", error);
    return {
      success: false,
      message: `Failed to update member role: ${(error as Error).message}`,
    };
  }
}

/**
 * Remove a member from the organization and all associated teams
 * @param userId - The ID of the user to remove
 * @returns An object containing success status and message
 */
export async function removeOrganizationMemberAction(userId: string) {
  try {
    if (!userId) {
      return {
        success: false,
        message: "User ID is required",
      };
    }

    const supabase = await createClient();
    const tenantId = await getTenantId();

    if (!tenantId) {
      return {
        success: false,
        message: "User not found in any organization",
      };
    }

    // Check if the current user has admin permissions
    const { data: currentUserRole } = await supabase
      .from("organization_members")
      .select("role")
      .eq("user_id", await getUserId())
      .eq("tenant_id", tenantId)
      .single();

    if (!currentUserRole || !["admin", "owner"].includes(currentUserRole.role)) {
      return {
        success: false,
        message: "You need admin permissions to remove members",
      };
    }

    // Check if the user being removed is an owner (cannot remove owners)
    const { data: targetUserRole } = await supabase
      .from("organization_members")
      .select("role")
      .eq("user_id", userId)
      .eq("tenant_id", tenantId)
      .single();

    if (targetUserRole && targetUserRole.role === "owner") {
      return {
        success: false,
        message: "Cannot remove an organization owner",
      };
    }

    // Start a transaction to ensure all operations complete together
    // First, get all team memberships for this user in the organization
    const { data: teamMemberships, error: teamQueryError } = await supabase
      .from("team_members")
      .select("id")
      .eq("user_id", userId)
      .eq("tenant_id", tenantId);

    if (teamQueryError) {
      console.info("[Remove Organization Member] Team query error:", teamQueryError);
      return {
        success: false,
        message: `Error finding team memberships: ${teamQueryError.message}`,
      };
    }

    // Delete from team_members first
    if (teamMemberships && teamMemberships.length > 0) {
      const teamMemberIds = teamMemberships.map((m) => m.id);
      const { error: teamDeleteError } = await supabase
        .from("team_members")
        .delete()
        .in("id", teamMemberIds);

      if (teamDeleteError) {
        console.info("[Remove Organization Member] Team deletion error:", teamDeleteError);
        return {
          success: false,
          message: `Failed to remove user from teams: ${teamDeleteError.message}`,
        };
      }
    }

    // Finally, remove from organization_members
    const { error: orgDeleteError } = await supabase
      .from("organization_members")
      .delete()
      .eq("user_id", userId)
      .eq("tenant_id", tenantId);

    if (orgDeleteError) {
      console.info("[Remove Organization Member] Organization deletion error:", orgDeleteError);
      return {
        success: false,
        message: `Failed to remove user from organization: ${orgDeleteError.message}`,
      };
    }

    return {
      success: true,
      message: "Member removed successfully from organization and teams",
    };
  } catch (error) {
    console.info("[Remove Organization Member] Unexpected error:", error);
    return {
      success: false,
      message: `Failed to remove member: ${(error as Error).message}`,
    };
  }
}
