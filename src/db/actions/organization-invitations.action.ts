"use server";

import { Invitation } from "@/app/home/<USER>/_components/_employees_components/invitation-types";
import { getTenantId } from "@/db/tenant.db";
import { sendOrganizationInvitationEmail } from "@/lib/email-service";
import { createClient } from "@/lib/supabase/server";
import { OrganizationInfoType } from "@/types/organization.types";
import crypto from "crypto";
import { revalidatePath } from "next/cache";

// Define consistent return types
type BaseActionResult = {
  success: boolean;
  message: string;
};

type InvitationActionResult = BaseActionResult & {
  invitation: Invitation | null;
  emailSent?: boolean;
};

type InvitationsListResult = BaseActionResult & {
  invitations: Invitation[];
};

type InvitationValidationResult = BaseActionResult & {
  isValid: boolean;
  invitation: Invitation | null;
  organization?: OrganizationInfoType | null;
};

/**
 * Creates an invitation for a user to join an organization
 */
export async function createOrganizationInvitationAction(
  email: string,
  organization: OrganizationInfoType
): Promise<InvitationActionResult> {
  try {
    const supabase = await createClient();

    // Get current user's organization
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      return {
        success: false,
        message: "You must be logged in to invite users",
        invitation: null,
      };
    }

    // Get current tenant ID (organization ID)
    // First try to get from organization_members
    const { data: orgMemberData } = await supabase
      .from("organization_members")
      .select("organization_id")
      .eq("organization_id", organization.id)
      .eq("user_id", userData.user.id)
      .single();

    if (!orgMemberData?.organization_id) {
      // Try the getTenantId helper as fallback
      const tenantId = await getTenantId();
      if (!tenantId) {
        return {
          success: false,
          message: "Could not determine your organization",
          invitation: null,
        };
      }
      organization.id = tenantId;
    }

    if (!organization) {
      return {
        success: false,
        message: "Could not retrieve organization information",
        invitation: null,
      };
    }

    // Check if an invitation already exists for this email and organization (regardless of status)
    const { data: existingInvitation } = await supabase
      .from("organization_invitations")
      .select("*")
      .eq("organization_id", organization.id)
      .eq("email", email)
      .single();

    // Generate a secure random token - only if we don't have an existing invitation or need a new one
    const token = existingInvitation?.token || crypto.randomBytes(32).toString("hex");

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    let invitation;
    let error;

    // If there's an existing invitation, update it instead of creating a new one
    if (existingInvitation) {
      const { data: updatedInvitation, error: updateError } = await supabase
        .from("organization_invitations")
        .update({
          status: "pending", // Always reset to pending when reinviting
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
        })
        .eq("id", existingInvitation.id)
        .select()
        .single();

      invitation = updatedInvitation;
      error = updateError;
    } else {
      // Create a new invitation record if none exists
      const { data: newInvitation, error: insertError } = await supabase
        .from("organization_invitations")
        .insert({
          organization_id: organization.id,
          email,
          token,
          status: "pending",
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      invitation = newInvitation;
      error = insertError;
    }

    if (error) {
      return {
        success: false,
        message: "Failed to manage invitation: " + error.message,
        invitation: null,
      };
    }

    // Send invitation email
    const emailResult = await sendOrganizationInvitationEmail({
      email,
      organizationName: organization.name,
      invitationToken: token,
      subdomain: organization.subdomain_id, // Pass the actual subdomain_id
    });

    if (!emailResult.success) {
      return {
        success: true,
        message:
          "Invitation processed, but email delivery failed. You may need to share the invitation link manually.",
        invitation,
        emailSent: false,
      };
    }

    // Revalidate the path to refresh the UI
    revalidatePath("/home/<USER>");

    const actionType = existingInvitation ? "updated" : "created";
    return {
      success: true,
      message: `Invitation ${actionType} and email sent successfully`,
      invitation,
      emailSent: true,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      invitation: null,
      emailSent: false,
    };
  }
}

/**
 * Gets all pending invitations for the current organization
 */
export async function getPendingOrganizationInvitationsAction(
  organizationId: string
): Promise<InvitationsListResult> {
  try {
    const supabase = await createClient();

    // Get current user's organization
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      return {
        success: false,
        message: "You must be logged in to view invitations",
        invitations: [],
      };
    }

    // Get current tenant ID (organization ID)
    if (!organizationId) {
      return {
        success: true, // Return success but with empty invitations
        message: "User has no organization yet",
        invitations: [],
      };
    }

    // Get all pending invitations for this organization
    const { data: invitations, error } = await supabase
      .from("organization_invitations")
      .select("*")
      .eq("organization_id", organizationId)
      .eq("status", "pending")
      .order("created_at", { ascending: false });

    if (error) {
      return {
        success: false,
        message: "Failed to fetch invitations: " + error.message,
        invitations: [],
      };
    }

    return {
      success: true,
      message: "Invitations fetched successfully",
      invitations: invitations || [],
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      invitations: [],
    };
  }
}

/**
 * Cancels an organization invitation
 */
export async function cancelOrganizationInvitationAction(
  invitationId: string,
  organization: OrganizationInfoType
): Promise<BaseActionResult> {
  try {
    const supabase = await createClient();

    // Get current user's organization
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      return {
        success: false,
        message: "You must be logged in to cancel invitations",
      };
    }

    // Get current tenant ID (organization ID)
    // First try to get from organization_members
    const { data: orgMemberData } = await supabase
      .from("organization_members")
      .select("organization_id, role")
      .eq("organization_id", organization.id)
      .eq("user_id", userData.user.id)
      .single();

    if (!orgMemberData?.organization_id) {
      // Try the getTenantId helper as fallback
      const tenantId = await getTenantId();
      if (!tenantId) {
        return {
          success: false,
          message: "Could not determine your organization",
        };
      }
      organization.id = tenantId;
    } else if (orgMemberData.role !== "admin" && orgMemberData.role !== "owner") {
      return {
        success: false,
        message: "You don't have permission to cancel invitations",
      };
    }

    if (!organization) {
      return {
        success: false,
        message: "Could not retrieve organization information",
      };
    }

    // Update invitation status to cancelled
    const { error } = await supabase
      .from("organization_invitations")
      .update({
        status: "cancelled",
        // We don't change the created_at or expires_at fields to preserve history
      })
      .eq("id", invitationId)
      .eq("organization_id", organization.id);

    if (error) {
      return {
        success: false,
        message: "Failed to cancel invitation: " + error.message,
      };
    }

    // Revalidate the path to refresh the UI
    revalidatePath("/home/<USER>");

    return {
      success: true,
      message: "Invitation cancelled successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}

/**
 * Resends an organization invitation
 */
export async function resendOrganizationInvitationAction(
  invitationId: string,
  organization: OrganizationInfoType
): Promise<InvitationActionResult> {
  try {
    const supabase = await createClient();

    // Get current user's organization
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      return {
        success: false,
        message: "You must be logged in to resend invitations",
        emailSent: false,
        invitation: null,
      };
    }

    // Get current tenant ID (organization ID)
    // First try to get from organization_members
    const { data: orgMemberData } = await supabase
      .from("organization_members")
      .select("organization_id, role")
      .eq("organization_id", organization.id)
      .eq("user_id", userData.user.id)
      .single();

    if (!orgMemberData?.organization_id) {
      // Try the getTenantId helper as fallback
      const tenantId = await getTenantId();
      if (!tenantId) {
        return {
          success: false,
          message: "Could not determine your organization",
          emailSent: false,
          invitation: null,
        };
      }
      organization.id = tenantId;
    } else if (orgMemberData.role !== "admin" && orgMemberData.role !== "owner") {
      return {
        success: false,
        message: "You don't have permission to resend invitations",
        emailSent: false,
        invitation: null,
      };
    }

    if (!organization) {
      return {
        success: false,
        message: "Could not retrieve organization information",
        emailSent: false,
        invitation: null,
      };
    }

    // Get the invitation to update
    const { data: invitation, error: fetchError } = await supabase
      .from("organization_invitations")
      .select("*")
      .eq("id", invitationId)
      .eq("organization_id", organization.id)
      .single();

    if (fetchError || !invitation) {
      return {
        success: false,
        message: "Invitation not found",
        emailSent: false,
        invitation: null,
      };
    }

    // Set new expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Update the invitation with new expiration
    const { error } = await supabase
      .from("organization_invitations")
      .update({
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString(),
      })
      .eq("id", invitationId);

    if (error) {
      return {
        success: false,
        message: "Failed to resend invitation: " + error.message,
        emailSent: false,
        invitation: null,
      };
    }

    // Get the organization for this invitation
    const { data: orgData, error: orgError } = await supabase
      .from("organizations")
      .select("name, subdomain_id")
      .eq("id", organization.id)
      .single();

    let emailSent = false;
    if (!orgError && orgData && invitation.email) {
      // Send invitation email
      const emailResult = await sendOrganizationInvitationEmail({
        email: invitation.email,
        organizationName: orgData.name || organization.name,
        invitationToken: invitation.token,
        subdomain: orgData.subdomain_id,
      });

      if (!emailResult.success) {
        return {
          success: true,
          message:
            "Invitation updated, but email delivery failed. You may need to share the invitation link manually.",
          emailSent: false,
          invitation,
        };
      }
      emailSent = true;
    }

    // Revalidate the path to refresh the UI
    revalidatePath("/home/<USER>");

    return {
      success: true,
      message: "Invitation resent successfully",
      emailSent,
      invitation,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      emailSent: false,
      invitation: null,
    };
  }
}

/**
 * Validates an invitation token
 */
export async function validateOrganizationInvitationTokenAction(
  token: string
): Promise<InvitationValidationResult> {
  try {
    const supabase = await createClient();

    // Get the invitation by token
    const { data: invitation, error } = await supabase
      .from("organization_invitations")
      .select("*")
      .eq("token", token)
      .eq("status", "pending")
      .single();

    if (error || !invitation) {
      return {
        success: false,
        message: "Invalid or expired invitation",
        isValid: false,
        invitation: null,
      };
    }

    // Check if the invitation has expired
    const expiresAt = new Date(invitation.expires_at);
    const now = new Date();

    if (now > expiresAt) {
      return {
        success: false,
        message: "Invitation has expired",
        isValid: false,
        invitation: null,
      };
    }

    // Get the organization for this invitation
    const { data: organization, error: orgError } = await supabase
      .from("organizations")
      .select("*")
      .eq("id", invitation.organization_id)
      .single();

    if (orgError || !organization) {
      return {
        success: false,
        message: "Organization not found",
        isValid: false,
        invitation: null,
      };
    }

    return {
      success: true,
      message: "Invitation is valid",
      isValid: true,
      invitation,
      organization,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
      isValid: false,
      invitation: null,
    };
  }
}

/**
 * Accepts an organization invitation and adds the user to the organization
 */
export async function acceptOrganizationInvitationAction(
  invitationToken: string,
  userId: string
): Promise<BaseActionResult & { organizationId?: string }> {
  try {
    // Validate input parameters
    if (!invitationToken || typeof invitationToken !== "string") {
      return {
        success: false,
        message: "Invalid invitation token provided",
      };
    }

    if (!userId || typeof userId !== "string") {
      return {
        success: false,
        message: "Invalid user ID provided",
      };
    }

    const { adminClient } = await import("@/lib/supabase/admin");

    // Find the invitation by token (either already accepted or pending)

    const { data: invitation, error: invitationError } = await adminClient
      .from("organization_invitations")
      .select("*")
      .eq("token", invitationToken)
      .maybeSingle();

    if (invitationError) {
      return {
        success: false,
        message: "Failed to find invitation: " + invitationError.message,
      };
    }

    if (!invitation) {
      return {
        success: false,
        message: "Invalid invitation: No invitation found with this token",
      };
    }

    // If already used/accepted, just return success
    if (invitation.status === "accepted") {
      return {
        success: true,
        message: "You have already accepted this invitation",
        organizationId: invitation.organization_id,
      };
    }

    // Check if the invitation has expired
    const expiresAt = new Date(invitation.expires_at);
    const now = new Date();
    if (now > expiresAt) {
      return {
        success: false,
        message: "Invitation has expired",
      };
    }

    // Add the user to the organization (ignore if already a member)
    const { error: memberError } = await adminClient.from("organization_members").upsert(
      {
        organization_id: invitation.organization_id,
        user_id: userId,
        role: "member",
        tenant_id: invitation.organization_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "user_id,organization_id",
        ignoreDuplicates: true,
      }
    );

    if (memberError && memberError.code !== "23505") {
      // Not a unique constraint error
      return {
        success: false,
        message: "Failed to add user to organization: " + memberError.message,
      };
    }

    // Update invitation status to used
    const { error: updateError } = await adminClient
      .from("organization_invitations")
      .update({
        status: "accepted",
        accepted_at: new Date().toISOString(),
        accepted_by: userId,
      })
      .eq("id", invitation.id);

    if (updateError) {
      return {
        success: false,
        message: "Failed to update invitation status: " + updateError.message,
      };
    }

    // Revalidate the path to refresh the UI
    revalidatePath("/home/<USER>");

    return {
      success: true,
      message: "Successfully joined organization",
      organizationId: invitation.organization_id,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
