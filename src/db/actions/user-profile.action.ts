"use server";

import { getCurrentUserOrganizationRoleFromDb } from "@/db/organization-members.db";
import { updateUserPositionInDb } from "@/db/profile.db";
import { getTenantId } from "@/db/tenant.db";
import { getUserMetadataFromDb } from "@/db/user.db";
import { UserProfile } from "@/types/user.types";

export async function getUserProfileAction() {
  const userMetadata = await getUserMetadataFromDb();
  const tenantId = await getTenantId();
  if (!tenantId) {
    return null;
  }
  const organizationMemberRole = await getCurrentUserOrganizationRoleFromDb(tenantId);
  const user: UserProfile = {
    name: userMetadata?.display_name,
    email: userMetadata?.email,
    joinedDate: new Date(userMetadata?.created_at).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
    avatar: userMetadata?.avatar_url,
    avatarFallback: userMetadata?.first_name?.[0] + userMetadata?.last_name?.[0] || "AA",
    location: userMetadata?.location || "Taiwan",
    department: userMetadata?.department || "R&D",
    role:
      organizationMemberRole?.data?.role.charAt(0).toUpperCase() +
        organizationMemberRole?.data?.role.slice(1) || "Unknown",
    position: userMetadata?.position || "Developer",
    stats: {
      projects: 3,
      tasksDone: 10,
      pullRequests: 2,
    },
  };

  return user;
}

/**
 * Update a user's position
 * @param userId - The user ID to update
 * @param position - The new position
 * @returns An object containing success status, message, and any data
 */
export async function updateUserPositionAction({
  userId,
  position,
}: {
  userId: string;
  position: string;
}) {
  if (!userId) {
    return {
      success: false,
      message: "User ID is required",
      data: null,
    };
  }

  // Validate the position
  if (!position || position.trim() === "") {
    return {
      success: false,
      message: "Position is required",
      data: null,
    };
  }

  // Call the database function to update the position
  const result = await updateUserPositionInDb(userId, position);

  return result;
}
