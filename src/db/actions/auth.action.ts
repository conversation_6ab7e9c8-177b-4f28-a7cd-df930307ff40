"use server";

import { getOrganizationExistsInDb } from "@/db/organization.db";
import { createClient } from "@/lib/supabase/server";
import { getCurrentSubdomain } from "@/lib/utils";
import { headers } from "next/headers";

export async function getAuthStateAction() {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  return !!session;
}

// Define type for the form state
export type FormState = {
  error: string | null;
  success?: boolean;
  redirectTo?: string;
};

export async function loginFormAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return {
      error: "Email and password are required",
      success: false,
    };
  }

  const supabase = await createClient();

  try {
    // Check if we're on a subdomain using the request headers
    const headersList = await headers();
    const hostname = headersList.get("host") || "";

    // Use the utility function to determine if this is a subdomain request
    const subdomain = getCurrentSubdomain(hostname);

    // If this is a subdomain request, verify the organization exists
    if (subdomain) {
      const { exists } = await getOrganizationExistsInDb(subdomain);

      if (!exists) {
        return {
          error: "This organization does not exist",
          success: false,
          redirectTo: "/",
        };
      }
    }

    // Proceed with authentication
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    return {
      error: null,
      success: true,
      // Redirect to /home if on subdomain, otherwise to /auth/organizations
      redirectTo: subdomain ? "/home" : "/auth/organizations",
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "An error occurred",
      success: false,
    };
  }
}
