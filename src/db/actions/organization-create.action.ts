"use server";

import {
  addOrganizationMemberInDb,
  createOrganizationInDb,
  getAllSubdomainsFromDb,
  getOrganizationExistsInDb,
} from "@/db/organization.db";
import { OrganizationSchema, organizationFormConfig } from "@/db/schemas/organization.schema";
import { getUserId } from "@/db/user.db";
import { revalidatePath } from "next/cache";
import { z } from "zod";

/**
 * Create a new organization and add the current user as an admin
 * This is a server action called from the client
 */
export async function createOrganizationAction(
  formData: z.infer<typeof OrganizationSchema>
): Promise<{
  success: boolean;
  message: string;
  organizationId?: string;
  error?: string;
}> {
  try {
    // Get current user
    const userId = await getUserId();

    if (!userId) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    // Check if organization with subdomain already exists
    const { exists, error: checkError } = await getOrganizationExistsInDb(formData.subdomain_id);

    // Handle database errors (except "not found" which is expected)
    if (checkError && checkError.code !== "PGRST116") {
      return {
        success: false,
        message: "Error checking if organization exists",
        error: checkError.message,
      };
    }

    if (exists) {
      return {
        success: false,
        message: "An organization with this subdomain already exists",
      };
    }

    // Create organization
    const { data: organization, error: orgError } = await createOrganizationInDb(formData);

    if (orgError) {
      return {
        success: false,
        message: "Failed to create organization",
        error: orgError.message,
      };
    }

    if (!organization) {
      return {
        success: false,
        message: "Failed to create organization - no data returned",
        error: "No data returned",
      };
    }

    // Add current user as organization admin
    const { error: memberError } = await addOrganizationMemberInDb(
      organization.id,
      userId,
      "admin"
    );

    if (memberError) {
      return {
        success: false,
        message: "Organization created but failed to add you as admin",
        error: memberError.message,
      };
    }

    // Revalidate the home page to show the new organization
    revalidatePath("/home");

    return {
      success: true,
      message: "Organization created successfully",
      organizationId: organization.id,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return {
      success: false,
      message: "An unexpected error occurred",
      error: errorMessage,
    };
  }
}

/**
 * Helper function to validate organization data
 * This can be used on the client or server side
 */
export async function validateOrganizationData(data: unknown): Promise<{
  success: boolean;
  errors?: Record<string, string[]>;
  data?: z.infer<typeof OrganizationSchema>;
}> {
  try {
    const validatedData = OrganizationSchema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Convert ZodError to a plain object with arrays of error messages
      const formattedErrors: Record<string, string[]> = {};

      error.errors.forEach((err) => {
        const path = err.path?.join(".");
        if (!formattedErrors[path]) {
          formattedErrors[path] = [];
        }
        formattedErrors[path].push(err.message);
      });

      return { success: false, errors: formattedErrors };
    }
    throw error;
  }
}

/**
 * Get all subdomains from all organizations in the database
 */
export async function getAllSubdomainsAction(): Promise<string[]> {
  const subdomains = await getAllSubdomainsFromDb();
  if (!subdomains) {
    return [];
  }
  return subdomains?.map((subdomain) => subdomain.subdomain_id) || [];
}

/**
 * Return schema information for client-side form usage
 */
export async function getOrganizationSchema() {
  return {
    schema: OrganizationSchema,
    config: organizationFormConfig,
  };
}
