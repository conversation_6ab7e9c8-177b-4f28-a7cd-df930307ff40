"use server";

import { getTenantId } from "@/db/tenant.db";
import { createClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";
import { connection } from "next/server";

/**
 * Get teams by tenant ID for current user
 * Returns only teams that the user is a member of
 */
export async function getTeamsByUserAndTenantIdFromDb(userId: string, tenantId: string) {
  await connection();
  const supabase = await createClient();
  return await supabase
    .from("teams")
    .select(
      `
      id,
      name,
      description,
      tenant_id,
      team_members!inner (
        user_id
      )
    `
    )
    .eq("tenant_id", tenantId)
    .eq("team_members.user_id", userId)
    .order("name");
}

/**
 * Get teams by tenant ID
 * Returns all teams in the tenant (for admin view)
 */
export async function getTeamsByTenantIdFromDb(tenantId: string) {
  await connection();
  const supabase = await createClient();
  return await supabase
    .from("teams")
    .select("id, name, description, tenant_id")
    .eq("tenant_id", tenantId)
    .order("name");
}

/**
 * Get team members by team IDs
 */
export async function getTeamMembersByTeamIdsFromDb(teamIds: string[], tenantId: string) {
  await connection();
  const supabase = await createClient();
  return await supabase
    .from("team_members")
    .select("id, team_id, user_id, role, position, created_at")
    .in("team_id", teamIds)
    .eq("tenant_id", tenantId);
}

/**
 * Get user profiles by user IDs
 */
export async function getUserProfilesByIdsFromDb(userIds: string[]) {
  await connection();
  const supabase = await createClient();
  return await supabase
    .from("profiles")
    .select("id, user_id, full_name, first_name, last_name, email, avatar_url, created_at")
    .in("user_id", userIds);
}

/**
 * Get projects by team IDs
 */
export async function getProjectsByTeamIdsFromDb(teamIds: string[]) {
  await connection();
  const supabase = await createClient();
  return await supabase.from("projects").select("id, name, status, team_id").in("team_id", teamIds);
}

/**
 * Get tenant ID from primary source (without cookies)
 */
export async function getBaseTenantIdFromDb(): Promise<string | null> {
  return await getTenantId();
}

/**
 * Get tenant ID from cookies (only use in dynamic contexts)
 */
export async function getTenantIdFromCookies(): Promise<string | null> {
  await connection();
  const cookieStore = await cookies();
  return cookieStore.get("tenant-id")?.value || null;
}

/**
 * Get current tenant ID with fallback mechanisms
 * Note: This function uses cookies, so it can only be used in dynamic contexts
 * For static routes, use getBaseTenantIdFromDb() instead
 */
export async function getCurrentTenantIdFromDb(): Promise<string | null> {
  await connection();

  // Get current tenant ID
  let tenantId = await getTenantId();

  // Fallback in case tenantId is null
  if (!tenantId) {
    const cookieStore = await cookies();
    tenantId = cookieStore.get("tenant-id")?.value || null;
  }

  return tenantId;
}

/**
 * Create a new team
 */
export async function createTeamInDb(
  tenantId: string,
  data: { name: string; description?: string }
) {
  await connection();
  const supabase = await createClient();

  const newTeam = {
    name: data.name,
    description: data.description || null,
    tenant_id: tenantId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  return await supabase.from("teams").insert(newTeam).select().single();
}

/**
 * Delete a team and all its members
 * This will also remove all team members associated with this team
 */
export async function deleteTeamFromDb(teamId: string, tenantId: string) {
  await connection();
  const supabase = await createClient();

  try {
    // First check if the team exists and belongs to the tenant
    const { data: team, error: teamError } = await supabase
      .from("teams")
      .select("id")
      .eq("id", teamId)
      .eq("tenant_id", tenantId)
      .single();

    if (teamError || !team) {
      return {
        success: false,
        error: teamError || new Error("Team not found or you don't have permission to delete it"),
      };
    }

    // Delete the team's members first (due to foreign key constraints)
    const { error: memberDeleteError } = await supabase
      .from("team_members")
      .delete()
      .eq("team_id", teamId);

    if (memberDeleteError) {
      return { success: false, error: memberDeleteError };
    }

    // Then delete the team itself
    const { error: teamDeleteError } = await supabase
      .from("teams")
      .delete()
      .eq("id", teamId)
      .eq("tenant_id", tenantId);

    if (teamDeleteError) {
      return { success: false, error: teamDeleteError };
    }

    return { success: true, error: null };
  } catch (error) {
    console.info("[deleteTeamFromDb] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Check if user is admin in any team
 */
export async function isUserAdminInAnyTeam(userId: string, tenantId: string) {
  await connection();
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("team_members")
    .select("role")
    .eq("user_id", userId)
    .eq("tenant_id", tenantId)
    .or("role.eq.admin,role.eq.owner");

  if (error) {
    console.info("[isUserAdminInAnyTeam] Error:", error);
    return false;
  }

  return data && data.length > 0;
}

/**
 * Get teams by project ID
 */
export async function getTeamsByProjectIdFromDb(projectId: string) {
  await connection();
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("project_teams")
    .select(
      `
      team_id,
      teams!team_id (
        id,
        name,
        description,
        tenant_id,
        created_at,
        updated_at
      )
    `
    )
    .eq("project_id", projectId);

  if (error) {
    console.info("[getTeamsByProjectIdFromDb] Error:", error);
    return { data: [], error };
  }

  // Transform the data to flatten the structure
  const teams = data.map((item) => item.teams);

  return { data: teams, error: null };
}

/**
 * Get projects by team ID
 */
export async function getProjectsByTeamIdFromDb(teamId: string) {
  await connection();
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("project_teams")
    .select(
      `
      project_id,
      projects!project_id (
        id,
        name,
        description,
        status,
        tenant_id,
        cluster_id,
        created_at,
        updated_at
      )
    `
    )
    .eq("team_id", teamId);

  if (error) {
    console.info("[getProjectsByTeamIdFromDb] Error:", error);
    return { data: [], error };
  }

  // Transform the data to flatten the structure
  const projects = data.map((item) => item.projects);

  return { data: projects, error: null };
}

/**
 * Assign a team to a project
 */
export async function assignTeamToProjectInDb(teamId: string, projectId: string, tenantId: string) {
  await connection();
  const supabase = await createClient();

  // First check if the relationship already exists
  const { data: existingData, error: checkError } = await supabase
    .from("project_teams")
    .select("id")
    .eq("team_id", teamId)
    .eq("project_id", projectId)
    .single();

  if (checkError && checkError.code !== "PGRST116") {
    // PGRST116 is "No rows returned" error
    console.info("[assignTeamToProjectInDb] Error checking existing relationship:", checkError);
    return { data: null, error: checkError };
  }

  // If relationship already exists, return it
  if (existingData) {
    return { data: existingData, error: null };
  }

  // Create the new relationship
  const { data, error } = await supabase
    .from("project_teams")
    .insert({
      team_id: teamId,
      project_id: projectId,
      tenant_id: tenantId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (error) {
    console.info("[assignTeamToProjectInDb] Error:", error);
    return { data: null, error };
  }

  return { data, error: null };
}

/**
 * Remove a team from a project
 */
export async function removeTeamFromProjectInDb(teamId: string, projectId: string) {
  await connection();
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("project_teams")
    .delete()
    .eq("team_id", teamId)
    .eq("project_id", projectId)
    .select()
    .single();

  if (error) {
    console.info("[removeTeamFromProjectInDb] Error:", error);
    return { data: null, error };
  }

  return { data, error: null };
}
