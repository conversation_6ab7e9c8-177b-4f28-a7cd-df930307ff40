import { createClient } from "@/lib/supabase/server";
import { ClusterWithProjects, Project } from "@/types/cluster-project.types";
import { Team } from "@/types/team.types";

/**
 * Fetches clusters and their projects. For each project, it includes only the teams
 * that the specified user is a member of.
 * Projects without any teams associated with the user are still included, but their 'teams' array will be empty.
 * The calling action will then filter out clusters that have no projects with user teams.
 */
export async function getClustersAndProjectsWithUserTeamsFromDb(
  tenantId: string,
  userId: string
): Promise<ClusterWithProjects[]> {
  const supabase = await createClient();

  const { data: clusters, error } = await supabase
    .from("clusters")
    .select(
      `
      id,
      name,
      slug,
      tenant_id,
      description,
      projects (
        id,
        name,
        slug,
        tenant_id,
        description,
        status,
        priority,
        created_at,
        updated_at,
        project_teams!inner (
          team_id,
          teams!inner (
            id,
            name,
            description,
            tenant_id,
            created_at,
            updated_at,
            team_members!inner (user_id)
          )
        )
      )
    `
    )
    .eq("tenant_id", tenantId)
    .eq("projects.project_teams.teams.team_members.user_id", userId)
    .order("name");

  if (error) {
    console.error("[getClustersAndProjectsWithUserTeamsFromDb] Error:", error);
    throw error;
  }

  // Transform the data to fit the ClusterWithProjects structure and ensure teams are correctly filtered and shaped.
  const transformedClusters = clusters.map((cluster) => {
    const projectsWithFilteredTeams = cluster.projects.map((project) => {
      const projectTeams = Array.isArray(project.project_teams) ? project.project_teams : [];

      const userTeamsForProject = projectTeams
        .map((pt) => {
          const team = Array.isArray(pt.teams) ? pt.teams[0] : pt.teams;

          // Check if the team exists and if the user is a member
          if (
            team &&
            Array.isArray(team.team_members) &&
            team.team_members.some((tm: { user_id: string }) => tm.user_id === userId)
          ) {
            return {
              id: team.id,
              name: team.name,
              description: team.description,
              tenant_id: team.tenant_id,
              created_at: team.created_at,
              updated_at: team.updated_at,
            } as Team;
          }
          return null;
        })
        .filter((team: Team | null): team is Team => team !== null); // Corrected filter type

      return {
        id: project.id,
        name: project.name,
        slug: project.slug,
        tenant_id: project.tenant_id,
        description: project.description,
        status: project.status,
        priority: project.priority,
        created_at: project.created_at,
        updated_at: project.updated_at,
        teams: userTeamsForProject,
      } as Project; // Use existing Project type
    });

    return {
      ...cluster,
      projects: projectsWithFilteredTeams,
    };
  });

  return transformedClusters as ClusterWithProjects[];
}
