import { createClient } from "@/lib/supabase/server";
import { getTenantId } from "./tenant.db";

export type UserProjectWithCluster = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  status: string;
  clusterId: string;
  clusterName: string;
  clusterSlug: string;
};

/**
 * Gets all projects for the current user across all accessible clusters
 */
export async function getUserProjectsFromDb(): Promise<UserProjectWithCluster[]> {
  const supabase = await createClient();
  const tenantId = await getTenantId();

  if (!tenantId) {
    return [];
  }

  // Get all projects for the current user in the current tenant
  const { data, error } = await supabase
    .from("projects")
    .select(
      `
      id,
      name,
      slug,
      description,
      status,
      clusters:cluster_id (
        id, 
        name,
        slug
      )
    `
    )
    .eq("tenant_id", tenantId)
    .order("name");

  if (error || !data) {
    console.info("Error fetching user projects:", error);
    return [];
  }

  // Transform the data to match the expected format
  return data.map((project) => {
    // Safely access the nested cluster data
    const cluster = project.clusters as unknown as { id: string; name: string; slug: string };

    return {
      id: project.id as string,
      name: project.name as string,
      slug: project.slug as string,
      description: project.description as string | null,
      status: project.status as string,
      clusterId: cluster.id,
      clusterName: cluster.name,
      clusterSlug: cluster.slug,
    };
  });
}
