"use server";

import { createClient } from "@/lib/supabase/server";
import { getCurrentSubdomain } from "@/lib/utils";
import { cookies, headers } from "next/headers";
import { connection } from "next/server";
import { cache } from "react";

/**
 * Get tenant ID from subdomain and headers for server components
 */
export const getTenantId = cache(async (): Promise<string | null> => {
  await connection();

  // Get hostname from headers
  const headersList = await headers();
  const hostname = headersList.get("host") || "";

  // Extract subdomain using the centralized utility
  const subdomain = getCurrentSubdomain(hostname);

  if (!subdomain) {
    // No subdomain detected, fall back to cookie
    const cookieStore = await cookies();
    const tenantId = cookieStore.get("tenant-id")?.value;

    return tenantId || null;
  }

  // Get organization/tenant ID from subdomain in one query
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("organizations")
    .select("id")
    .eq("subdomain_id", subdomain)
    .single();

  if (error || !data) {
    // Fall back to cookie if lookup fails
    const cookieStore = await cookies();
    const tenantId = cookieStore.get("tenant-id")?.value;

    return tenantId || null;
  }

  return data.id;
});

/**
 * Hook for client components to get the current tenant ID
 */
export async function useTenantId(): Promise<string | null> {
  await connection();
  const cookieStore = await cookies();
  return cookieStore.get("tenant-id")?.value || null;
}
