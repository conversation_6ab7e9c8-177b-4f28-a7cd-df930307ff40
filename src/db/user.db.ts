"use server";

import { createClient } from "@/lib/supabase/server";
import { UserMetadata } from "@supabase/supabase-js";
import { redirect } from "next/navigation";

/**
 * Get current authenticated user
 */
export async function getCurrentUser() {
  const supabase = await createClient();
  return await supabase.auth.getUser();
}

/**
 * Get the current authenticated user's ID
 * Server-side function with redirect capability
 * Returns null if not authenticated
 * @param redirectToLogin If true, will redirect to login page if not authenticated
 */
export async function getUserId(redirectToLogin = true): Promise<string | null> {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Handle unauthenticated state
  if (!session) {
    if (redirectToLogin) {
      // This is a server action - redirect will work
      redirect("/auth/login");
    }
    return null;
  }

  const { data, error } = await supabase.auth.getUser();
  if (error || !data.user) return null;

  return data.user.id;
}

/**
 * Client-safe version that returns authStatus instead of redirecting
 * Use this from client components
 * @returns Object with userId and authenticated status
 */
export async function getUserIdSafe(): Promise<{ userId: string | null; authenticated: boolean }> {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return { userId: null, authenticated: false };
  }

  const { data, error } = await supabase.auth.getUser();
  if (error || !data.user) {
    return { userId: null, authenticated: false };
  }

  return { userId: data.user.id, authenticated: true };
}

/**
 * Get the current authenticated user's metadata
 * Returns null if not authenticated
 * @param redirectToLogin If true, will redirect to login page if not authenticated
 */
export async function getUserMetadataFromDb(redirectToLogin = true): Promise<UserMetadata | null> {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Handle unauthenticated state
  if (!session) {
    if (redirectToLogin) {
      // This is a server action - redirect will work
      redirect("/auth/login");
    }
    return null;
  }

  const { data, error } = await supabase.auth.getUser();
  if (error || !data.user) return null;

  return {
    ...data.user.user_metadata,
    created_at: data.user.created_at,
  };
}

/**
 * Client-safe version that returns metadata and auth status without redirecting
 * Use this from client components
 */
export async function getUserMetadataSafe(): Promise<{
  metadata: UserMetadata | null;
  authenticated: boolean;
}> {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return { metadata: null, authenticated: false };
  }

  const { data, error } = await supabase.auth.getUser();
  if (error || !data.user) {
    return { metadata: null, authenticated: false };
  }

  return {
    metadata: {
      ...data.user.user_metadata,
      created_at: data.user.created_at,
    },
    authenticated: true,
  };
}

/**
 * Delete the current authenticated user from the database
 * Returns null if not authenticated
 */
export async function deleteUserFromDb(): Promise<{ error?: string }> {
  const supabase = await createClient();

  try {
    // Use regular user deletion
    // const { error } = await supabase.auth.admin.deleteUser(user.id);
    const { error } = await supabase.rpc("delete_user");
    if (error) throw error;

    return {};
  } catch (error) {
    console.info(error);
    return {
      error: error instanceof Error ? error.message : "Failed to delete user",
    };
  }
}
