"use server";

import { createClient } from "@/lib/supabase/server";

/**
 * Get a team member by ID
 */
export async function getTeamMemberByIdFromDb(teamMemberId: string) {
  const supabase = await createClient();

  // First get the current user's tenant_id
  const { data: currentUser } = await getCurrentTeamMemberFromDb();
  if (!currentUser) {
    return { data: null, error: new Error("No current user found") };
  }

  const tenantId = currentUser.tenant_id;

  // Build query with tenant_id check
  const { data, error } = await supabase
    .from("team_members")
    .select("*")
    .eq("id", teamMemberId)
    .eq("tenant_id", tenantId);

  if (!data?.length) {
    return { data: null, error: new Error("Team member not found in your organization") };
  }

  return { data, error };
}

/**
 * Get current user's team member records
 */
export async function getCurrentTeamMemberFromDb() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { data: null, error: new Error("Not authenticated") };
    }

    const { data, error } = await supabase.from("team_members").select("*").eq("user_id", user.id);

    if (error) {
      return { data: null, error };
    }

    if (!data || data.length === 0) {
      return {
        data: null,
        error: new Error("User is not a member of any team"),
      };
    }

    // Check if user has admin role in any team
    const hasAdminRole = data.some((member) => member.role === "admin" || member.role === "owner");

    if (!hasAdminRole) {
      return {
        data: null,
        error: new Error("You must be an admin in at least one team to perform this action"),
      };
    }

    return { data: data[0], error: null }; // Return first team member record since we just need user info
  } catch (error) {
    console.info("[getCurrentTeamMemberFromDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Create a new team member
 */
export async function createTeamMemberInDb(data: {
  userId: string;
  teamId: string;
  role: string;
  position: string;
  tenantId: string;
}) {
  try {
    const supabase = await createClient();

    const newTeamMember = {
      user_id: data.userId,
      team_id: data.teamId,
      role: data.role,
      position: data.position,
      tenant_id: data.tenantId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data: teamMember, error } = await supabase
      .from("team_members")
      .insert(newTeamMember)
      .select()
      .single();

    if (error) {
      return { data: null, error };
    }

    return { data: teamMember, error: null };
  } catch (error) {
    console.info("[createTeamMemberInDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Update the team of a team member
 */
export async function updateTeamOfTeamMemberInDb(teamMemberId: string, teamId: string) {
  try {
    const supabase = await createClient();

    // Get all required data in a single query by joining tables
    const { data: teamMember, error: teamMemberError } = await supabase
      .from("team_members")
      .select("*, user:user_id")
      .eq("id", teamMemberId)
      .single();

    if (teamMemberError || !teamMember) {
      return {
        data: null,
        error: teamMemberError || new Error(`Team member with ID ${teamMemberId} not found`),
      };
    }

    // Check if user is already in the destination team
    const { count, error: countError } = await supabase
      .from("team_members")
      .select("*", { count: "exact", head: true })
      .eq("user_id", teamMember.user_id)
      .eq("team_id", teamId);

    if (countError) {
      return { data: null, error: new Error("Failed to check existing team membership") };
    }

    if (count && count > 0) {
      return {
        data: null,
        error: new Error("User is already a member of the destination team"),
      };
    }

    // Use a transaction to ensure both operations succeed or fail together
    const newTeamMember = {
      user_id: teamMember.user_id,
      team_id: teamId,
      role: teamMember.role,
      position: teamMember.position || null,
      tenant_id: teamMember.tenant_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to new team and get the ID
    const { data: newMember, error: insertError } = await supabase
      .from("team_members")
      .insert(newTeamMember)
      .select()
      .single();

    if (insertError) {
      return { data: null, error: new Error("Failed to add team member to new team") };
    }

    // Remove from old team
    const { error: deleteError } = await supabase
      .from("team_members")
      .delete()
      .eq("id", teamMemberId);

    if (deleteError) {
      // If deletion fails, clean up the inserted record
      await supabase.from("team_members").delete().eq("id", newMember.id);
      return { data: null, error: new Error("Failed to remove team member from original team") };
    }

    return {
      data: {
        ...newMember,
        oldTeamId: teamMember.team_id,
        newTeamId: teamId,
      },
      error: null,
    };
  } catch (error) {
    console.info("[updateTeamOfTeamMemberInDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Remove a team member from a team
 */
export async function removeTeamMemberFromTeamInDb(teamMemberId: string, teamId: string) {
  try {
    const supabase = await createClient();

    const { error: teamMemberError } = await supabase
      .from("team_members")
      .delete()
      .eq("id", teamMemberId)
      .eq("team_id", teamId);

    if (teamMemberError) {
      return {
        data: null,
        error: teamMemberError || new Error(`Team member with ID ${teamMemberId} not found`),
      };
    }

    return {
      data: {
        teamMemberId: teamMemberId,
        teamId: teamId,
      },
      error: null,
    };
  } catch (error) {
    console.info("[removeTeamMemberFromTeamInDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}

/**
 * Update a team member's role and position
 */
export async function updateTeamMemberDetailsInDb(
  teamMemberId: string,
  data: { role?: string; position?: string }
) {
  try {
    const supabase = await createClient();

    // Get current team member to verify access
    const { data: teamMember, error: teamMemberError } = await getTeamMemberByIdFromDb(
      teamMemberId
    );

    if (teamMemberError || !teamMember) {
      return {
        data: null,
        error: teamMemberError || new Error(`Team member with ID ${teamMemberId} not found`),
      };
    }

    // Prepare update data
    const updateData = {
      ...(data.role && { role: data.role }),
      ...(data.position && { position: data.position }),
      updated_at: new Date().toISOString(),
    };

    // Update the team member
    const { data: updatedMember, error: updateError } = await supabase
      .from("team_members")
      .update(updateData)
      .eq("id", teamMemberId)
      .select()
      .single();

    if (updateError) {
      return { data: null, error: updateError };
    }

    return { data: updatedMember, error: null };
  } catch (error) {
    console.info("[updateTeamMemberDetailsInDb] Unexpected error:", error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error("An unexpected error occurred"),
    };
  }
}
