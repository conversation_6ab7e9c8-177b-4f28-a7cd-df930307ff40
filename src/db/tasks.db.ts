"use server";

import { DbTaskModel } from "@/app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_actions/tasks.actions";
import { type TaskGroupModel, type TaskModel } from "@/db/schemas/tasks.schema";
import { createClient } from "@/lib/supabase/server";
import { PageParams } from "@/types/next.types";
import { getProjectBySlugAndTenantId } from "./actions/project.action";
import { getTenantId } from "./tenant.db";

/**
 * Gets all task groups for a specific project
 *
 * @param props - Page params containing project and cluster slugs
 * @returns An array of task groups
 */
export async function getTaskGroupsByProjectFromDb(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
): Promise<TaskGroupModel[]> {
  try {
    const supabase = await createClient();
    const params = await props.params;

    // Get tenant ID first
    const tenantId = await getTenantId();
    if (!tenantId) {
      console.error("No tenant ID found for the current user");
      return [];
    }

    // Get project directly with tenant ID for better efficiency
    const currentProject = await getProjectBySlugAndTenantId(params["project-slug"], tenantId);

    if (!currentProject?.id) {
      console.error(
        "No project found with slug:",
        params["project-slug"],
        "and tenantId:",
        tenantId
      );
      return [];
    }

    // Get all tasks for this project
    const { data: taskGroups, error: taskGroupsError } = await supabase
      .from("task_group")
      .select("*")
      .eq("project_id", currentProject.id)
      .eq("tenant_id", tenantId);

    if (taskGroupsError) {
      console.error("Error fetching task groups:", taskGroupsError);
      throw taskGroupsError;
    }

    return (taskGroups || []).map((group) => ({
      id: group.id,
      name: group.name,
      color: group.color,
      icon: group.icon,
      count: group.count || 0,
      projectId: group.project_id,
      createdAt: group.created_at,
      updatedAt: group.updated_at,
      tasks: [],
    }));
  } catch (error) {
    console.error("Error in getTaskGroupsByProject:", error);
    return [];
  }
}

/**
 * Gets completed tasks for a specific project (server-side filtered)
 *
 * @param projectId - The project ID to fetch completed tasks for
 * @returns An array of completed tasks
 */
export async function getCompletedTasksByProjectFromDb(projectId: string): Promise<TaskModel[]> {
  try {
    const supabase = await createClient();

    // Use the database function that properly JOINs tasks with tags and filters for completed tasks
    const { data: tasksData, error: tasksError } = await supabase.rpc("get_tasks_with_tags", {
      project_id_param: projectId,
    });

    if (tasksError) {
      console.error("Error fetching completed tasks:", tasksError);
      throw tasksError;
    }

    if (!tasksData || tasksData.length === 0) {
      return [];
    }

    // Transform and filter for completed tasks only
    const completedTasks = tasksData
      .map((task: DbTaskModel) => formatTaskFromDb(task))
      .filter((task: TaskModel) => task.lifeCycleStatus === "dev done" || task.status === "Done");

    return completedTasks;
  } catch (error) {
    console.error("Error in getCompletedTasksByProjectFromDb:", error);
    return [];
  }
}

/**
 * Gets all tasks for a specific project
 *
 * @param props - Page params containing project and cluster slugs
 * @returns An array of tasks
 */
export async function getTasksByProjectFromDb(
  props: PageParams<{ "cluster-slug": string; "project-slug": string }>
): Promise<TaskModel[]> {
  try {
    const supabase = await createClient();
    const params = await props.params;

    // Get tenant ID first
    const tenantId = await getTenantId();
    if (!tenantId) {
      console.error("No tenant ID found for the current user");
      return [];
    }

    // Get project directly with tenant ID for better efficiency
    const currentProject = await getProjectBySlugAndTenantId(params["project-slug"], tenantId);

    if (!currentProject?.id) {
      console.error(
        "No project found with slug:",
        params["project-slug"],
        "and tenantId:",
        tenantId
      );
      return [];
    }

    // Use the database function that properly JOINs tasks with tags
    const { data: tasksData, error: tasksError } = await supabase.rpc("get_tasks_with_tags", {
      project_id_param: currentProject.id,
    });

    if (tasksError) {
      console.error("Error fetching tasks:", tasksError);
      throw tasksError;
    }

    // Format tasks and return as an array using the new data structure
    return (tasksData || []).map((task: DbTaskModel) => formatTaskFromDb(task));
  } catch (error) {
    console.error("Error in getTasksByProject:", error);
    throw error;
  }
}

/**
 * Formats a task from the database into the expected TaskModel format
 * This handles the data structure returned by get_tasks_with_tags() function
 */
function formatTaskFromDb(dbTask: DbTaskModel): TaskModel {
  // Handle fields that may be objects or strings
  let subtasks = [];
  let tags = [];
  let assignees = [];

  // Parse subtasks if needed
  try {
    if (dbTask.subtasks) {
      if (typeof dbTask.subtasks === "string") {
        subtasks = JSON.parse(dbTask.subtasks);
      } else if (Array.isArray(dbTask.subtasks)) {
        subtasks = dbTask.subtasks;
      }
    }
  } catch (e) {
    console.warn("Error parsing subtasks:", e);
  }

  // Parse tags from the new structure (JSON string from get_tasks_with_tags)
  try {
    if (dbTask.tags) {
      if (typeof dbTask.tags === "string") {
        tags = JSON.parse(dbTask.tags);
      } else if (Array.isArray(dbTask.tags)) {
        tags = dbTask.tags;
      }
    }
  } catch (e) {
    console.warn("Error parsing tags:", e);
  }

  // Parse assignees if needed
  try {
    if (dbTask.assignees) {
      if (typeof dbTask.assignees === "string") {
        assignees = JSON.parse(dbTask.assignees);
      } else if (Array.isArray(dbTask.assignees)) {
        assignees = dbTask.assignees;
      }
    }
    // Ensure assignees is always an array
    if (!Array.isArray(assignees)) {
      assignees = [];
    }
  } catch (e) {
    console.warn("Error parsing assignees:", e);
    assignees = []; // Always default to empty array
  }

  // Handle visible_to_team_ids which might be a string JSON array or null
  let visibleToTeams = [] as string[];
  try {
    if (dbTask.visible_to_team_ids) {
      if (typeof dbTask.visible_to_team_ids === "string") {
        visibleToTeams = JSON.parse(dbTask.visible_to_team_ids);
      } else if (Array.isArray(dbTask.visible_to_team_ids)) {
        visibleToTeams = dbTask.visible_to_team_ids;
      }
    }
  } catch (e) {
    console.warn("Error parsing visible_to_team_ids:", e);
  }

  return {
    id: dbTask.id,
    key: dbTask.key,
    title: dbTask.title,
    description: dbTask.description,
    value: dbTask.value,
    status: dbTask.status,
    lifeCycleStatus: dbTask.life_cycle_status, // Note: this matches the DB function output
    tags: tags, // Now properly parsed TagModel array
    sprint: dbTask.sprint,
    week: dbTask.week,
    dueDate: dbTask.due_date,
    doneDate: dbTask.done_date,
    deliveredDate: dbTask.delivered_date,
    assignees: assignees,
    effort: dbTask.effort,
    positionInColumn: dbTask.position_in_column || 1, // Use the new column-scoped position
    attachments: [], // Attachments are not stored in the database task model
    createdAt: dbTask.created_at,
    updatedAt: dbTask.updated_at,
    subtasks: subtasks,
    visibleToTeams,
    isDummy: false,
    isOptimistic: false,
  };
}

/**
 * Get tags for a specific project from database
 *
 * @param projectId - The project ID to fetch tags for
 * @returns Array of tags for the project
 */
export async function getTagsByProjectFromDb(projectId: string) {
  try {
    const supabase = await createClient();

    const { data: tags, error } = await supabase
      .from("task_tags")
      .select("id, name, project_id, created_at, updated_at")
      .eq("project_id", projectId)
      .order("name");

    if (error) {
      console.error("Error fetching tags:", error);
      throw error;
    }

    return tags || [];
  } catch (error) {
    console.error("Error in getTagsByProjectFromDb:", error);
    return [];
  }
}

/**
 * Get project team members (potential assignees) from database
 *
 * @param projectId - The project ID to fetch team members for
 * @returns Array of team members for the project
 */
export async function getProjectAssigneesFromDb(projectId: string) {
  try {
    const supabase = await createClient();

    // Strategy 1: Try to get teams associated with the project
    let teamIds: string[] = [];

    try {
      const { data: projectTeams, error: projectTeamsError } = await supabase
        .from("project_teams")
        .select("team_id")
        .eq("project_id", projectId);

      if (!projectTeamsError && projectTeams && projectTeams.length > 0) {
        teamIds = projectTeams.map((pt) => pt.team_id);
      }
    } catch (error) {
      // project_teams table might not exist, continue with empty teamIds
      console.warn("Error in getProjectAssigneesFromDb:", {
        error: error instanceof Error ? error.message : "Unknown error",
        projectId,
      });
    }

    // Strategy 2: If we have team IDs, get team members using simple queries
    if (teamIds.length > 0) {
      try {
        // First get team members
        const { data: teamMembers, error: membersError } = await supabase
          .from("team_members")
          .select("user_id")
          .in("team_id", teamIds);

        if (!membersError && teamMembers && teamMembers.length > 0) {
          const userIds = [...new Set(teamMembers.map((tm) => tm.user_id))]; // Deduplicate

          // Then get profiles separately with a simple query
          const { data: profiles, error: profilesError } = await supabase
            .from("profiles")
            .select("user_id, first_name, last_name, full_name, email, avatar_url")
            .in("user_id", userIds);

          if (!profilesError && profiles && profiles.length > 0) {
            const assignees = profiles.map((profile) => ({
              id: profile.user_id,
              name:
                profile.full_name ||
                `${profile.first_name || ""} ${profile.last_name || ""}`.trim() ||
                profile.email ||
                "Unknown User",
              email: profile.email,
              avatar: profile.avatar_url,
            }));

            return assignees;
          }
        }
      } catch (error) {
        // Continue to fallback strategies
        console.warn("Error in getProjectAssigneesFromDb:", {
          error: error instanceof Error ? error.message : "Unknown error",
          projectId,
        });
      }
    }

    // Strategy 3: Fallback - try to get any users that might be assignees from existing tasks
    try {
      const { data: existingTasks, error: tasksError } = await supabase
        .from("tasks")
        .select("assignees")
        .eq("project_id", projectId)
        .not("assignees", "is", null);

      if (!tasksError && existingTasks && existingTasks.length > 0) {
        // Extract user IDs from existing task assignees
        const allAssignees = new Map();

        existingTasks.forEach((task) => {
          try {
            let assignees = [];
            if (typeof task.assignees === "string") {
              assignees = JSON.parse(task.assignees);
            } else if (Array.isArray(task.assignees)) {
              assignees = task.assignees;
            }

            assignees.forEach(
              (assignee: { id: string; name: string; email?: string; avatar?: string }) => {
                if (assignee && assignee.id) {
                  allAssignees.set(assignee.id, {
                    id: assignee.id,
                    name: assignee.name || "Unknown User",
                    email: assignee.email || "",
                    avatar: assignee.avatar || null,
                  });
                }
              }
            );
          } catch (error) {
            console.warn("Error in getProjectAssigneesFromDb:", {
              error: error instanceof Error ? error.message : "Unknown error",
              projectId,
            });
            // Skip malformed assignee data
          }
        });

        if (allAssignees.size > 0) {
          return Array.from(allAssignees.values());
        }
      }
    } catch (error) {
      // Continue to final fallback
      console.warn("Error in getProjectAssigneesFromDb:", {
        error: error instanceof Error ? error.message : "Unknown error",
        projectId,
      });
    }

    // Strategy 4: Final fallback - return empty array (this is normal for new projects)
    return [];
  } catch (error) {
    console.warn("Error in getProjectAssigneesFromDb:", {
      error: error instanceof Error ? error.message : "Unknown error",
      projectId,
    });
    return [];
  }
}
