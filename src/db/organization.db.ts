import { OrganizationData } from "@/db/schemas/organization.schema";
import { createClient } from "@/lib/supabase/server";

/**
 * Check if an organization with the given subdomain already exists
 */
export async function getOrganizationExistsInDb(subdomain: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("organizations")
    .select("id")
    .eq("subdomain_id", subdomain)
    .single();

  return {
    exists: !!data,
    data,
    error,
  };
}

/**
 * Create a new organization in the database
 */
export async function createOrganizationInDb(organizationData: OrganizationData) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("organizations")
    .insert({
      name: organizationData.name,
      description: organizationData.description || "",
      subdomain_id: organizationData.subdomain_id,
      max_users: organizationData.max_users,
    })
    .select("id")
    .single();

  return { data, error };
}

/**
 * Add a user as a member of an organization
 */
export async function addOrganizationMemberInDb(
  organizationId: string,
  userId: string,
  role: "admin" | "member" = "member"
) {
  // First import the admin client
  const { adminClient } = await import("@/lib/supabase/admin");

  try {
    const { error } = await adminClient.from("organization_members").insert({
      organization_id: organizationId,
      user_id: userId,
      role,
      tenant_id: organizationId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (error) {
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error("Failed to add user to organization"),
    };
  }
}

/**
 * Get the current organization based on the tenant ID
 */
export async function getCurrentOrganization(tenantId: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("organizations")
    .select("id, name, subdomain_id")
    .eq("id", tenantId)
    .single();

  if (error || !data) {
    return null;
  }

  return data;
}

/**
 * Get all existing subdomains
 * @returns {Promise<{data: {subdomain_id: string}[] | null, error: Error | null}>} Array of subdomain IDs or error
 */
export async function getAllSubdomainsFromDb() {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("organizations")
    .select("subdomain_id")
    .order("subdomain_id");

  if (error || !data) {
    return null;
  }

  return data;
}

/**
 * Verify if a subdomain exists and redirect to main domain if not
 * @param subdomain - The subdomain to verify
 * @returns The verification result and redirect info
 */
export async function verifySubdomainOrRedirect(subdomain: string | null) {
  // If no subdomain, no need to verify
  if (!subdomain) {
    return {
      isValid: true,
      shouldRedirect: false,
    };
  }

  // Check if organization exists
  const { exists } = await getOrganizationExistsInDb(subdomain);

  return {
    isValid: exists,
    shouldRedirect: !exists,
    redirectUrl: "/", // Redirect to root of main domain
  };
}
