import { NavItem, SettingsNavItem } from "@/types/sidebar.types";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

type MenuCategoryMap = {
  [key: string]: NavItem[] | SettingsNavItem[];
};

/**
 * Extracts cluster and project slugs from a pathname
 */
function extractRouteParams(pathname: string): {
  clusterSlug: string | null;
  projectSlug: string | null;
} {
  if (!pathname) return { clusterSlug: null, projectSlug: null };

  const parts = pathname?.split("/").filter(Boolean);

  // Check if we have enough parts and if they follow the expected pattern
  if (parts.length < 4 || parts[0] !== "clusters" || parts[2] !== "projects") {
    return { clusterSlug: null, projectSlug: null };
  }

  return {
    clusterSlug: parts[1], // Now at index 1 after "clusters"
    projectSlug: parts[3], // Now at index 3 after "projects"
  };
}

/**
 * Checks if a URL is active based on the current pathname
 * Handles exact matches, parent paths, and dynamic routes with cluster/project slugs
 */
function isUrlActive(url: string, pathname: string): boolean {
  // Handle special cases
  if (url === "/project-settings" && pathname?.includes("/project-settings")) {
    return true;
  }

  if (url === "/") {
    return pathname === "/";
  }

  if (url === "#") {
    return false;
  }

  // Skip special URLs like /home or external URLs
  if (url.startsWith("http") || url.startsWith("/home")) {
    return pathname === url;
  }

  // Extract cluster and project slugs from the current pathname
  const { clusterSlug, projectSlug } = extractRouteParams(pathname);

  // Skip dynamic URL processing if we don't have cluster and project
  if (!clusterSlug || !projectSlug) {
    // For paths without cluster/project, use direct comparison
    if (pathname === url) return true;

    if (url !== "/" && pathname?.startsWith(url)) {
      const nextChar = pathname?.charAt(url.length);
      return nextChar === "" || nextChar === "/";
    }

    return false;
  }

  // For paths with cluster/project, compare the base path portion
  // New URL pattern: /clusters/[cluster-slug]/projects/[project-slug]
  const basePath =
    pathname?.substring(`/clusters/${clusterSlug}/projects/${projectSlug}`.length) || "/";

  // If URL starts with /, compare directly with basePath
  if (url.startsWith("/")) {
    if (basePath === url) return true;

    if (url !== "/" && basePath?.startsWith(url)) {
      const nextChar = basePath?.charAt(url.length);
      return nextChar === "" || nextChar === "/";
    }
  } else {
    // If URL doesn't start with /, add / for comparison
    const urlWithSlash = "/" + url;
    if (basePath === urlWithSlash) return true;

    if (urlWithSlash !== "/" && basePath?.startsWith(urlWithSlash)) {
      const nextChar = basePath?.charAt(urlWithSlash.length);
      return nextChar === "" || nextChar === "/";
    }
  }

  return false;
}

/**
 * A hook that takes menu categories and updates their isActive status based on the current pathname
 * Supports dynamic routing with cluster and project slugs
 */
export function useActiveMenu(menuCategories: MenuCategoryMap): MenuCategoryMap {
  const pathname = usePathname();

  // Use useMemo to calculate the active menu items only when pathname or menuCategories change
  return useMemo(() => {
    if (!pathname) return menuCategories;

    // Create a new object with updated active states
    return Object.entries(menuCategories).reduce<MenuCategoryMap>((acc, [key, category]) => {
      // Process each category
      const updatedCategory = category.map((item) => {
        // Check if the main item's URL matches the current pathname
        const isMainItemActive = isUrlActive(item.url, pathname);

        // Process subitems if they exist
        let updatedSubItems;
        let isSubItemActive = false;

        if (item.subItems?.length) {
          updatedSubItems = item.subItems.map((subItem) => {
            const isActive = isUrlActive(subItem.url, pathname);
            // Track if any subitem is active
            if (isActive) isSubItemActive = true;
            return {
              ...subItem,
              isActive,
            };
          });
        }

        // Determine if the collapsible menu should be expanded
        // Expand it if the item or any of its subitems are active
        const shouldBeExpanded = isMainItemActive || isSubItemActive;

        return {
          ...item,
          isActive: isMainItemActive || isSubItemActive,
          isExpanded: shouldBeExpanded || item.isExpanded,
          // Only include subItems if they exist
          ...(item.subItems?.length && { subItems: updatedSubItems }),
        };
      });

      acc[key] = updatedCategory;
      return acc;
    }, {});
  }, [pathname, menuCategories]);
}
