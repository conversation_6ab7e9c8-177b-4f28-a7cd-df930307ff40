"use client";

import { usePathname } from "next/navigation";

export function useRouteParams() {
  const pathname = usePathname();
  if (!pathname) return { clusterSlug: null, projectSlug: null, currentPath: null };

  // Match the new URL pattern: clusters/[cluster-slug]/projects/[project-slug]
  const parts = pathname?.split("/").filter(Boolean);

  // Check if we have enough parts and if they follow the expected pattern
  if (parts.length < 4 || parts[0] !== "clusters" || parts[2] !== "projects") {
    return { clusterSlug: null, projectSlug: null, currentPath: null };
  }

  // Extract the relevant slugs and current path
  return {
    clusterSlug: parts[1], // Now at index 1 after "clusters"
    projectSlug: parts[3], // Now at index 3 after "projects"
    currentPath: parts.length > 4 ? parts[4] : null,
  };
}
