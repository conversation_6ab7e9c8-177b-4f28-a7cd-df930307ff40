"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCallback } from "react";

export function useNavigationCacheProtection() {
  const queryClient = useQueryClient();
  const router = useRouter();

  const protectCacheBeforeNavigation = useCallback(() => {
    const queryCache = queryClient.getQueryCache();
    const now = Date.now();

    // Get all queries and mark them as fresh to prevent refetching
    queryCache.getAll().forEach((query) => {
      if (query.state.data) {
        // Update the dataUpdatedAt to make the query appear fresh
        query.state.dataUpdatedAt = now;
        query.state.fetchStatus = "idle";

        // Explicitly set the query data to ensure it's preserved
        queryClient.setQueryData(query.queryKey, query.state.data, {
          updatedAt: now,
        });
      }
    });
  }, [queryClient]);

  const navigateWithCacheProtection = useCallback(
    (href: string, options?: { replace?: boolean }) => {
      protectCacheBeforeNavigation();

      // Small delay to ensure cache protection takes effect
      setTimeout(() => {
        if (options?.replace) {
          router.replace(href);
        } else {
          router.push(href);
        }
      }, 10);
    },
    [protectCacheBeforeNavigation, router]
  );

  return {
    protectCacheBeforeNavigation,
    navigateWithCacheProtection,
  };
}
