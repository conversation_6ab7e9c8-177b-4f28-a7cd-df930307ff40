"use client";

import { getCurrentSubdomain } from "@/lib/utils";
import { useEffect, useState } from "react";

export function useSubdomain() {
  const [subdomain, setSubdomain] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Use the centralized subdomain detection utility
    const detectedSubdomain = getCurrentSubdomain();

    // For localhost development, show "Localhost" for display purposes
    if (detectedSubdomain === null && window.location.hostname === "localhost") {
      setSubdomain("Localhost");
    } else {
      setSubdomain(detectedSubdomain);
    }
  }, []);

  return subdomain;
}
