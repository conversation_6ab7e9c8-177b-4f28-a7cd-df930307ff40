"use client";

import {
  QueryClient,
  QueryClientProvider as TanstackQueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useEffect, useRef } from "react";

// Create a singleton QueryClient instance to persist across navigation
let globalQueryClient: QueryClient | undefined = undefined;

function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 15 * 60 * 1000, // 15 minutes
        gcTime: 4 * 60 * 60 * 1000, // 4 hours - keep data in memory longer
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: false, // Critical: don't refetch on mount if data exists
        retry: 1,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Keep previous data during refetch to prevent loading states
        placeholderData: (previousData: unknown) => previousData,
        // Optimize change notifications
        notifyOnChangeProps: "all",
        structuralSharing: true,
      },
      mutations: {
        retry: 1,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
  });
}

function getQueryClient() {
  if (typeof window === "undefined") {
    // Server: always make a new query client
    return createQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    if (!globalQueryClient) {
      globalQueryClient = createQueryClient();
    }
    return globalQueryClient;
  }
}

export function QueryClientProvider({ children }: { children: React.ReactNode }) {
  const queryClient = getQueryClient();
  const hasSetupRef = useRef(false);

  useEffect(() => {
    // Only run setup once
    if (hasSetupRef.current) return;
    hasSetupRef.current = true;

    // Prevent cache clearing during navigation
    const handleRouteChange = () => {
      // Mark all active queries to prevent garbage collection
      const queryCache = queryClient.getQueryCache();

      queryCache.getAll().forEach((query) => {
        if (query.state.data && !query.isStale()) {
          // Touch the query to keep it fresh
          queryCache.build(queryClient, {
            queryKey: query.queryKey,
            queryFn: query.options.queryFn,
          });
        }
      });
    };

    // Listen for Next.js route changes
    const handleBeforeUnload = () => {
      handleRouteChange();
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        handleRouteChange();
      }
    };

    // Multiple event listeners to catch navigation
    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // For Next.js app router navigation events
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      handleRouteChange();
      return originalPushState.apply(this, args);
    };

    history.replaceState = function (...args) {
      handleRouteChange();
      return originalReplaceState.apply(this, args);
    };

    window.addEventListener("popstate", handleRouteChange);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("popstate", handleRouteChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, [queryClient]);

  return (
    <TanstackQueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </TanstackQueryClientProvider>
  );
}
