/**
 * Standardized React Query cache keys for the entire application
 *
 * Naming Convention:
 * - Use kebab-case for all keys
 * - Use hierarchical structure where appropriate
 * - Keep keys consistent across the application
 *
 * Cache Configuration Guidelines:
 * - User data: 1 hour stale time (rarely changes)
 * - Organization data: 2 hours stale time (very rarely changes)
 * - Project data: 1 hour stale time (rarely changes)
 * - Task data: 10 minutes stale time (changes moderately)
 * - Tags/Assignees: 15 minutes stale time (changes infrequently)
 */

// User-related queries
export const userQueryKeys = {
  all: ["user"] as const,
  profile: () => [...userQueryKeys.all, "profile"] as const,
  projects: () => [...userQueryKeys.all, "projects"] as const,
};

// Organization-related queries (scoped to prevent conflicts)
export const organizationQueryKeys = {
  all: ["organization"] as const,
  current: () => [...organizationQueryKeys.all, "current"] as const,
  clusters: () => [...organizationQueryKeys.all, "clusters"] as const,
  projects: () => [...organizationQueryKeys.all, "projects"] as const,
  userRole: () => [...organizationQueryKeys.all, "user-role"] as const,
  teams: () => [...organizationQueryKeys.all, "teams"] as const,
  userTeams: () => [...organizationQueryKeys.all, "user-teams"] as const,
  // Home-specific organization data (prevents conflicts with project org data)
  homeContext: () => ["home", "organization"] as const,
};

// Home page specific queries (properly scoped to prevent cache conflicts)
export const homeQueryKeys = {
  all: ["home"] as const,
  userTeamProjects: () => [...homeQueryKeys.all, "user-team-projects"] as const,
  // Use home-scoped organization to prevent conflicts
  organization: () => [...homeQueryKeys.all, "organization"] as const,
  // Home-scoped organization data
  teams: () => [...homeQueryKeys.all, "organization", "teams"] as const,
  userTeams: () => [...homeQueryKeys.all, "organization", "user-teams"] as const,
  userRole: () => [...homeQueryKeys.all, "organization", "user-role"] as const,
  clusters: () => [...homeQueryKeys.all, "organization", "clusters"] as const,
  projects: () => [...homeQueryKeys.all, "organization", "projects"] as const,
};

// Task-related queries
export const taskQueryKeys = {
  all: ["tasks"] as const,
  lists: () => [...taskQueryKeys.all, "list"] as const,
  list: (projectId: string) => [...taskQueryKeys.lists(), projectId] as const,
  details: () => [...taskQueryKeys.all, "detail"] as const,
  detail: (id: string) => [...taskQueryKeys.details(), id] as const,
  groups: (projectId: string) => [...taskQueryKeys.all, "groups", projectId] as const,
  completed: (projectSlug: string) => [...taskQueryKeys.all, "completed", projectSlug] as const,
};

// Project-related queries
export const projectQueryKeys = {
  all: ["project"] as const,
  tags: (projectId: string) => [...projectQueryKeys.all, "tags", projectId] as const,
  assignees: (projectId: string) => [...projectQueryKeys.all, "assignees", projectId] as const,
};

// Optimal cache configurations
export const cacheConfigs = {
  // User data - rarely changes
  user: {
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  },

  // Organization data - very rarely changes
  organization: {
    staleTime: 2 * 60 * 60 * 1000, // 2 hours
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  },

  // Project data - rarely changes
  project: {
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  },

  // Task data - changes moderately
  tasks: {
    staleTime: 10 * 60 * 1000, // 10 minutes - background refresh
    gcTime: 2 * 60 * 60 * 1000, // 2 hours - keep for navigation UX
  },

  // Project metadata (tags, assignees) - changes infrequently
  projectMeta: {
    staleTime: 15 * 60 * 1000, // 15 minutes - background refresh
    gcTime: 2 * 60 * 60 * 1000, // 2 hours - keep for navigation UX
  },

  // Home page data - changes moderately, increased for navigation UX
  home: {
    staleTime: 60 * 60 * 1000, // 1 hour - increased for better navigation UX
    gcTime: 4 * 60 * 60 * 1000, // 4 hours - longer retention for navigation
  },

  // Completed tasks - changes less frequently
  completedTasks: {
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },

  // Static data that rarely changes
  static: {
    staleTime: Infinity,
    gcTime: Infinity,
  },
} as const;

// Common query options
export const commonQueryOptions = {
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: 1,
} as const;
