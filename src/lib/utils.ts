import { type SupabaseClient } from "@supabase/supabase-js";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Merge class names
 * @param inputs - The class names to merge
 * @returns The merged class names
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get the current domain based on the environment
 * @param hostname - Optional hostname for server-side detection
 * @returns The current domain
 */
export function getCurrentDomain(hostname?: string) {
  const nodeEnv = process.env.NODE_ENV;

  // If hostname is provided (server-side), use it for detection
  if (hostname) {
    if (hostname.includes("renwu.dev")) {
      return process.env.NEXT_PUBLIC_PREPROD_DOMAIN || "renwu.dev";
    }
    if (hostname.includes("renwu.app")) {
      return process.env.NEXT_PUBLIC_PROD_DOMAIN || "renwu.app";
    }
    if (hostname.includes("localhost")) {
      return process.env.NEXT_PUBLIC_DEV_DOMAIN || "localhost:3000";
    }
  }

  // Client-side domain detection based on actual hostname
  if (typeof window !== "undefined") {
    // If we're in the browser and on renwu.dev, force preprod domain
    if (window.location.hostname.includes("renwu.dev")) {
      return process.env.NEXT_PUBLIC_PREPROD_DOMAIN || "renwu.dev";
    }

    // If we're in the browser and on renwu.app, force prod domain
    if (window.location.hostname.includes("renwu.app")) {
      return process.env.NEXT_PUBLIC_PROD_DOMAIN || "renwu.app";
    }

    // If we're in the browser and on localhost, force dev domain
    if (window.location.hostname.includes("localhost")) {
      return process.env.NEXT_PUBLIC_DEV_DOMAIN || "localhost:3000";
    }
  }

  // Server-side environment detection fallback
  if (nodeEnv === "development") {
    return process.env.NEXT_PUBLIC_DEV_DOMAIN || "localhost:3000";
  } else if (process.env.VERCEL_ENV === "production") {
    return process.env.NEXT_PUBLIC_PROD_DOMAIN || "renwu.app";
  } else if (process.env.VERCEL_ENV === "preview") {
    // test or any other env defaults to preprod
    return process.env.NEXT_PUBLIC_PREPROD_DOMAIN || "renwu.dev";
  } else {
    return process.env.NEXT_PUBLIC_DEV_DOMAIN || "localhost:3000";
  }
}

/**
 * Extract the subdomain from a hostname
 * @param hostname - The hostname to parse (optional, defaults to window.location.hostname in browser)
 * @returns The subdomain string or null if on main domain
 *
 * @example
 * // Development
 * getCurrentSubdomain("acme.localhost:3000") // → "acme"
 * getCurrentSubdomain("localhost:3000") // → null
 *
 * // Production
 * getCurrentSubdomain("acme.renwu.app") // → "acme"
 * getCurrentSubdomain("renwu.app") // → null
 *
 * // Browser usage (client-side)
 * getCurrentSubdomain() // → extracts from window.location.hostname
 */
export function getCurrentSubdomain(hostname?: string): string | null {
  // Get hostname from parameter or browser location
  let host: string;
  if (hostname) {
    host = hostname;
  } else if (typeof window !== "undefined") {
    host = window.location.hostname;
  } else {
    // Server-side without hostname parameter
    return null;
  }

  // Handle empty or invalid hostnames
  if (!host || typeof host !== "string") {
    return null;
  }

  // Remove port from hostname for consistent parsing
  const normalizedHost = host.split(":")[0];
  const isDev = process.env.NODE_ENV === "development";

  if (isDev) {
    // Development environment: handle localhost subdomains
    if (normalizedHost === "localhost") {
      return null; // Main domain
    }
    if (normalizedHost.endsWith(".localhost")) {
      const subdomain = normalizedHost.split(".")[0];
      return subdomain || null;
    }
    return null;
  } else {
    // Production/Test environments: handle real domain subdomains
    const baseDomain = getCurrentDomain().split(":")[0]; // Remove port if any

    // Check if it's the main domain (with or without www)
    if (normalizedHost === baseDomain || normalizedHost === `www.${baseDomain}`) {
      return null; // Main domain
    }

    // Check if it's a subdomain of the base domain
    if (normalizedHost.endsWith(`.${baseDomain}`)) {
      const subdomain = normalizedHost.split(".")[0];
      return subdomain || null;
    }

    return null;
  }
}

/**
 * Get the protocol based on the environment
 * @returns The protocol
 */
export function getProtocol() {
  return process.env.NODE_ENV === "development" ? "http" : "https";
}

/**
 * Check if the URL is a subdomain
 * @param url - The URL to check
 * @returns True if the URL is a subdomain, false otherwise
 */
export function isSubdomain(url: string) {
  const regex = new RegExp(/^([a-z]+\:\/{2})?([\w-]+\.[\w-]+\.\w+)$/);

  return !!url.match(regex);
}

/**
 * Go to the main domain
 * @param path - The path to go to
 * @returns The URL to the main domain
 */
export function goToMainDomain(path: string) {
  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${domain}/${cleanPath}`;
}

/**
 * Go to the subdomain
 * @param subdomain - The subdomain to go to
 * @param path - The path to go to
 * @returns The URL to the subdomain
 */
export function goToSubdomain(subdomain: string, path: string) {
  if (!subdomain) {
    return goToMainDomain(path);
  }

  const domain = getCurrentDomain();
  const protocol = getProtocol();

  // Ensure path doesn't start with a slash when concatenating
  const cleanPath = path?.startsWith("/") ? path?.substring(1) : path;

  return `${protocol}://${subdomain}.${domain}/${cleanPath}`;
}

/**
 * Generate a slug
 * @param name - The name to generate a slug from
 * @returns The slug
 */
export function generateSlug(name: string): string {
  return name
    .toLowerCase() // Convert to lowercase
    .trim() // Remove leading and trailing whitespace
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading and trailing hyphens
}

/**
 * Ensure a slug is unique
 * @param supabase - The Supabase client
 * @param table - The table to check
 * @param slug - The slug to check
 * @returns The unique slug
 */
export async function ensureUniqueSlug(
  supabase: SupabaseClient,
  table: string,
  slug: string
): Promise<string> {
  // Check if slug exists
  const { data, error } = await supabase.from(table).select("slug").eq("slug", slug).limit(1);

  if (error) throw error;

  // If no matching slug found, return the original
  if (!data || data.length === 0) return slug;

  // Otherwise, find a unique slug by adding a number
  let counter = 1;
  let newSlug = `${slug}-${counter}`;

  let isUnique = false;
  while (!isUnique) {
    const { data, error } = await supabase.from(table).select("slug").eq("slug", newSlug).limit(1);

    if (error) throw error;

    if (!data || data.length === 0) {
      isUnique = true;
    } else {
      counter++;
      newSlug = `${slug}-${counter}`;
    }
  }

  return newSlug;
}

/**
 * Build a dynamic URL with cluster and project slugs
 * @param baseUrl - The base URL (e.g., "/dashboard")
 * @param params - The route parameters (clusterSlug and projectSlug)
 * @returns The complete URL
 */
export function buildDynamicUrl(
  baseUrl: string,
  params?: { clusterSlug?: string | null; projectSlug?: string | null }
) {
  // Skip processing for special URLs like /home or external URLs
  if (baseUrl.startsWith("http") || baseUrl.startsWith("/home") || baseUrl.startsWith("#")) {
    return baseUrl;
  }

  // If we don't have cluster and project slugs, return the base URL
  if (!params?.clusterSlug || !params?.projectSlug) {
    return baseUrl;
  }

  // Build the dynamic URL with new pattern: /clusters/[cluster-slug]/projects/[project-slug]
  return `/clusters/${params.clusterSlug}/projects/${params.projectSlug}${
    baseUrl.startsWith("/") ? baseUrl : `/${baseUrl}`
  }`;
}
