"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect } from "react";

/**
 * Hook for managing persistent cache across navigation
 * Prevents aggressive cache clearing and maintains data for better UX
 */
export function usePersistentCache() {
  const queryClient = useQueryClient();

  /**
   * Preserve project cache to prevent unnecessary refetches during navigation
   */
  const preserveProjectCache = useCallback(
    (projectId: string) => {
      if (!projectId) return;

      // Mark project data as fresh to prevent unnecessary refetches
      const projectQueries = [
        ["tasks", "list", projectId],
        ["project", "tags", projectId],
        ["project", "assignees", projectId],
        ["tasks", "groups", projectId],
      ];

      projectQueries.forEach((queryKey) => {
        const existingData = queryClient.getQueryData(queryKey);
        if (existingData) {
          // Update the data timestamp to mark as recently accessed
          queryClient.setQueryData(queryKey, existingData, {
            updatedAt: Date.now(),
          });
        }
      });
    },
    [queryClient]
  );

  /**
   * Warm cache for frequently accessed projects
   */
  const warmProjectCache = useCallback(
    async (projectId: string) => {
      if (!projectId) return;

      // Prefetch project data if not already cached
      const taskQueryKey = ["tasks", "list", projectId];
      const existingTasks = queryClient.getQueryData(taskQueryKey);

      if (!existingTasks) {
        // Prefetch tasks in background
        queryClient.prefetchQuery({
          queryKey: taskQueryKey,
          queryFn: async () => {
            const { getTasks } = await import(
              "@/app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_actions/tasks.actions"
            );
            const result = await getTasks(projectId);
            return result.tasks || [];
          },
          staleTime: 10 * 60 * 1000, // 10 minutes
          gcTime: 2 * 60 * 60 * 1000, // 2 hours
        });
      }
    },
    [queryClient]
  );

  /**
   * Clean up stale cache entries while preserving recently accessed data
   */
  const cleanupStaleCache = useCallback(() => {
    const now = Date.now();
    const staleThreshold = 4 * 60 * 60 * 1000; // 4 hours

    // Remove only very old queries that haven't been accessed recently
    queryClient.removeQueries({
      predicate: (query) => {
        const lastUpdated = query.state.dataUpdatedAt;
        const isVeryOld = now - lastUpdated > staleThreshold;

        // Don't remove home or user profile data
        const isProtectedData =
          query.queryKey[0] === "home" ||
          query.queryKey[0] === "user" ||
          query.queryKey[0] === "organization";

        return isVeryOld && !isProtectedData;
      },
    });
  }, [queryClient]);

  /**
   * Get cache statistics for monitoring
   */
  const getCacheStats = useCallback(() => {
    const allQueries = queryClient.getQueryCache().getAll();
    const stats = {
      totalQueries: allQueries.length,
      homeQueries: allQueries.filter((q) => q.queryKey[0] === "home").length,
      projectQueries: allQueries.filter(
        (q) => q.queryKey[0] === "tasks" || q.queryKey[0] === "project"
      ).length,
      userQueries: allQueries.filter((q) => q.queryKey[0] === "user").length,
      organizationQueries: allQueries.filter((q) => q.queryKey[0] === "organization").length,
    };
    return stats;
  }, [queryClient]);

  /**
   * Prevent cache invalidation during navigation
   */
  const protectNavigationCache = useCallback(() => {
    // Mark all current queries as recently accessed to prevent cleanup
    const allQueries = queryClient.getQueryCache().getAll();
    const now = Date.now();

    allQueries.forEach((query) => {
      if (query.state.data) {
        queryClient.setQueryData(query.queryKey, query.state.data, {
          updatedAt: now,
        });
      }
    });
  }, [queryClient]);

  // Cleanup stale cache periodically
  useEffect(() => {
    const interval = setInterval(cleanupStaleCache, 30 * 60 * 1000); // Every 30 minutes
    return () => clearInterval(interval);
  }, [cleanupStaleCache]);

  return {
    preserveProjectCache,
    warmProjectCache,
    cleanupStaleCache,
    getCacheStats,
    protectNavigationCache,
  };
}

/**
 * Hook for monitoring cache performance
 */
export function useCacheMonitoring() {
  const queryClient = useQueryClient();

  const logCacheActivity = useCallback((action: string, queryKey: unknown[], details?: string) => {
    if (process.env.NODE_ENV === "development") {
      console.log(`🗄️ Cache ${action}:`, {
        queryKey,
        details,
        timestamp: new Date().toISOString(),
      });
    }
  }, []);

  const monitorCacheHits = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    const hitRate =
      queries.reduce((acc, query) => {
        const hits = query.state.fetchStatus === "idle" && query.state.data ? 1 : 0;
        return acc + hits;
      }, 0) / queries.length;

    return {
      totalQueries: queries.length,
      hitRate: Math.round(hitRate * 100),
      activeQueries: queries.filter((q) => q.getObserversCount() > 0).length,
    };
  }, [queryClient]);

  return {
    logCacheActivity,
    monitorCacheHits,
  };
}
