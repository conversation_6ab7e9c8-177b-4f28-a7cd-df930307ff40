"use server";

import { sendEmail } from "./email-service";

/**
 * Simple test function to send an email via Resend
 * This is used only for testing email sending functionality
 */
export async function testResendEmail(to: string): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await sendEmail({
      to,
      from: "<EMAIL>",
      fromName: "Renwu Testing",
      replyTo: "<EMAIL>",
      subject: "Test Email from Renwu",
      html: `
        <div style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333333;">
          <h2 style="color: #2563eb; margin-bottom: 20px;">This is a test email</h2>
          <p style="margin-bottom: 15px; line-height: 1.5;">If you're seeing this, Resend is working correctly!</p>
          <p style="margin-bottom: 15px; line-height: 1.5;">Time sent: ${new Date().toISOString()}</p>
          <p style="color: #666666; font-size: 14px; margin-top: 30px; border-top: 1px solid #eeeeee; padding-top: 20px;">This is an automated test email from Renwu.</p>
        </div>
      `,
      text: `This is a test email. If you're seeing this, Resend is working correctly! Time sent: ${new Date().toISOString()}`,
    });

    return result;
  } catch (error) {
    console.error("Error in test email:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "An unknown error occurred while testing email",
    };
  }
}
