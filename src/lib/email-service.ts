"use server";

import { Resend } from "resend";
import { goToMainDomain, goToSubdomain } from "./utils";

// Initialize Resend with your API key
const resend = new Resend(process.env.RESEND_API_KEY);

type SendEmailOptions = {
  to: string;
  subject: string;
  html: string;
  text?: string;
  from?: string;
  replyTo?: string;
  fromName?: string;
};

/**
 * Sends an email using Resend
 */
export async function sendEmail(
  options: SendEmailOptions
): Promise<{ success: boolean; error?: string }> {
  try {
    // Format the from address with a name to improve deliverability
    const fromName = options.fromName || "Renwu";
    const fromEmail = options.from || process.env.EMAIL_FROM || "<EMAIL>";
    const formattedFrom = `${fromName} <${fromEmail}>`;

    // Use reply-to to improve deliverability and help with spam filters
    const replyTo = options.replyTo || "<EMAIL>";

    const to = options.to;
    const subject = options.subject;

    // Send email with proper from format and reply-to header
    const { error } = await resend.emails.send({
      from: formattedFrom,
      replyTo: replyTo,
      to,
      subject,
      html: options.html,
      text: options.text || undefined,
      headers: {
        // Custom headers to help with spam prevention
        "X-Entity-Ref-ID": `${Date.now()}-${Math.random().toString(36).substring(2, 10)}`, // Unique ID for each email
      },
    });

    if (error) {
      console.error("Error sending email:", error);
      return {
        success: false,
        error: error.message,
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending email:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "An unknown error occurred while sending email",
    };
  }
}

/**
 * Sends an organization invitation email
 */
export async function sendOrganizationInvitationEmail(params: {
  email: string;
  organizationName: string;
  invitationToken: string;
  subdomain?: string; // This should be the organization's subdomain
}): Promise<{ success: boolean; error?: string }> {
  const { email, organizationName, invitationToken, subdomain } = params;

  // Build the invitation URL using proper utility functions
  // Use the organization's subdomain passed in params, not the current request subdomain
  let invitationUrl: string;

  if (subdomain) {
    // Direct to organization subdomain
    invitationUrl = goToSubdomain(subdomain, `auth/sign-up?invitation_token=${invitationToken}`);
  } else {
    // Fallback to main domain
    invitationUrl = goToMainDomain(`auth/sign-up?invitation_token=${invitationToken}`);
  }

  // Create the email HTML content with improved design
  const htmlContent = `
    <div style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333333;">
      <h2 style="color: #2563eb; margin-bottom: 20px;">You've been invited to join ${organizationName}</h2>
      <p style="margin-bottom: 15px; line-height: 1.5;">You've been invited to join the ${organizationName} organization on Renwu. Join your team to start collaborating!</p>
      <p style="margin-bottom: 25px; line-height: 1.5;">Click the button below to accept the invitation and create your account:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${invitationUrl}" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
          Accept Invitation
        </a>
      </div>
      <p style="margin-top: 25px; margin-bottom: 10px; line-height: 1.5;">Or copy and paste this URL into your browser:</p>
      <p style="word-break: break-all; color: #666666; margin-bottom: 25px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-size: 14px;">${invitationUrl}</p>
      <p style="margin-bottom: 10px; line-height: 1.5;">This invitation will expire in 7 days.</p>
      <p style="color: #666666; font-size: 14px; margin-top: 30px; border-top: 1px solid #eeeeee; padding-top: 20px;">If you did not expect this invitation, you can safely ignore this email.</p>
    </div>
  `;

  // Create the email text content (for email clients that don't support HTML)
  const textContent = `
    You've been invited to join ${organizationName}
    
    You've been invited to join the ${organizationName} organization on Renwu.
    
    Click the link below to accept the invitation and create your account:
    ${invitationUrl}
    
    This invitation will expire in 7 days.
    
    If you did not expect this invitation, you can ignore this email.
  `;

  // Send the email using our verified domain
  return sendEmail({
    to: email,
    from: "<EMAIL>", // Use our verified domain
    fromName: `${organizationName} Team`, // Add a friendly sender name
    replyTo: "<EMAIL>", // Add reply-to for better deliverability
    subject: `Join ${organizationName} on Renwu`,
    html: htmlContent,
    text: textContent,
  });
}
