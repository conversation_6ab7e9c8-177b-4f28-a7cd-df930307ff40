import { getCurrentDomain } from "@/lib/utils";
import { createBrowserClient } from "@supabase/ssr";

const isDev = process.env.NODE_ENV === "development";

export function createClient() {
  // Get the current domain dynamically when creating the client
  const currentDomain = getCurrentDomain();

  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookieOptions: {
        ...(isDev ? {} : { domain: `.${currentDomain}` }),
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: "/",
        sameSite: "lax",
        secure: !isDev,
      },
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
      },
    }
  );
}
