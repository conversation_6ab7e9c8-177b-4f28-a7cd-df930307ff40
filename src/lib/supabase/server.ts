import { getCurrentDomain } from "@/lib/utils";
import { createServerClient } from "@supabase/ssr";
import { cookies, headers } from "next/headers";

const isDev = process.env.NODE_ENV === "development";

export async function createClient() {
  const cookieStore = await cookies();

  // Get hostname from request headers for accurate domain detection
  let hostname: string | undefined;
  try {
    const headersList = await headers();
    hostname = headersList.get("host") || undefined;
  } catch {
    // Headers not available in some contexts
    hostname = undefined;
  }

  // Get the current domain dynamically when creating the client
  const currentDomain = getCurrentDomain(hostname);

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookieOptions: {
        ...(isDev ? {} : { domain: `.${currentDomain}` }),
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: "/",
        sameSite: "lax",
        secure: !isDev,
      },
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, {
                ...options,
                ...(isDev ? {} : { domain: `.${currentDomain}` }),
              })
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}
