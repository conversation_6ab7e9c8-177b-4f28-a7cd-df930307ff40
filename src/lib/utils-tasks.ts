import { ColumnStatus, LifeCycleStatus, TaskModel, Value } from "@/db/schemas/tasks.schema";
import {
  ChevronDownIcon,
  ChevronsUpIcon,
  ChevronUpIcon,
  DotIcon,
  LucideIcon,
  MinusIcon,
} from "lucide-react";

// Parse effort helper
export function parseEffort(value: string): number {
  // Handle plain numbers by assuming they're days
  if (/^\d+$/.test(value)) {
    return parseInt(value);
  }
  const weeks = parseInt(value.match(/(\d+)w/)?.[1] || "0");
  const days = parseInt(value.match(/(\d+)d/)?.[1] || "0");
  return weeks * 7 + days;
}

// Format effort helper
export function formatEffort(days: number): string {
  if (days < 7) return `${days}d`;
  const weeks = Math.floor(days / 7);
  const remainingDays = days % 7;
  return remainingDays > 0 ? `${weeks}w${remainingDays}d` : `${weeks}w`;
}

// Map task status to lifecycle status automatically with more variety
export function mapStatusToLifeCycle(status: string): string {
  // Define mapping between status and lifecycle status
  switch (status.toLowerCase()) {
    case "backlog":
      return "created";
    case "to do":
      return "ready";
    case "in progress":
      return "started";
    case "in review":
      return "test done"; // Changed to show variety
    case "done":
      return "dev done"; // Changed to final stage
    default:
      return "created"; // Default fallback value
  }
}

// Update task lifecycle status based on status change
export function updateTaskLifeCycle(task: TaskModel, newStatus: string): TaskModel {
  // Get the new lifecycle status based on the new status
  const newLifeCycleStatus = mapStatusToLifeCycle(newStatus);

  // Return updated task with new lifecycle status
  return {
    ...task,
    status: newStatus as ColumnStatus,
    lifeCycleStatus: newLifeCycleStatus as LifeCycleStatus,
  };
}

// Get icon mapping for task value
export function getValueIcon(value: Value): { value: Value; lucideIcon: LucideIcon } {
  const variants: Record<Value, { value: Value; lucideIcon: LucideIcon }> = {
    Huge: { value: "Huge", lucideIcon: ChevronsUpIcon },
    High: { value: "High", lucideIcon: ChevronUpIcon },
    Normal: { value: "Normal", lucideIcon: MinusIcon },
    Low: { value: "Low", lucideIcon: ChevronDownIcon },
    "Not set": { value: "Not set", lucideIcon: DotIcon },
  };

  return variants[value];
}
