import {
  ArrowLeft,
  AudioWaveform,
  BarChart3,
  CalendarCheck,
  Clock,
  CombineIcon,
  Command,
  GalleryVerticalEnd,
  History,
  Home,
  LayoutDashboard,
  LineChart,
  List,
  ListChecks,
  MapIcon,
  NetworkIcon,
  PackageCheck,
  Settings,
  Target,
} from "lucide-react";

import {
  AccountSettingsNavData,
  AnalyticsNavData,
  CeremoniesNavData,
  DeliveryNavData,
  DiscoveryNavData,
  HomeNavData,
  HubNavData,
  ProjectsData,
  SettingsNavData,
  TeamsData,
  TSidebarProjectData,
  UserProfile,
} from "@/types/sidebar.types";
import { LucideIcon } from "lucide-react";

// User profile data
export const userData: UserProfile = {
  name: "<PERSON>",
  email: "<EMAIL>",
  avatar: "https://github.com/TonyKYC.png",
};

// Teams data
export const teamsData: TeamsData = {
  items: [
    {
      name: "Mobile App",
      logo: GalleryVerticalEnd,
      plan: "Ai-app-v2",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: GalleryVerticalEnd,
      plan: "Free",
    },
  ],
};

// Home navigation data
// SHOULD NOT HAVE SUBITEMS !
export const homeNavData: HomeNavData = {
  items: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Cluster",
      url: "/cluster",
      icon: NetworkIcon as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Goals",
      url: "/goals",
      icon: Target as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Discovery navigation data
export const discoveryNavData: DiscoveryNavData = {
  items: [
    {
      title: "Create & Prioritize",
      url: "/ideas",
      icon: CombineIcon as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Roadmap",
      url: "/roadmap",
      icon: MapIcon as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Delivery navigation data
export const deliveryNavData: DeliveryNavData = {
  items: [
    {
      title: "Tasks",
      url: "/tasks",
      icon: List as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    // {
    //   title: "Task boards",
    //   url: "/board",
    //   icon: SquareKanban as LucideIcon,
    //   isActive: false,
    //   isExpanded: false,
    // },
    {
      title: "Completed tasks",
      url: "/completed-tasks",
      icon: ListChecks as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Deployments",
      url: "/deployments",
      icon: PackageCheck as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Ceremonies data
export const ceremoniesNavData: CeremoniesNavData = {
  items: [
    {
      title: "Daily standup",
      url: "/daily-standup",
      icon: Clock as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Sprint review",
      url: "/sprint-review",
      icon: CalendarCheck as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Retrospective",
      url: "/retrospective",
      icon: History as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};
// Analytics navigation data
export const analyticsNavData: AnalyticsNavData = {
  items: [
    {
      title: "Performance Analytics",
      url: "/analytics",
      icon: LineChart as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Status Reports",
      url: "/reports",
      icon: BarChart3 as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};
// Settings navigation data
export const navSettingsData: SettingsNavData = {
  items: [
    {
      title: "Project Settings",
      url: "/settings",
      icon: Settings as LucideIcon,
      isActive: false,
    },
    {
      title: "Back to Your Homepage",
      url: "/home",
      icon: ArrowLeft as LucideIcon,
      isActive: false,
    },
  ],
};

// Projects data
export const projectsData: ProjectsData = {
  items: [
    {
      id: 1,
      name: "Renwu Inc",
      url: "/dashboard",
      icon: GalleryVerticalEnd as LucideIcon,
    },
    {
      id: 2,
      name: "Acme Corp.",
      url: "/ddemo/ashboard",
      icon: AudioWaveform as LucideIcon,
    },
    {
      id: 3,
      name: "Daka Corp.",
      url: "/dashboard",
      icon: Command as LucideIcon,
    },
  ],
};

// Legacy sidebarProjectData for backward compatibility
export const sidebarProjectData: TSidebarProjectData = {
  user: userData,
  teams: teamsData.items,
  navHome: homeNavData.items,
  navDiscovery: discoveryNavData.items,
  navDelivery: deliveryNavData.items,
  navAnalytics: analyticsNavData.items,
  navSettings: navSettingsData.items,
  projects: projectsData.items,
};

// Hub navigation data
export const hubNavData: HubNavData = {
  items: [
    {
      title: "Homepage",
      url: "/home",
      icon: Home as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

export const accountSettingsNavData: AccountSettingsNavData = {
  items: [
    {
      title: "User Account",
      url: "/home/<USER>",
      icon: Settings as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};
