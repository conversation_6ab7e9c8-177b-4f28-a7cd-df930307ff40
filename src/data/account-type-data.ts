import { type AccountTypeCard } from "@/types/account.types";
import { HousePlus, type LucideIcon, Merge, Zap } from "lucide-react";

// Account type card data
const accountTypeCards: AccountTypeCard[] = [
  {
    id: "individual",
    title: "Individual Project",
    description:
      "Create a personal workspace with full control over all settings. Perfect for freelancers and individual creators.",
    icon: Zap as LucideIcon,
  },
  {
    id: "create-organization",
    title: "Create a new Organization",
    description:
      "Set up a collaborative workspace for your team with shared resources and customizable permission levels.",
    icon: HousePlus as LucideIcon,
  },
  {
    id: "organization-invite-only",
    title: "Join an existing Organization",
    description:
      "Connect to an organization you've been invited to. Access shared resources and collaborate with team members.",
    icon: Merge as LucideIcon,
  },
];

export { accountTypeCards };
