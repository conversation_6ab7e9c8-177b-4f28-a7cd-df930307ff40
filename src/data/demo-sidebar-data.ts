import {
  ArrowLeft,
  AudioWaveform,
  BarChart3,
  Calendar1,
  Command,
  GalleryVerticalEnd,
  Home,
  LayoutDashboard,
  LayoutList,
  LineChart,
  List,
  ListChecks,
  ListPlus,
  Map,
  Settings,
  SquareKanban,
  Target,
  TrendingUp,
  UserSearch,
} from "lucide-react";

import {
  AccountSettingsNavData,
  AnalyticsNavData,
  DeliveryNavData,
  DiscoveryNavData,
  HomeNavData,
  HubNavData,
  ProjectsData,
  SettingsNavData,
  TeamsData,
  TSidebarProjectData,
  UserProfile,
} from "@/components/demo-navbar/demo-sidebar.types";
import { LucideIcon } from "lucide-react";

// User profile data
export const userData: UserProfile = {
  name: "<PERSON>",
  email: "<EMAIL>",
  avatar: "https://github.com/TonyKYC.png",
};

// Teams data
export const teamsData: TeamsData = {
  items: [
    {
      name: "Renwu Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: GalleryVerticalEnd,
      plan: "Free",
    },
  ],
};

// Home navigation data
// SHOULD NOT HAVE SUBITEMS !
export const homeNavData: HomeNavData = {
  items: [
    {
      title: "Dashboard",
      url: "/demo/dashboard",
      icon: LayoutDashboard as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Agenda",
      url: "/demo/calendar",
      icon: Calendar1 as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Roadmap",
      url: "/demo/roadmap",
      icon: Map as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Goals",
      url: "/demo/goals",
      icon: Target as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Discovery navigation data
export const discoveryNavData: DiscoveryNavData = {
  items: [
    {
      title: "Ideas for new features",
      url: "/demo/ideas",
      icon: UserSearch as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Prioritization",
      url: "/demo/prioritization",
      icon: LayoutList as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Ready for dev",
      url: "/demo/development-ready",
      icon: ListPlus as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Delivery navigation data
export const deliveryNavData: DeliveryNavData = {
  items: [
    {
      title: "List of tasks",
      url: "/demo/backlog",
      icon: List as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Task boards",
      url: "/demo/board",
      icon: SquareKanban as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Release management",
      url: "/demo/release",
      icon: ListChecks as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

// Analytics navigation data
export const analyticsNavData: AnalyticsNavData = {
  items: [
    {
      title: "Performance Analytics",
      url: "/demo/analytics",
      icon: LineChart as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Status Reports",
      url: "/demo/reports",
      icon: BarChart3 as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
    {
      title: "Project Insights",
      url: "/demo/insights",
      icon: TrendingUp as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};
// Settings navigation data
export const navSettingsData: SettingsNavData = {
  items: [
    {
      title: "Project Settings",
      url: "/demo/project-settings",
      icon: Settings as LucideIcon,
      isActive: false,
    },
    {
      title: "Back to Your Homepage",
      url: "/home",
      icon: ArrowLeft as LucideIcon,
      isActive: false,
    },
  ],
};

// Projects data
export const projectsData: ProjectsData = {
  items: [
    {
      id: 1,
      name: "Renwu Inc",
      url: "/demo/dashboard",
      icon: GalleryVerticalEnd as LucideIcon,
    },
    {
      id: 2,
      name: "Acme Corp.",
      url: "/ddemo/ashboard",
      icon: AudioWaveform as LucideIcon,
    },
    {
      id: 3,
      name: "Daka Corp.",
      url: "/demo/dashboard",
      icon: Command as LucideIcon,
    },
  ],
};

// Legacy sidebarProjectData for backward compatibility
export const sidebarProjectData: TSidebarProjectData = {
  user: userData,
  teams: teamsData.items,
  navHome: homeNavData.items,
  navDiscovery: discoveryNavData.items,
  navDelivery: deliveryNavData.items,
  navAnalytics: analyticsNavData.items,
  navSettings: navSettingsData.items,
  projects: projectsData.items,
};

// Hub navigation data
export const hubNavData: HubNavData = {
  items: [
    {
      title: "Homepage",
      url: "/home",
      icon: Home as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};

export const accountSettingsNavData: AccountSettingsNavData = {
  items: [
    {
      title: "User Account",
      url: "/account-settings",
      icon: Settings as LucideIcon,
      isActive: false,
      isExpanded: false,
    },
  ],
};
