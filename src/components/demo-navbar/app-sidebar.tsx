"use client";

import * as React from "react";
import { type NavItem, type SettingsNavItem } from "./demo-sidebar.types";

import { NavMain } from "@/components/navbar/nav-main";
import { NavSettings } from "@/components/navbar/nav-settings";
import { NavUser } from "@/components/navbar/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  accountSettingsNavData,
  analyticsNavData,
  deliveryNavData,
  discoveryNavData,
  homeNavData,
  hubNavData,
  navSettingsData,
  teamsData,
  userData,
} from "@/data/demo-sidebar-data";
import { useActiveMenu } from "@/hooks/use-active-menu";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { ThemeToggle } from "../ui/theme-toggle";
import { DemoTeamSwitcher } from "./demo-team-switcher";
import { NavOverview } from "./nav-overview";
import { RenwuAiCommandSearch } from "./nav-renwu-ai-command-search";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const isPublicRoute = pathname === "/";
  const isSignedIn = isPublicRoute ? false : true;

  // Memoize the menu categories to prevent unnecessary re-renders
  const menuCategories = React.useMemo(() => {
    return {
      navHome: homeNavData.items,
      navDiscovery: discoveryNavData.items,
      navDelivery: deliveryNavData.items,
      navAnalytics: analyticsNavData.items,
      navSettings: navSettingsData.items,
      navHub: hubNavData.items,
      navAccountSettings: accountSettingsNavData.items,
    };
  }, []);

  // Use the active menu hook to determine which menu items are active
  const activeMenus = useActiveMenu(menuCategories);
  const { state } = useSidebar();
  const isExpanded = state === "expanded";

  return (
    isSignedIn && (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader
          className={cn(
            "flex items-center justify-between transition-all duration-300",
            `${isExpanded ? " flex-row mr-1" : "flex-col"}`
          )}
        >
          <DemoTeamSwitcher teams={teamsData.items} />
          <ThemeToggle />
        </SidebarHeader>
        <SidebarContent>
          <RenwuAiCommandSearch />
          <NavOverview groupLabel="Overview" items={activeMenus.navHome as NavItem[]} />
          <NavMain groupLabel="Discovery" items={activeMenus.navDiscovery as NavItem[]} />
          <NavMain groupLabel="Delivery" items={activeMenus.navDelivery as NavItem[]} />
          <NavMain groupLabel="Analytics" items={activeMenus.navAnalytics as NavItem[]} />
          <NavSettings
            groupLabel="Settings"
            items={activeMenus.navSettings as SettingsNavItem[]}
            className="mt-auto"
          />
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={userData} />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    )
  );
}
