"use client";

import {
  ArrowUpRightIcon,
  CircleFadingPlusIcon,
  FileInputIcon,
  FolderPlusIcon,
  Sparkles,
} from "lucide-react";
import * as React from "react";

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command";
import { AnimatedGradientText } from "../origin-ui/animated-gradient-text";
import { Button } from "../ui/button";
import { useSidebar } from "../ui/sidebar";

export function RenwuAiCommandSearch() {
  const { state } = useSidebar();
  const isExpanded = state === "expanded";

  const [open, setOpen] = React.useState(false);
  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  return (
    <>
      {isExpanded ? (
        <AnimatedGradientText
          label="Your AI Assistant"
          className="mx-3 text-xs"
          onClick={() => setOpen(true)}
        />
      ) : (
        <Button
          variant="ghost"
          className="mx-1.5 relative flex justify-center bg-white/40 cursor-pointer dark:bg-black/40"
          onClick={() => setOpen(true)}
        >
          <div className="absolute inset-0 rounded-md bg-gradient-to-r from-[#ffaa40]/50 via-[#9c40ff]/50 to-[#ffaa40]/50 p-[1px] gradient-animate ![mask-composite:subtract] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] pointer-events-none" />
          <Sparkles className="h-3.5 w-3.5 icon-pulse" />
        </Button>
      )}
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder="Type a command or search..." />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          <CommandGroup heading="Quick start">
            <CommandItem>
              <FolderPlusIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>New folder</span>
              <CommandShortcut className="justify-center">⌘N</CommandShortcut>
            </CommandItem>
            <CommandItem>
              <FileInputIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>Import document</span>
              <CommandShortcut className="justify-center">⌘I</CommandShortcut>
            </CommandItem>
            <CommandItem>
              <CircleFadingPlusIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>Add block</span>
              <CommandShortcut className="justify-center">⌘B</CommandShortcut>
            </CommandItem>
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="Navigation">
            <CommandItem>
              <ArrowUpRightIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>Go to dashboard</span>
            </CommandItem>
            <CommandItem>
              <ArrowUpRightIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>Go to board</span>
            </CommandItem>
            <CommandItem>
              <ArrowUpRightIcon size={16} className="mr-2 opacity-60" aria-hidden="true" />
              <span>Go to settings</span>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
