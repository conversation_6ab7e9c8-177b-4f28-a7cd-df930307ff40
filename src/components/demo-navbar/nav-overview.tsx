"use client";

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { type LucideIcon } from "lucide-react";

export function NavOverview({
  groupLabel,
  items,
}: {
  groupLabel: string;
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    isExpanded?: boolean;
  }[];
}) {
  // Get the current sidebar state using the useSidebar hook
  const { state } = useSidebar();
  const isExpanded = state === "expanded";

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{groupLabel}</SidebarGroupLabel>
      <SidebarMenu>
        {/* First two items displayed side by side only when sidebar is expanded */}
        {isExpanded && items.length >= 2 ? (
          <>
            {/* First row - items 1 & 2 */}
            <div className="flex gap-2 w-full">
              {items.slice(0, 2).map((item) => (
                <SidebarMenuItem key={item.title} className="flex-1 w-1/2">
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    className={cn(
                      item.isActive &&
                        "active-menu text-accent-foreground font-medium"
                    )}
                  >
                    <a href={item.url} className="w-full">
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </div>

            {/* Second row - items 3 & 4 */}
            {items.length >= 4 && (
              <div className="flex gap-2 w-full">
                {items.slice(2, 4).map((item) => (
                  <SidebarMenuItem key={item.title} className="flex-1 w-1/2">
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      className={cn(
                        item.isActive &&
                          "active-menu text-accent-foreground font-medium"
                      )}
                    >
                      <a href={item.url} className="w-full">
                        {item.icon && <item.icon />}
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </div>
            )}

            {/* Remaining items in vertical layout */}
            {items.slice(4).map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  tooltip={item.title}
                  className={cn(
                    item.isActive &&
                      "active-menu text-accent-foreground font-medium"
                  )}
                >
                  <a href={item.url}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </>
        ) : (
          // Default vertical layout for all items when sidebar is collapsed
          items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                tooltip={item.title}
                className={cn(
                  item.isActive &&
                    "active-menu text-accent-foreground font-medium"
                )}
              >
                <a href={item.url}>
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
