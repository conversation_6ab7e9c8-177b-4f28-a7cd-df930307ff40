import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>ooter, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { cn, goToMainDomain } from "@/lib/utils";
import { Computer, CreditCard, HelpCircle, LogOut, Menu, TvMinimal, User } from "lucide-react";
import Link from "next/link";
import { LogoutButton } from "../auth/logout-button";
import GlobalNotificationBell from "../origin-ui/global-notification-bell";
import RenwuLogo from "../svg/renwu-logo";
import { Badge } from "../ui/badge";
import { TopbarRenwuAiCommandSearch } from "./topbar-renwu-ai-command-search";

const menuItems = [
  { name: "Homepage", compactName: "Home", href: "/home" },
  { name: "Organization", compactName: "Orgs", href: "/demo/home/<USER>" },
  { name: "Clusters", compactName: "Clusters", href: "/demo/home/<USER>" },
  { name: "Team", compactName: "Team", href: "/home/<USER>" },
  { name: "Account Settings", compactName: "Account", href: "/demo/home/<USER>" },
];

export function AppTopbar() {
  return (
    <div className="sticky top-2 left-0 right-0 z-50  backdrop-blur-lg">
      <div className="flex items-center justify-between h-16 px-4 md:px-6">
        {/* Logo and brand */}
        <div className="flex items-center gap-4 md:gap-14">
          <Link href="/" className="flex items-center">
            <RenwuLogo className="w-16 h-16" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex md:items-center md:gap-6">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "text-sm font-medium transition-colors hover:text-primary px-2 py-2 rounded-md  hover:underline hover:underline-offset-8",
                  "[&[data-state=active]]:underline [&[data-state=active]]:underline-offset-8"
                )}
              >
                <span className="hidden xl:block">{item.name}</span>
                <span className="xl:hidden">{item.compactName}</span>
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <div className="hidden max-md:block">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Menu className="w-6 h-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0">
                <>
                  <RenwuLogo className="fixed top-0 left-0 w-16 h-16" />
                  <SheetTitle className="flex items-center justify-center gap-2 mt-4">
                    Menu
                  </SheetTitle>

                  <nav className="flex flex-col gap-2 p-4 pt-8">
                    {menuItems.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="text-base font-medium py-2 px-2 rounded-md hover:bg-accent"
                      >
                        {item.name}
                      </Link>
                    ))}
                    <Link href="/demo/home/">
                      <Badge variant="default" className="py-1 px-2 mt-2">
                        Demo Account
                      </Badge>
                    </Link>
                  </nav>
                  <SheetFooter>
                    <ThemeToggle className="flex self-end" />

                    <LogoutButton variant="default" className="h-10" isMenuButton />
                  </SheetFooter>
                </>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Topbar Actions */}
        <div className="flex items-center gap-2 md:gap-4">
          <div className="hidden xl:block">
            <TopbarRenwuAiCommandSearch />
          </div>
          <GlobalNotificationBell side="bottom" align="end" />
          <ThemeToggle />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative w-8 h-8 rounded-full">
                <Avatar className="w-8 h-8">
                  <AvatarImage src="https://github.com/shadcn.png" alt="User" />
                  <AvatarFallback>UN</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <TvMinimal className="w-4 h-4" />
                  <Link href="/demo/home/">Demo Application</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <Link href="/home/<USER>">Account</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <Computer className="w-4 h-4" />
                  <Link href={goToMainDomain("auth/organizations")}>Switch Organization</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  <Link href="/home/<USER>">Manage Plan</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" />
                  <Link href="/home/<USER>">Help</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <LogOut className="w-4 h-4" />
                  <LogoutButton variant="ghost" isMenuButton />
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}

export default AppTopbar;
