"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { loginFormAction } from "@/db/actions/auth.action";
import { acceptOrganizationInvitationAction } from "@/db/actions/organization-invitations.action";
import { cn, getCurrentSubdomain, goToMainDomain } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useActionState, useCallback, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { toast } from "sonner";

export function LoginForm({
  className,
  invitationToken,
  invitationEmail,
  ...props
}: React.ComponentPropsWithoutRef<"div"> & {
  subdomain: string | null;
  invitationToken?: string;
  invitationEmail?: string;
}) {
  const router = useRouter();
  const [state, formAction] = useActionState(loginFormAction, { error: null });
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [subdomain, setSubdomain] = useState<string | null>(null);
  const [formValues, setFormValues] = useState({
    email: invitationEmail || "",
    password: "",
  });

  // Detect subdomain on component mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const subdomain = getCurrentSubdomain(window.location.hostname);
      if (subdomain) {
        setSubdomain(subdomain);
      }
    }
  }, []);

  let organizationName = "User Portal";
  if (props.subdomain === null) {
    organizationName = "User Portal";
  } else {
    organizationName = props.subdomain[0].toUpperCase() + props.subdomain.slice(1);
  }

  // Handle invitation acceptance after successful login
  const handleInvitationAcceptance = useCallback(async () => {
    if (!invitationToken) return;

    try {
      toast.info("Adding you to the organization...");

      // Get the current user from Supabase client
      const { createClient } = await import("@/lib/supabase/client");
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Unable to get user information");
        router.push("/home");
        return;
      }

      const result = await acceptOrganizationInvitationAction(invitationToken, user.id);

      if (result.success) {
        toast.success("Successfully joined organization");
        router.push("/home");
      } else {
        toast.error(`Error joining organization: ${result.message}`);
        router.push("/home");
      }
    } catch (inviteError) {
      console.error("Exception during invitation acceptance:", inviteError);
      toast.error(
        `Error joining organization: ${
          inviteError instanceof Error ? inviteError.message : "Unknown error"
        }`
      );
      router.push("/home");
    }
  }, [invitationToken, router]);

  // Handle redirect if login is successful
  useEffect(() => {
    if (state.success && state.redirectTo) {
      setIsRedirecting(true);

      // If we have an invitation token, handle the invitation acceptance
      if (invitationToken) {
        handleInvitationAcceptance();
      } else {
        router.push(state.redirectTo);
      }
    }
  }, [state.success, state.redirectTo, router, invitationToken, handleInvitationAcceptance]);

  // Create a wrapped form action that will pass along the form data
  const handleFormAction = async (formData: FormData) => {
    // Store form values before submission
    setFormValues({
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    });
    await formAction(formData);
  };

  // Handle input changes to keep state in sync
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className={cn("flex flex-col gap-6 -mt-6", className)} {...props}>
      <Card>
        <CardHeader className="flex flex-col items-center justify-center gap-2">
          <div
            className="flex size-11 shrink-0 items-center justify-center rounded-full border"
            aria-hidden="true"
          >
            <svg
              className="stroke-zinc-800 dark:stroke-zinc-100"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 32 32"
              aria-hidden="true"
            >
              <circle cx="16" cy="16" r="12" fill="none" strokeWidth="8" />
            </svg>
          </div>
          <CardTitle className="flex flex-col items-center sm:text-center">
            <span className="text-4xl font-bold">{organizationName}</span>
          </CardTitle>
          <CardDescription className="sm:text-center w-3/4">
            {invitationToken ? (
              <span>Sign in to accept your organization invitation.</span>
            ) : (
              <span>Welcome back, please enter your credentials to login to your account.</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={handleFormAction} className="mt-6">
            <div className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  autoComplete="email"
                  required
                  value={formValues.email}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </Link>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="password"
                  autoComplete="current-password"
                  required
                  value={formValues.password}
                  onChange={handleInputChange}
                />
              </div>
              {state.error && <p className="text-sm text-red-500">{state.error}</p>}
              <div>
                <SubmitButton isRedirecting={isRedirecting} />
              </div>
            </div>
            <div className="mt-4 text-center text-sm">
              Don&apos;t have an account?{" "}
              <Link href="/auth/sign-up" className="underline underline-offset-4">
                Sign up
              </Link>
            </div>
            {subdomain && (
              <div className="mt-4 text-center text-sm">
                Login to your{" "}
                <Link href={goToMainDomain("auth/login")} className="underline underline-offset-4">
                  Portal
                </Link>
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

// Update the submit button to accept and use the redirecting state
function SubmitButton({ isRedirecting }: { isRedirecting: boolean }) {
  const { pending } = useFormStatus();
  const isLoading = pending || isRedirecting;

  return (
    <Button type="submit" className="w-full disabled:opacity-100" disabled={isLoading}>
      {isLoading ? (
        <div className="w-4 h-4 border-t-2 border-b-2 border-white/80 rounded-full pointer-events-none animate-spin"></div>
      ) : (
        "Login"
      )}
    </Button>
  );
}
