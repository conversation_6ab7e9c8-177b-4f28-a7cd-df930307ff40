"use client";

import { useOrganizationEmployees } from "@/app/home/<USER>/use-organization-employees";
import { SendIcon, UserRoundPlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRef, useState, useTransition } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createOrganizationInvitationAction } from "@/db/actions/organization-invitations.action";
import { OrganizationInfoType } from "@/types/organization.types";
import { UserProfile } from "@/types/user.types";
import { toast } from "sonner";

type MemberInvitationProps = {
  size?: "sm" | "default" | "lg";
  organization: OrganizationInfoType;
};

export default function MemberInvitation({
  size = "default",
  organization,
}: MemberInvitationProps) {
  const [emails, setEmails] = useState(["", ""]);
  const [isPending, startTransition] = useTransition();
  const lastInputRef = useRef<HTMLInputElement>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  const { data: employeesData } = useOrganizationEmployees();
  const existingEmails =
    employeesData?.employees?.map((emp: UserProfile) => emp.email.toLowerCase()) || [];

  const addEmail = () => {
    setEmails([...emails, ""]);
  };

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails];
    newEmails[index] = value;
    setEmails(newEmails);
  };

  const handleSendInvitations = () => {
    // Filter out empty emails
    const validEmails = emails.filter((email) => email.trim() !== "");

    if (validEmails.length === 0) {
      toast.error("Please enter at least one email address");
      return;
    }

    startTransition(async () => {
      try {
        let newInvitations = 0;
        let updatedInvitations = 0;
        let failedInvitations = 0;

        // Create invitations and send emails
        for (const email of validEmails) {
          if (existingEmails.includes(email.toLowerCase())) {
            toast.error(`${email} is already a member of this organization`);
            continue; // Skip this email
          }

          const result = await createOrganizationInvitationAction(email, organization);

          if (result.success) {
            // Check if it was a new invitation or an updated one based on the message
            if (result.message && result.message.includes("updated")) {
              updatedInvitations++;
              toast.success(`Existing invitation for ${email} has been updated and resent`);
            } else {
              newInvitations++;
              toast.success(`New invitation sent to ${email}`);
            }
          } else {
            failedInvitations++;
            toast.error(`Failed to invite ${email}: ${result.message}`);
          }
        }

        // Display a summary if multiple invitations were processed
        if (validEmails.length > 1) {
          const summaryMessage = [];
          if (newInvitations > 0) summaryMessage.push(`${newInvitations} new invitation(s) sent`);
          if (updatedInvitations > 0)
            summaryMessage.push(`${updatedInvitations} invitation(s) updated and resent`);
          if (failedInvitations > 0)
            summaryMessage.push(`${failedInvitations} invitation(s) failed`);

          toast.info(summaryMessage.join(", "));
        }

        // Clear the email inputs
        setEmails(["", ""]);
        setIsDialogOpen(false);

        // Refresh the server components to show new invitations
        router.refresh();
      } catch (error) {
        toast.error("An error occurred while sending invitations");
        console.error("Invitation error:", error);
      }
    });
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size={size}>
          <UserRoundPlusIcon className="w-4 h-4 mr-2" />
          Invite members
        </Button>
      </DialogTrigger>
      <DialogContent
        className="w-full max-w-md sm:max-w-lg"
        onOpenAutoFocus={(e) => {
          e.preventDefault();
          lastInputRef.current?.focus();
        }}
      >
        <div className="flex flex-col gap-2">
          <div
            className="self-center flex size-11 shrink-0 items-center justify-center rounded-full border"
            aria-hidden="true"
          >
            <UserRoundPlusIcon className="opacity-80" size={16} />
          </div>
          <DialogHeader className="self-center">
            <DialogTitle className="text-center">Invite team members</DialogTitle>
            <DialogDescription className="text-center">
              Enter email addresses of people you&apos;d like to invite to your organization.
            </DialogDescription>
          </DialogHeader>
        </div>

        <div className="space-y-5">
          <div className="space-y-4">
            <div className="*:not-first:mt-2">
              <Label>Invite via email</Label>
              <div className="space-y-3">
                {emails.map((email, index) => (
                  <Input
                    key={index}
                    id={`team-email-${index + 1}`}
                    placeholder="<EMAIL>"
                    type="email"
                    value={email}
                    onChange={(e) => handleEmailChange(index, e.target.value)}
                    ref={index === emails.length - 1 ? lastInputRef : undefined}
                    autoFocus={index === 0}
                  />
                ))}
              </div>
            </div>
            <button
              type="button"
              onClick={addEmail}
              className="text-sm underline hover:no-underline"
            >
              + Add another
            </button>
          </div>
          <Button
            type="button"
            className="w-full"
            onClick={handleSendInvitations}
            disabled={isPending}
          >
            <SendIcon className="w-4 h-4 mr-2" />
            {isPending ? "Sending invites..." : "Send invites"}
          </Button>
        </div>

        <p className="text-xs text-muted-foreground">
          The recipients will receive an email with a secure link to join your organization. They
          will need to create an account or sign in to accept the invitation.
        </p>
      </DialogContent>
    </Dialog>
  );
}
