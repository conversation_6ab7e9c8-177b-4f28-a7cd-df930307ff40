"use client";

import { But<PERSON> } from "@/components/ui/button";
import { createClient } from "@/lib/supabase/client";
import { cn } from "@/lib/utils";
import { LogOut } from "lucide-react";
import { useRouter } from "next/navigation";

type LogoutButtonProps = {
  variant?: "ghost" | "default";
  className?: string;
  withIcon?: boolean;
  isMenuButton?: boolean;
};

export function LogoutButton({
  variant = "ghost",
  className,
  withIcon = true,
  isMenuButton = false,
}: LogoutButtonProps) {
  const router = useRouter();

  const handleLogout = () => {
    // Start navigation immediately
    router.push("/");

    // Handle logout in the background
    const supabase = createClient();
    supabase.auth.signOut({ scope: "global" }).catch((error) => {
      console.info("Error during logout:", error);
      // Optionally handle error (e.g., show toast notification)
    });
  };

  return (
    <Button
      variant={variant}
      onClick={handleLogout}
      className={cn(`${isMenuButton && "p-2 h-full w-full"}`, className)}
    >
      {withIcon && <LogOut className="w-4 h-4" />}
      Logout
    </Button>
  );
}
