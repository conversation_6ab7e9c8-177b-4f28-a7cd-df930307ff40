"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  acceptOrganizationInvitationAction,
  validateOrganizationInvitationTokenAction,
} from "@/db/actions/organization-invitations.action";
import { createClient } from "@/lib/supabase/client";
import { cn, getCurrentSubdomain, goToMainDomain, goToSubdomain } from "@/lib/utils";
import { UserProfileDB } from "@/types/user.types";
import { AlertCircle } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

type SignUpFormProps = React.ComponentPropsWithoutRef<"div"> & {
  invitationToken?: string;
  invitationEmail?: string;
};

export function SignUpForm({
  className,
  invitationToken,
  invitationEmail,
  ...props
}: SignUpFormProps) {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState(invitationEmail || "");
  const [password, setPassword] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [subdomain, setSubdomain] = useState<string | null>(null);
  const [isExistingUser, setIsExistingUser] = useState(false);
  const [organizationName, setOrganizationName] = useState<string | null>(null);
  const [originalFirstName, setOriginalFirstName] = useState("");
  const [originalLastName, setOriginalLastName] = useState("");
  const router = useRouter();

  // Detect subdomain on component mount
  useEffect(() => {
    const subdomain = getCurrentSubdomain(window.location.hostname);
    if (subdomain) {
      setSubdomain(subdomain);
    }
  }, []);

  const fetchInvitationData = useCallback(async () => {
    if (!invitationToken) return;

    try {
      // Validate invitation and get organization info
      const invitationResult = await validateOrganizationInvitationTokenAction(invitationToken);

      if (invitationResult.success && invitationResult.organization) {
        setOrganizationName(invitationResult.organization.name);
      }

      // Check if user already exists and get their profile data
      if (invitationEmail) {
        const supabase = createClient();

        // Check if user exists in auth.users by trying to get their profile
        const { data: profiles, error } = await supabase
          .from("profiles")
          .select("first_name, last_name, user_id")
          .eq("email", invitationEmail)
          .maybeSingle();

        if (!error && profiles) {
          // User exists - this is an existing user joining a new organization
          setIsExistingUser(true);
          setFirstName(profiles.first_name || "");
          setLastName(profiles.last_name || "");
          setOriginalFirstName(profiles.first_name || "");
          setOriginalLastName(profiles.last_name || "");
        }
      }
    } catch (error) {
      console.error("Error fetching invitation data:", error);
    }
  }, [invitationToken, invitationEmail]);

  // Fetch invitation and profile data when component mounts with invitation token
  useEffect(() => {
    if (invitationToken && invitationEmail) {
      fetchInvitationData();
    }
  }, [invitationToken, invitationEmail, fetchInvitationData]);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    const supabase = createClient();
    setIsLoading(true);
    setError(null);

    if (password !== repeatPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      // For invitation-based sign-ups, check if user already exists first
      if (invitationToken) {
        // Try to sign in first to see if this is an existing user
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (signInData?.user && !signInError) {
          // User exists and signed in successfully
          toast.info("Account detected - adding you to this organization...");

          // Check if user updated their profile information
          const profileChanged = firstName !== originalFirstName || lastName !== originalLastName;

          // Only update profile if it actually changed
          if (profileChanged) {
            const profileData: Partial<UserProfileDB> = {
              first_name: firstName,
              last_name: lastName,
              full_name: `${firstName} ${lastName}`.trim(),
              email: email,
            };

            try {
              const profileResponse = await fetch("/api/auth/create-profile", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  userId: signInData.user.id,
                  profileData,
                }),
              });

              if (profileResponse.ok) {
                toast.success("Profile updated successfully");
              }
            } catch (profileError) {
              console.error("Error updating profile:", profileError);
            }
          }

          try {
            const result = await acceptOrganizationInvitationAction(
              invitationToken,
              signInData.user.id
            );

            if (result.success) {
              toast.success("Successfully joined organization");
              router.push("/home");
              return;
            } else {
              toast.error(`Error joining organization: ${result.message}`);
              setIsLoading(false);
              return;
            }
          } catch (inviteError) {
            toast.error(
              `Error joining organization: ${
                inviteError instanceof Error ? inviteError.message : "Unknown error"
              }`
            );
            setIsLoading(false);
            return;
          }
        }

        // If sign-in failed (but not due to invalid credentials), it might be a new user
        // Invalid credentials error code is usually "invalid_login_credentials" or similar
        if (
          signInError &&
          !signInError.message.toLowerCase().includes("invalid") &&
          !signInError.message.toLowerCase().includes("password") &&
          !signInError.message.toLowerCase().includes("credentials")
        ) {
          // Some other error occurred, let's handle it
          console.error("Sign-in attempt failed:", signInError);
          toast.error("Unable to verify existing account. Please try again.");
          setIsLoading(false);
          return;
        }

        // If we get here, it means either:
        // 1. Invalid credentials (new user needs to sign up)
        // 2. User doesn't exist (new user needs to sign up)
        // Continue with sign-up flow below
      }

      // First, sign up the user
      // Build proper redirect URL using established utility functions
      const currentSubdomain = getCurrentSubdomain(window.location.hostname);

      // Build redirect URL - redirect to login on the appropriate domain
      let redirectUrl: string;
      if (currentSubdomain && invitationToken) {
        // If we're on a subdomain with an invitation, redirect to subdomain login
        redirectUrl = goToSubdomain(currentSubdomain, "auth/login");
      } else {
        // Otherwise redirect to main domain login
        redirectUrl = goToMainDomain("auth/login");
      }

      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: `${firstName} ${lastName}`.trim(),
            first_name: firstName,
            last_name: lastName,
          },
        },
      });

      if (signUpError) {
        // Check if it's a "user already exists" error
        if (
          signUpError.message.toLowerCase().includes("already") ||
          signUpError.message.toLowerCase().includes("exists")
        ) {
          toast.error("An account with this email already exists. Please sign in instead.");
          // Redirect to sign-in page with invitation token
          const signInUrl = invitationToken
            ? `/auth/login?invitation=${invitationToken}&email=${encodeURIComponent(email)}`
            : "/auth/login";
          router.push(signInUrl);
          return;
        }
        throw signUpError;
      }

      // Check if this is an existing user (identities array is empty for existing users)
      const isExistingUserCheck = data.user?.identities?.length === 0;

      // If sign-up was successful and a user was created, proceed with profile creation and organization joining
      if (data.user) {
        const profileData: Partial<UserProfileDB> = {
          first_name: firstName,
          last_name: lastName,
          full_name: `${firstName} ${lastName}`.trim(),
          email: email,
        };

        try {
          // User feedback based on whether they're new or existing
          if (isExistingUserCheck) {
            toast.info("Account detected - adding you to this organization...");
          } else {
            toast.info("Setting up your new account...");
          }

          // Store the information needed for operations
          const userId = data.user.id;

          // Proceed to success page - we'll handle the rest in the background
          router.push("/auth/sign-up-success");

          // Create or update the profile using our server API with upsert logic
          const profileResponse = await fetch("/api/auth/create-profile", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              userId,
              profileData,
            }),
          });

          if (!profileResponse.ok) {
            console.error(`Profile creation failed with status: ${profileResponse.status}`);
            const errorText = await profileResponse.text();
            toast.error(`Error creating profile: ${errorText || "Server error"}`);
            return;
          }

          const profileResult = await profileResponse.json();

          if (!profileResult.success) {
            console.error("Error creating profile:", profileResult.error);
            toast.error(`Error creating profile: ${profileResult.error}`);
            return;
          } else {
            toast.success("Profile created successfully");
          }

          // Handle organization joining based on invitation token or subdomain
          if (invitationToken) {
            try {
              const result = await acceptOrganizationInvitationAction(invitationToken, userId);

              if (result.success) {
                toast.success("Successfully joined organization");
              } else {
                toast.error(`Error joining organization: ${result.message}`);
              }
            } catch (inviteError) {
              toast.error(
                `Error joining organization: ${
                  inviteError instanceof Error ? inviteError.message : "Unknown error"
                }`
              );
            }
          } else if (subdomain) {
            // For subdomain-based sign-up
            const supabaseClient = createClient();

            // First find the organization
            const { data: organization, error: orgLookupError } = await supabaseClient
              .from("organizations")
              .select("id")
              .eq("subdomain_id", subdomain)
              .single();

            if (orgLookupError || !organization) {
              toast.error(
                `Error finding organization: ${orgLookupError?.message || "Organization not found"}`
              );
              return;
            }

            // Use our API endpoint to add the user to the organization
            const joinResponse = await fetch("/api/auth/join-organization", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                userId,
                organizationId: organization.id,
              }),
            });

            const joinResult = await joinResponse.json();

            if (!joinResult.success) {
              toast.error(`Error joining organization: ${joinResult.error}`);
            } else {
              toast.success("Successfully joined organization");
            }
          }
        } catch (profileError) {
          // Log the error but continue with the sign-up flow
          console.error("Error processing sign-up:", profileError);
          toast.error(
            `Error creating profile: ${
              profileError instanceof Error ? profileError.message : "Unknown error"
            }`
          );
        }
      }
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
      setIsLoading(false);
    }
  };

  // Determine the card title and description based on context
  const getCardTitle = () => {
    if (invitationToken && organizationName) {
      return isExistingUser
        ? `You are invited to join ${organizationName}`
        : `Welcome to ${organizationName}`;
    }
    return "Welcome to Renwu";
  };

  const getCardDescription = () => {
    if (invitationToken && organizationName) {
      if (isExistingUser) {
        return "Sign in with your password to join this organization.";
      } else {
        return "Complete your profile to join this organization.";
      }
    }
    return "Create your account to get started.";
  };

  const getButtonText = () => {
    if (invitationToken && organizationName) {
      return isLoading ? "Joining organization..." : `Join ${organizationName}`;
    }
    return isLoading ? "Creating account..." : "Sign up";
  };

  return (
    <div className={cn("flex flex-col gap-6 -mt-6", className)} {...props}>
      <Card>
        <CardHeader className="flex flex-col items-center justify-center gap-2">
          <div
            className="flex items-center justify-center border rounded-full size-11 shrink-0"
            aria-hidden="true"
          >
            <svg
              className="stroke-zinc-800 dark:stroke-zinc-100"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 32 32"
              aria-hidden="true"
            >
              <circle cx="16" cy="16" r="12" fill="none" strokeWidth="8" />
            </svg>
          </div>
          <CardTitle className="text-lg sm:text-center">{getCardTitle()}</CardTitle>
          <CardDescription className="sm:text-center">
            {getCardDescription()}
            {subdomain && !invitationToken && (
              <span className="block mt-1 text-sm">
                You&apos;ll be added to the <strong>{subdomain}</strong> organization upon sign up.
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Info banner for existing users */}
          {isExistingUser && invitationToken && (
            <div className="mb-6 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">Account Found</p>
                  <p>
                    We found your existing account. Your profile is shared across organizations -
                    any changes to your name will update your profile everywhere.
                  </p>
                </div>
              </div>
            </div>
          )}

          <form className="mt-6" onSubmit={handleSignUp}>
            <div className="flex flex-col gap-6">
              <div className="flex w-full gap-2">
                <div className="grid w-1/2 gap-2">
                  <Label htmlFor="first-name">First Name</Label>
                  <Input
                    id="first-name"
                    type="text"
                    placeholder="John"
                    required
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                  />
                </div>
                <div className="grid w-1/2 gap-2">
                  <Label htmlFor="last-name">Last Name</Label>
                  <Input
                    id="last-name"
                    type="text"
                    placeholder="Smith"
                    required
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={!!invitationEmail}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                </div>
                <Input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="repeat-password">Repeat Password</Label>
                </div>
                <Input
                  id="repeat-password"
                  type="password"
                  required
                  value={repeatPassword}
                  onChange={(e) => setRepeatPassword(e.target.value)}
                />
              </div>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <Button type="submit" className="w-full" disabled={isLoading}>
                {getButtonText()}
              </Button>
            </div>
            <div className="mt-4 text-sm text-center">
              Already have an account?{" "}
              <Link
                href={
                  invitationToken
                    ? `/auth/login?invitation=${invitationToken}&email=${encodeURIComponent(email)}`
                    : "/auth/login"
                }
                className="underline underline-offset-4"
              >
                Login
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
