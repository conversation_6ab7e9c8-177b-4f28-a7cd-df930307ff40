"use client";

import { useNavigationCacheProtection } from "@/hooks/use-navigation-cache-protection";
import Link, { type LinkProps } from "next/link";
import { forwardRef, type MouseEvent, type ReactNode } from "react";

interface ProtectedLinkProps extends Omit<LinkProps, "onClick"> {
  children: ReactNode;
  className?: string;
  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;
  // Add option to use programmatic navigation instead of Link
  useProgrammaticNavigation?: boolean;
}

/**
 * Enhanced Link component that protects React Query cache during navigation
 * Use this instead of regular Next.js Link for better cache preservation
 */
export const ProtectedLink = forwardRef<HTMLAnchorElement, ProtectedLinkProps>(
  ({ children, onClick, href, useProgrammaticNavigation = false, ...props }, ref) => {
    const { protectCacheBeforeNavigation, navigateWithCacheProtection } =
      useNavigationCacheProtection();

    const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
      if (useProgrammaticNavigation) {
        // Prevent default link behavior
        e.preventDefault();

        // Use programmatic navigation with cache protection
        navigateWithCacheProtection(href.toString());
      } else {
        // For regular Link behavior, just protect cache
        protectCacheBeforeNavigation();
      }

      // Call original onClick if provided
      onClick?.(e);
    };

    // If using programmatic navigation, render as button-styled anchor
    if (useProgrammaticNavigation) {
      return (
        <a
          ref={ref}
          href={href.toString()}
          onClick={handleClick}
          {...props}
          role="button"
          style={{ cursor: "pointer" }}
        >
          {children}
        </a>
      );
    }

    // Regular Link with cache protection
    return (
      <Link ref={ref} href={href} onClick={handleClick} {...props} prefetch>
        {children}
      </Link>
    );
  }
);

ProtectedLink.displayName = "ProtectedLink";
