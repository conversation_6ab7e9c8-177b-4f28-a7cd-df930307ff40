"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { type UserModel } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { User } from "lucide-react";

interface AssigneeDisplayProps {
  assignees: UserModel[];
  variant?: "card" | "row" | "compact";
  maxVisible?: number;
  showNames?: boolean;
  className?: string;
}

/**
 * Reusable component for displaying task assignees
 * Supports different variants for different contexts (card, row, compact)
 */
export function AssigneeDisplay({
  assignees,
  variant = "card",
  maxVisible = 3,
  showNames = false,
  className,
}: AssigneeDisplayProps) {
  // Handle empty assignees
  if (!assignees || assignees.length === 0) {
    return (
      <div className={cn("flex items-center", className)}>
        {variant === "row" ? (
          <span className="text-xs text-gray-400 dark:text-gray-500">Unassigned</span>
        ) : (
          <Avatar
            className={cn(
              variant === "compact" ? "size-5" : variant === "card" ? "size-6" : "size-7"
            )}
          >
            <AvatarFallback className="bg-gray-100 dark:bg-gray-800">
              <User
                className={cn(
                  "text-gray-400 dark:text-gray-500",
                  variant === "compact" ? "size-3" : variant === "card" ? "size-4" : "size-4"
                )}
              />
            </AvatarFallback>
          </Avatar>
        )}
      </div>
    );
  }

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Determine how many assignees to show
  const visibleAssignees = assignees.slice(0, maxVisible);
  const remainingCount = assignees.length - maxVisible;

  // Size classes based on variant
  const avatarSizeClass = cn(
    variant === "compact" ? "size-4" : variant === "card" ? "size-5" : "size-6"
  );

  return (
    <div className={cn("flex items-center", className)}>
      {variant === "row" && showNames ? (
        // Row variant with names
        <div className="flex items-center gap-1">
          {visibleAssignees.map((assignee, index) => (
            <div key={assignee.id || index} className="flex items-center gap-0.5">
              <Avatar className={cn(avatarSizeClass, "border-2 border-white dark:border-gray-800")}>
                {assignee.avatar && <AvatarImage src={assignee.avatar} alt={assignee.name} />}
                <AvatarFallback className="text-[8px] bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300">
                  {getInitials(assignee.name)}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs">{assignee.name}</span>
              {index < visibleAssignees.length - 1 && (
                <span className="text-sm text-gray-400">,</span>
              )}
            </div>
          ))}
          {remainingCount > 0 && (
            <Avatar className={cn(avatarSizeClass, "border-2 border-white dark:border-gray-800")}>
              <AvatarFallback className="text-[10px] bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                +{remainingCount}
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      ) : (
        // Card and compact variants (avatar only)
        <div className="flex -space-x-1">
          {visibleAssignees.map((assignee, index) => (
            <Avatar
              key={assignee.id || index}
              className={cn(
                avatarSizeClass,
                assignees.length > 1 && "border-2 border-white dark:border-gray-800"
              )}
            >
              {assignee.avatar && <AvatarImage src={assignee.avatar} alt={assignee.name} />}
              <AvatarFallback
                className={cn(
                  variant === "compact" ? "text-[8px]" : "text-[10px]",
                  "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300"
                )}
              >
                {getInitials(assignee.name)}
              </AvatarFallback>
            </Avatar>
          ))}
          {remainingCount > 0 && (
            <Avatar className={cn(avatarSizeClass, "border-2 border-white dark:border-gray-800")}>
              <AvatarFallback
                className={cn(
                  variant === "compact" ? "text-[8px]" : "text-[10px]",
                  "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                )}
              >
                +{remainingCount}
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      )}
    </div>
  );
}
