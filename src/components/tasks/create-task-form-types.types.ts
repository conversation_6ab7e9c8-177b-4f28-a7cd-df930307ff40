export interface FormValues {
  title: string;
  description?: string;
  status?: string;
  value?: string;
  effort?: number;
  dueDate?: Date | null;
  visibleToTeams: string[];
  tags?: { id: string; name: string }[];
  assignees?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
  }[];
}

export interface CurrentUser {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
}
