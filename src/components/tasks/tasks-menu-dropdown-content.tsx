import {
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { TaskModel } from "@/db/schemas/tasks.schema";
import { BoltIcon, CopyPlusIcon, FilesIcon, PenIcon, TrashIcon } from "lucide-react";

interface TaskMenuProps {
  task?: TaskModel;
  onEdit?: () => void;
  onCopy?: () => void;
  onRename?: () => void;
  onClone?: () => void;
  onDelete?: (task?: TaskModel) => void;
}

export default function TaskMenu({
  task,
  onEdit,
  onCopy,
  onRename,
  onClone,
  onDelete,
}: TaskMenuProps) {
  return (
    <DropdownMenuContent>
      <DropdownMenuGroup>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            onCopy?.();
          }}
          disabled
        >
          <CopyPlusIcon className="h-4 w-4 opacity-60" aria-hidden="true" />
          Copy
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            onRename?.();
          }}
          disabled
        >
          <PenIcon className="h-4 w-4 opacity-60" aria-hidden="true" />
          Rename
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.();
          }}
          disabled
        >
          <BoltIcon className="h-4 w-4 opacity-60" aria-hidden="true" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            onClone?.();
          }}
          disabled
        >
          <FilesIcon className="h-4 w-4 opacity-60" aria-hidden="true" />
          Clone
        </DropdownMenuItem>
        <DropdownMenuItem
          variant="destructive"
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.(task);
          }}
        >
          <TrashIcon className="h-4 w-4" aria-hidden="true" />
          <span className="dark:text-red-700">Delete</span>
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  );
}
