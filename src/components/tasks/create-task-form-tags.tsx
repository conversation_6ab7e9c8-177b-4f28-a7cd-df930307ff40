"use client";

import { FormDescription, FormField, FormLabel, FormMessage } from "@/components/ui/form";
import MultipleSelector, { type Option } from "@/components/ui/multiselect";
import { TagModel } from "@/db/schemas/tasks.schema";
import { useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "./create-task-form-types.types";

interface CreateTaskFormTagsProps {
  form: UseFormReturn<FormValues>;
  availableTags: TagModel[];
}

export function CreateTaskFormTags({ form, availableTags }: CreateTaskFormTagsProps) {
  // Convert TagModel[] to Option[] for MultipleSelector (replacing mock data with real data)
  const tagOptions: Option[] = useMemo(
    () =>
      availableTags.map((tag) => ({
        value: tag.id,
        label: tag.name,
      })),
    [availableTags]
  );

  return (
    <FormField
      control={form.control}
      name="tags"
      render={({ field }) => {
        // Convert current field value (TagModel[]) to Option[] for MultipleSelector
        const selectedOptions: Option[] = (field.value || []).map((tag: TagModel) => ({
          value: tag.id,
          label: tag.name,
        }));

        // Handle value change from MultipleSelector
        const handleValueChange = (options: Option[]) => {
          // Convert Option[] back to TagModel[] for the form
          const selectedTags: TagModel[] = options.map((option) => {
            const originalTag = availableTags.find((tag) => tag.id === option.value);
            return originalTag || { id: option.value, name: option.label };
          });
          field.onChange(selectedTags);
        };

        return (
          <div className="*:not-first:mt-2">
            <FormLabel>Tags</FormLabel>
            <MultipleSelector
              commandProps={{
                label: "Select tags",
              }}
              value={selectedOptions}
              defaultOptions={tagOptions}
              placeholder="Select tags"
              hideClearAllButton
              hidePlaceholderWhenSelected
              emptyIndicator={
                <p className="text-center text-sm">
                  {availableTags.length === 0
                    ? "No tags available for this project yet."
                    : "No results found"}
                </p>
              }
              onChange={handleValueChange}
            />
            <FormDescription className="text-xs">
              Select tags to categorize this task (optional)
            </FormDescription>
            <FormMessage />
          </div>
        );
      }}
    />
  );
}
