"use client";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "./create-task-form-types.types";

interface CreateTaskFormBasicInfoProps {
  form: UseFormReturn<FormValues>;
}

export function CreateTaskFormBasicInfo({ form }: CreateTaskFormBasicInfoProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Title <span className="text-destructive">*</span>
            </FormLabel>
            <FormControl>
              <Input placeholder="Enter task title" {...field} className="w-full" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe what needs to be done and any additional context..."
                {...field}
                className="min-h-[100px] resize-y"
              />
            </FormControl>
            <FormDescription className="text-xs">
              Be specific about requirements and expected outcomes
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
