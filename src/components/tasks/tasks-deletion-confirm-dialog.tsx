import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { type TaskModel } from "@/db/schemas/tasks.schema";

interface TaskDeletionConfirmDialogProps {
  task: TaskModel | null;
  isOpen: boolean;
  isDeleting?: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  variant?: "default" | "danger";
  size?: "sm" | "md" | "lg" | "xl";
}

export function TaskDeletionConfirmDialog({
  task,
  isOpen,
  isDeleting = false,
  onOpenChange,
  onConfirm,
  variant = "default",
  size = "md",
}: TaskDeletionConfirmDialogProps) {
  if (!task) return null;

  const handleDelete = () => {
    try {
      // Log that we're about to confirm deletion
      console.log("TaskDeletionConfirmDialog: Confirming deletion of task", task.id);

      // Call the onConfirm callback
      onConfirm();

      // Close the dialog if onConfirm doesn't do it
      if (isOpen) {
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error in task deletion confirmation:", error);
      // Ensure the dialog closes even if there was an error
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className={`max-w-${size === "sm" ? "sm" : size === "lg" ? "lg" : size === "xl" ? "xl" : "md"}`}
      >
        <DialogHeader>
          <DialogTitle>{variant === "danger" ? "Delete Task" : "Confirm Delete Task"}</DialogTitle>
          <DialogDescription>
            You are about to delete task{" "}
            <strong>
              {task.key}: {task.title}
            </strong>
            . This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className={variant === "danger" ? "bg-red-600 hover:bg-red-700" : ""}
            data-testid="delete-task-confirm-button"
          >
            {isDeleting ? "Deleting..." : "Delete Task"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default TaskDeletionConfirmDialog;
