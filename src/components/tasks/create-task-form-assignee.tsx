"use client";

import { useProjectAssignees } from "@/app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_hooks/use-crud-tasks";
import { AssigneeDisplay } from "@/components/tasks/tasks-assignee-display";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import MultipleSelector, { type Option } from "@/components/ui/multiselect";
import { UserModel } from "@/db/schemas/tasks.schema";
import { useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "./create-task-form-types.types";

interface CreateTaskFormAssigneeProps {
  form: UseFormReturn<FormValues>;
  projectId: string;
}

export function CreateTaskFormAssignee({ form, projectId }: CreateTaskFormAssigneeProps) {
  // Fetch available assignees for the project
  const assigneesQuery = useProjectAssignees(projectId, true);

  // Memoize available assignees to prevent unnecessary re-renders
  const availableAssignees = useMemo(() => assigneesQuery.data || [], [assigneesQuery.data]);

  // Convert available assignees to options for MultipleSelector
  const assigneeOptions: Option[] = useMemo(
    () =>
      availableAssignees.map((assignee) => ({
        value: assignee.id,
        label: assignee.name,
      })),
    [availableAssignees]
  );

  return (
    <FormField
      control={form.control}
      name="assignees"
      render={({ field }) => {
        // Convert UserModel[] to Option[] for MultipleSelector
        const selectedOptions: Option[] = (field.value || []).map((assignee: UserModel) => ({
          value: assignee.id,
          label: assignee.name,
        }));

        return (
          <FormItem>
            <FormLabel>Assignees</FormLabel>
            <FormDescription className="text-xs">
              Select who should be assigned to this task. You are assigned by default.
            </FormDescription>
            <FormControl>
              <MultipleSelector
                value={selectedOptions}
                defaultOptions={assigneeOptions}
                placeholder="Select assignees..."
                hideClearAllButton
                hidePlaceholderWhenSelected
                onChange={(options) => {
                  // Convert Option[] back to UserModel[]
                  const newAssignees: UserModel[] = options.map((option) => {
                    const existingAssignee = availableAssignees.find(
                      (assignee) => assignee.id === option.value
                    );
                    return (
                      existingAssignee || {
                        id: option.value,
                        name: option.label,
                      }
                    );
                  });
                  field.onChange(newAssignees);
                }}
                emptyIndicator={
                  <p className="text-center text-sm text-gray-500">
                    {availableAssignees.length === 0
                      ? "No assignees available for this project yet."
                      : "No results found"}
                  </p>
                }
                className="min-h-[2.5rem]"
                commandProps={{
                  label: "Select assignees",
                }}
              />
            </FormControl>

            {/* Show current assignees */}
            {field.value && field.value.length > 0 && (
              <div className="mt-2">
                <AssigneeDisplay assignees={field.value} variant="row" maxVisible={3} showNames />
              </div>
            )}

            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
