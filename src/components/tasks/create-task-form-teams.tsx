"use client";

import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "./create-task-form-types.types";

interface CreateTaskFormTeamsProps {
  form: UseFormReturn<FormValues>;
  projectTeams: { id: string; name: string }[];
  isLoadingTeams: boolean;
}

export function CreateTaskFormTeams({
  form,
  projectTeams,
  isLoadingTeams,
}: CreateTaskFormTeamsProps) {
  return (
    <FormField
      control={form.control}
      name="visibleToTeams"
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            Visible to Teams <span className="text-destructive">*</span>
          </FormLabel>
          <FormDescription className="text-xs">
            Select which teams can see and work on this task
          </FormDescription>
          <div className="space-y-2">
            {isLoadingTeams ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading teams...</span>
              </div>
            ) : (
              <>
                {projectTeams.length > 0 ? (
                  <div className="grid grid-cols-2 gap-2">
                    {projectTeams.map((team) => (
                      <FormItem
                        key={team.id}
                        className="flex items-center space-x-2 space-y-0 bg-background/50 rounded-md p-2 border"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(team.id)}
                            onCheckedChange={(checked) => {
                              const currentValue = field.value || [];
                              if (checked) {
                                field.onChange([...currentValue, team.id]);
                              } else {
                                field.onChange(currentValue.filter((id) => id !== team.id));
                              }
                            }}
                          />
                        </FormControl>
                        <FormLabel className="text-sm cursor-pointer">{team.name}</FormLabel>
                      </FormItem>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 border border-dashed rounded-md text-center">
                    <p className="text-sm text-muted-foreground">
                      No teams found for this project.
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
