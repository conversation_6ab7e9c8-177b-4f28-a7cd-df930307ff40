import TaskMenu from "@/components/tasks/tasks-menu-dropdown-content";
import { TaskModel } from "@/db/schemas/tasks.schema";
import { DropdownMenu, DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { useState } from "react";
import { Button } from "../ui/button";

interface TaskAdditionalMenuProps {
  children: React.ReactNode;
  task?: TaskModel;
  projectId?: string;
  onDelete?: (task?: TaskModel) => void;
  onEdit?: (task: TaskModel) => void;
  onCopy?: (task: TaskModel) => void;
  onRename?: (task: TaskModel) => void;
  onClone?: (task: TaskModel) => void;
}

export const TaskAdditionalMenu = ({
  children,
  task,
  onDelete,
  onEdit,
  onCopy,
  onRename,
  onClone,
}: TaskAdditionalMenuProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(true);
  };

  const handleDelete = (task?: TaskModel) => {
    if (onDelete) {
      onDelete(task);
    }
  };

  const handleEdit = () => {
    if (task && onEdit) {
      onEdit(task);
    }
  };

  const handleCopy = () => {
    if (task && onCopy) {
      onCopy(task);
    }
  };

  const handleRename = () => {
    if (task && onRename) {
      onRename(task);
    }
  };

  const handleClone = () => {
    if (task && onClone) {
      onClone(task);
    }
  };

  return (
    <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen} modal={true}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="cursor-pointer"
          onClick={handleMenuClick}
          data-no-propagation
        >
          {children}
        </Button>
      </DropdownMenuTrigger>
      <TaskMenu
        task={task}
        onDelete={handleDelete}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onRename={handleRename}
        onClone={handleClone}
      />
    </DropdownMenu>
  );
};
