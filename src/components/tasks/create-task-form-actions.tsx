"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Plus } from "lucide-react";

interface CreateTaskFormActionsProps {
  isSubmitting: boolean;
  onCancel?: () => void;
  onSubmit: () => void;
}

export function CreateTaskFormActions({
  isSubmitting,
  onCancel,
  onSubmit,
}: CreateTaskFormActionsProps) {
  return (
    <div className="flex justify-end gap-3 pt-4">
      <Button type="button" variant="outline" onClick={() => onCancel?.()}>
        Cancel
      </Button>
      <Button
        type="submit"
        onClick={onSubmit}
        disabled={isSubmitting}
        className="flex items-center gap-2"
      >
        {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
        Create Task
      </Button>
    </div>
  );
}
