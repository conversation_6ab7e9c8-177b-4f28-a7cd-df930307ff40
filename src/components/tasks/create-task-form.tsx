"use client";

import { CreateTaskInput } from "@/app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_actions/tasks.actions";
import {
  useCreateTask,
  useCurrentUser,
  useTags,
} from "@/app/clusters/[cluster-slug]/projects/[project-slug]/tasks/_hooks/use-crud-tasks";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { getTeamsByProjectAction } from "@/db/actions/team-projects.action";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { CreateTaskFormActions } from "./create-task-form-actions";
import { CreateTaskFormAssignee } from "./create-task-form-assignee";
import { CreateTaskFormBasicInfo } from "./create-task-form-basic-info";
import { CreateTaskFormProperties } from "./create-task-form-properties";
import { CreateTaskFormTags } from "./create-task-form-tags";
import { CreateTaskFormTeams } from "./create-task-form-teams";
import { FormValues } from "./create-task-form-types.types";

// Define a more comprehensive form schema
const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  status: z.string().optional(),
  value: z.string().optional(),
  effort: z.coerce.number().min(0).optional(),
  dueDate: z.date().optional().nullable(),
  visibleToTeams: z.array(z.string()).min(1, "At least one team must be selected"),
  tags: z.array(z.object({ id: z.string(), name: z.string() })).optional(),
  assignees: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        email: z.string().optional(),
        avatar: z.string().optional(),
      })
    )
    .optional(),
});

interface CreateTaskFormProps {
  projectId: string;
  clusterId: string;
  tenantId: string;
  teams?: { id: string; name: string }[];
  onSuccess?: () => void;
}

export default function CreateTaskForm({
  projectId,
  tenantId,
  teams = [],
  onSuccess,
}: CreateTaskFormProps) {
  const createTaskMutation = useCreateTask();
  const [projectTeams, setProjectTeams] = useState<{ id: string; name: string }[]>(teams);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);
  const [hasSubmitError, setHasSubmitError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Use React Query hooks to fetch data - but don't block the form
  const tagsQuery = useTags(projectId, true);
  const currentUserQuery = useCurrentUser(true);

  // Extract data from queries
  const availableTags = tagsQuery.data || [];
  const currentUser = currentUserQuery.data;

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      status: "Backlog",
      value: "Normal",
      effort: 0,
      dueDate: null,
      visibleToTeams: teams.length === 1 ? [teams[0].id] : [],
      tags: [],
      assignees: currentUser ? [currentUser] : [],
    },
  });

  // Load teams if not provided (but don't block the form)
  useEffect(() => {
    if (teams.length === 0) {
      const loadTeams = async () => {
        setIsLoadingTeams(true);
        try {
          const result = await getTeamsByProjectAction(projectId);
          if (result.success && result.data) {
            const teamsData = result.data.map((team) => ({ id: team.id, name: team.name }));
            setProjectTeams(teamsData);
            if (teamsData.length === 1) {
              form.setValue("visibleToTeams", [teamsData[0].id]);
            }
          } else {
            toast.error(result.message || "Failed to load teams for this project");
          }
        } catch (error) {
          console.error("Error loading teams:", error);
          toast.error("Failed to load teams for this project");
        } finally {
          setIsLoadingTeams(false);
        }
      };

      loadTeams();
    }
  }, [projectId, teams, form]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setHasSubmitError(false);
      setErrorMessage("");

      // Validate visible teams - required field
      if (!values.visibleToTeams || values.visibleToTeams.length === 0) {
        toast.error("At least one team must be selected");
        setHasSubmitError(true);
        setErrorMessage("At least one team must be selected");
        return;
      }

      // Ensure tags array is properly formatted (tags are now optional)
      const formattedTags = (values.tags || []).map((tag) => ({
        id: tag.id,
        name: tag.name,
      }));

      // Format assignees for the task input
      const formattedAssignees = (values.assignees || []).map((assignee) => ({
        id: assignee.id,
        name: assignee.name,
        avatar: assignee.avatar,
      }));

      // Create the task input object with all required fields
      const taskInput: CreateTaskInput = {
        title: values.title,
        description: values.description,
        status: values.status || "Backlog",
        value: values.value || "Normal",
        effort: values.effort ?? 0,
        dueDate: values.dueDate ? values.dueDate.toISOString() : null,
        visibleToTeams: Array.isArray(values.visibleToTeams) ? values.visibleToTeams : [],
        tags: formattedTags,
        assignees: formattedAssignees,
        project_id: projectId,
        tenant_id: tenantId,
      };

      const loadingToastId = toast.loading("Creating task...");
      const result = await createTaskMutation.mutateAsync(taskInput);

      if (result.error) {
        toast.dismiss(loadingToastId);
        toast.error(`Failed to create task: ${result.error.message}`);
        setHasSubmitError(true);
        setErrorMessage(result.error.message);
        return;
      }

      toast.dismiss(loadingToastId);

      // Reset form and state
      form.reset({
        title: "",
        description: "",
        status: "Backlog",
        value: "Normal",
        effort: 0,
        dueDate: null,
        visibleToTeams: teams.length === 1 ? [teams[0].id] : [],
        tags: [],
        assignees: currentUser ? [currentUser] : [],
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: unknown) {
      console.error("Error creating task:", error);
      toast.error(
        `Error creating task: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      setHasSubmitError(true);
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to create task. Please try again."
      );
    }
  };

  return (
    <div className="w-full mx-auto p-4">
      <h3 className="text-xl font-semibold mb-4">Create New Task</h3>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {hasSubmitError && (
            <div className="p-3 mb-4 border border-destructive/50 bg-destructive/10 rounded-md">
              <p className="text-sm font-medium text-destructive">
                {errorMessage || "An error occurred. Please try again."}
              </p>
            </div>
          )}

          {/* Basic Information */}
          <CreateTaskFormBasicInfo form={form} />

          <Separator />

          {/* Task Properties */}
          <CreateTaskFormProperties form={form} />

          <Separator />

          {/* Tags, Assignees, and Teams */}
          <div className="space-y-6">
            <CreateTaskFormTags form={form} availableTags={availableTags} />
            <CreateTaskFormAssignee form={form} projectId={projectId} />
            <CreateTaskFormTeams
              form={form}
              projectTeams={projectTeams}
              isLoadingTeams={isLoadingTeams}
            />
          </div>
        </form>
      </Form>

      <CreateTaskFormActions
        isSubmitting={form.formState.isSubmitting}
        onCancel={onSuccess}
        onSubmit={form.handleSubmit(onSubmit)}
      />
    </div>
  );
}
