import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LifeCycleStatus } from "@/db/schemas/tasks.schema";
import { cn } from "@/lib/utils";
import { Check, ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// Life cycle status badge component
export function TaskLifeCycleStatusBadge(props: {
  taskId: string;
  initialStatus?: LifeCycleStatus;
  onStatusChange?: (status: LifeCycleStatus) => void;
}) {
  const [currentStatus, setCurrentStatus] = useState<LifeCycleStatus>(
    props.initialStatus || "created"
  );

  // Define all lifecycle statuses
  const allStatuses: LifeCycleStatus[] = [
    "created",
    "ready",
    "started",
    "dev done",
    "test done",
    "deploy ready",
    "delivered",
  ];

  // Update current status when initialStatus changes
  useEffect(() => {
    if (props.initialStatus) {
      setCurrentStatus(props.initialStatus);
    }
  }, [props.initialStatus]);

  const variants: Record<
    LifeCycleStatus,
    { statusIndicator: string; border: string; text: string; bg: string }
  > = {
    created: {
      statusIndicator: "bg-gray-400 dark:bg-gray-500",
      border: "border-gray-200 dark:border-gray-700",
      text: "text-gray-700 dark:text-gray-400",
      bg: "bg-gray-50 dark:bg-gray-950",
    },
    ready: {
      statusIndicator: "bg-sky-400  dark:bg-sky-500",
      border: "border-sky-200 dark:border-sky-700",
      text: "text-sky-700 dark:text-sky-400",
      bg: "bg-sky-50 dark:bg-sky-950",
    },
    started: {
      statusIndicator: "bg-amber-400  dark:bg-amber-500",
      border: "border-amber-200 dark:border-amber-700",
      text: "text-amber-700 dark:text-amber-400",
      bg: "bg-amber-50 dark:bg-amber-950",
    },
    "dev done": {
      statusIndicator: "bg-green-400  dark:bg-green-500",
      border: "border-green-200 dark:border-green-700",
      text: "text-green-700 dark:text-green-400",
      bg: "bg-green-50 dark:bg-green-950",
    },
    "test done": {
      statusIndicator: "bg-purple-400  dark:bg-purple-500",
      border: "border-purple-200 dark:border-purple-700",
      text: "text-purple-700 dark:text-purple-400",
      bg: "bg-purple-50 dark:bg-purple-950",
    },
    "deploy ready": {
      statusIndicator: "bg-indigo-400  dark:bg-indigo-500",
      border: "border-indigo-200 dark:border-indigo-700",
      text: "text-indigo-700 dark:text-indigo-400",
      bg: "bg-indigo-50 dark:bg-indigo-950",
    },
    delivered: {
      statusIndicator: "bg-emerald-400  dark:bg-emerald-500",
      border: "border-emerald-200 dark:border-emerald-700",
      text: "text-emerald-700 dark:text-emerald-400",
      bg: "bg-emerald-50 dark:bg-emerald-950",
    },
  };

  const handleStatusChange = (status: LifeCycleStatus) => {
    setCurrentStatus(status);
    if (props.onStatusChange) {
      props.onStatusChange(status);
    }
    toast.info(`Task ${props.taskId} status changed to ${status}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center cursor-pointer group">
          <Badge
            variant="outline"
            className={cn(
              "font-normal text-xs py-0.5 px-2 group-hover:pr-1",
              variants[currentStatus].border,
              variants[currentStatus].text,
              variants[currentStatus].bg
            )}
          >
            {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
            <ChevronDown className="hidden w-3 h-3 transition-all duration-200 scale-0 group-hover:block group-hover:scale-100" />
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-[140px]">
        {allStatuses.map((status) => (
          <DropdownMenuItem
            key={status}
            className={cn(
              "flex items-center justify-between text-xs",
              status === currentStatus ? "bg-gray-50 dark:bg-gray-800/60" : ""
            )}
            onClick={(e) => {
              e.stopPropagation();
              handleStatusChange(status);
            }}
          >
            <div className="flex items-center">
              <span
                className={cn(
                  "w-2 h-2 rounded-full mr-2",
                  status === "created" && "bg-gray-400 dark:bg-gray-500",
                  status === "ready" && "bg-sky-400 dark:bg-sky-500",
                  status === "started" && "bg-amber-400 dark:bg-amber-500",
                  status === "dev done" && "bg-green-400 dark:bg-green-500",
                  status === "test done" && "bg-purple-400 dark:bg-purple-500",
                  status === "deploy ready" && "bg-indigo-400 dark:bg-indigo-500",
                  status === "delivered" && "bg-emerald-400 dark:bg-emerald-500"
                )}
              />
              <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            {status === currentStatus && (
              <Check className="w-3 h-3 text-gray-500 dark:text-gray-400" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
