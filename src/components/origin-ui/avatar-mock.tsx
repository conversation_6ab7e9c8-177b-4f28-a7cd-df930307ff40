"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

function generateRandomSeed(length = 8) {
  const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
  return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join("");
}

export default function AvatarMock({
  fallback,
  className,
}: {
  fallback?: string;
  className?: string;
}) {
  const [seed, setSeed] = useState<string | null>(null);

  useEffect(() => {
    const randomSeed = generateRandomSeed();
    setSeed(randomSeed);
  }, []);

  const avatarUrl = seed ? `https://api.dicebear.com/7.x/identicon/svg?seed=${seed}` : undefined;

  return (
    <Avatar className={cn("w-6 h-6", className)}>
      <AvatarImage src={avatarUrl} alt="Random Avatar" />
      <AvatarFallback>{fallback || "TM"}</AvatarFallback>
    </Avatar>
  );
}
