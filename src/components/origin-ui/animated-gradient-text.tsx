import { cn } from "@/lib/utils";
import { <PERSON>rk<PERSON> } from "lucide-react";

export function AnimatedGradientText({
  label,
  className,
  onClick,
}: {
  label: string;
  className?: string;
  onClick?: () => void;
}) {
  return (
    <div
      className={cn(
        "group relative flex max-w-full flex-row items-center justify-between rounded-md bg-white/40 px-2 py-1.5 text-sm font-medium shadow-[inset_0_-5px_10px_#8fdfff1f] backdrop-blur-sm transition-shadow duration-500 ease-out hover:shadow-[inset_0_-5px_10px_#8fdfff3f] dark:bg-black/40 cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <div className="absolute inset-0 block h-full w-full rounded-md bg-gradient-to-r from-[#ffaa40]/50 via-[#9c40ff]/50 to-[#ffaa40]/50 p-[1px] gradient-animate ![mask-composite:subtract] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)]" />
      <div className="flex flex-row items-center justify-center">
        <Sparkles className="h-3.5 w-3.5 text-[#f39829] icon-pulse ml-1" />
        <hr className="mx-2 h-5 w-px shrink-0 bg-gradient-to-b from-gray-300/10 via-gray-300/90 to-gray-300/10" />
        <span className="inline bg-gradient-to-r from-[#f39829] via-[#9c40ff] to-[#ffaa40] gradient-animate bg-clip-text text-transparent">
          {label}
        </span>
      </div>
      <kbd className="ml-4 max-h-full inline-flex h-4 py-2.5 items-center rounded border bg-background/80 px-1.5 font-medium text-tiny text-muted-foreground/80">
        ⌘K
      </kbd>
    </div>
  );
}
