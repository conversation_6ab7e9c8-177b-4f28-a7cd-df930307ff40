"use client";

import { useId, useState } from "react";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export default function RadioSwitch({
  value1,
  value2,
  icon1,
  icon2,
  defaultValue = "off",
  onChange,
}: {
  value1: string;
  value2: string;
  icon1?: React.ReactNode;
  icon2?: React.ReactNode;
  defaultValue?: "off" | "on";
  onChange?: (value: "off" | "on") => void;
}) {
  const id = useId();
  const [selectedValue, setSelectedValue] = useState<"off" | "on">(defaultValue);

  const handleValueChange = (value: string) => {
    const newValue = value as "off" | "on";
    setSelectedValue(newValue);
    onChange?.(newValue);
  };

  return (
    <div className="bg-input/50 inline-flex h-8 rounded-md p-0.5">
      <RadioGroup
        value={selectedValue}
        onValueChange={handleValueChange}
        className="group after:bg-background has-focus-visible:after:border-ring has-focus-visible:after:ring-ring/50 relative inline-grid grid-cols-[1fr_1fr] items-center gap-0 text-sm font-medium after:absolute after:inset-y-0 after:w-1/2 after:rounded-sm after:shadow-xs after:transition-[translate,box-shadow] after:duration-300 after:ease-[cubic-bezier(0.16,1,0.3,1)] has-focus-visible:after:ring-[3px] data-[state=off]:after:translate-x-0 data-[state=on]:after:translate-x-full"
        data-state={selectedValue}
      >
        <label className="group-data-[state=on]:text-muted-foreground/70 relative z-10 inline-flex h-full min-w-8 cursor-pointer items-center justify-center px-2 whitespace-nowrap transition-colors select-none">
          <span className="flex items-center gap-2">
            {icon1 && icon1}
            <span className="hidden md:block">{value1}</span>
          </span>
          <RadioGroupItem id={`${id}-1`} value="off" className="sr-only" />
        </label>
        <label className="group-data-[state=off]:text-muted-foreground/70 relative z-10 inline-flex h-full min-w-8 cursor-pointer items-center justify-center px-2 whitespace-nowrap transition-colors select-none">
          <span className="flex items-center gap-2">
            <span className="px-2 xl:px-0">{icon2 && icon2}</span>
            <span className="hidden md:block">{value2}</span>
          </span>
          <RadioGroupItem id={`${id}-2`} value="on" className="sr-only" />
        </label>
      </RadioGroup>
    </div>
  );
}
