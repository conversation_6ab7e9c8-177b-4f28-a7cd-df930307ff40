"use client";

import { Input } from "@/components/ui/input";
import { LoaderCircleIcon, MicIcon, SearchIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface SearchInputWithVoiceProps {
  id: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  className?: string;
}

export function SearchInputWithVoice({
  id,
  placeholder = "Search...",
  onChange,
  className,
}: SearchInputWithVoiceProps) {
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (inputValue) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
    setIsLoading(false);
  }, [inputValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    onChange?.(value);
  };

  return (
    <div className="relative">
      <Input
        id={id}
        className={`peer ps-9 pe-9 bg-white dark:bg-[#1e1e1e] ${className ?? ""}`}
        placeholder={placeholder}
        type="search"
        value={inputValue}
        onChange={handleChange}
      />
      <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
        {isLoading ? (
          <LoaderCircleIcon
            className="animate-spin"
            size={16}
            role="status"
            aria-label="Loading..."
          />
        ) : (
          <SearchIcon size={16} aria-hidden="true" />
        )}
      </div>
      <button
        className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
        aria-label="Press to speak"
        type="submit"
      >
        <MicIcon size={16} aria-hidden="true" />
      </button>
    </div>
  );
}
