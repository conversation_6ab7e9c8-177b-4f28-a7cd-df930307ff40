import { TooltipContent, TooltipTrigger } from "@radix-ui/react-tooltip";

import { Tooltip } from "@radix-ui/react-tooltip";

import { TooltipProvider } from "@radix-ui/react-tooltip";
import AvatarMock from "./avatar-mock";

export default function AvatarsGroupMock() {
  // Generate some mock members for the avatar stack
  const mockMembers = Array(7).fill(null);
  const displayCount = 5; // Number of avatars to show before the +N
  const remainingCount = mockMembers.length - displayCount;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center">
            <div className="flex items-center -space-x-2">
              {mockMembers.slice(0, displayCount).map((_, idx) => (
                <AvatarMock
                  key={idx}
                  className="ring-2 ring-background border border-foreground/20 bg-muted"
                />
              ))}
              {remainingCount > 0 && (
                <div className="flex items-center justify-center w-6.5 h-6.5 rounded-full bg-muted text-muted-foreground text-xs font-medium border border-foreground/20 ring-2 ring-background z-10">
                  +{remainingCount}
                </div>
              )}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-sm">Team Members ({mockMembers.length})</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
