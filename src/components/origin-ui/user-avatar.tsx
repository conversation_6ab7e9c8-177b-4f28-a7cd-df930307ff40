"use client";

import { cacheConfigs, commonQueryOptions } from "@/lib/query-keys";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

const UserAvatar = () => {
  const [isMounted, setIsMounted] = useState(false);

  // Use React Query instead of direct database calls
  const { data: user, isLoading: loading } = useQuery({
    queryKey: ["user", "profile"],
    queryFn: async () => {
      const { getUserProfileAction } = await import("@/db/actions/user-profile.action");
      try {
        return await getUserProfileAction();
      } catch (error) {
        console.info("Error fetching user profile:", error);
        return null;
      }
    },
    ...cacheConfigs.user,
    ...commonQueryOptions,
    enabled: isMounted, // Only fetch after mounting to prevent hydration issues
  });

  // Ensure component is mounted before showing dynamic content
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Use a simple fallback for empty names
  const getInitials = () => {
    if (!user) return "";
    const firstInitial = user.name?.[0] || "";
    const lastInitial = user.name?.split(" ")?.[1]?.[0] || "";
    return firstInitial + lastInitial || "U"; // Default to "U" if no initials
  };

  // Show consistent fallback during SSR and initial client render
  if (!isMounted) {
    return (
      <Avatar className="w-8 h-8">
        <AvatarFallback className="bg-gray-200 dark:bg-gray-700">
          <span className="sr-only">Loading user avatar</span>
        </AvatarFallback>
      </Avatar>
    );
  }

  return (
    <Avatar className="w-8 h-8">
      {loading ? (
        <AvatarFallback className="animate-pulse bg-gray-200 dark:bg-gray-700">
          <span className="sr-only">Loading user avatar</span>
        </AvatarFallback>
      ) : (
        <>
          <AvatarImage src={user?.avatar} alt="User" />
          <AvatarFallback>{getInitials()}</AvatarFallback>
        </>
      )}
    </Avatar>
  );
};

export default UserAvatar;
