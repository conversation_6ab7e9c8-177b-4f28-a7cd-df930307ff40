import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ChartLine,
  MessageCircleMoreIcon,
  PanelsTopLeftIcon,
} from "lucide-react";
import CheckboxSimple from "./checkbox-simple";
import TimelineActivity from "./timeline-activity";
import TimelineMessage from "./timeline-message";
import { Button } from "../ui/button";

export default function MenuTabs1() {
  return (
    <Tabs defaultValue="tab-1">
      <ScrollArea>
        <TabsList className="text-foreground mb-3 h-auto gap-2 rounded-none border-b bg-transparent px-0 py-1">
          {/* Comments */}
          <TabsTrigger
            value="tab-1"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            <MessageCircleMoreIcon
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Comments
          </TabsTrigger>

          {/* Subtasks */}
          <TabsTrigger
            value="tab-2"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            <PanelsTopLeftIcon
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Subtasks
            <Badge
              className="bg-primary/15 ms-1.5 min-w-5 px-1"
              variant="secondary"
            >
              5
            </Badge>
          </TabsTrigger>

          {/* Activity */}
          <TabsTrigger
            value="tab-3"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            <ChartLine
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Activity
          </TabsTrigger>
        </TabsList>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Tabs content - Comments */}
      <TabsContent value="tab-1">
        <TimelineMessage />
      </TabsContent>
      {/* Tabs content - Subtasks */}
      <TabsContent value="tab-2">
        <div className="pb-4 text-muted-foreground text-xs font-medium">
          Subtasks
        </div>

        <div className="flex flex-col gap-2">
          <CheckboxSimple label="Review code architecture" />
          <CheckboxSimple label="Update API documentation" />
          <CheckboxSimple label="Test edge cases" />
          <CheckboxSimple label="Optimize database queries" />
          <CheckboxSimple label="Write unit tests" />
        </div>
        <Button variant="outline" className="mt-6">
          Add subtask
        </Button>
      </TabsContent>

      {/* Tabs content - Activity */}
      <TabsContent value="tab-3">
        <TimelineActivity />
      </TabsContent>
    </Tabs>
  );
}
