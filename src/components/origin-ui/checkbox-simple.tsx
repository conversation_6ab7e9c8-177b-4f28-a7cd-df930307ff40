import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useId } from "react";

export default function CheckboxSimple(item: {
  label: string;
  checked?: boolean;
}) {
  const id = useId();
  return (
    <div className="flex items-center gap-2">
      <Checkbox id={id} defaultChecked={item.checked} />
      <Label htmlFor={id} className="peer-data-[state=checked]:line-through">
        {item.label}
      </Label>
    </div>
  );
}
