import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from "@/components/ui/timeline";
import AvatarMock from "./avatar-mock";

const items = [
  {
    id: 1,
    date: "15 minutes ago",
    title: "<PERSON>",
    action: "opened a new issue",
    description: "I'm having trouble with the new component library. It's not rendering properly.",
    image: AvatarMock,
  },
  {
    id: 2,
    date: "10 minutes ago",
    title: "<PERSON>",
    action: "commented on",
    description:
      "Hey <PERSON>, I'm having trouble with the new component library. It's not rendering properly.",
    image: AvatarMock,
  },
  {
    id: 3,
    date: "5 minutes ago",
    title: "<PERSON>",
    action: "assigned you to",
    description: "The new component library is not rendering properly. Can you take a look?",
    image: AvatarMock,
  },
  {
    id: 4,
    date: "2 minutes ago",
    title: "<PERSON>",
    action: "closed the issue",
    description: "The issue has been fixed. Please review the changes.",
    image: AvatarMock,
  },
];

export default function TimelineMessage() {
  return (
    <Timeline>
      <div className="pb-4 text-muted-foreground text-xs font-medium">Comments</div>

      {items.map((item) => (
        <TimelineItem
          key={item.id}
          step={item.id}
          className="group-data-[orientation=vertical]/timeline:ms-10 group-data-[orientation=vertical]/timeline:not-last:pb-8"
        >
          <TimelineHeader>
            <TimelineSeparator className="group-data-[orientation=vertical]/timeline:-left-7 group-data-[orientation=vertical]/timeline:h-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=vertical]/timeline:translate-y-6.5" />
            <TimelineTitle className="mt-0.5">
              {item.title}{" "}
              <span className="text-muted-foreground text-sm font-normal">{item.action}</span>
            </TimelineTitle>
            <TimelineIndicator className="bg-primary/10 group-data-completed/timeline-item:bg-primary group-data-completed/timeline-item:text-primary-foreground flex size-6 items-center justify-center border-none group-data-[orientation=vertical]/timeline:-left-7">
              <AvatarMock />
            </TimelineIndicator>
          </TimelineHeader>
          <TimelineContent className="text-foreground mt-2 rounded-lg border px-4 py-3">
            {item.description}
            <TimelineDate className="mt-1 mb-0">{item.date}</TimelineDate>
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  );
}
