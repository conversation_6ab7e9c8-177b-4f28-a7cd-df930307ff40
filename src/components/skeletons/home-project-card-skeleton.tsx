import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function ProjectCardSkeleton() {
  return (
    <Card className="flex flex-col w-80 h-full flex-shrink-0">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <div className="flex gap-1">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
        <Skeleton className="h-4 w-48 mt-2" />
      </CardHeader>
      <CardContent className="flex-1 flex flex-col">
        <div className="flex items-center mb-4">
          <div className="h-2 w-2 mr-2 bg-gray-50 rounded-full" />
          <Skeleton className="h-4 w-20" />
          <div className="ml-auto flex items-center">
            <Skeleton className="h-4 w-4 mr-1" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
        <div className="mt-2 mb-4">
          <div className="flex justify-between text-sm">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-8" />
          </div>
          <Skeleton className="h-2 mt-1" />
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="flex -space-x-2">
            <Skeleton className="h-8 w-8 rounded-full border-2 border-background" />
            <Skeleton className="h-8 w-8 rounded-full border-2 border-background" />
            <Skeleton className="h-8 w-8 rounded-full border-2 border-background" />
          </div>
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="mt-6">
          <Skeleton className="h-10 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
