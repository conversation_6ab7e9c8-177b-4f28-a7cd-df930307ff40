import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function UserProfileSkeleton() {
  return (
    <Card className="col-span-1 p-6 flex flex-col h-full w-full shadow-xs">
      <div className="flex flex-col gap-6 flex-1 justify-between">
        <div className="flex flex-col gap-6">
          <div className="flex items-start gap-6">
            <Skeleton className="h-24 w-24 rounded-full flex-shrink-0" />

            <div className="flex flex-col flex-1 min-w-0">
              <div className="flex flex-row justify-between items-center w-full mb-2">
                <Skeleton className="h-7 w-40" />
                <Skeleton className="h-5 w-24 flex-shrink-0" />
              </div>

              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-20" />
                <span className="text-muted-foreground">•</span>
                <Skeleton className="h-5 w-24" />
              </div>

              <div className="flex flex-col gap-2 mt-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-4 flex-shrink-0 text-muted-foreground/50" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-4 flex-shrink-0 text-muted-foreground/50" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-4 flex-shrink-0 text-muted-foreground/50" />
                  <Skeleton className="h-4 w-40" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-8 border rounded-lg p-4">
          <div className="flex flex-col items-center justify-center">
            <Skeleton className="h-7 w-8 mb-1" />
            <Skeleton className="h-4 w-16 mt-1" />
          </div>
          <div className="flex flex-col items-center justify-center border-x">
            <Skeleton className="h-7 w-8 mb-1" />
            <Skeleton className="h-4 w-16 mt-1" />
          </div>
          <div className="flex flex-col items-center justify-center">
            <Skeleton className="h-7 w-8 mb-1" />
            <Skeleton className="h-4 w-16 mt-1" />
          </div>
        </div>
      </div>
    </Card>
  );
}
