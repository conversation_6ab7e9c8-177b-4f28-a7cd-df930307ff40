import { Skeleton } from "../ui/skeleton";

/**
 * A skeleton loading component for the Kanban view
 * Displays a placeholder version of the Kanban board with columns and task cards
 */
export function KanbanViewSkeleton() {
  // Create 4 columns to match the typical Kanban board layout
  return (
    <div className="flex flex-col md:flex-row gap-4 md:gap-6 justify-evenly overflow-x-auto">
      {Array.from({ length: 4 }).map((_, i) => (
        <div
          key={i}
          className="rounded-lg border shadow-2xs w-full flex flex-col overflow-hidden h-[calc(100vh-16rem)]"
        >
          {/* Column header */}
          <div className="py-3 px-4 flex items-center justify-between border-b bg-gray-50/80 dark:bg-gray-800/30 flex-shrink-0">
            <div className="flex items-center gap-2">
              <Skeleton className="h-3.5 w-3.5 rounded-full" />
              <Skeleton className="h-5 w-24" />
            </div>
            <Skeleton className="h-5 w-8 rounded-full" />
          </div>

          {/* Column content */}
          <div className="flex-1 p-3 overflow-y-auto bg-gray-50/20 dark:bg-background md:p-4">
            <div className="space-y-2">
              {/* Generate 1-3 card skeletons per column */}
              {Array.from({ length: 1 }).map((_, j) => (
                <div
                  key={j}
                  className="border rounded-lg p-2 shadow-none bg-white dark:bg-gray-900/40"
                >
                  {/* Card header with tags */}
                  <div className="flex justify-between mb-2">
                    <div className="flex gap-1">
                      <Skeleton className="h-5 w-12 rounded-full" />
                      <Skeleton className="h-5 w-14 rounded-full" />
                    </div>
                    <Skeleton className="h-5 w-6" />
                  </div>

                  {/* Card title */}
                  <Skeleton className="h-5 w-full mb-3" />
                  <Skeleton className="h-5 w-4/5 mb-4" />

                  {/* Card footer */}
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-4 w-16 rounded-full" />
                    </div>
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
