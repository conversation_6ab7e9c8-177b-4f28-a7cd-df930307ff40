import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bar<PERSON>eader,
  <PERSON>bar<PERSON><PERSON>,
} from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";

export function AppSidebarSkeleton() {
  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="flex items-center justify-between transition-all duration-300">
        {/* Project Switcher Skeleton */}
        <div className="flex items-center gap-2 w-full px-2">
          <Skeleton className="w-8 h-8 rounded-md" />
          <div className="flex flex-col gap-1 flex-1">
            <Skeleton className="w-24 h-4" />
            <Skeleton className="w-32 h-3" />
          </div>
          <Skeleton className="w-4 h-4" />
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* AI Command Search Skeleton */}
        <div className="px-2 mb-4">
          <Skeleton className="w-full h-10 rounded-md" />
        </div>

        <div className="ml-1 space-y-6">
          {/* Overview Section */}
          <div className="space-y-2">
            <Skeleton className="w-20 h-4 ml-2" />
            <div className="space-y-1">
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>

          {/* Create & Prioritize Section */}
          <div className="space-y-2">
            <Skeleton className="w-32 h-4 ml-2" />
            <div className="space-y-1">
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>

          {/* Track & Deploy Section */}
          <div className="space-y-2">
            <Skeleton className="w-28 h-4 ml-2" />
            <div className="space-y-1">
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>

          {/* Ceremonies Section */}
          <div className="space-y-2">
            <Skeleton className="w-24 h-4 ml-2" />
            <div className="space-y-1">
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>

          {/* Analytics & Reports Section */}
          <div className="space-y-2">
            <Skeleton className="w-36 h-4 ml-2" />
            <div className="space-y-1">
              <Skeleton className="w-full h-8 rounded-md" />
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>
        </div>
      </SidebarContent>

      <SidebarFooter>
        {/* Settings Section */}
        <div className="space-y-2 px-0">
          <Skeleton className="w-16 h-4 ml-2" />
          <div className="space-y-1">
            <Skeleton className="w-full h-8 rounded-md" />
            <Skeleton className="w-full h-8 rounded-md" />
          </div>
        </div>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
