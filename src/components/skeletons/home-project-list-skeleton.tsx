import { ProjectCardSkeleton } from "@/components/skeletons";
import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "../ui/card";

export function ProjectListSkeleton() {
  // Simulate 1 cluster with 3 project cards
  return (
    <Card className="grid gap-6 w-full max-w-full h-full overflow-hidden">
      <div className="flex items-center justify-between ml-1 px-6">
        <div>
          <h2 className="text-xl font-semibold">Your Projects</h2>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            Cluster:{" "}
            <span className="font-medium">
              <Skeleton className="h-4 w-24" />
            </span>
          </div>
        </div>
        <Skeleton className="h-5 w-24" />
      </div>
      <div className="p-0 overflow-hidden">
        <div className="relative w-full overflow-hidden">
          <div className="flex flex-row gap-4 pb-2 px-6 min-w-full w-max">
            {[...Array(3)].map((_, cardIdx) => (
              <ProjectCardSkeleton key={cardIdx} />
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}
