import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

// Internal component, not exported
function TaskRowSkeleton() {
  return (
    <div className="grid grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(180px,1.8fr)_minmax(140px,1fr)_minmax(90px,0.7fr)_minmax(80px,0.5fr)] gap-x-3 items-center px-4 py-2.5 border-b h-12 dark:border-gray-800/60 dark:bg-gray-950/10">
      {/* ID */}
      <div className="flex items-center gap-1.5">
        <Skeleton className="h-3 w-14" />
        <Skeleton className="h-4 w-4 rounded-full" />
      </div>

      {/* Name */}
      <Skeleton className="h-4 w-full max-w-[180px]" />

      {/* Life Cycle */}
      <Skeleton className="h-5 w-20 rounded-full" />

      {/* Value */}
      <Skeleton className="h-5 w-16 rounded-full" />

      {/* Tags */}
      <div className="flex gap-1">
        <Skeleton className="h-4 w-12 rounded-full" />
        <Skeleton className="h-4 w-14 rounded-full" />
        <Skeleton className="h-4 w-10 rounded-full" />
      </div>

      {/* Due date */}
      <Skeleton className="h-4 w-24" />

      {/* Assignees */}
      <div className="flex -space-x-2">
        <Skeleton className="h-6 w-6 rounded-full border-2 border-white dark:border-gray-900" />
        <Skeleton className="h-6 w-6 rounded-full border-2 border-white dark:border-gray-900" />
        <Skeleton className="h-6 w-6 rounded-full border-2 border-white dark:border-gray-900" />
      </div>

      {/* Effort */}
      <Skeleton className="h-3 w-8" />
    </div>
  );
}

export function TaskGroupSkeleton({
  color = "gray",
  taskCount = 5,
}: {
  color?: string;
  taskCount?: number;
}) {
  const colorMap: Record<string, { bg: string; border: string; text: string; badge: string }> = {
    gray: {
      bg: "bg-gray-50 dark:bg-gray-800/50",
      border: "border-gray-200 dark:border-gray-700",
      text: "text-gray-700 dark:text-gray-300",
      badge: "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300",
    },
    amber: {
      bg: "bg-amber-50 dark:bg-amber-900/30",
      border: "border-amber-200 dark:border-amber-800/60",
      text: "text-amber-700 dark:text-amber-300",
      badge: "bg-amber-100 text-amber-700 dark:bg-amber-900/50 dark:text-amber-300",
    },
    blue: {
      bg: "bg-blue-50 dark:bg-blue-900/30",
      border: "border-blue-200 dark:border-blue-800/60",
      text: "text-blue-700 dark:text-blue-300",
      badge: "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300",
    },
    green: {
      bg: "bg-green-50 dark:bg-green-900/30",
      border: "border-green-200 dark:border-green-800/60",
      text: "text-green-700 dark:text-green-300",
      badge: "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300",
    },
    purple: {
      bg: "bg-purple-50 dark:bg-purple-900/30",
      border: "border-purple-200 dark:border-purple-800/60",
      text: "text-purple-700 dark:text-purple-300",
      badge: "bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300",
    },
    orange: {
      bg: "bg-orange-50 dark:bg-orange-900/30",
      border: "border-orange-200 dark:border-orange-800/60",
      text: "text-orange-700 dark:text-orange-300",
      badge: "bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300",
    },
    red: {
      bg: "bg-red-50 dark:bg-red-900/30",
      border: "border-red-200 dark:border-red-800/60",
      text: "text-red-700 dark:text-red-300",
      badge: "bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300",
    },
  };

  const colorClasses = colorMap[color] || colorMap.gray;

  return (
    <div className="mb-4 animate-pulse">
      <div
        className={cn(
          "flex items-center justify-between gap-2 px-4 py-2 rounded-lg mb-1",
          colorClasses.bg,
          colorClasses.border
        )}
      >
        <div className="flex items-center gap-1.5">
          <Skeleton className="h-3.5 w-3.5 rounded-full" />
          <Skeleton className="h-4 w-24" />
          <Badge className={cn("ml-1.5 text-xs px-1.5 py-0", colorClasses.badge)}>
            <Skeleton className="h-3 w-4" />
          </Badge>
        </div>
        <div className="flex items-center gap-6">
          <Badge
            variant="outline"
            className={cn("ml-1.5 text-xs px-1.5 py-0", colorClasses.text, colorClasses.border)}
          >
            <Skeleton className="h-3 w-8" />
          </Badge>
          <Skeleton className="h-4 w-4" />
        </div>
      </div>

      <div className="overflow-hidden border rounded-md dark:border-gray-800/60 dark:bg-gray-900/20">
        {/* Header */}
        <div className="grid grid-cols-[100px_minmax(200px,2.5fr)_minmax(120px,0.9fr)_minmax(90px,0.7fr)_minmax(180px,1.8fr)_minmax(140px,1fr)_minmax(90px,0.7fr)_minmax(80px,0.5fr)] gap-x-3 px-4 py-2 bg-gray-50 dark:bg-gray-800/50 text-xs font-medium text-gray-500 dark:text-gray-400 border-b dark:border-gray-800/60">
          <Skeleton className="h-3 w-8" />
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-10" />
          <Skeleton className="h-3 w-8" />
          <Skeleton className="h-3 w-14" />
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-12" />
        </div>

        {/* Task rows */}
        {Array.from({ length: taskCount }).map((_, index) => (
          <TaskRowSkeleton key={index} />
        ))}
      </div>
    </div>
  );
}

export function TaskListSkeleton() {
  const groupColors = ["gray", "blue", "amber", "purple", "green"];
  const taskCounts = [4, 3, 2, 3, 2];

  return (
    <div className="space-y-4">
      {groupColors.map((color, index) => (
        <TaskGroupSkeleton key={index} color={color} taskCount={taskCounts[index]} />
      ))}
    </div>
  );
}

export default TaskListSkeleton;
