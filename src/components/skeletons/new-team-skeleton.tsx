import { Skeleton } from "@/components/ui/skeleton";

export default function NewTeamSkeleton() {
  return (
    <div className="flex flex-col items-center justify-center w-full max-w-2xl m-auto p-6 border rounded-lg">
      {/* Header skeleton */}
      <div className="w-full mb-8 text-center">
        <Skeleton className="h-8 w-48 mx-auto mb-2" />
        <Skeleton className="h-4 w-64 mx-auto" />
      </div>

      {/* Form skeleton */}
      <div className="w-full space-y-6">
        {/* Team name field */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full" />
        </div>

        {/* Description field */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-24 w-full" />
        </div>

        {/* Members section */}
        <div className="space-y-4">
          <Skeleton className="h-4 w-28" />
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-8 flex-1" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-8 flex-1" />
            </div>
          </div>
          <Skeleton className="h-8 w-32" />
        </div>

        {/* Button */}
        <Skeleton className="h-10 w-full mt-8" />
      </div>
    </div>
  );
}
