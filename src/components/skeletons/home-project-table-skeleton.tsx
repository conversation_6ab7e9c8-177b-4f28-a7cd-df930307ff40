import { Skeleton } from "@/components/ui/skeleton";

export function ProjectTableRowSkeleton() {
  return (
    <tr className="hover:bg-muted/30">
      <td className="px-4 py-3 text-sm font-medium">
        <Skeleton className="h-4 w-32" />
      </td>
      <td className="px-4 py-3 text-sm">
        <Skeleton className="h-5 w-20" />
      </td>
      <td className="px-4 py-3 text-sm">
        <div className="flex items-center">
          <div className="h-2 w-2 mr-2 rounded-full bg-gray-200" />
          <Skeleton className="h-4 w-16" />
        </div>
      </td>
      <td className="px-4 py-3 text-sm">
        <div className="flex flex-wrap gap-1 max-w-xs">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-16" />
        </div>
      </td>
      <td className="px-4 py-3 text-sm text-right">
        <Skeleton className="h-8 w-24 ml-auto" />
      </td>
    </tr>
  );
}

export function ProjectTableSkeleton() {
  return (
    <div className="w-full">
      <div className="rounded-md border">
        <table className="w-full divide-y divide-border">
          <thead>
            <tr className="bg-muted/50">
              <th className="px-4 py-3 text-left text-sm font-medium">Project</th>
              <th className="px-4 py-3 text-left text-sm font-medium">Cluster</th>
              <th className="px-4 py-3 text-left text-sm font-medium">Status</th>
              <th className="px-4 py-3 text-left text-sm font-medium">Your Teams</th>
              <th className="px-4 py-3 text-right text-sm font-medium">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {Array.from({ length: 5 }).map((_, index) => (
              <ProjectTableRowSkeleton key={index} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
