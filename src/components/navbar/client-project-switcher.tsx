"use client";

import { getUserProjects } from "@/db/actions/user-projects.action";
import { useQuery } from "@tanstack/react-query";
import { ChevronsUpDownIcon, GalleryVerticalEnd } from "lucide-react";
import { usePathname } from "next/navigation";
import { TeamSwitcher } from "./nav-team-switcher";

export function ClientProjectSwitcher() {
  // Use React Query instead of useState/useEffect to prevent unnecessary refetches
  const {
    data: projects = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user", "projects"],
    queryFn: getUserProjects,
    staleTime: 60 * 60 * 1000, // 1 hour - projects rarely change
    gcTime: 2 * 60 * 60 * 1000, // 2 hours cache
    refetchOnWindowFocus: false,
    retry: 1,
  });

  if (error) {
    console.error("Error loading projects:", error);
  }

  if (isLoading || !projects || projects.length === 0) {
    return <ProjectSwitcherSkeleton />;
  }

  return <TeamSwitcher projects={projects} />;
}

function ProjectSwitcherSkeleton() {
  const pathname = usePathname();

  // Extract cluster and project slugs from the current pathname
  const pathParts = pathname?.split("/").filter(Boolean) || [];
  const currentClusterSlug = pathParts[0];
  const currentProjectSlug = pathParts[1];

  // Find the active project based on the current URL
  const currentCluster = currentClusterSlug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  const currentProject = currentProjectSlug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  return (
    <div className="flex items-center w-full h-12 px-2">
      {/* Project Icon Container */}
      <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
        <GalleryVerticalEnd className="size-4" />
      </div>
      {/* Project and Cluster Names */}
      <div className="ml-2 grid flex-1 text-left text-sm leading-tight gap-0">
        <span className="truncate font-medium">{currentProject}</span>
        <span className="truncate text-xs">{currentCluster}</span>
      </div>

      {/* Chevron Icon */}
      <ChevronsUpDownIcon size={16} className="ml-auto" />
    </div>
  );
}
