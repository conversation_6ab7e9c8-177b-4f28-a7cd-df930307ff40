import {
  <PERSON><PERSON>,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Sheet,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { getCurrentUserOrganizationRoleAction } from "@/db/actions/organization-members.action";
import { cn, goToMainDomain } from "@/lib/utils";
import { Computer, CreditCard, HelpCircle, Menu, TvMinimal, User } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { LogoutButton } from "../auth/logout-button";
import GlobalNotificationBell from "../origin-ui/global-notification-bell";
import UserAvatar from "../origin-ui/user-avatar";
import RenwuLogo from "../svg/renwu-logo";
import { Skeleton } from "../ui/skeleton";
import { TopbarAiCommandSearch } from "./topbar-ai-command-search";

const menuItems = [
  {
    name: "Homepage",
    compactName: "Home",
    href: "/home",
    isAdminRequired: false,
  },
  {
    name: "My Account",
    compactName: "Account",
    href: "/home/<USER>",
    isAdminRequired: false,
  },
  {
    name: "My Teams",
    compactName: "Teams",
    href: "/home/<USER>",
    isAdminRequired: false,
  },
  {
    name: "Organization",
    compactName: "Orgs",
    href: "/home/<USER>",
    isAdminRequired: true,
  },
  {
    name: "Clusters",
    compactName: "Clusters",
    href: "/home/<USER>",
    isAdminRequired: true,
  },
  { name: "Docs", compactName: "Docs", href: "/docs", isAdminRequired: true },
];

export async function AppTopbar() {
  // Check if user is admin
  const { data: userRole } = await getCurrentUserOrganizationRoleAction();
  const isAdmin = userRole?.isAdmin;

  // Filter menu items based on admin status
  const visibleMenuItems = menuItems.filter(
    (item) => !item.isAdminRequired || (item.isAdminRequired && isAdmin)
  );

  return (
    <div className="sticky top-0 left-0 py-2 right-0 z-50 border-b backdrop-blur-lg">
      <div className="flex items-center justify-between h-16 px-4 md:px-6">
        {/* Logo and brand */}
        <div className="flex items-center gap-4 md:gap-14">
          <Link href="/" className="flex items-center">
            <RenwuLogo className="w-16 h-16" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex md:items-center md:gap-6">
            {visibleMenuItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "text-sm font-medium transition-colors px-2 py-2 rounded-md hover:underline hover:underline-offset-8 relative",
                  "[&[data-state=active]]:underline [&[data-state=active]]:underline-offset-8"
                )}
              >
                <span className="hidden xl:block">{item.name}</span>
                <span className="xl:hidden">{item.compactName}</span>
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <div className="hidden max-md:block">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Menu className="w-6 h-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0">
                <>
                  <RenwuLogo className="fixed top-0 left-0 w-16 h-16" />
                  <SheetTitle className="flex items-center justify-center gap-2 mt-4">
                    Menu
                  </SheetTitle>

                  <nav className="flex flex-col gap-2 p-4 pt-8">
                    {visibleMenuItems.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="px-2 py-2 text-base font-medium rounded-md hover:bg-accent"
                      >
                        {item.name}
                      </Link>
                    ))}
                  </nav>
                  <SheetFooter>
                    <ThemeToggle className="flex self-end" />

                    <LogoutButton variant="default" className="h-10" isMenuButton />
                  </SheetFooter>
                </>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Topbar Actions & Profile */}

        <div className="flex items-center gap-4">
          <div className="hidden xl:block">
            <TopbarAiCommandSearch />
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <GlobalNotificationBell side="bottom" align="end" />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative w-8 h-8 rounded-full">
                <Suspense fallback={<Skeleton className="h-8 w-8 rounded-full" />}>
                  <UserAvatar />
                </Suspense>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <TvMinimal className="w-4 h-4" />
                  <Link href="/demo/home/">Demo Application</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <Link href="/home/<USER>">Account</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <Computer className="w-4 h-4" />
                  <Link href={goToMainDomain("auth/organizations")}>Organizations</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  <Link href="/home/<USER>">Manage Plan</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <div className="flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" />
                  <Link href="/home/<USER>">Help</Link>
                </div>
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              <DropdownMenuItem>
                <LogoutButton
                  variant="ghost"
                  className="flex justify-start w-full -ml-3"
                  isMenuButton
                />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}

export default AppTopbar;
