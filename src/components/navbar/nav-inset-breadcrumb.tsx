"use client";

import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbElli<PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouteParams } from "@/hooks/use-route-params";
import { useSubdomain } from "@/hooks/use-subdomain";
import Link from "next/link";

export function InsetBreadcrumb() {
  const subdomain = useSubdomain();
  const { clusterSlug, projectSlug, currentPath } = useRouteParams();

  // Check for all required route parameters first
  if (!clusterSlug || !projectSlug || !currentPath) return null;
  // Then check for subdomain
  if (!subdomain) return null;

  // Format all slugs with capitalization
  const organizationSlug = subdomain.charAt(0).toUpperCase() + subdomain.slice(1);
  const formattedClusterSlug = clusterSlug.charAt(0).toUpperCase() + clusterSlug.slice(1);
  const formattedProjectSlug = projectSlug.charAt(0).toUpperCase() + projectSlug.slice(1);
  const formattedCurrentPath = currentPath?.charAt(0).toUpperCase() + currentPath?.slice(1);

  // Create breadcrumb items array for dropdown
  const allItems = [
    {
      label: organizationSlug,
      href: "/home",
      id: "org",
    },
    {
      label: formattedClusterSlug,
      href: `/clusters/${clusterSlug}`,
      id: "cluster",
    },
    {
      label: formattedProjectSlug,
      href: `/clusters/${clusterSlug}/projects/${projectSlug}/dashboard`,
      id: "project",
    },
    {
      label: formattedCurrentPath,
      href: null, // Current page, no link
      id: "current",
    },
  ];

  return (
    <Breadcrumb className="line-clamp-1 h-16 flex items-center overflow-hidden">
      <BreadcrumbList>
        {/* Ellipsis dropdown - shown at different breakpoints depending on how many items should be hidden */}
        <BreadcrumbItem className="sm:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center">
              <BreadcrumbEllipsis />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {allItems.slice(0, 3).map((item) => (
                <DropdownMenuItem key={item.id} asChild>
                  <Link href={item.href || "#"} className="flex items-center">
                    {item.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </BreadcrumbItem>

        <BreadcrumbItem className="hidden sm:inline-flex md:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center">
              <BreadcrumbEllipsis />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {allItems.slice(0, 2).map((item) => (
                <DropdownMenuItem key={item.id} asChild>
                  <Link href={item.href || "#"} className="flex items-center">
                    {item.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </BreadcrumbItem>

        <BreadcrumbItem className="hidden md:inline-flex lg:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center">
              <BreadcrumbEllipsis />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {allItems.slice(0, 1).map((item) => (
                <DropdownMenuItem key={item.id} asChild>
                  <Link href={item.href || "#"} className="flex items-center">
                    {item.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </BreadcrumbItem>

        {/* Only shown on lg and larger screens */}
        <BreadcrumbItem className="hidden lg:inline-flex">
          <BreadcrumbLink href="/home">{organizationSlug}</BreadcrumbLink>
        </BreadcrumbItem>

        {/* Always show separator after ellipsis or first item */}
        <BreadcrumbSeparator className="hidden sm:list-item px-0" />

        {/* Only shown on md and larger screens */}
        <BreadcrumbItem className="hidden md:inline-flex">
          <BreadcrumbLink href={`/clusters/${clusterSlug}`}>{formattedClusterSlug}</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:list-item px-0" />

        {/* Only shown on sm and larger screens */}
        <BreadcrumbItem className="hidden sm:inline-flex">
          <BreadcrumbLink href={`/clusters/${clusterSlug}/projects/${projectSlug}/dashboard`}>
            {formattedProjectSlug}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="list-item px-0" />

        {/* Always visible - current page */}
        <BreadcrumbItem>
          <BreadcrumbPage>{formattedCurrentPath}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
}
