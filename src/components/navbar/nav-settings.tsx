"use client";

import { useRouteParams } from "@/hooks/use-route-params";
import { buildDynamicUrl, cn } from "@/lib/utils";
import { type LucideIcon } from "lucide-react";
import * as React from "react";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { ProtectedLink } from "../ui/protected-link";

export function NavSettings({
  groupLabel,
  items,
  ...props
}: {
  groupLabel: string;
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
  }[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const { clusterSlug, projectSlug } = useRouteParams();

  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarGroupLabel>{groupLabel}</SidebarGroupLabel>
        <SidebarMenu>
          {items.map((item) => {
            const dynamicUrl = buildDynamicUrl(item.url, { clusterSlug, projectSlug });
            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  size="default"
                  className={cn(item.isActive && "active-menu text-accent-foreground font-medium")}
                >
                  <ProtectedLink href={dynamicUrl}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </ProtectedLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
