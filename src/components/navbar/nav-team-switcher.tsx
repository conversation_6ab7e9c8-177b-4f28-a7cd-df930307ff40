"use client";

import { ChevronsUpDownIcon, GalleryVerticalEnd, Plus } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { UserProjectWithCluster } from "@/db/user-projects.db";
import { Query, useQueryClient } from "@tanstack/react-query";
import { Suspense } from "react";
import { toast } from "sonner";
import { Skeleton } from "../ui/skeleton";

type TeamSwitcherProps = {
  projects: UserProjectWithCluster[];
};

export function TeamSwitcher({ projects }: TeamSwitcherProps) {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();

  // If no projects, return nothing
  if (!projects.length) {
    toast.error("No projects found.", {
      description: "Please create a project to continue.",
    });
    return null;
  }

  // Extract cluster and project slugs from the current pathname
  const pathParts = pathname?.split("/").filter(Boolean);

  if (!pathParts) return null;

  // Check if the path follows the new URL pattern
  let currentClusterSlug = "";
  let currentProjectSlug = "";

  if (pathParts.length >= 4 && pathParts[0] === "clusters" && pathParts[2] === "projects") {
    currentClusterSlug = pathParts[1];
    currentProjectSlug = pathParts[3];
  } else if (pathParts.length >= 2) {
    // Fallback for old URL pattern
    currentClusterSlug = pathParts[0];
    currentProjectSlug = pathParts[1];
  }

  // Find the active project based on the current URL
  const currentCluster = currentClusterSlug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  const currentProject = currentProjectSlug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  // Handle project selection with new URL pattern
  const handleProjectSelect = (clusterSlug: string, projectSlug: string) => {
    // Protect cache before navigation
    const allQueries = queryClient.getQueryCache().getAll();
    const now = Date.now();
    allQueries.forEach((query: Query) => {
      if (query.state.data) {
        queryClient.setQueryData(query.queryKey, query.state.data, {
          updatedAt: now,
        });
      }
    });

    router.push(`/clusters/${clusterSlug}/projects/${projectSlug}/dashboard`);
  };

  // Use a static icon for now as specified
  const ProjectIcon = GalleryVerticalEnd;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <ProjectIcon className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{currentProject}</span>
                <span className="truncate text-xs">
                  <span className="text-muted-foreground">cluster-</span>
                  {currentCluster}
                </span>
              </div>
              <ChevronsUpDownIcon className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-fit rounded-lg ml-2"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <Suspense fallback={<Skeleton className="w-full h-4" />}>
              <DropdownMenuLabel className="flex items-center justify-between text-muted-foreground text-xs">
                <span>Projects</span>
                <span className="text-[10px] font-medium">(Clusters)</span>
              </DropdownMenuLabel>
              {projects.map((project) => (
                <DropdownMenuItem
                  key={project.id}
                  onClick={() => handleProjectSelect(project.clusterSlug, project.slug)}
                  className="gap-2 p-2"
                >
                  <div className="flex size-6 items-center justify-center rounded-md border">
                    <ProjectIcon className="size-3.5 shrink-0" />
                  </div>
                  <span className="flex-1 truncate mr-6">{project.name}</span>
                  <span className="text-tiny text-muted-foreground">{project.clusterName}</span>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="gap-2 p-2"
                onClick={() => {
                  // Protect cache before navigation
                  const allQueries = queryClient.getQueryCache().getAll();
                  const now = Date.now();
                  allQueries.forEach((query: Query) => {
                    if (query.state.data) {
                      queryClient.setQueryData(query.queryKey, query.state.data, {
                        updatedAt: now,
                      });
                    }
                  });
                  router.push("/home/<USER>");
                }}
              >
                <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                  <Plus className="size-4" />
                </div>
                <div className="text-muted-foreground font-medium">Create project</div>
              </DropdownMenuItem>
            </Suspense>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
