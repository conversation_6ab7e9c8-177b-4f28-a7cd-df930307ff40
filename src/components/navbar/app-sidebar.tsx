"use client";

import { type NavItem, type SettingsNavItem } from "@/types/sidebar.types";
import * as React from "react";

import { ClientProjectSwitcher } from "@/components/navbar/client-project-switcher";
import { NavMain } from "@/components/navbar/nav-main";
import { NavSettings } from "@/components/navbar/nav-settings";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import {
  accountSettingsNavData,
  analyticsNavData,
  ceremoniesNavData,
  deliveryNavData,
  discoveryNavData,
  homeNavData,
  hubNavData,
  navSettingsData,
} from "@/data/sidebar-data";
import { useActiveMenu } from "@/hooks/use-active-menu";
import { AiCommandSearch } from "./nav-ai-command-search";

export function AppSidebar() {
  // Memoize the menu categories to prevent unnecessary re-renders
  const menuCategories = React.useMemo(() => {
    return {
      navHome: homeNavData.items,
      navDiscovery: discoveryNavData.items,
      navDelivery: deliveryNavData.items,
      navCeremonies: ceremoniesNavData.items,
      navAnalytics: analyticsNavData.items,
      navSettings: navSettingsData.items,
      navHub: hubNavData.items,
      navAccountSettings: accountSettingsNavData.items,
    };
  }, []);

  // Use the active menu hook to determine which menu items are active
  const activeMenus = useActiveMenu(menuCategories);

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="flex items-center justify-between transition-all duration-300">
        <ClientProjectSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <AiCommandSearch />
        <span className="ml-1">
          <NavMain groupLabel="Overview" items={activeMenus.navHome as NavItem[]} />
          <NavMain groupLabel="Create & Prioritize" items={activeMenus.navDiscovery as NavItem[]} />
          <NavMain groupLabel="Track & Deploy" items={activeMenus.navDelivery as NavItem[]} />
          <NavMain groupLabel="Ceremonies" items={activeMenus.navCeremonies as NavItem[]} />
          <NavMain groupLabel="Analytics & Reports" items={activeMenus.navAnalytics as NavItem[]} />
        </span>
      </SidebarContent>
      <SidebarFooter>
        <NavSettings
          groupLabel="Settings"
          items={activeMenus.navSettings as SettingsNavItem[]}
          className="mt-auto px-0"
        />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
