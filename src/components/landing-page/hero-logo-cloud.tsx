import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export const HeroLogoCloud = () => {
  return (
    <section className="bg-background py-16">
      <div className="group relative m-auto max-w-5xl px-6">
        <div className="absolute inset-0 z-10 flex scale-95 items-center justify-center opacity-0 duration-500 group-hover:scale-100 group-hover:opacity-100">
          <Link
            href="/"
            className="block text-sm duration-150 hover:opacity-75"
          >
            <span> Meet Our Customers</span>

            <ChevronRight className="ml-1 inline-block size-3" />
          </Link>
        </div>
        <div className="group-hover:blur-xs mx-auto mt-12 grid max-w-2xl grid-cols-4 gap-x-12 gap-y-8 transition-all duration-500 group-hover:opacity-50 sm:gap-x-16 sm:gap-y-14">
          <div className="flex">
            <Image
              className="mx-auto h-5 w-5 dark:invert"
              src="https://html.tailus.io/blocks/customers/nvidia.svg"
              alt="Nvidia Logo"
              height={20}
              width={20}
            />
          </div>

          <div className="flex">
            <Image
              className="mx-auto h-4 w-4 dark:invert"
              src="https://html.tailus.io/blocks/customers/column.svg"
              alt="Column Logo"
              height={16}
              width={16}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-4 w-4 dark:invert"
              src="https://html.tailus.io/blocks/customers/github.svg"
              alt="GitHub Logo"
              height={16}
              width={16}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-5 w-5 dark:invert"
              src="https://html.tailus.io/blocks/customers/nike.svg"
              alt="Nike Logo"
              height={20}
              width={20}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-5 w-5 dark:invert"
              src="https://html.tailus.io/blocks/customers/lemonsqueezy.svg"
              alt="Lemon Squeezy Logo"
              height={20}
              width={20}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-4 w-4 dark:invert"
              src="https://html.tailus.io/blocks/customers/tailwindcss.svg"
              alt="Tailwind CSS Logo"
              height={16}
              width={16}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-6 w-6 dark:invert"
              src="https://html.tailus.io/blocks/customers/openai.svg"
              alt="OpenAI Logo"
              height={24}
              width={24}
            />
          </div>
          <div className="flex">
            <Image
              className="mx-auto h-6 w-6 dark:invert"
              src="https://html.tailus.io/blocks/customers/vercel.svg"
              alt="Vercel Logo"
              width={16}
              height={16}
            />
          </div>
        </div>
      </div>
    </section>
  );
};
