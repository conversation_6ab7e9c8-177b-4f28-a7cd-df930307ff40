import { <PERSON><PERSON>, Zap } from "lucide-react";
import Image from "next/image";

export const HeroSolution = () => {
  return (
    <section id="solution" className="py-20 md:py-32">
      <div className="mx-auto max-w-6xl space-y-10 px-6 md:space-y-16">
        <h2 className="relative z-10 max-w-xl text-4xl font-medium tracking-tight lg:text-5xl">
          Ship Faster with AI-Powered Project Management
        </h2>
        <div className="relative flex flex-col md:flex-row md:items-center md:gap-12">
          <div className="relative z-10 space-y-6 md:w-1/2">
            <div className="space-y-4">
              <p className="text-body">
                Transform your development pipeline with our{" "}
                <span className="text-title font-medium">
                  AI-powered project management platform
                </span>{" "}
                that revolutionizes team collaboration.
              </p>
              <p>
                Our intelligent system optimizes workflows, automates routine
                tasks, and provides data-driven insights to accelerate your
                development cycle.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 pt-8 sm:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Zap className="size-5" />
                  <h3 className="font-medium">40% Productivity Boost</h3>
                </div>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Enhance team efficiency with AI-driven task management and
                  automation.
                </p>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Cpu className="size-5" />
                  <h3 className="font-medium">60% Faster Delivery</h3>
                </div>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Streamline your pipeline with intelligent workflow
                  optimization.
                </p>
              </div>
            </div>
          </div>

          <div className="relative mt-12 h-fit md:mt-0 md:w-1/2">
            <div
              aria-hidden
              className="bg-gradient-to-l from-transparent from-70% to-background absolute inset-0 z-10 hidden md:block"
            />
            <div className="relative rounded-2xl border border-dotted border-border/50 p-2">
              <Image
                src="/hero-release.png"
                className="hidden rounded-xl dark:block"
                alt="Project management dashboard dark theme"
                width={1207}
                height={829}
                priority
              />
              <Image
                src="/hero-release-light.png"
                className="rounded-xl shadow dark:hidden"
                alt="Project management dashboard light theme"
                width={1207}
                height={829}
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
