"use client";

import Renwu<PERSON><PERSON> from "@/components/svg/renwu-logo";
import { AnimatedGroup } from "@/components/ui/animated-group";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { createClient } from "@/lib/supabase/client";
import { cn, getCurrentSubdomain, goToMainDomain, goToSubdomain } from "@/lib/utils";
import { ArrowRight, Menu, X } from "lucide-react";
import Link from "next/link";
import React from "react";
import { getAuthStateAction } from "../../db/actions/auth.action";

const menuItems = [
  { name: "Features", href: "#features" },
  { name: "Solution", href: "#solution" },
  { name: "Pricing", href: "#pricing" },
  { name: "About", href: "#footer" },
];

export const HeroHeader = () => {
  const [menuState, setMenuState] = React.useState(false);
  const [isScrolled, setIsScrolled] = React.useState(false);
  const [isLoggedIn, setIsLoggedIn] = React.useState<boolean>(false);
  const [subdomain, setSubdomain] = React.useState<string | null>(null);

  const checkAuth = async () => {
    const authState = await getAuthStateAction();
    setIsLoggedIn(authState);
  };

  React.useEffect(() => {
    // Set up scroll event listener
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);

    // Initial auth check
    checkAuth();

    // Detect subdomain
    if (typeof window !== "undefined") {
      const subdomain = getCurrentSubdomain(window.location.hostname);
      if (subdomain) {
        setSubdomain(subdomain);
      }
    }

    // Set up Supabase auth listener
    const supabase = createClient();
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(() => {
      checkAuth();
    });

    // Also check auth state when window gets focus
    const handleFocus = () => {
      checkAuth();
    };
    window.addEventListener("focus", handleFocus);

    // Clean up all event listeners and subscriptions
    return () => {
      window.removeEventListener("scroll", handleScroll);
      subscription.unsubscribe();
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  // Extract auth buttons to a separate component for better readability
  const AuthButtons = () => {
    if (isLoggedIn) {
      // Use subdomain if available for better UX
      const dashboardUrl = subdomain ? goToSubdomain(subdomain, "home") : "/auth/organizations";

      // Create a single display text
      let buttonText = "Dashboard";
      if (subdomain && subdomain.length > 0) {
        buttonText = subdomain.charAt(0).toUpperCase() + subdomain.slice(1);
      }

      return (
        <>
          <Button
            asChild
            size="sm"
            className={cn("group", isScrolled ? "lg:inline-flex" : "inline-flex")}
          >
            {subdomain && (
              <Link href={goToMainDomain("auth/organizations")}>
                <span>Dashboard</span>
              </Link>
            )}
          </Button>
          <Button
            asChild
            size="sm"
            className={cn("group", isScrolled ? "lg:inline-flex" : "inline-flex")}
          >
            <Link href={dashboardUrl}>
              <span>{buttonText}</span>
              <ArrowRight className="size-4 group-hover:translate-x-0.5 transition-transform duration-300" />
            </Link>
          </Button>
        </>
      );
    }

    return (
      <>
        <Button
          asChild
          variant="outline"
          size="sm"
          className={cn(isScrolled && "lg:hidden", "cursor-pointer")}
        >
          <Link href="/auth/login">
            <span>Login</span>
          </Link>
        </Button>

        <Button asChild size="sm" className={cn(isScrolled && "lg:hidden")}>
          <Link href="/auth/sign-up">
            <span>Sign Up</span>
          </Link>
        </Button>
        <Button asChild size="sm" className={cn(isScrolled ? "lg:inline-flex" : "hidden")}>
          <Link href="/auth/sign-up">
            <span>Get Started</span>
          </Link>
        </Button>
      </>
    );
  };

  return (
    <header>
      <nav data-state={menuState && "active"} className="fixed z-20 w-full px-2">
        <AnimatedGroup preset="fade" className="pointer-events-none">
          <div
            className={cn(
              "mx-auto mt-2 max-w-6xl px-6 transition-all duration-300 lg:px-12",
              isScrolled &&
                "bg-background/50 max-w-4xl rounded-2xl border backdrop-blur-xl shadow-sm lg:px-5"
            )}
          >
            <div className="relative flex flex-wrap items-center justify-between gap-6 lg:gap-0">
              <div className="flex w-full justify-between lg:w-auto pointer-events-auto">
                <Link href="/" aria-label="home" className="flex items-center space-x-2">
                  <RenwuLogo className="w-16 h-16" />
                </Link>

                <button
                  onClick={() => setMenuState(!menuState)}
                  aria-label={menuState == true ? "Close Menu" : "Open Menu"}
                  className="relative z-20 -m-2.5 -mr-4 block cursor-pointer p-2.5 lg:hidden"
                >
                  <Menu className="in-data-[state=active]:rotate-180 in-data-[state=active]:scale-0 in-data-[state=active]:opacity-0 m-auto size-6 duration-200" />
                  <X className="in-data-[state=active]:rotate-0 in-data-[state=active]:scale-100 in-data-[state=active]:opacity-100 absolute inset-0 m-auto size-6 -rotate-180 scale-0 opacity-0 duration-200" />
                </button>
              </div>

              <div className="absolute inset-0 m-auto hidden size-fit lg:block pointer-events-auto">
                <ul className="flex gap-8 text-sm">
                  {menuItems.map((item, index) => (
                    <li key={index}>
                      <Link
                        href={item.href}
                        className="hover:text-muted-foreground text-accent-foreground block duration-150"
                      >
                        <span>{item.name}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="bg-background in-data-[state=active]:block lg:in-data-[state=active]:flex mb-6 hidden w-full flex-wrap items-center justify-end space-y-8 rounded-3xl border p-6 shadow-2xl shadow-zinc-300/20 md:flex-nowrap lg:m-0 lg:flex lg:w-fit lg:gap-6 lg:space-y-0 lg:border-transparent lg:bg-transparent lg:p-0 lg:shadow-none dark:shadow-none dark:lg:bg-transparent pointer-events-auto">
                <div className="lg:hidden">
                  <ul className="space-y-6 text-base">
                    {menuItems.map((item, index) => (
                      <li key={index}>
                        <Link
                          href={item.href}
                          className="text-muted-foreground hover:text-accent-foreground block duration-150"
                        >
                          <span>{item.name}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex w-full flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit">
                  <ThemeToggle />
                  <AuthButtons />
                </div>
              </div>
            </div>
          </div>
        </AnimatedGroup>
      </nav>
    </header>
  );
};
