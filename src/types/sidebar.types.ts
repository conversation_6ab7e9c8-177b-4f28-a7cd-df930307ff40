import { LucideIcon } from "lucide-react";

// Base navigation item type for reuse
export interface BaseNavItem {
  title: string;
  url: string;
  icon: LucideIcon;
  isActive: boolean;
}

// Extended navigation item with optional subItems and expanded state
export interface NavItem extends BaseNavItem {
  isExpanded?: boolean;
  subItems?: {
    title: string;
    url: string;
    isActive?: boolean;
  }[];
}

// Support navigation item (simplified version)
export interface SettingsNavItem extends BaseNavItem {
  isExpanded?: boolean;
  subItems?: {
    title: string;
    url: string;
    isActive?: boolean;
  }[];
}

// Project item
export interface ProjectItem {
  id: number;
  name: string;
  url: string;
  icon: LucideIcon;
  isActive?: boolean;
}

// Team item
export interface TeamItem {
  name: string;
  logo: LucideIcon;
  plan: string;
}

// User profile data
export interface UserProfile {
  name: string;
  email: string;
  avatar: string;
}

// Home navigation data
export interface HomeNavData {
  items: NavItem[];
}

// Discovery navigation data
export interface DiscoveryNavData {
  items: NavItem[];
}

// Delivery navigation data
export interface DeliveryNavData {
  items: NavItem[];
}

// Ceremonies navigation data
export interface CeremoniesNavData {
  items: NavItem[];
}

// Analytics navigation data
export interface AnalyticsNavData {
  items: NavItem[];
}

// Support navigation data
export interface SettingsNavData {
  items: SettingsNavItem[];
}

// Projects data
export interface ProjectsData {
  items: ProjectItem[];
}

// Teams data
export interface TeamsData {
  items: TeamItem[];
}

// Complete sidebar data types for reference
export interface SidebarData {
  user: UserProfile;
  teams: TeamsData;
  navHome: HomeNavData;
  navDiscovery: DiscoveryNavData;
  navDelivery: DeliveryNavData;
  navCeremonies: CeremoniesNavData;
  navAnalytics: AnalyticsNavData;
  navSettings: SettingsNavData;
  projects: ProjectsData;
}

// Legacy type for backward compatibility
export interface TSidebarProjectData {
  user: UserProfile;
  teams: TeamItem[];
  navHome: NavItem[];
  navDiscovery: NavItem[];
  navDelivery: NavItem[];
  navAnalytics: NavItem[];
  navSettings: SettingsNavItem[];
  projects: ProjectItem[];
}

export interface HubNavData {
  items: NavItem[];
}
export interface AccountSettingsNavData {
  items: NavItem[];
}
