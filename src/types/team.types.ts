// Define base types for team data structures

export type Position =
  | "No one knows"
  | "Developer"
  | "Lead Developer"
  | "Product Manager"
  | "Project Manager"
  | "UI/UX Designer"
  | "QA Engineer"
  | "QA Lead"
  | "Engineering Manager"
  | "CTO"
  | "VP of Engineering"
  | "CEO";

export type Role = "owner" | "admin" | "member" | "guest";

export type Team = {
  id: string;
  user_id?: string;
  name: string;
  description: string | null;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  team_members: TeamMember[];
};

export type TeamMember = {
  id: string;
  team_id: string;
  user_id: string;
  role: Role;
  position: Position;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  // These fields are added after joining with users table
  name?: string;
  email?: string;
  avatarUrl?: string;
};

export type Project = {
  id: string;
  name: string;
  color?: string;
};

export type TeamWithMembers = Team & {
  members: TeamMember[];
  projects: Project[];
};

// UI-specific types
export type TeamMemberUI = {
  id: string;
  userId: string;
  name: string;
  email: string;
  role: Role;
  position: Position;
  avatarUrl?: string;
  createdAt: string;
  dateAdded?: string;
};

export type TeamUI = {
  id: string;
  name: string;
  description: string | null;
  tenantId: string;
  members: TeamMemberUI[];
  projects: Project[];
};
