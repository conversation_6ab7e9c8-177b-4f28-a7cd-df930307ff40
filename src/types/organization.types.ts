export interface OrganizationType {
  id: string;
  name: string;
  created_at: string;
  description: string | null;
  max_users: number;
  member_role: string;
  subdomain_id: string;
  updated_at: string;
}

export interface OrganizationMemberType {
  id: string;
  name: string;
  email: string;
  role: string;
}

export type OrganizationInfoType = {
  id: string;
  name: string;
  subdomain_id: string;
};
