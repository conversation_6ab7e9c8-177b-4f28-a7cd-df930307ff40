// Database types (match the actual database fields)
export type UserProfileDB = {
  id?: string | undefined;
  user_id?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  location?: string;
  avatar_url?: string;
  department?: string;
  role?: string;
  position?: string;
  projects_count?: number;
  tasks_done?: number;
  pull_requests?: number;
  email?: string;
  tenant_id?: string;
  joined_date?: string;
  created_at?: string;
  updated_at?: string;
};

// User stats type for frontend
export type UserStats = {
  projects?: number;
  tasksDone?: number;
  pullRequests?: number;
};

// Frontend user profile type
export type UserProfile = {
  name: string;
  email: string;
  joinedDate: string;
  location?: string;
  avatar?: string;
  avatarFallback?: string;
  department?: string;
  role?: string;
  position?: string;
  stats?: UserStats;
};
