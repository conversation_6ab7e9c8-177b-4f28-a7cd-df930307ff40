---
description: 
globs: 
alwaysApply: true
---
---
description: ENFORCE style in our application
globs: **/*
---
- Write concise, technical TypeScript code using functional and declarative programming patterns.
- Avoid classes; prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
- Structure files into: exported component, subcomponents, helpers, static content, and types.

## syntax-and-formatting

- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Write declarative JSX.

## typescript-usage

- Use TypeScript for all code; prefer types over interfaces.
- Avoid enums; use maps instead.
- Use functional components with TypeScript types.

## ui-and-styling

- Use Shadcn UI, Radix, and Tailwind for components and styling.
- Implement responsive design with Tailwind CSS using a mobile-first approach.

## performance-optimization

- Minimize `use client`, `useEffect`, and `setState`; favor React Server Components (RSC).
- Wrap client components in `Suspense` with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, and implement lazy loading.

## database-querying-and-data-model-creation

- Use Prisma SDK to query the database.
- For data models, read the `.prisma` files.

## key-conventions

- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client': Favor server components and Next.js SSR for data fetching or state management.
- Use 'use client' only for Web API access in small components.
