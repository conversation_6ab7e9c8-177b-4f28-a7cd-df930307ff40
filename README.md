# Renwu - Next.js Onboarding Application

A modern project management application built with Next.js 15, Tailwind CSS 4, and Shadcn UI components, providing a seamless user experience for creating individual accounts or organizations.

## Features

- **Multi-path Onboarding**: Support for individual users, new organizations, or joining existing organizations
- **Step-by-step Process**: Guided onboarding with animated stepper components
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark Mode Support**: Complete theme switching capability
- **Modern UI Components**: Built with Shadcn UI and Radix UI primitives

## Tech Stack

- [Next.js 15](https://nextjs.org/) - React framework with App Router
- [React 19](https://react.dev/) - UI library
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS 4](https://tailwindcss.com/) - Utility-first CSS framework
- [Shadcn UI](https://ui.shadcn.com/) - Re-usable UI components
- [Radix UI](https://www.radix-ui.com/) - Accessible UI primitives
- [Lucide Icons](https://lucide.dev/) - Beautiful SVG icons

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v18 or newer)
- [pnpm](https://pnpm.io/) (v8 or newer)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/renwu.git
cd renwu
```

2. Install dependencies using pnpm:

```bash
pnpm install
```

3. Start the development server:

```bash
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Available Scripts

In the project directory, you can run:

### `pnpm dev`

Runs the app in development mode with hot-reloading.

### `pnpm build`

Builds the application for production to the `.next` folder.

### `pnpm start`

Starts the production server after building the application.

### `pnpm lint`

Runs ESLint to check for code quality issues.

## Project Structure

```
src/
├── app/                 # App Router pages and layouts
│   ├── (auth)/          # Authentication routes
│   ├── (onboarding)/    # Onboarding flows
│   │   └── onboarding/  # Onboarding pages
│   │       ├── _components/      # Shared onboarding components
│   │       ├── individual/       # Individual onboarding flow
│   │       ├── organization/     # Organization creation flow
│   │       └── organization-invite-only/ # Join organization flow
│   └── (pages)/         # General pages
├── components/          # Reusable UI components
│   └── ui/              # Shadcn UI components
├── data/                # Static data files
├── lib/                 # Utility functions and helpers
└── types/               # TypeScript type definitions
```

## Onboarding Flow

The application supports three distinct onboarding paths:

1. **Individual Project**: For personal use with customizable options
2. **Create a new Organization**: Set up a collaborative workspace for teams
3. **Join an existing Organization**: Connect to an organization via invitation

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
