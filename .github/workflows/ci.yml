# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

jobs:
  build:
    runs-on: ubuntu-latest

    env:
      PROJECT_ID: xnufxmuwqpqsjvrfexkof
      NEXT_PUBLIC_SUPABASE_URL: https://xnufxmuwqpqsjvrfexko.supabase.co
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}

    steps:
      # 1. Checkout your repository
      - name: Checkout repository
        uses: actions/checkout@v4

      # 2. Install pnpm CLI (makes pnpm available in PATH)
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "10.10.0"
          run_install: false

      # 3. Setup Node.js (v22.14.0) with pnpm dependency caching
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22.14.0"
          cache: "pnpm"
          cache-dependency-path: "pnpm-lock.yaml"

      # 4. Restore Next.js cache for faster builds
      - name: Cache Next.js build output
        uses: actions/cache@v4
        with:
          path: |
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.yaml') }}-${{ hashFiles('**/*.{js,jsx,ts,tsx}') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.yaml') }}-

      # 5. Install dependencies
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      # 6. Run ESLint and treat warnings as failures
      - name: Lint (fail on warnings)
        run: pnpm exec eslint . --ext .ts,.tsx --max-warnings 0

      # 7. Build the project (exit non-zero on errors)
      - name: Build
        run: pnpm build
